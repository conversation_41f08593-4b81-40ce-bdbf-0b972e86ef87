﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


template <typename R>
struct VirtualFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename R, typename T1>
struct VirtualFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
struct InterfaceActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename T1>
struct InterfaceActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename R>
struct InterfaceFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename R, typename T1>
struct InterfaceFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};

struct Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87;
struct Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6;
struct Action_2_t33A6FCCA2D3E549AA88F7D182C5D500BD6473B5F;
struct ConcurrentDictionary_2_tF598E45B2A3ECB23FD311D829FB0AB32B1201ACF;
struct ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582;
struct ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB;
struct ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91;
struct ContainerPropertyBag_1_t2279C723A3D81E0BE90A787349C987FC490033EF;
struct ContainerPropertyBag_1_t3A0DBCD09BBF64291800B0275CC7E1F2EECA0FEF;
struct ContainerPropertyBag_1_t4B3135B801859CF3F6877AEC19D1DA289FFC301D;
struct ContainerPropertyBag_1_tA2A29BDB4DB4DD00EDAB85E6154345DEEC0BF63B;
struct ContainerPropertyBag_1_t2A9DE8C991C6C2537259B466807EEEFC4B68B0E6;
struct ContainerPropertyBag_1_t34F580FC14E2EF6488F9B8CEE5D00BE38DBC2496;
struct ContainerPropertyBag_1_tFC736A3382282864C67252047BC56B960AD3C486;
struct ContainerPropertyBag_1_t5E98E44A087C66C99B6B48FE49F35A9460817892;
struct ContainerPropertyBag_1_tA7C51CF32C3F7B0B198B114B368B2E5BA498D33E;
struct ContainerPropertyBag_1_tC5EF4ED9E953B090F0F1A7314A738165FB6277B8;
struct ContainerPropertyBag_1_t827E6E22E54C7702E357022F57F04833A6FAF09E;
struct ContainerPropertyBag_1_t569DC4A1FC35A7A716C097213C60CC9FA986B5A9;
struct Dictionary_2_t4DFECF621C500453991651A5A026D9FCC484C340;
struct Dictionary_2_tBCB806C16A2ADAA77600E3249746F7B6CB638FB1;
struct Dictionary_2_tF0CBEE260E4459BD7FBD88642D10D13072A1733B;
struct Dictionary_2_t493CD60A61DC7E34769F611A3B574105A00F7C8E;
struct Dictionary_2_tB38AB04EAE4BF1A8AB4F3EB5C6D61C0BA1C63485;
struct Dictionary_2_tE037C9F1FF6B6E97DF0AE5A0BE4042375C5D9D8C;
struct Dictionary_2_t4A4C489FCB5A50BCD97D454B320936FFB26B6259;
struct Dictionary_2_tAFF77A63BAC70A823CF7D433372A35F9A9FA53F9;
struct Dictionary_2_tF48CBAD539B087D7950015E9ACC37504C4F7F095;
struct Dictionary_2_t4C7611B1B9A7003542D940F8505DCFF0186D9648;
struct Dictionary_2_t2EAF120A37FFF6007EA0D0D9C99EEE1028E5CD2E;
struct Func_1_tD5C081AE11746B200C711DD48DBEB00E3A9276D4;
struct Func_1_t367387BB2C476D3F32DB12161B5FDC128DC3231C;
struct Func_1_t067EFEB6C5607DEFF52B09F0A11295959E292D7C;
struct Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4;
struct Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164;
struct Func_2_tE1F0D41563EE092E5E5540B061449FDE88F1DC00;
struct Func_2_t9A0D493A82DCC47C9C819A3B045E02D9B5DDCE1B;
struct Func_2_t7AF8146EC94DFCBB0F1B3E70111C1FB21D39F00E;
struct IEnumerable_1_tF5487DDC9BFE72D77C0A279762BD5851F02B4F03;
struct IEnumerable_1_t9BFC4EA32B04B96A5BB13A056B7E299ADC431143;
struct IEnumerable_1_tE0A67F3FB4C800B39F37C976E22371A7DF4DDB1C;
struct IEnumerable_1_tF95C9E01A913DD50575531C8305932628663D9E9;
struct IEnumerable_1_t6686595E4CB7AC210F0EF075F7B1DD4A21D3902B;
struct IEnumerator_1_t17A98E9C91AD59AC8DCA7D9C70E659E9F6583901;
struct IEqualityComparer_1_t0C79004BFE79D9DBCE6C2250109D31D468A9A68E;
struct IOrderedEnumerable_1_tC9F0A12162DCD59B555B0727A93DECF9082C971C;
struct IOrderedEnumerable_1_t0E680E8E1A4A676334F2A0C9A6F9B93135A65EAC;
struct IReadOnlyList_1_tFAB749F93AC9273AC78377822795638920495BA2;
struct List_1_tE031E2B1B3599B1B808F7E565E285D0265829963;
struct List_1_tB0305892E26D0C482A75FD711E7CF6277D867EAA;
struct List_1_t514193F05FE01729A5ADF1CBD11F2A3282FEA464;
struct List_1_tF5A67096A0D68151D8E28B9AC51E2BEBA8BA6488;
struct List_1_t02FDA4992BFC534B37236850E63707788E0AE2AE;
struct List_1_t5D909E7F131356377BE392FFE312172FA301EB67;
struct List_1_tF7ED81D8CAE526E27D4579AA1E0D00A211BAE4FE;
struct List_1_t097837D1E5E15D037E5CC2958A380C5BB39F6B54;
struct List_1_t061B25A9A84AEF321FDDB1F001818B7F63981724;
struct List_1_t9F62F67493B4FDE523DD405881E159817B80CAC2;
struct List_1_tD37BEFB27A25988692C61CAA0CFCDE15B216B1DC;
struct List_1_t4A27DCC9A4080D8DA642DEA4EFFEBA72D6471715;
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D;
struct List_1_tF7A85406B1E07A43CE2AD7A590CA5354CAC5A8E9;
struct List_1_t4B77DB8D00EC6CC4705EB5F2FCC506472734EA72;
struct ObjectPool_1_t13378E8CD14858BB05EA28BAB9CC49EDB91BA259;
struct ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A;
struct PropertyBag_1_tD413081183D831D5FD1DB047C6388E9EA8522318;
struct PropertyBag_1_tFA4D14E31A2A501D102D88481DCBC9501A6EF390;
struct PropertyBag_1_t0967321B2D6F146150A7805676EB01A1FD0E97B6;
struct PropertyBag_1_tE522EC0E4E4C916EBF192933D686DEFDD27B9474;
struct PropertyBag_1_t2F1387D95A1E9B93E66C397645BFB8BCD265219B;
struct PropertyBag_1_tC24A0A7B21F69286D618A79A4F7CD6F8E9E64E46;
struct PropertyBag_1_t1E23861777A25A773264A698434477DCE18B34CF;
struct PropertyBag_1_tA16FBB091D74A21AC539D829F65E5E8FAF2DB075;
struct PropertyBag_1_t91378217184AE0BD640D9A003EF2CB208071D593;
struct PropertyBag_1_tDA117C10FEABA385803A90E976050FB2D270FAD2;
struct PropertyBag_1_tD11940BE9DAD74F61B141A397F3D5722E7FF2601;
struct PropertyBag_1_t206E61AF5C231793E92CCCFEA28B724EEE20722D;
struct Property_2_tC4748332B44E20FE7C498E90E74A718F89F44471;
struct Property_2_tAB010676E84B6005B47BB524D152BA4859373D2D;
struct Property_2_tBEC632CE0FBA6F323B365C780F1F4C4C62A850ED;
struct Property_2_t4214D5666464CEE51F044573A59C738C2D562085;
struct Property_2_t4395571972396AEE0D2671461D1603BB8D296451;
struct Property_2_tE0D61821CEE6E9C49C6C1BCAD5F62D78A24F7A56;
struct Property_2_t4CA8B837C8B5E5C163CFFA715548D9980679EC46;
struct Property_2_tC1A1D28677DB791F77E60F16E0A734BE3616141F;
struct Property_2_tDFB93AAC56F8B372E59EEACF0AD971295487CCEA;
struct Property_2_t69BB6E67D83C59343BA1D7DCC6BDE82B297986D1;
struct Property_2_tD234CECBCBBAF0E5115582B104A904CB400D927F;
struct Property_2_tDF56505DA6AA5557884F8A3D032C17337E8E3951;
struct Tables_t3227C6BAB9A24497B5BC2BD985C3E4D2277B3FF6;
struct Tables_t316BB64324D26FA8258618817399CACAC8488FC7;
struct Tables_t6037A68AF76F1F1F11530A3316850BA0C19358EC;
struct KeyValuePair_2U5BU5D_tAC18A9810981C66C9D2F31F570CC2487A3BD1FE9;
struct KeyValuePair_2U5BU5D_tCE588BEDBCF8B165F2080DFD16E96DF3B5A982E7;
struct KeyValuePair_2U5BU5D_t146564E6A49A4F28A57A5ED01FB1A805D98D62E6;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct MemberInfoU5BU5D_t4CB6970BB166E8E1CFB06152B2A2284971873053;
struct MethodInfoU5BU5D_tDF3670604A0AECF814A0B0BA09B91FBF0D6A3265;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263;
struct ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129;
struct Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA;
struct Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235;
struct BoundsIntPropertyBag_t36236827A488493B27B94BF7EB678949732F2052;
struct BoundsPropertyBag_tA22B9256F487C40279FFFA58AD0E8D6CF29BA83F;
struct ColorPropertyBag_tFA63F917A9DAA7F376DD37FF114B3D548E00BCC6;
struct CreatePropertyAttribute_t481619BD84F5E29767718DE127DD1E781A3B6F92;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct DontCreatePropertyAttribute_t759A6C6F524F0E05DEFD69A827104FEC2B648C0E;
struct FieldInfo_t;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA;
struct IPropertyBag_t602D45F19F9BFA3E370929AC5023258AAF62E8C2;
struct IPropertyBagRegister_tE78909517CE561497EC85687E3A2B6EA89519FF0;
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB;
struct InvalidProgramException_t5715C9ED8CFE88C0DB9CD5614BA3D72BA9259C9B;
struct MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553;
struct MemberInfo_t;
struct MethodBase_t;
struct MethodInfo_t;
struct MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8;
struct NonSerializedAttribute_t3E57D1A7E7F31DE192694AB4C8927BAE7D1BE177;
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A;
struct PropertyInfo_t;
struct RectIntPropertyBag_t523552E9F6C38079E84B8F2ADE24828B4BC9DE73;
struct RectPropertyBag_tA405FA98439EEF5D80DA2FC601583B49CCB638EC;
struct ReflectedPropertyBagAttribute_tF96B3D583AE055F3962792F4B6DDBB973322792D;
struct ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct SerializeField_t18B4EDCAA7D3D570AAB8A5CCBF29B2CEA7A3CA9C;
struct String_t;
struct StringBuilder_t;
struct SystemVersionPropertyBag_t2BC09BA510A4DE999004F35E9930AA414DF46E8A;
struct Type_t;
struct Vector2IntPropertyBag_t2C43760B9A4208AD87AEF2615AEF035A489502AF;
struct Vector2PropertyBag_t566CAC975576D5F03B957E62DBA553ECE659AB49;
struct Vector3IntPropertyBag_t534CCCF8AD649649C930E5F3DDA3C0A734F932C7;
struct Vector3PropertyBag_t4FDB90B998E0F82C33B88823F8108862054CED41;
struct Vector4PropertyBag_t648481977877F79BDB6095EE5B491D8757A9DB5C;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct PositionProperty_t413AD67DF4061C4B811A40AA01E44568337B8FA5;
struct SizeProperty_t9A3CD66744B590FB53B427877E1E781350ADF5C9;
struct CenterProperty_tD6327DAAB6BB3A87B57B00FDBB79F5891F611272;
struct ExtentsProperty_t59E99CC5DAA1A0F80F2344E6297BABFF27F0B88F;
struct AProperty_t71D13C040377A5A0BDD9E1614DC61B65689411AC;
struct BProperty_t8C06F5F0A1928368B508116004897B283E10E5C4;
struct GProperty_t59EEA3B6DFE305EF122097DBA3B950684B224ACD;
struct RProperty_t8729556DDC5FF3CC31717171450B4EA749328E5D;
struct HeightProperty_tA93FD47B23964C044C6A92A943AFA11CAFF3E661;
struct WidthProperty_t342CF43E06CF1BB3A8B11095799F09CDA16C0080;
struct XProperty_tE606E1E3830D04DAEB0CE238A511A8CD0AFCF91F;
struct YProperty_t405447FD0661D730A61A82DBB5322B7645C9C409;
struct HeightProperty_t412F58E91B9683344CB1007B485A4479A0698790;
struct WidthProperty_t7CC0B7F8C816515B4ED673CBFFA702091CDA5CD7;
struct XProperty_tF3F7B9087649DB0DA8ABA99553075498B0D9E9BC;
struct YProperty_t6ED0B1CD2A67BAC3E33FA1CFD4424E0469DED13D;
struct U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523;
struct U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D;
struct BuildProperty_t7D4DAF6A9110609825BB8D6A15C94A6BAF28E100;
struct MajorProperty_tBBB67865E97B18DD17B9FB742398C3A3F79E8A54;
struct MinorProperty_tE0CA1F38999206EF615F6DAAF085F12C45D6D7DC;
struct RevisionProperty_t50A6B465CC86B9998606B7D5F614AE674BE69B6B;
struct U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203;
struct ITypeConstructor_tAEA6BC4B87F0F3739E4457F21908EF55F666EB08;
struct NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE;
struct TypeConstructorVisitor_tFC61250F68BF43FE889F5B1002CBFF90648E9C7F;
struct XProperty_t975009BA2182514E2B728FF83159426523F5C743;
struct YProperty_t541C5E0B4E92F21E8E986589A75A6411FC7496ED;
struct XProperty_t8C4D7E192D5B86997EE4863E5A6999B9543590E1;
struct YProperty_tBA23A08DCCCC01ED187D24048C43EAA5AB648E7D;
struct XProperty_tB7813C282F53A8DE5C913CB5B66811EF57A86ACF;
struct YProperty_t737EB7504629EEBEB734ABDAF170EE06776D3C55;
struct ZProperty_t6B9433EC1EB2C6EE71D8CD868ADA04EF3AFD90D7;
struct XProperty_t012028338C3B87093E42427325BC449033296278;
struct YProperty_tE39DDA65525AD937577DFBB83BD942BDE5D3650F;
struct ZProperty_tCE2945E21C8A6EDBDED46B6106BDBB6852C1FB9B;
struct WProperty_t8C921D95C0FC58C4F39423D4E530BBEDE6447841;
struct XProperty_t7D24F952200020A50C34C5525355F6308A3B9CDC;
struct YProperty_t140E8C4F8AB9EF9D6F3B7EBFDADFA838271CB395;
struct ZProperty_t431D10EB8A38306B642314AB1AD505F3BB355322;

IL2CPP_EXTERN_C RuntimeClass* AProperty_t71D13C040377A5A0BDD9E1614DC61B65689411AC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* BProperty_t8C06F5F0A1928368B508116004897B283E10E5C4_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* BoundsIntPropertyBag_t36236827A488493B27B94BF7EB678949732F2052_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* BoundsPropertyBag_tA22B9256F487C40279FFFA58AD0E8D6CF29BA83F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* BuildProperty_t7D4DAF6A9110609825BB8D6A15C94A6BAF28E100_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* CenterProperty_tD6327DAAB6BB3A87B57B00FDBB79F5891F611272_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ColorPropertyBag_tFA63F917A9DAA7F376DD37FF114B3D548E00BCC6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ContainerPropertyBag_1_t2279C723A3D81E0BE90A787349C987FC490033EF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ContainerPropertyBag_1_t2A9DE8C991C6C2537259B466807EEEFC4B68B0E6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ContainerPropertyBag_1_t34F580FC14E2EF6488F9B8CEE5D00BE38DBC2496_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ContainerPropertyBag_1_t3A0DBCD09BBF64291800B0275CC7E1F2EECA0FEF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ContainerPropertyBag_1_t4B3135B801859CF3F6877AEC19D1DA289FFC301D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ContainerPropertyBag_1_t569DC4A1FC35A7A716C097213C60CC9FA986B5A9_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ContainerPropertyBag_1_t5E98E44A087C66C99B6B48FE49F35A9460817892_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ContainerPropertyBag_1_t827E6E22E54C7702E357022F57F04833A6FAF09E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ContainerPropertyBag_1_tA7C51CF32C3F7B0B198B114B368B2E5BA498D33E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ContainerPropertyBag_1_tC5EF4ED9E953B090F0F1A7314A738165FB6277B8_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ContainerPropertyBag_1_tFC736A3382282864C67252047BC56B960AD3C486_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ExtentsProperty_t59E99CC5DAA1A0F80F2344E6297BABFF27F0B88F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* FieldInfo_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Func_1_t067EFEB6C5607DEFF52B09F0A11295959E292D7C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Func_1_t367387BB2C476D3F32DB12161B5FDC128DC3231C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Func_2_t7AF8146EC94DFCBB0F1B3E70111C1FB21D39F00E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GProperty_t59EEA3B6DFE305EF122097DBA3B950684B224ACD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* HeightProperty_t412F58E91B9683344CB1007B485A4479A0698790_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* HeightProperty_tA93FD47B23964C044C6A92A943AFA11CAFF3E661_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IEnumerable_1_t9BFC4EA32B04B96A5BB13A056B7E299ADC431143_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IEnumerator_1_t17A98E9C91AD59AC8DCA7D9C70E659E9F6583901_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IPropertyBagRegister_tE78909517CE561497EC85687E3A2B6EA89519FF0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IPropertyBag_t602D45F19F9BFA3E370929AC5023258AAF62E8C2_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IReadOnlyCollection_1_tB0C1D5CC73DC8C9F615E80A005D312E046F4D762_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IReadOnlyList_1_tFAB749F93AC9273AC78377822795638920495BA2_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ITypeConstructor_tAEA6BC4B87F0F3739E4457F21908EF55F666EB08_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InvalidProgramException_t5715C9ED8CFE88C0DB9CD5614BA3D72BA9259C9B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t4B77DB8D00EC6CC4705EB5F2FCC506472734EA72_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MajorProperty_tBBB67865E97B18DD17B9FB742398C3A3F79E8A54_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MinorProperty_tE0CA1F38999206EF615F6DAAF085F12C45D6D7DC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PositionProperty_t413AD67DF4061C4B811A40AA01E44568337B8FA5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PropertyInfo_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RProperty_t8729556DDC5FF3CC31717171450B4EA749328E5D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RectIntPropertyBag_t523552E9F6C38079E84B8F2ADE24828B4BC9DE73_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RectPropertyBag_tA405FA98439EEF5D80DA2FC601583B49CCB638EC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RevisionProperty_t50A6B465CC86B9998606B7D5F614AE674BE69B6B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RuntimeObject_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SizeProperty_t9A3CD66744B590FB53B427877E1E781350ADF5C9_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* StringBuilder_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* String_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SystemVersionPropertyBag_t2BC09BA510A4DE999004F35E9930AA414DF46E8A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TypeConstructorVisitor_tFC61250F68BF43FE889F5B1002CBFF90648E9C7F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Type_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector2IntPropertyBag_t2C43760B9A4208AD87AEF2615AEF035A489502AF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector2PropertyBag_t566CAC975576D5F03B957E62DBA553ECE659AB49_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector3IntPropertyBag_t534CCCF8AD649649C930E5F3DDA3C0A734F932C7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector3PropertyBag_t4FDB90B998E0F82C33B88823F8108862054CED41_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector4PropertyBag_t648481977877F79BDB6095EE5B491D8757A9DB5C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* WProperty_t8C921D95C0FC58C4F39423D4E530BBEDE6447841_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* WidthProperty_t342CF43E06CF1BB3A8B11095799F09CDA16C0080_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* WidthProperty_t7CC0B7F8C816515B4ED673CBFFA702091CDA5CD7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* XProperty_t012028338C3B87093E42427325BC449033296278_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* XProperty_t7D24F952200020A50C34C5525355F6308A3B9CDC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* XProperty_t8C4D7E192D5B86997EE4863E5A6999B9543590E1_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* XProperty_t975009BA2182514E2B728FF83159426523F5C743_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* XProperty_tB7813C282F53A8DE5C913CB5B66811EF57A86ACF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* XProperty_tE606E1E3830D04DAEB0CE238A511A8CD0AFCF91F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* XProperty_tF3F7B9087649DB0DA8ABA99553075498B0D9E9BC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* YProperty_t140E8C4F8AB9EF9D6F3B7EBFDADFA838271CB395_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* YProperty_t405447FD0661D730A61A82DBB5322B7645C9C409_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* YProperty_t541C5E0B4E92F21E8E986589A75A6411FC7496ED_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* YProperty_t6ED0B1CD2A67BAC3E33FA1CFD4424E0469DED13D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* YProperty_t737EB7504629EEBEB734ABDAF170EE06776D3C55_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* YProperty_tBA23A08DCCCC01ED187D24048C43EAA5AB648E7D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* YProperty_tE39DDA65525AD937577DFBB83BD942BDE5D3650F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ZProperty_t431D10EB8A38306B642314AB1AD505F3BB355322_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ZProperty_t6B9433EC1EB2C6EE71D8CD868ADA04EF3AFD90D7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ZProperty_tCE2945E21C8A6EDBDED46B6106BDBB6852C1FB9B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF____m_FieldInfo_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984____m_PropertyInfo_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral007174B876FA7EECC4152046B9308966D3E2B5B8;
IL2CPP_EXTERN_C String_t* _stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B;
IL2CPP_EXTERN_C String_t* _stringLiteral0A04B971B03DA607CE6C455184037B660CA89F78;
IL2CPP_EXTERN_C String_t* _stringLiteral0BA8CB3B900ECEF5E697192B8CDA6B626EB0CE72;
IL2CPP_EXTERN_C String_t* _stringLiteral0F9BE39FE19AFE46EEE9236F011A9433EAA21F69;
IL2CPP_EXTERN_C String_t* _stringLiteral115036F179EA48E7662F9BE55E0E00A42DDE6DA7;
IL2CPP_EXTERN_C String_t* _stringLiteral20E39C3AB7068FAFD9E4B868E16D2E5BC64D4952;
IL2CPP_EXTERN_C String_t* _stringLiteral2B9B6C84CC15492CCB290C4B79418FA6D7DD24C1;
IL2CPP_EXTERN_C String_t* _stringLiteral2F1705A1AA8BA6FCE863E7F2CBA4BC28458C77AE;
IL2CPP_EXTERN_C String_t* _stringLiteral30EAC41CDC5339E618C57498BD0319713142D96E;
IL2CPP_EXTERN_C String_t* _stringLiteral316316045E2DB71BB9C0748EE882DBF00C83FD8E;
IL2CPP_EXTERN_C String_t* _stringLiteral3477F5F15DC74EADC79ED337F3988716EE1670E9;
IL2CPP_EXTERN_C String_t* _stringLiteral38FB386B58970DA493E868CAC2AC6550E559B5D7;
IL2CPP_EXTERN_C String_t* _stringLiteral3EE5A4671A4E5AEBD31CA5F013A7773DC18ED22B;
IL2CPP_EXTERN_C String_t* _stringLiteral40915EBE202C5DCE0A0658C8080663943D342AF3;
IL2CPP_EXTERN_C String_t* _stringLiteral45045578780FD0B9DC1862175DE1C8476691896A;
IL2CPP_EXTERN_C String_t* _stringLiteral4609D79FE2FAD95C38B6DA64FC671E8594984D4C;
IL2CPP_EXTERN_C String_t* _stringLiteral46A3E4CF4159352EA245C4FF7641E52B161DCFF3;
IL2CPP_EXTERN_C String_t* _stringLiteral4D9AD25D7DFFA5DF4C19141B7788780E102E8D03;
IL2CPP_EXTERN_C String_t* _stringLiteral4EC998918E53FB3C460B5C3148F7ABAADC6EF6A1;
IL2CPP_EXTERN_C String_t* _stringLiteral50639CAD49418C7B223CC529395C0E2A3892501C;
IL2CPP_EXTERN_C String_t* _stringLiteral53009C1E06A9EDAD1337D065B65F07A5745635D7;
IL2CPP_EXTERN_C String_t* _stringLiteral53CDB29A789F785BCE9C340E574B4877B0E4D7EB;
IL2CPP_EXTERN_C String_t* _stringLiteral5596D6057770EA4D394A7BADC0C6729E361C1342;
IL2CPP_EXTERN_C String_t* _stringLiteral5E2CF53B436B05607F3EE43FB81D75975A00662A;
IL2CPP_EXTERN_C String_t* _stringLiteral647E0FB2B5E859CC4BD7C73623B82C8EFABA2563;
IL2CPP_EXTERN_C String_t* _stringLiteral673CC9996FD90AFE21BD8D0E6E6824353AF4BDA2;
IL2CPP_EXTERN_C String_t* _stringLiteral704C5FCE5B7025BF4463EAB8CEF61810FB875636;
IL2CPP_EXTERN_C String_t* _stringLiteral725B1CAFF9B49E1231FDA15B85166BBEFAA36A11;
IL2CPP_EXTERN_C String_t* _stringLiteral758733BDBED83CBFF4F635AC26CA92AAE477F75D;
IL2CPP_EXTERN_C String_t* _stringLiteral7E03D76FD473371698842C3641D0E7A9547686ED;
IL2CPP_EXTERN_C String_t* _stringLiteral88BDF3D0791A560245652E772545C49897854443;
IL2CPP_EXTERN_C String_t* _stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1;
IL2CPP_EXTERN_C String_t* _stringLiteral9CE1604D659135925CCC4DD1F526AFFE42E689F1;
IL2CPP_EXTERN_C String_t* _stringLiteralA33A5CAE02B786C2060461DF8C6764B4C05E9423;
IL2CPP_EXTERN_C String_t* _stringLiteralA9FEAF5F50923952C1AC3A473DE3C7E17D23B907;
IL2CPP_EXTERN_C String_t* _stringLiteralAAA7BDF9D882584A96DB5561B9FA7C14404B3824;
IL2CPP_EXTERN_C String_t* _stringLiteralBD2F86A6F80C957D36F5792A1101E475BB128D86;
IL2CPP_EXTERN_C String_t* _stringLiteralC8FA9ED3100EEB33720799E0BF36802352500BBE;
IL2CPP_EXTERN_C String_t* _stringLiteralCD5BCEE5AAB18574F8AD6E7DDC5232CE0BE6D6E3;
IL2CPP_EXTERN_C String_t* _stringLiteralCDEC055C1A4411C67143F959BC96DE196E510348;
IL2CPP_EXTERN_C String_t* _stringLiteralCE876740FAE0F74584A26D196FFCDCF9055FFB6A;
IL2CPP_EXTERN_C String_t* _stringLiteralD890B2BC5E5200965CD02403ABB6C221A614A1B7;
IL2CPP_EXTERN_C String_t* _stringLiteralE1E5CE10BE86E259146E8ADE82FB423C65C3FFD7;
IL2CPP_EXTERN_C String_t* _stringLiteralE3165827D406DD8F354BE7515C630A771E98916A;
IL2CPP_EXTERN_C String_t* _stringLiteralE3AB954C27345B5777E41817C31696D6AC0E87C1;
IL2CPP_EXTERN_C String_t* _stringLiteralE3DFC065B6A6D9931B797808DD066491AAB82B29;
IL2CPP_EXTERN_C String_t* _stringLiteralEBF5FE0CBF3EEA67902EEA700B7216E3DB0D8E84;
IL2CPP_EXTERN_C String_t* _stringLiteralEF8AE9E6CBCFDABA932FBEB4C85964F450F724F5;
IL2CPP_EXTERN_C String_t* _stringLiteralF1A902BAD5B9128E503E43473944346B65454351;
IL2CPP_EXTERN_C String_t* _stringLiteralF26F502B14F503952C33ADFF928357DED0388E8D;
IL2CPP_EXTERN_C String_t* _stringLiteralF2C108AA83BE591285C6E2190E1EEB2F1ADBAB24;
IL2CPP_EXTERN_C String_t* _stringLiteralF3C6C902DBF80139640F6554F0C3392016A8ADF7;
IL2CPP_EXTERN_C String_t* _stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D;
IL2CPP_EXTERN_C String_t* _stringLiteralFE09717218C9DB873D50817C8EE39FA626116789;
IL2CPP_EXTERN_C const RuntimeMethod* ConcurrentDictionary_2_TryAdd_m2CEBA7D69347F97B298F46E19C8736585DA58317_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConcurrentDictionary_2_TryGetValue_mBD42E58E3B97D8884F8ABD2ED7FB00AE05C3D7B3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConcurrentDictionary_2_TryGetValue_mC760AE4BCF6AFC3CB7C64E8A405B651395A9D238_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConcurrentDictionary_2_TryGetValue_mDEA54BBDF227F094A9D476D95BC3A3DF5077048B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConcurrentDictionary_2__ctor_m2CD607D007C68544A77E46F721E1F7A20EFBD2BE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConcurrentDictionary_2__ctor_m4D5B845B9E86D2F0D817BCD2FB496291F8B6734B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConcurrentDictionary_2__ctor_m6B09103AE0E5ACDEA8157B5E09343647B1A55E80_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConcurrentDictionary_2_set_Item_m050171C2656C2982971EECEA9026CADF5AEF72B1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ConcurrentDictionary_2_set_Item_m5EF95C43AE2498CE264FCEE432AE379AD39651C2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m1A8D6D29FEC8E7BB998E685F8A0CDFF7685AA4D4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m354E48D6271D3BE0511C80D3BBE3389C3DAA0538_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m56A8F79AE5548660FD24207FA0D287E84B15BAF3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m8C4C8B4E763DF565341D1474E761E3E4628E1960_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m321C399F1891C5BDC7012B34EC2670D56995AB6D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m582D8F897B3FA9A209026D9CB0D2CBCA2091CD7A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m6DBD44004CEA7C31A6639E946F82B44E3797E8AE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mA9C2ACFCDCD7269581FBF0D648DE6495840A79F4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mE9A22EEC7A77E08AEF0D374E723786F7F1CB63E2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1_AddProperty_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m3CD2BB7313CFB80B5C21BFB971910AC718564437_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1_AddProperty_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m2D875551D9368012276D3FDD3DC12FD9A4BE3987_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1__ctor_m002B18E65D2DE825AC92248D478593AD1A3F524F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1__ctor_m0B3C26C575C4406E1478D8D4FFB946522E95F740_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1__ctor_m0D4393716F0234CD9550422C6CB0FCDAEE0376E4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1__ctor_m14BD526E7B6C44170A4EF0E46DD101AA7D1C558E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1__ctor_m48D10232A046CC68EF83CE5D3B5454C195078055_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1__ctor_m664143BCBE736CBB716E37C4DC7CFEC21BB17D92_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1__ctor_mB9D6850E283177CAED47EE19A760C168ADCCF35C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1__ctor_mD88378C16AD1F8C2A20F3521D02DA3B6811B1FB5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1__ctor_mDF062EDE672E0636FD2510B7574F918A7924BAE1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1__ctor_mED3672BE63BBCAE82B5E328167D256B540E9C287_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContainerPropertyBag_1__ctor_mFDAE4DA91A1BCD387794384FABBE63CD54647DFB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* CustomAttributeExtensions_GetCustomAttribute_TisCreatePropertyAttribute_t481619BD84F5E29767718DE127DD1E781A3B6F92_mD72676F02AEB5E69DC033D1585E2106D0A40B8DB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* CustomAttributeExtensions_GetCustomAttribute_TisDontCreatePropertyAttribute_t759A6C6F524F0E05DEFD69A827104FEC2B648C0E_m3C664BC58E8B561E56DB4E2FEB7BFA8FF2D4DFB8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* CustomAttributeExtensions_GetCustomAttribute_TisNonSerializedAttribute_t3E57D1A7E7F31DE192694AB4C8927BAE7D1BE177_m2ED36C703953CABE08AA5B122C7604D9C2733021_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* CustomAttributeExtensions_GetCustomAttribute_TisSerializeField_t18B4EDCAA7D3D570AAB8A5CCBF29B2CEA7A3CA9C_m8CA1E6E172CEFA60B58858DD7FFC13D340EC18E3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerable_All_TisType_t_m9EA20839AE686AA0B1927379BF8642A005ACFDE5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerable_First_TisMethodInfo_t_mF25EDA3EE26D685AA41FF116BE54C056DD0D179B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerable_OrderBy_TisMemberInfo_t_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_mE25DD086E1B3B32ABF6B1CC2AE0323EA6782FF91_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_mD9035342BE1CF0A3EEEE157D1796DA385C0C3F69_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* NonConstructable_Instantiate_m9FE0A208C273DC31D79B29A334F4DA2BF63A8AF6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ObjectPool_1_Get_m9A0C64A471F1134751E86CFABE36283B06E29F8D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ObjectPool_1_Release_mD38ED7186BD4E9178B260810EEA654454B3BB478_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ObjectPool_1__ctor_m35C71BF5CBC8DED0ED44D82D926F61E584CA5AD4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PropertyBag_Register_TisBoundsInt_t4E757DE5EFF9FCB42000F173360DDC63B5585485_m2606075F145B891D9C481304597F0464E5F8A8AA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PropertyBag_Register_TisBounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_m8A39CB42251835524F1068E49B6CF4BE5AD65032_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PropertyBag_Register_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1904BF66773B1294832B629C1FD5C32920AEEFDD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PropertyBag_Register_TisRectInt_t1744D10E1063135DA9D574F95205B98DAC600CB8_mF48E3B0570AD4D40331B885BCECF6A7A199327E4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PropertyBag_Register_TisRect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_m0EE819FDFD53BD79B74A1D270244805D2BD796A8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PropertyBag_Register_TisVector2Int_t69B2886EBAB732D9B880565E18E7568F3DE0CE6A_mF3979F0F1B3DEBC415368280E538AE95E7277EE3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PropertyBag_Register_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m9D4158376507E88B5DB59B60B56E2EBAC5567282_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PropertyBag_Register_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m74735F036C3540DFAF301D182A48C6EEB836F4E1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PropertyBag_Register_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m920D0EDA2A222D333EC898C0E4405F9D0A9000EF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PropertyBag_Register_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m1952774FD6803DED13AB832F702970F5F588917C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PropertyBag_Register_TisVersion_tE426DB5655D0F22920AE16A2AA9AB7781B8255A7_m976EBCE3744148CDF5AF1174347D890DF7B63A6D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Property_2__ctor_m0053E84A8D6B34BFDAB7795833421AF8A4F0BAEE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Property_2__ctor_m00887E3260117C76E2592EEB152472A442E5ECFF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Property_2__ctor_m3D03E5489D8244E706F51006F1B0D0C5250A7A86_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Property_2__ctor_m6CCA84842C767E1068B2F635177225E05FFE539B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Property_2__ctor_m6D93F660253A4C7EC344945B11B74A1EC5E2052B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Property_2__ctor_mE2253FF920B6B8BA104EB3F4C6E8A871A34892E7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ReflectedPropertyBagProvider_IsValidPropertyType_mFBA6F8CF9F5B94652229D6A151B5302C460F05BF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TypeTraits_IsContainer_m46D04F3E3219371CC5F133E2CC54BCA46FD72505_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TypeUtility_CheckCanBeInstantiated_mB2C921D41DC94D83758FC5CFDFEE26911F62B14F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TypeUtility_CheckIsAssignableFrom_m284900A7E86ACD68227E5E3DCC9430EEC7E1E03E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TypeUtility_SetExplicitInstantiationMethod_TisString_t_m1BB80FFC484AB56A416E91F9E4EAB2FF75861EFE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TypeUtility__cctor_mFCC4029AD0D2F3FBE3F7117AC485DF687DE409A5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CGetPropertyMembersU3Ed__22_System_Collections_IEnumerator_Reset_mBF507A053052F0001483795394E045C4E2EB826C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec_U3CGetPropertyMembersU3Eb__22_0_m50914E0FCA3654CF9091BFCA37780956B2CA0136_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec_U3C_cctorU3Eb__11_0_m59D3FD88A1841D56C6A2D231E993AA6D2E6465E1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec_U3C_cctorU3Eb__11_1_mCF36865D4A819F0C0DDA987F30077434E56BC826_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec_U3C_cctorU3Eb__11_2_mBA773F3E344C145FE939B19C5EE690F9FEC60716_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec_U3C_ctorU3Eb__10_0_m98A9F8B7DD2A14EBBB86606DCFE2CB988291758F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Char_t521A6F19B456D956AF452D926C32709DC03D6B17_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* RuntimeObject_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* String_t_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_0_0_0_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct MemberInfoU5BU5D_t4CB6970BB166E8E1CFB06152B2A2284971873053;
struct MethodInfoU5BU5D_tDF3670604A0AECF814A0B0BA09B91FBF0D6A3265;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t6ABB08D657305240CCB5811B938C3AA98914E43C 
{
};
struct ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582  : public RuntimeObject
{
	Tables_t3227C6BAB9A24497B5BC2BD985C3E4D2277B3FF6* ____tables;
	RuntimeObject* ____comparer;
	bool ____growLockArray;
	int32_t ____budget;
	KeyValuePair_2U5BU5D_tAC18A9810981C66C9D2F31F570CC2487A3BD1FE9* ____serializationArray;
	int32_t ____serializationConcurrencyLevel;
	int32_t ____serializationCapacity;
};
struct ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB  : public RuntimeObject
{
	Tables_t316BB64324D26FA8258618817399CACAC8488FC7* ____tables;
	RuntimeObject* ____comparer;
	bool ____growLockArray;
	int32_t ____budget;
	KeyValuePair_2U5BU5D_tCE588BEDBCF8B165F2080DFD16E96DF3B5A982E7* ____serializationArray;
	int32_t ____serializationConcurrencyLevel;
	int32_t ____serializationCapacity;
};
struct ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91  : public RuntimeObject
{
	Tables_t6037A68AF76F1F1F11530A3316850BA0C19358EC* ____tables;
	RuntimeObject* ____comparer;
	bool ____growLockArray;
	int32_t ____budget;
	KeyValuePair_2U5BU5D_t146564E6A49A4F28A57A5ED01FB1A805D98D62E6* ____serializationArray;
	int32_t ____serializationConcurrencyLevel;
	int32_t ____serializationCapacity;
};
struct List_1_t4B77DB8D00EC6CC4705EB5F2FCC506472734EA72  : public RuntimeObject
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A  : public RuntimeObject
{
	List_1_tF7A85406B1E07A43CE2AD7A590CA5354CAC5A8E9* ___m_List;
	Func_1_t067EFEB6C5607DEFF52B09F0A11295959E292D7C* ___m_CreateFunc;
	Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6* ___m_ActionOnGet;
	Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6* ___m_ActionOnRelease;
	Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6* ___m_ActionOnDestroy;
	int32_t ___m_MaxSize;
	bool ___m_CollectionCheck;
	int32_t ___U3CCountAllU3Ek__BackingField;
};
struct Property_2_tC4748332B44E20FE7C498E90E74A718F89F44471  : public RuntimeObject
{
	List_1_t4A27DCC9A4080D8DA642DEA4EFFEBA72D6471715* ___m_Attributes;
};
struct Property_2_tAB010676E84B6005B47BB524D152BA4859373D2D  : public RuntimeObject
{
	List_1_t4A27DCC9A4080D8DA642DEA4EFFEBA72D6471715* ___m_Attributes;
};
struct Property_2_tBEC632CE0FBA6F323B365C780F1F4C4C62A850ED  : public RuntimeObject
{
	List_1_t4A27DCC9A4080D8DA642DEA4EFFEBA72D6471715* ___m_Attributes;
};
struct Property_2_t4395571972396AEE0D2671461D1603BB8D296451  : public RuntimeObject
{
	List_1_t4A27DCC9A4080D8DA642DEA4EFFEBA72D6471715* ___m_Attributes;
};
struct Property_2_tE0D61821CEE6E9C49C6C1BCAD5F62D78A24F7A56  : public RuntimeObject
{
	List_1_t4A27DCC9A4080D8DA642DEA4EFFEBA72D6471715* ___m_Attributes;
};
struct Property_2_t4CA8B837C8B5E5C163CFFA715548D9980679EC46  : public RuntimeObject
{
	List_1_t4A27DCC9A4080D8DA642DEA4EFFEBA72D6471715* ___m_Attributes;
};
struct Property_2_tC1A1D28677DB791F77E60F16E0A734BE3616141F  : public RuntimeObject
{
	List_1_t4A27DCC9A4080D8DA642DEA4EFFEBA72D6471715* ___m_Attributes;
};
struct Property_2_tDFB93AAC56F8B372E59EEACF0AD971295487CCEA  : public RuntimeObject
{
	List_1_t4A27DCC9A4080D8DA642DEA4EFFEBA72D6471715* ___m_Attributes;
};
struct Property_2_t69BB6E67D83C59343BA1D7DCC6BDE82B297986D1  : public RuntimeObject
{
	List_1_t4A27DCC9A4080D8DA642DEA4EFFEBA72D6471715* ___m_Attributes;
};
struct Property_2_tD234CECBCBBAF0E5115582B104A904CB400D927F  : public RuntimeObject
{
	List_1_t4A27DCC9A4080D8DA642DEA4EFFEBA72D6471715* ___m_Attributes;
};
struct Property_2_tDF56505DA6AA5557884F8A3D032C17337E8E3951  : public RuntimeObject
{
	List_1_t4A27DCC9A4080D8DA642DEA4EFFEBA72D6471715* ___m_Attributes;
};
struct Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA  : public RuntimeObject
{
};
struct DefaultPropertyBagInitializer_tD44FA15437300A3C1D7AF7A4497D7AFAD306C571  : public RuntimeObject
{
};
struct MemberInfo_t  : public RuntimeObject
{
};
struct PropertyBag_t2F42343455C93CC5FB7CAD060175209F8A75AF92  : public RuntimeObject
{
};
struct PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C  : public RuntimeObject
{
};
struct ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF  : public RuntimeObject
{
	MethodInfo_t* ___m_CreatePropertyMethod;
	MethodInfo_t* ___m_CreatePropertyBagMethod;
	MethodInfo_t* ___m_CreateIndexedCollectionPropertyBagMethod;
	MethodInfo_t* ___m_CreateSetPropertyBagMethod;
	MethodInfo_t* ___m_CreateKeyValueCollectionPropertyBagMethod;
	MethodInfo_t* ___m_CreateKeyValuePairPropertyBagMethod;
	MethodInfo_t* ___m_CreateArrayPropertyBagMethod;
	MethodInfo_t* ___m_CreateListPropertyBagMethod;
	MethodInfo_t* ___m_CreateHashSetPropertyBagMethod;
	MethodInfo_t* ___m_CreateDictionaryPropertyBagMethod;
};
struct ReflectionUtilities_t9109365C3089351EFFA385C3EFDFAB6845914D99  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct StringBuilder_t  : public RuntimeObject
{
	CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB* ___m_ChunkChars;
	StringBuilder_t* ___m_ChunkPrevious;
	int32_t ___m_ChunkLength;
	int32_t ___m_ChunkOffset;
	int32_t ___m_MaxCapacity;
};
struct TypeTraits_t22E967F7897BA8C4B6AB3A3E7024CC9C0E4C2D94  : public RuntimeObject
{
};
struct TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523  : public RuntimeObject
{
};
struct U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	MemberInfo_t* ___U3CU3E2__current;
	int32_t ___U3CU3El__initialThreadId;
	Type_t* ___type;
	Type_t* ___U3CU3E3__type;
	RuntimeObject* ___U3CmembersU3E5__1;
	RuntimeObject* ___U3CU3Es__2;
	MemberInfo_t* ___U3CmemberU3E5__3;
	bool ___U3ChasDontCreatePropertyAttributeU3E5__4;
	bool ___U3ChasCreatePropertyAttributeU3E5__5;
	bool ___U3ChasNonSerializedAttributeU3E5__6;
	bool ___U3ChasSerializedFieldAttributeU3E5__7;
	FieldInfo_t* ___U3CfieldU3E5__8;
};
struct U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203  : public RuntimeObject
{
};
struct NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE  : public RuntimeObject
{
};
struct TypeConstructorVisitor_tFC61250F68BF43FE889F5B1002CBFF90648E9C7F  : public RuntimeObject
{
	RuntimeObject* ___TypeConstructor;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17 
{
	Il2CppChar ___m_value;
};
struct CreatePropertyAttribute_t481619BD84F5E29767718DE127DD1E781A3B6F92  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct DontCreatePropertyAttribute_t759A6C6F524F0E05DEFD69A827104FEC2B648C0E  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct FieldInfo_t  : public MemberInfo_t
{
};
struct FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF 
{
	FieldInfo_t* ___m_FieldInfo;
	String_t* ___U3CNameU3Ek__BackingField;
};
struct FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshaled_pinvoke
{
	FieldInfo_t* ___m_FieldInfo;
	char* ___U3CNameU3Ek__BackingField;
};
struct FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshaled_com
{
	FieldInfo_t* ___m_FieldInfo;
	Il2CppChar* ___U3CNameU3Ek__BackingField;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct MethodBase_t  : public MemberInfo_t
{
};
struct NonSerializedAttribute_t3E57D1A7E7F31DE192694AB4C8927BAE7D1BE177  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct PropertyAttribute_t5E0CB5A6CDA6E24CBD4FF26DE3B0C29D8BB54BF0  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct PropertyInfo_t  : public MemberInfo_t
{
};
struct PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984 
{
	PropertyInfo_t* ___m_PropertyInfo;
	String_t* ___U3CNameU3Ek__BackingField;
};
struct PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshaled_pinvoke
{
	PropertyInfo_t* ___m_PropertyInfo;
	char* ___U3CNameU3Ek__BackingField;
};
struct PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshaled_com
{
	PropertyInfo_t* ___m_PropertyInfo;
	Il2CppChar* ___U3CNameU3Ek__BackingField;
};
struct ReflectedPropertyBagAttribute_tF96B3D583AE055F3962792F4B6DDBB973322792D  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct SerializeField_t18B4EDCAA7D3D570AAB8A5CCBF29B2CEA7A3CA9C  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct PositionProperty_t413AD67DF4061C4B811A40AA01E44568337B8FA5  : public Property_2_tAB010676E84B6005B47BB524D152BA4859373D2D
{
};
struct SizeProperty_t9A3CD66744B590FB53B427877E1E781350ADF5C9  : public Property_2_tAB010676E84B6005B47BB524D152BA4859373D2D
{
};
struct CenterProperty_tD6327DAAB6BB3A87B57B00FDBB79F5891F611272  : public Property_2_tC4748332B44E20FE7C498E90E74A718F89F44471
{
};
struct ExtentsProperty_t59E99CC5DAA1A0F80F2344E6297BABFF27F0B88F  : public Property_2_tC4748332B44E20FE7C498E90E74A718F89F44471
{
};
struct AProperty_t71D13C040377A5A0BDD9E1614DC61B65689411AC  : public Property_2_tBEC632CE0FBA6F323B365C780F1F4C4C62A850ED
{
};
struct BProperty_t8C06F5F0A1928368B508116004897B283E10E5C4  : public Property_2_tBEC632CE0FBA6F323B365C780F1F4C4C62A850ED
{
};
struct GProperty_t59EEA3B6DFE305EF122097DBA3B950684B224ACD  : public Property_2_tBEC632CE0FBA6F323B365C780F1F4C4C62A850ED
{
};
struct RProperty_t8729556DDC5FF3CC31717171450B4EA749328E5D  : public Property_2_tBEC632CE0FBA6F323B365C780F1F4C4C62A850ED
{
};
struct HeightProperty_tA93FD47B23964C044C6A92A943AFA11CAFF3E661  : public Property_2_tE0D61821CEE6E9C49C6C1BCAD5F62D78A24F7A56
{
};
struct WidthProperty_t342CF43E06CF1BB3A8B11095799F09CDA16C0080  : public Property_2_tE0D61821CEE6E9C49C6C1BCAD5F62D78A24F7A56
{
};
struct XProperty_tE606E1E3830D04DAEB0CE238A511A8CD0AFCF91F  : public Property_2_tE0D61821CEE6E9C49C6C1BCAD5F62D78A24F7A56
{
};
struct YProperty_t405447FD0661D730A61A82DBB5322B7645C9C409  : public Property_2_tE0D61821CEE6E9C49C6C1BCAD5F62D78A24F7A56
{
};
struct HeightProperty_t412F58E91B9683344CB1007B485A4479A0698790  : public Property_2_t4395571972396AEE0D2671461D1603BB8D296451
{
};
struct WidthProperty_t7CC0B7F8C816515B4ED673CBFFA702091CDA5CD7  : public Property_2_t4395571972396AEE0D2671461D1603BB8D296451
{
};
struct XProperty_tF3F7B9087649DB0DA8ABA99553075498B0D9E9BC  : public Property_2_t4395571972396AEE0D2671461D1603BB8D296451
{
};
struct YProperty_t6ED0B1CD2A67BAC3E33FA1CFD4424E0469DED13D  : public Property_2_t4395571972396AEE0D2671461D1603BB8D296451
{
};
struct BuildProperty_t7D4DAF6A9110609825BB8D6A15C94A6BAF28E100  : public Property_2_tDF56505DA6AA5557884F8A3D032C17337E8E3951
{
};
struct MajorProperty_tBBB67865E97B18DD17B9FB742398C3A3F79E8A54  : public Property_2_tDF56505DA6AA5557884F8A3D032C17337E8E3951
{
};
struct MinorProperty_tE0CA1F38999206EF615F6DAAF085F12C45D6D7DC  : public Property_2_tDF56505DA6AA5557884F8A3D032C17337E8E3951
{
};
struct RevisionProperty_t50A6B465CC86B9998606B7D5F614AE674BE69B6B  : public Property_2_tDF56505DA6AA5557884F8A3D032C17337E8E3951
{
};
struct XProperty_t975009BA2182514E2B728FF83159426523F5C743  : public Property_2_tC1A1D28677DB791F77E60F16E0A734BE3616141F
{
};
struct YProperty_t541C5E0B4E92F21E8E986589A75A6411FC7496ED  : public Property_2_tC1A1D28677DB791F77E60F16E0A734BE3616141F
{
};
struct XProperty_t8C4D7E192D5B86997EE4863E5A6999B9543590E1  : public Property_2_t4CA8B837C8B5E5C163CFFA715548D9980679EC46
{
};
struct YProperty_tBA23A08DCCCC01ED187D24048C43EAA5AB648E7D  : public Property_2_t4CA8B837C8B5E5C163CFFA715548D9980679EC46
{
};
struct XProperty_tB7813C282F53A8DE5C913CB5B66811EF57A86ACF  : public Property_2_t69BB6E67D83C59343BA1D7DCC6BDE82B297986D1
{
};
struct YProperty_t737EB7504629EEBEB734ABDAF170EE06776D3C55  : public Property_2_t69BB6E67D83C59343BA1D7DCC6BDE82B297986D1
{
};
struct ZProperty_t6B9433EC1EB2C6EE71D8CD868ADA04EF3AFD90D7  : public Property_2_t69BB6E67D83C59343BA1D7DCC6BDE82B297986D1
{
};
struct XProperty_t012028338C3B87093E42427325BC449033296278  : public Property_2_tDFB93AAC56F8B372E59EEACF0AD971295487CCEA
{
};
struct YProperty_tE39DDA65525AD937577DFBB83BD942BDE5D3650F  : public Property_2_tDFB93AAC56F8B372E59EEACF0AD971295487CCEA
{
};
struct ZProperty_tCE2945E21C8A6EDBDED46B6106BDBB6852C1FB9B  : public Property_2_tDFB93AAC56F8B372E59EEACF0AD971295487CCEA
{
};
struct WProperty_t8C921D95C0FC58C4F39423D4E530BBEDE6447841  : public Property_2_tD234CECBCBBAF0E5115582B104A904CB400D927F
{
};
struct XProperty_t7D24F952200020A50C34C5525355F6308A3B9CDC  : public Property_2_tD234CECBCBBAF0E5115582B104A904CB400D927F
{
};
struct YProperty_t140E8C4F8AB9EF9D6F3B7EBFDADFA838271CB395  : public Property_2_tD234CECBCBBAF0E5115582B104A904CB400D927F
{
};
struct ZProperty_t431D10EB8A38306B642314AB1AD505F3BB355322  : public Property_2_tD234CECBCBBAF0E5115582B104A904CB400D927F
{
};
struct BindingFlags_t5DC2835E4AE9C1862B3AD172EF35B6A5F4F1812C 
{
	int32_t ___value__;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct InstantiationKind_t9B77929786BCA193B4A916F2F25793598CF0DF7D 
{
	int32_t ___value__;
};
struct MemberTypes_t26BAB0893BEC9328F2F64E8BACDA967C445632E5 
{
	int32_t ___value__;
};
struct MethodInfo_t  : public MethodBase_t
{
};
struct MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8  : public PropertyAttribute_t5E0CB5A6CDA6E24CBD4FF26DE3B0C29D8BB54BF0
{
	float ___min;
};
struct RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B 
{
	intptr_t ___value;
};
struct PropertyBag_1_tD413081183D831D5FD1DB047C6388E9EA8522318  : public RuntimeObject
{
	int32_t ___U3CInstantiationKindU3Ek__BackingField;
};
struct PropertyBag_1_tFA4D14E31A2A501D102D88481DCBC9501A6EF390  : public RuntimeObject
{
	int32_t ___U3CInstantiationKindU3Ek__BackingField;
};
struct PropertyBag_1_t0967321B2D6F146150A7805676EB01A1FD0E97B6  : public RuntimeObject
{
	int32_t ___U3CInstantiationKindU3Ek__BackingField;
};
struct PropertyBag_1_t2F1387D95A1E9B93E66C397645BFB8BCD265219B  : public RuntimeObject
{
	int32_t ___U3CInstantiationKindU3Ek__BackingField;
};
struct PropertyBag_1_tC24A0A7B21F69286D618A79A4F7CD6F8E9E64E46  : public RuntimeObject
{
	int32_t ___U3CInstantiationKindU3Ek__BackingField;
};
struct PropertyBag_1_t1E23861777A25A773264A698434477DCE18B34CF  : public RuntimeObject
{
	int32_t ___U3CInstantiationKindU3Ek__BackingField;
};
struct PropertyBag_1_tA16FBB091D74A21AC539D829F65E5E8FAF2DB075  : public RuntimeObject
{
	int32_t ___U3CInstantiationKindU3Ek__BackingField;
};
struct PropertyBag_1_t91378217184AE0BD640D9A003EF2CB208071D593  : public RuntimeObject
{
	int32_t ___U3CInstantiationKindU3Ek__BackingField;
};
struct PropertyBag_1_tDA117C10FEABA385803A90E976050FB2D270FAD2  : public RuntimeObject
{
	int32_t ___U3CInstantiationKindU3Ek__BackingField;
};
struct PropertyBag_1_tD11940BE9DAD74F61B141A397F3D5722E7FF2601  : public RuntimeObject
{
	int32_t ___U3CInstantiationKindU3Ek__BackingField;
};
struct PropertyBag_1_t206E61AF5C231793E92CCCFEA28B724EEE20722D  : public RuntimeObject
{
	int32_t ___U3CInstantiationKindU3Ek__BackingField;
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct Type_t  : public MemberInfo_t
{
	RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ____impl;
};
struct Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6  : public MulticastDelegate_t
{
};
struct ContainerPropertyBag_1_t2279C723A3D81E0BE90A787349C987FC490033EF  : public PropertyBag_1_tD413081183D831D5FD1DB047C6388E9EA8522318
{
	List_1_tE031E2B1B3599B1B808F7E565E285D0265829963* ___m_PropertiesList;
	Dictionary_2_t4DFECF621C500453991651A5A026D9FCC484C340* ___m_PropertiesHash;
};
struct ContainerPropertyBag_1_t3A0DBCD09BBF64291800B0275CC7E1F2EECA0FEF  : public PropertyBag_1_tFA4D14E31A2A501D102D88481DCBC9501A6EF390
{
	List_1_tB0305892E26D0C482A75FD711E7CF6277D867EAA* ___m_PropertiesList;
	Dictionary_2_tBCB806C16A2ADAA77600E3249746F7B6CB638FB1* ___m_PropertiesHash;
};
struct ContainerPropertyBag_1_t4B3135B801859CF3F6877AEC19D1DA289FFC301D  : public PropertyBag_1_t0967321B2D6F146150A7805676EB01A1FD0E97B6
{
	List_1_t514193F05FE01729A5ADF1CBD11F2A3282FEA464* ___m_PropertiesList;
	Dictionary_2_tF0CBEE260E4459BD7FBD88642D10D13072A1733B* ___m_PropertiesHash;
};
struct ContainerPropertyBag_1_t2A9DE8C991C6C2537259B466807EEEFC4B68B0E6  : public PropertyBag_1_t2F1387D95A1E9B93E66C397645BFB8BCD265219B
{
	List_1_tF5A67096A0D68151D8E28B9AC51E2BEBA8BA6488* ___m_PropertiesList;
	Dictionary_2_t493CD60A61DC7E34769F611A3B574105A00F7C8E* ___m_PropertiesHash;
};
struct ContainerPropertyBag_1_t34F580FC14E2EF6488F9B8CEE5D00BE38DBC2496  : public PropertyBag_1_tC24A0A7B21F69286D618A79A4F7CD6F8E9E64E46
{
	List_1_t02FDA4992BFC534B37236850E63707788E0AE2AE* ___m_PropertiesList;
	Dictionary_2_tB38AB04EAE4BF1A8AB4F3EB5C6D61C0BA1C63485* ___m_PropertiesHash;
};
struct ContainerPropertyBag_1_tFC736A3382282864C67252047BC56B960AD3C486  : public PropertyBag_1_t1E23861777A25A773264A698434477DCE18B34CF
{
	List_1_t5D909E7F131356377BE392FFE312172FA301EB67* ___m_PropertiesList;
	Dictionary_2_tE037C9F1FF6B6E97DF0AE5A0BE4042375C5D9D8C* ___m_PropertiesHash;
};
struct ContainerPropertyBag_1_t5E98E44A087C66C99B6B48FE49F35A9460817892  : public PropertyBag_1_tA16FBB091D74A21AC539D829F65E5E8FAF2DB075
{
	List_1_tF7ED81D8CAE526E27D4579AA1E0D00A211BAE4FE* ___m_PropertiesList;
	Dictionary_2_t4A4C489FCB5A50BCD97D454B320936FFB26B6259* ___m_PropertiesHash;
};
struct ContainerPropertyBag_1_tA7C51CF32C3F7B0B198B114B368B2E5BA498D33E  : public PropertyBag_1_t91378217184AE0BD640D9A003EF2CB208071D593
{
	List_1_t097837D1E5E15D037E5CC2958A380C5BB39F6B54* ___m_PropertiesList;
	Dictionary_2_tAFF77A63BAC70A823CF7D433372A35F9A9FA53F9* ___m_PropertiesHash;
};
struct ContainerPropertyBag_1_tC5EF4ED9E953B090F0F1A7314A738165FB6277B8  : public PropertyBag_1_tDA117C10FEABA385803A90E976050FB2D270FAD2
{
	List_1_t061B25A9A84AEF321FDDB1F001818B7F63981724* ___m_PropertiesList;
	Dictionary_2_tF48CBAD539B087D7950015E9ACC37504C4F7F095* ___m_PropertiesHash;
};
struct ContainerPropertyBag_1_t827E6E22E54C7702E357022F57F04833A6FAF09E  : public PropertyBag_1_tD11940BE9DAD74F61B141A397F3D5722E7FF2601
{
	List_1_t9F62F67493B4FDE523DD405881E159817B80CAC2* ___m_PropertiesList;
	Dictionary_2_t4C7611B1B9A7003542D940F8505DCFF0186D9648* ___m_PropertiesHash;
};
struct ContainerPropertyBag_1_t569DC4A1FC35A7A716C097213C60CC9FA986B5A9  : public PropertyBag_1_t206E61AF5C231793E92CCCFEA28B724EEE20722D
{
	List_1_tD37BEFB27A25988692C61CAA0CFCDE15B216B1DC* ___m_PropertiesList;
	Dictionary_2_t2EAF120A37FFF6007EA0D0D9C99EEE1028E5CD2E* ___m_PropertiesHash;
};
struct Func_1_t367387BB2C476D3F32DB12161B5FDC128DC3231C  : public MulticastDelegate_t
{
};
struct Func_1_t067EFEB6C5607DEFF52B09F0A11295959E292D7C  : public MulticastDelegate_t
{
};
struct Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4  : public MulticastDelegate_t
{
};
struct Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164  : public MulticastDelegate_t
{
};
struct Func_2_t7AF8146EC94DFCBB0F1B3E70111C1FB21D39F00E  : public MulticastDelegate_t
{
};
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
	String_t* ____paramName;
};
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct InvalidProgramException_t5715C9ED8CFE88C0DB9CD5614BA3D72BA9259C9B  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129  : public ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263
{
};
struct BoundsIntPropertyBag_t36236827A488493B27B94BF7EB678949732F2052  : public ContainerPropertyBag_1_t3A0DBCD09BBF64291800B0275CC7E1F2EECA0FEF
{
};
struct BoundsPropertyBag_tA22B9256F487C40279FFFA58AD0E8D6CF29BA83F  : public ContainerPropertyBag_1_t2279C723A3D81E0BE90A787349C987FC490033EF
{
};
struct ColorPropertyBag_tFA63F917A9DAA7F376DD37FF114B3D548E00BCC6  : public ContainerPropertyBag_1_t4B3135B801859CF3F6877AEC19D1DA289FFC301D
{
};
struct RectIntPropertyBag_t523552E9F6C38079E84B8F2ADE24828B4BC9DE73  : public ContainerPropertyBag_1_t34F580FC14E2EF6488F9B8CEE5D00BE38DBC2496
{
};
struct RectPropertyBag_tA405FA98439EEF5D80DA2FC601583B49CCB638EC  : public ContainerPropertyBag_1_t2A9DE8C991C6C2537259B466807EEEFC4B68B0E6
{
};
struct SystemVersionPropertyBag_t2BC09BA510A4DE999004F35E9930AA414DF46E8A  : public ContainerPropertyBag_1_t569DC4A1FC35A7A716C097213C60CC9FA986B5A9
{
};
struct Vector2IntPropertyBag_t2C43760B9A4208AD87AEF2615AEF035A489502AF  : public ContainerPropertyBag_1_t5E98E44A087C66C99B6B48FE49F35A9460817892
{
};
struct Vector2PropertyBag_t566CAC975576D5F03B957E62DBA553ECE659AB49  : public ContainerPropertyBag_1_tFC736A3382282864C67252047BC56B960AD3C486
{
};
struct Vector3IntPropertyBag_t534CCCF8AD649649C930E5F3DDA3C0A734F932C7  : public ContainerPropertyBag_1_tC5EF4ED9E953B090F0F1A7314A738165FB6277B8
{
};
struct Vector3PropertyBag_t4FDB90B998E0F82C33B88823F8108862054CED41  : public ContainerPropertyBag_1_tA7C51CF32C3F7B0B198B114B368B2E5BA498D33E
{
};
struct Vector4PropertyBag_t648481977877F79BDB6095EE5B491D8757A9DB5C  : public ContainerPropertyBag_1_t827E6E22E54C7702E357022F57F04833A6FAF09E
{
};
struct ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582_StaticFields
{
	bool ___s_isValueWriteAtomic;
};
struct ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB_StaticFields
{
	bool ___s_isValueWriteAtomic;
};
struct ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91_StaticFields
{
	bool ___s_isValueWriteAtomic;
};
struct List_1_t4B77DB8D00EC6CC4705EB5F2FCC506472734EA72_StaticFields
{
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___s_emptyArray;
};
struct PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_StaticFields
{
	ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582* ___s_PropertyBags;
	List_1_t4B77DB8D00EC6CC4705EB5F2FCC506472734EA72* ___s_RegisteredTypes;
	Action_2_t33A6FCCA2D3E549AA88F7D182C5D500BD6473B5F* ___NewTypeRegistered;
	ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF* ___s_PropertyBagProvider;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields
{
	ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91* ___s_TypeConstructors;
	MethodInfo_t* ___s_CreateTypeConstructor;
	ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB* ___s_CachedResolvedName;
	ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A* ___s_Builders;
	RuntimeObject* ___syncedPoolObject;
};
struct U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_StaticFields
{
	U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523* ___U3CU3E9;
	Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164* ___U3CU3E9__10_0;
	Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4* ___U3CU3E9__22_0;
};
struct U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_StaticFields
{
	U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203* ___U3CU3E9;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17_StaticFields
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___s_categoryForLatin1;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct Type_t_StaticFields
{
	Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235* ___s_defaultBinder;
	Il2CppChar ___Delimiter;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___EmptyTypes;
	RuntimeObject* ___Missing;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterAttribute;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterName;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterNameIgnoreCase;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct MethodInfoU5BU5D_tDF3670604A0AECF814A0B0BA09B91FBF0D6A3265  : public RuntimeArray
{
	ALIGN_FIELD (8) MethodInfo_t* m_Items[1];

	inline MethodInfo_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline MethodInfo_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, MethodInfo_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline MethodInfo_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline MethodInfo_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, MethodInfo_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB  : public RuntimeArray
{
	ALIGN_FIELD (8) Type_t* m_Items[1];

	inline Type_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Type_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Type_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Type_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Type_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Type_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248  : public RuntimeArray
{
	ALIGN_FIELD (8) String_t* m_Items[1];

	inline String_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline String_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, String_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline String_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline String_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, String_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct MemberInfoU5BU5D_t4CB6970BB166E8E1CFB06152B2A2284971873053  : public RuntimeArray
{
	ALIGN_FIELD (8) MemberInfo_t* m_Items[1];

	inline MemberInfo_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline MemberInfo_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, MemberInfo_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline MemberInfo_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline MemberInfo_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, MemberInfo_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConcurrentDictionary_2__ctor_m0891574E19148D39C27C179BF2787093757C5F57_gshared (ConcurrentDictionary_2_tF598E45B2A3ECB23FD311D829FB0AB32B1201ACF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Func_1__ctor_m663374A863E492A515BE9626B6F0E444991834E8_gshared (Func_1_tD5C081AE11746B200C711DD48DBEB00E3A9276D4* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ObjectPool_1__ctor_m4CED6C10E611A3CC63F3CF84423C183C1412177F_gshared (ObjectPool_1_t13378E8CD14858BB05EA28BAB9CC49EDB91BA259* __this, Func_1_tD5C081AE11746B200C711DD48DBEB00E3A9276D4* ___0_createFunc, Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* ___1_actionOnGet, Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* ___2_actionOnRelease, Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* ___3_actionOnDestroy, bool ___4_collectionCheck, int32_t ___5_defaultCapacity, int32_t ___6_maxSize, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TypeUtility_SetExplicitInstantiationMethod_TisRuntimeObject_mE9AE43E037CA7786FCC097D961262F0634370C79_gshared (Func_1_tD5C081AE11746B200C711DD48DBEB00E3A9276D4* ___0_constructor, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ConcurrentDictionary_2_TryGetValue_mE794BBB2811B9244CF8ECB11994F70DEB6BEFDF7_gshared (ConcurrentDictionary_2_tF598E45B2A3ECB23FD311D829FB0AB32B1201ACF* __this, RuntimeObject* ___0_key, RuntimeObject** ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ConcurrentDictionary_2_set_Item_m95DD60ECF2EBCA55F2EC3B0AC122FE0C0D7D4E39_gshared (ConcurrentDictionary_2_tF598E45B2A3ECB23FD311D829FB0AB32B1201ACF* __this, RuntimeObject* ___0_key, RuntimeObject* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* ObjectPool_1_Get_m239BB169D8FEF3A2694E9A961C473D3807D67D89_gshared (ObjectPool_1_t13378E8CD14858BB05EA28BAB9CC49EDB91BA259* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ObjectPool_1_Release_m71F1CADB7AD9CC20BD824583A3675A4260965DB5_gshared (ObjectPool_1_t13378E8CD14858BB05EA28BAB9CC49EDB91BA259* __this, RuntimeObject* ___0_element, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyBag_Register_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1904BF66773B1294832B629C1FD5C32920AEEFDD_gshared (PropertyBag_1_t0967321B2D6F146150A7805676EB01A1FD0E97B6* ___0_propertyBag, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyBag_Register_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m9D4158376507E88B5DB59B60B56E2EBAC5567282_gshared (PropertyBag_1_t1E23861777A25A773264A698434477DCE18B34CF* ___0_propertyBag, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyBag_Register_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m920D0EDA2A222D333EC898C0E4405F9D0A9000EF_gshared (PropertyBag_1_t91378217184AE0BD640D9A003EF2CB208071D593* ___0_propertyBag, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyBag_Register_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m1952774FD6803DED13AB832F702970F5F588917C_gshared (PropertyBag_1_tD11940BE9DAD74F61B141A397F3D5722E7FF2601* ___0_propertyBag, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyBag_Register_TisVector2Int_t69B2886EBAB732D9B880565E18E7568F3DE0CE6A_mF3979F0F1B3DEBC415368280E538AE95E7277EE3_gshared (PropertyBag_1_tA16FBB091D74A21AC539D829F65E5E8FAF2DB075* ___0_propertyBag, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyBag_Register_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m74735F036C3540DFAF301D182A48C6EEB836F4E1_gshared (PropertyBag_1_tDA117C10FEABA385803A90E976050FB2D270FAD2* ___0_propertyBag, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyBag_Register_TisRect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_m0EE819FDFD53BD79B74A1D270244805D2BD796A8_gshared (PropertyBag_1_t2F1387D95A1E9B93E66C397645BFB8BCD265219B* ___0_propertyBag, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyBag_Register_TisRectInt_t1744D10E1063135DA9D574F95205B98DAC600CB8_mF48E3B0570AD4D40331B885BCECF6A7A199327E4_gshared (PropertyBag_1_tC24A0A7B21F69286D618A79A4F7CD6F8E9E64E46* ___0_propertyBag, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyBag_Register_TisBounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_m8A39CB42251835524F1068E49B6CF4BE5AD65032_gshared (PropertyBag_1_tD413081183D831D5FD1DB047C6388E9EA8522318* ___0_propertyBag, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyBag_Register_TisBoundsInt_t4E757DE5EFF9FCB42000F173360DDC63B5585485_m2606075F145B891D9C481304597F0464E5F8A8AA_gshared (PropertyBag_1_tFA4D14E31A2A501D102D88481DCBC9501A6EF390* ___0_propertyBag, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyBag_Register_TisRuntimeObject_m6780E7E03BC7A9FE3EBB2CED5A8250D914FFC1AE_gshared (PropertyBag_1_tE522EC0E4E4C916EBF192933D686DEFDD27B9474* ___0_propertyBag, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1__ctor_m664143BCBE736CBB716E37C4DC7CFEC21BB17D92_gshared (ContainerPropertyBag_1_t4B3135B801859CF3F6877AEC19D1DA289FFC301D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mA9C2ACFCDCD7269581FBF0D648DE6495840A79F4_gshared (ContainerPropertyBag_1_t4B3135B801859CF3F6877AEC19D1DA289FFC301D* __this, Property_2_tBEC632CE0FBA6F323B365C780F1F4C4C62A850ED* ___0_property, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F_gshared (Property_2_tBEC632CE0FBA6F323B365C780F1F4C4C62A850ED* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1__ctor_m48D10232A046CC68EF83CE5D3B5454C195078055_gshared (ContainerPropertyBag_1_tFC736A3382282864C67252047BC56B960AD3C486* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m6DBD44004CEA7C31A6639E946F82B44E3797E8AE_gshared (ContainerPropertyBag_1_tFC736A3382282864C67252047BC56B960AD3C486* __this, Property_2_t4CA8B837C8B5E5C163CFFA715548D9980679EC46* ___0_property, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Property_2__ctor_mE2253FF920B6B8BA104EB3F4C6E8A871A34892E7_gshared (Property_2_t4CA8B837C8B5E5C163CFFA715548D9980679EC46* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1__ctor_mED3672BE63BBCAE82B5E328167D256B540E9C287_gshared (ContainerPropertyBag_1_tA7C51CF32C3F7B0B198B114B368B2E5BA498D33E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m582D8F897B3FA9A209026D9CB0D2CBCA2091CD7A_gshared (ContainerPropertyBag_1_tA7C51CF32C3F7B0B198B114B368B2E5BA498D33E* __this, Property_2_tDFB93AAC56F8B372E59EEACF0AD971295487CCEA* ___0_property, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Property_2__ctor_m00887E3260117C76E2592EEB152472A442E5ECFF_gshared (Property_2_tDFB93AAC56F8B372E59EEACF0AD971295487CCEA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1__ctor_mD88378C16AD1F8C2A20F3521D02DA3B6811B1FB5_gshared (ContainerPropertyBag_1_t827E6E22E54C7702E357022F57F04833A6FAF09E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m321C399F1891C5BDC7012B34EC2670D56995AB6D_gshared (ContainerPropertyBag_1_t827E6E22E54C7702E357022F57F04833A6FAF09E* __this, Property_2_tD234CECBCBBAF0E5115582B104A904CB400D927F* ___0_property, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA_gshared (Property_2_tD234CECBCBBAF0E5115582B104A904CB400D927F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1__ctor_m0D4393716F0234CD9550422C6CB0FCDAEE0376E4_gshared (ContainerPropertyBag_1_t5E98E44A087C66C99B6B48FE49F35A9460817892* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m354E48D6271D3BE0511C80D3BBE3389C3DAA0538_gshared (ContainerPropertyBag_1_t5E98E44A087C66C99B6B48FE49F35A9460817892* __this, Property_2_tC1A1D28677DB791F77E60F16E0A734BE3616141F* ___0_property, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Property_2__ctor_m0053E84A8D6B34BFDAB7795833421AF8A4F0BAEE_gshared (Property_2_tC1A1D28677DB791F77E60F16E0A734BE3616141F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1__ctor_mB9D6850E283177CAED47EE19A760C168ADCCF35C_gshared (ContainerPropertyBag_1_tC5EF4ED9E953B090F0F1A7314A738165FB6277B8* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m8C4C8B4E763DF565341D1474E761E3E4628E1960_gshared (ContainerPropertyBag_1_tC5EF4ED9E953B090F0F1A7314A738165FB6277B8* __this, Property_2_t69BB6E67D83C59343BA1D7DCC6BDE82B297986D1* ___0_property, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Property_2__ctor_m3D03E5489D8244E706F51006F1B0D0C5250A7A86_gshared (Property_2_t69BB6E67D83C59343BA1D7DCC6BDE82B297986D1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1__ctor_m002B18E65D2DE825AC92248D478593AD1A3F524F_gshared (ContainerPropertyBag_1_t2A9DE8C991C6C2537259B466807EEEFC4B68B0E6* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mE9A22EEC7A77E08AEF0D374E723786F7F1CB63E2_gshared (ContainerPropertyBag_1_t2A9DE8C991C6C2537259B466807EEEFC4B68B0E6* __this, Property_2_t4395571972396AEE0D2671461D1603BB8D296451* ___0_property, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0_gshared (Property_2_t4395571972396AEE0D2671461D1603BB8D296451* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1__ctor_mFDAE4DA91A1BCD387794384FABBE63CD54647DFB_gshared (ContainerPropertyBag_1_t34F580FC14E2EF6488F9B8CEE5D00BE38DBC2496* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m1A8D6D29FEC8E7BB998E685F8A0CDFF7685AA4D4_gshared (ContainerPropertyBag_1_t34F580FC14E2EF6488F9B8CEE5D00BE38DBC2496* __this, Property_2_tE0D61821CEE6E9C49C6C1BCAD5F62D78A24F7A56* ___0_property, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230_gshared (Property_2_tE0D61821CEE6E9C49C6C1BCAD5F62D78A24F7A56* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1__ctor_m0B3C26C575C4406E1478D8D4FFB946522E95F740_gshared (ContainerPropertyBag_1_t2279C723A3D81E0BE90A787349C987FC490033EF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1_AddProperty_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m2D875551D9368012276D3FDD3DC12FD9A4BE3987_gshared (ContainerPropertyBag_1_t2279C723A3D81E0BE90A787349C987FC490033EF* __this, Property_2_tC4748332B44E20FE7C498E90E74A718F89F44471* ___0_property, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Property_2__ctor_m6D93F660253A4C7EC344945B11B74A1EC5E2052B_gshared (Property_2_tC4748332B44E20FE7C498E90E74A718F89F44471* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1__ctor_mDF062EDE672E0636FD2510B7574F918A7924BAE1_gshared (ContainerPropertyBag_1_t3A0DBCD09BBF64291800B0275CC7E1F2EECA0FEF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1_AddProperty_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m3CD2BB7313CFB80B5C21BFB971910AC718564437_gshared (ContainerPropertyBag_1_t3A0DBCD09BBF64291800B0275CC7E1F2EECA0FEF* __this, Property_2_tAB010676E84B6005B47BB524D152BA4859373D2D* ___0_property, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Property_2__ctor_m6CCA84842C767E1068B2F635177225E05FFE539B_gshared (Property_2_tAB010676E84B6005B47BB524D152BA4859373D2D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1__ctor_m3377C4A2AFE52C17B429C1A35C2F2C9E953455AA_gshared (ContainerPropertyBag_1_tA2A29BDB4DB4DD00EDAB85E6154345DEEC0BF63B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_mBEAC8DBB778A44CAFB1950D04453DAF312DEDE32_gshared (ContainerPropertyBag_1_tA2A29BDB4DB4DD00EDAB85E6154345DEEC0BF63B* __this, Property_2_t4214D5666464CEE51F044573A59C738C2D562085* ___0_property, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Property_2__ctor_m8513F2C4ED671798A7491909C328236F38A1AEB3_gshared (Property_2_t4214D5666464CEE51F044573A59C738C2D562085* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Property_2_AddAttribute_m7BE72727942237415CA702C9A13E7BB9CB108E58_gshared (Property_2_t4214D5666464CEE51F044573A59C738C2D562085* __this, Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA* ___0_attribute, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ConcurrentDictionary_2_TryAdd_mA7D07DAB0E4F228BEDBEBA3BEA124A7C839B0DFC_gshared (ConcurrentDictionary_2_tF598E45B2A3ECB23FD311D829FB0AB32B1201ACF* __this, RuntimeObject* ___0_key, RuntimeObject* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Func_2__ctor_m13C0A7F33154D861E2A041B52E88461832DA1697_gshared (Func_2_tE1F0D41563EE092E5E5540B061449FDE88F1DC00* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Enumerable_First_TisRuntimeObject_mC6FA107934316BE28EA02DDB9EADA7AE8DD1916B_gshared (RuntimeObject* ___0_source, Func_2_tE1F0D41563EE092E5E5540B061449FDE88F1DC00* ___1_predicate, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Enumerable_All_TisRuntimeObject_m57CDB9DB99F9E77214B47367D213B06AC3ED97FF_gshared (RuntimeObject* ___0_source, Func_2_tE1F0D41563EE092E5E5540B061449FDE88F1DC00* ___1_predicate, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Func_2__ctor_mEB7603EDE6D79A62E5BD74A896F030D2C9F2A821_gshared (Func_2_t9A0D493A82DCC47C9C819A3B045E02D9B5DDCE1B* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Enumerable_OrderBy_TisRuntimeObject_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m0CD0677A1278BFC7E13405D11D1EF30919C9E9A2_gshared (RuntimeObject* ___0_source, Func_2_t9A0D493A82DCC47C9C819A3B045E02D9B5DDCE1B* ___1_keySelector, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* CustomAttributeExtensions_GetCustomAttribute_TisRuntimeObject_m5B4B712DB5F08EBF4518A1973C9F11C15A4BEAE9_gshared (MemberInfo_t* ___0_element, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ReflectionUtilities_SanitizeMemberName_m00515F1FC752D0A52D90143E58D19A7D40D799AE (MemberInfo_t* ___0_info, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FieldMember__ctor_mA6F2BB809BD42F26804E1E9A78B80ECA6C65FF03 (FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* __this, FieldInfo_t* ___0_fieldInfo, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* FieldMember_get_Name_m9301F65E796E51B6F4EF0837AEB3EB9AD25293D6_inline (FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool FieldInfo_get_IsInitOnly_m476BB9325A68BDD56B088D3E8407F75FA1388ED9 (FieldInfo_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool FieldMember_get_IsReadOnly_mF9CA4CBB2973908DA90F49DD86124FC769168FB0 (FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* FieldMember_get_ValueType_m76238497CE787FD26A79341A8F3207EFF31CE63A (FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* CustomAttributeExtensions_GetCustomAttributes_m2851556A37AAF9A808EFB2C603D11E48635FA785 (MemberInfo_t* ___0_element, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* FieldMember_GetCustomAttributes_m3A1CF3269B1FD4C659FDEA8D51CFAE003612101D (FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* PropertyMember_get_Name_mCB0EBF067D82D0871C8051116D34A7D82EBE67FB_inline (PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PropertyMember_get_IsReadOnly_m595190E0A259DF7A4CD106BFF16949E939F511C5 (PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* PropertyMember_get_ValueType_m40F8CF0BBF20782A6C594365911716F386FE4E1A (PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyMember__ctor_m0B727AF7D87194ED8EA453CC4CF01A10563E2334 (PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* __this, PropertyInfo_t* ___0_propertyInfo, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PropertyMember_GetCustomAttributes_mE4214E27A5A04DF047573888EDD9590FD6C85409 (PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC (Type_t* ___0_left, Type_t* ___1_right, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentNullException__ctor_m444AE141157E333844FC1A9500224C2F9FD24F4B (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* __this, String_t* ___0_paramName, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Type_get_IsPrimitive_m46ACAAB8F754D37713E3E45437705F4F58FAFA18 (Type_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Type_get_IsPointer_mC8AAAFEC4E4CEA59DAD0032B85D1BB224763278B (Type_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57 (RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ___0_handle, const RuntimeMethod* method) ;
inline void ConcurrentDictionary_2__ctor_m2CD607D007C68544A77E46F721E1F7A20EFBD2BE (ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91* __this, const RuntimeMethod* method)
{
	((  void (*) (ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91*, const RuntimeMethod*))ConcurrentDictionary_2__ctor_m0891574E19148D39C27C179BF2787093757C5F57_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
inline void ConcurrentDictionary_2__ctor_m6B09103AE0E5ACDEA8157B5E09343647B1A55E80 (ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB* __this, const RuntimeMethod* method)
{
	((  void (*) (ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB*, const RuntimeMethod*))ConcurrentDictionary_2__ctor_m0891574E19148D39C27C179BF2787093757C5F57_gshared)(__this, method);
}
inline void Func_1__ctor_mD927C23DE447A096B2B1F7D75ECDFE610DD39F2A (Func_1_t067EFEB6C5607DEFF52B09F0A11295959E292D7C* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Func_1_t067EFEB6C5607DEFF52B09F0A11295959E292D7C*, RuntimeObject*, intptr_t, const RuntimeMethod*))Func_1__ctor_m663374A863E492A515BE9626B6F0E444991834E8_gshared)(__this, ___0_object, ___1_method, method);
}
inline void Action_1__ctor_m1540C8C2516AEA9F0AEE87470E2815CA36C560C7 (Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared)(__this, ___0_object, ___1_method, method);
}
inline void ObjectPool_1__ctor_m35C71BF5CBC8DED0ED44D82D926F61E584CA5AD4 (ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A* __this, Func_1_t067EFEB6C5607DEFF52B09F0A11295959E292D7C* ___0_createFunc, Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6* ___1_actionOnGet, Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6* ___2_actionOnRelease, Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6* ___3_actionOnDestroy, bool ___4_collectionCheck, int32_t ___5_defaultCapacity, int32_t ___6_maxSize, const RuntimeMethod* method)
{
	((  void (*) (ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A*, Func_1_t067EFEB6C5607DEFF52B09F0A11295959E292D7C*, Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6*, Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6*, Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6*, bool, int32_t, int32_t, const RuntimeMethod*))ObjectPool_1__ctor_m4CED6C10E611A3CC63F3CF84423C183C1412177F_gshared)(__this, ___0_createFunc, ___1_actionOnGet, ___2_actionOnRelease, ___3_actionOnDestroy, ___4_collectionCheck, ___5_defaultCapacity, ___6_maxSize, method);
}
inline void Func_1__ctor_m27A68E928C1D9158EAAD261086B9BC424339327B (Func_1_t367387BB2C476D3F32DB12161B5FDC128DC3231C* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Func_1_t367387BB2C476D3F32DB12161B5FDC128DC3231C*, RuntimeObject*, intptr_t, const RuntimeMethod*))Func_1__ctor_m663374A863E492A515BE9626B6F0E444991834E8_gshared)(__this, ___0_object, ___1_method, method);
}
inline void TypeUtility_SetExplicitInstantiationMethod_TisString_t_m1BB80FFC484AB56A416E91F9E4EAB2FF75861EFE (Func_1_t367387BB2C476D3F32DB12161B5FDC128DC3231C* ___0_constructor, const RuntimeMethod* method)
{
	((  void (*) (Func_1_t367387BB2C476D3F32DB12161B5FDC128DC3231C*, const RuntimeMethod*))TypeUtility_SetExplicitInstantiationMethod_TisRuntimeObject_mE9AE43E037CA7786FCC097D961262F0634370C79_gshared)(___0_constructor, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_op_Inequality_m8C940F3CFC42866709D7CA931B3D77B4BE94BCB6 (String_t* ___0_a, String_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MethodInfo_op_Equality_m1466AB76300C9F07856E706E7E914062175189D1 (MethodInfo_t* ___0_left, MethodInfo_t* ___1_right, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InvalidProgramException__ctor_m75BD70D9AEEE6B109A3FB51897615B6DAA992B28 (InvalidProgramException_t5715C9ED8CFE88C0DB9CD5614BA3D72BA9259C9B* __this, const RuntimeMethod* method) ;
inline bool ConcurrentDictionary_2_TryGetValue_mC760AE4BCF6AFC3CB7C64E8A405B651395A9D238 (ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB* __this, Type_t* ___0_key, String_t** ___1_value, const RuntimeMethod* method)
{
	return ((  bool (*) (ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB*, Type_t*, String_t**, const RuntimeMethod*))ConcurrentDictionary_2_TryGetValue_mE794BBB2811B9244CF8ECB11994F70DEB6BEFDF7_gshared)(__this, ___0_key, ___1_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* TypeUtility_GetTypeDisplayName_mCDE259ECEE5E6A4E7C52D4319D7E5D856E19F862 (Type_t* ___0_type, RuntimeObject* ___1_args, int32_t* ___2_argIndex, const RuntimeMethod* method) ;
inline void ConcurrentDictionary_2_set_Item_m5EF95C43AE2498CE264FCEE432AE379AD39651C2 (ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB* __this, Type_t* ___0_key, String_t* ___1_value, const RuntimeMethod* method)
{
	((  void (*) (ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB*, Type_t*, String_t*, const RuntimeMethod*))ConcurrentDictionary_2_set_Item_m95DD60ECF2EBCA55F2EC3B0AC122FE0C0D7D4E39_gshared)(__this, ___0_key, ___1_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Type_get_IsNested_mCF57E6A68BA5CEDDB9DA81CB34B6945F414FB3FD (Type_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t String_IndexOf_mE21E78F35EF4A7768E385A72814C88D22B689966 (String_t* __this, Il2CppChar ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Substring_m6BA4A3FA3800FE92662D0847CC8E1EEF940DF472 (String_t* __this, int32_t ___0_startIndex, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Int32_Parse_m273CA1A9C7717C99641291A95C543711C0202AF0 (String_t* ___0_s, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Remove_m8266E0BF5D8565D4CDC850F21E9178AE254C3E85 (String_t* __this, int32_t ___0_startIndex, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Monitor_Exit_m05B2CF037E2214B3208198C282490A2A475653FA (RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Monitor_Enter_m3CDB589DA1300B513D55FDCFB52B63E879794149 (RuntimeObject* ___0_obj, bool* ___1_lockTaken, const RuntimeMethod* method) ;
inline StringBuilder_t* ObjectPool_1_Get_m9A0C64A471F1134751E86CFABE36283B06E29F8D (ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A* __this, const RuntimeMethod* method)
{
	return ((  StringBuilder_t* (*) (ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A*, const RuntimeMethod*))ObjectPool_1_Get_m239BB169D8FEF3A2694E9A961C473D3807D67D89_gshared)(__this, method);
}
inline void ObjectPool_1_Release_mD38ED7186BD4E9178B260810EEA654454B3BB478 (ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A* __this, StringBuilder_t* ___0_element, const RuntimeMethod* method)
{
	((  void (*) (ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A*, StringBuilder_t*, const RuntimeMethod*))ObjectPool_1_Release_m71F1CADB7AD9CC20BD824583A3675A4260965DB5_gshared)(__this, ___0_element, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringBuilder_t* StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D (StringBuilder_t* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* TypeUtility_GetTypeDisplayName_m45C565A0737008926DD1D4CA6B453A475C8E77FD (Type_t* ___0_type, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t StringBuilder_get_Length_mDEA041E7357C68CC3B5885276BB403676DAAE0D8 (StringBuilder_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987 (String_t* ___0_format, RuntimeObject* ___1_arg0, RuntimeObject* ___2_arg1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Type_get_IsInterface_m484A7D9321E72758EABE7F36AE266EB0905957EC (Type_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Type_get_IsValueType_m59AE2E0439DC06347B8D6B38548F3CBA54D38318 (Type_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Type_op_Inequality_m83209C7BB3C05DFBEA3B6199B0BEFE8037301172 (Type_t* ___0_left, Type_t* ___1_right, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PropertyBagStore_GetPropertyBag_m90F2EBB48D60993594856358C572964C8011143E (Type_t* ___0_type, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TypeConstructorVisitor__ctor_m496AFA7B1A835D40C4BEDBD5A7D1804CF196DE23 (TypeConstructorVisitor_tFC61250F68BF43FE889F5B1002CBFF90648E9C7F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NonConstructable__ctor_m6B6DB41FBB71A153AFAC3B10CF35D2503D8CA982 (NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE* __this, const RuntimeMethod* method) ;
inline void ConcurrentDictionary_2_set_Item_m050171C2656C2982971EECEA9026CADF5AEF72B1 (ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91* __this, Type_t* ___0_key, RuntimeObject* ___1_value, const RuntimeMethod* method)
{
	((  void (*) (ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91*, Type_t*, RuntimeObject*, const RuntimeMethod*))ConcurrentDictionary_2_set_Item_m95DD60ECF2EBCA55F2EC3B0AC122FE0C0D7D4E39_gshared)(__this, ___0_key, ___1_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* MethodBase_Invoke_mEEF3218648F111A8C338001A7804091A0747C826 (MethodBase_t* __this, RuntimeObject* ___0_obj, ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___1_parameters, const RuntimeMethod* method) ;
inline bool ConcurrentDictionary_2_TryGetValue_mBD42E58E3B97D8884F8ABD2ED7FB00AE05C3D7B3 (ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91* __this, Type_t* ___0_key, RuntimeObject** ___1_value, const RuntimeMethod* method)
{
	return ((  bool (*) (ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91*, Type_t*, RuntimeObject**, const RuntimeMethod*))ConcurrentDictionary_2_TryGetValue_mE794BBB2811B9244CF8ECB11994F70DEB6BEFDF7_gshared)(__this, ___0_key, ___1_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* TypeUtility_CreateTypeConstructor_mCD83647C237EE7D7DC578EE0FFF12124F4670D74 (Type_t* ___0_type, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* TypeUtility_GetTypeConstructor_m46157C15601C266EE0DB2324E834E1CE4CBFE1DC (Type_t* ___0_type, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m647EBF831F54B6DF7D5AFA5FD012CF4EE7571B6A (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ___0_values, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465 (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162 (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_mD06BD054A2682CAA505B6F75C45B8D4736FBFA39 (U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StringBuilder__ctor_m1D99713357DE05DAFA296633639DB55F8C30587D (StringBuilder_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringBuilder_t* StringBuilder_Clear_m2D76F6533574F40A4E3E2DC4E730277CBD0AF8F6 (StringBuilder_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ColorPropertyBag__ctor_m695D2E7503EA97F7BB7252A882546C1C8B11D1BC (ColorPropertyBag_tFA63F917A9DAA7F376DD37FF114B3D548E00BCC6* __this, const RuntimeMethod* method) ;
inline void PropertyBag_Register_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1904BF66773B1294832B629C1FD5C32920AEEFDD (PropertyBag_1_t0967321B2D6F146150A7805676EB01A1FD0E97B6* ___0_propertyBag, const RuntimeMethod* method)
{
	((  void (*) (PropertyBag_1_t0967321B2D6F146150A7805676EB01A1FD0E97B6*, const RuntimeMethod*))PropertyBag_Register_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1904BF66773B1294832B629C1FD5C32920AEEFDD_gshared)(___0_propertyBag, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector2PropertyBag__ctor_mF30206E01ECFEDF6726878BEDA3DA53A2D24D00F (Vector2PropertyBag_t566CAC975576D5F03B957E62DBA553ECE659AB49* __this, const RuntimeMethod* method) ;
inline void PropertyBag_Register_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m9D4158376507E88B5DB59B60B56E2EBAC5567282 (PropertyBag_1_t1E23861777A25A773264A698434477DCE18B34CF* ___0_propertyBag, const RuntimeMethod* method)
{
	((  void (*) (PropertyBag_1_t1E23861777A25A773264A698434477DCE18B34CF*, const RuntimeMethod*))PropertyBag_Register_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m9D4158376507E88B5DB59B60B56E2EBAC5567282_gshared)(___0_propertyBag, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector3PropertyBag__ctor_mC00E39D196C789D68E6F910494E433E729E0E4B6 (Vector3PropertyBag_t4FDB90B998E0F82C33B88823F8108862054CED41* __this, const RuntimeMethod* method) ;
inline void PropertyBag_Register_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m920D0EDA2A222D333EC898C0E4405F9D0A9000EF (PropertyBag_1_t91378217184AE0BD640D9A003EF2CB208071D593* ___0_propertyBag, const RuntimeMethod* method)
{
	((  void (*) (PropertyBag_1_t91378217184AE0BD640D9A003EF2CB208071D593*, const RuntimeMethod*))PropertyBag_Register_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m920D0EDA2A222D333EC898C0E4405F9D0A9000EF_gshared)(___0_propertyBag, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector4PropertyBag__ctor_m6B7DFA6F651C0DC6522B1AB7E179752BA836AE10 (Vector4PropertyBag_t648481977877F79BDB6095EE5B491D8757A9DB5C* __this, const RuntimeMethod* method) ;
inline void PropertyBag_Register_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m1952774FD6803DED13AB832F702970F5F588917C (PropertyBag_1_tD11940BE9DAD74F61B141A397F3D5722E7FF2601* ___0_propertyBag, const RuntimeMethod* method)
{
	((  void (*) (PropertyBag_1_tD11940BE9DAD74F61B141A397F3D5722E7FF2601*, const RuntimeMethod*))PropertyBag_Register_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m1952774FD6803DED13AB832F702970F5F588917C_gshared)(___0_propertyBag, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector2IntPropertyBag__ctor_mA5E766EC29B1E37CAA759CBB09997D25FAFB0631 (Vector2IntPropertyBag_t2C43760B9A4208AD87AEF2615AEF035A489502AF* __this, const RuntimeMethod* method) ;
inline void PropertyBag_Register_TisVector2Int_t69B2886EBAB732D9B880565E18E7568F3DE0CE6A_mF3979F0F1B3DEBC415368280E538AE95E7277EE3 (PropertyBag_1_tA16FBB091D74A21AC539D829F65E5E8FAF2DB075* ___0_propertyBag, const RuntimeMethod* method)
{
	((  void (*) (PropertyBag_1_tA16FBB091D74A21AC539D829F65E5E8FAF2DB075*, const RuntimeMethod*))PropertyBag_Register_TisVector2Int_t69B2886EBAB732D9B880565E18E7568F3DE0CE6A_mF3979F0F1B3DEBC415368280E538AE95E7277EE3_gshared)(___0_propertyBag, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector3IntPropertyBag__ctor_mECB1D0CC81ACF263C6C0D126BB285A11CBACB2BC (Vector3IntPropertyBag_t534CCCF8AD649649C930E5F3DDA3C0A734F932C7* __this, const RuntimeMethod* method) ;
inline void PropertyBag_Register_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m74735F036C3540DFAF301D182A48C6EEB836F4E1 (PropertyBag_1_tDA117C10FEABA385803A90E976050FB2D270FAD2* ___0_propertyBag, const RuntimeMethod* method)
{
	((  void (*) (PropertyBag_1_tDA117C10FEABA385803A90E976050FB2D270FAD2*, const RuntimeMethod*))PropertyBag_Register_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m74735F036C3540DFAF301D182A48C6EEB836F4E1_gshared)(___0_propertyBag, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RectPropertyBag__ctor_m4C5D1C260902338AC90DB837C39FE28695ED0110 (RectPropertyBag_tA405FA98439EEF5D80DA2FC601583B49CCB638EC* __this, const RuntimeMethod* method) ;
inline void PropertyBag_Register_TisRect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_m0EE819FDFD53BD79B74A1D270244805D2BD796A8 (PropertyBag_1_t2F1387D95A1E9B93E66C397645BFB8BCD265219B* ___0_propertyBag, const RuntimeMethod* method)
{
	((  void (*) (PropertyBag_1_t2F1387D95A1E9B93E66C397645BFB8BCD265219B*, const RuntimeMethod*))PropertyBag_Register_TisRect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_m0EE819FDFD53BD79B74A1D270244805D2BD796A8_gshared)(___0_propertyBag, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RectIntPropertyBag__ctor_mFFAC4C410B920C26B29CB6A309FB7EEF90E75A4F (RectIntPropertyBag_t523552E9F6C38079E84B8F2ADE24828B4BC9DE73* __this, const RuntimeMethod* method) ;
inline void PropertyBag_Register_TisRectInt_t1744D10E1063135DA9D574F95205B98DAC600CB8_mF48E3B0570AD4D40331B885BCECF6A7A199327E4 (PropertyBag_1_tC24A0A7B21F69286D618A79A4F7CD6F8E9E64E46* ___0_propertyBag, const RuntimeMethod* method)
{
	((  void (*) (PropertyBag_1_tC24A0A7B21F69286D618A79A4F7CD6F8E9E64E46*, const RuntimeMethod*))PropertyBag_Register_TisRectInt_t1744D10E1063135DA9D574F95205B98DAC600CB8_mF48E3B0570AD4D40331B885BCECF6A7A199327E4_gshared)(___0_propertyBag, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoundsPropertyBag__ctor_mB3B92E349452661D037A9AB2A8B6581ECFEBB111 (BoundsPropertyBag_tA22B9256F487C40279FFFA58AD0E8D6CF29BA83F* __this, const RuntimeMethod* method) ;
inline void PropertyBag_Register_TisBounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_m8A39CB42251835524F1068E49B6CF4BE5AD65032 (PropertyBag_1_tD413081183D831D5FD1DB047C6388E9EA8522318* ___0_propertyBag, const RuntimeMethod* method)
{
	((  void (*) (PropertyBag_1_tD413081183D831D5FD1DB047C6388E9EA8522318*, const RuntimeMethod*))PropertyBag_Register_TisBounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_m8A39CB42251835524F1068E49B6CF4BE5AD65032_gshared)(___0_propertyBag, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoundsIntPropertyBag__ctor_mE2B2BB212558FE1C221E01D8FCEC4C41B02C6BF5 (BoundsIntPropertyBag_t36236827A488493B27B94BF7EB678949732F2052* __this, const RuntimeMethod* method) ;
inline void PropertyBag_Register_TisBoundsInt_t4E757DE5EFF9FCB42000F173360DDC63B5585485_m2606075F145B891D9C481304597F0464E5F8A8AA (PropertyBag_1_tFA4D14E31A2A501D102D88481DCBC9501A6EF390* ___0_propertyBag, const RuntimeMethod* method)
{
	((  void (*) (PropertyBag_1_tFA4D14E31A2A501D102D88481DCBC9501A6EF390*, const RuntimeMethod*))PropertyBag_Register_TisBoundsInt_t4E757DE5EFF9FCB42000F173360DDC63B5585485_m2606075F145B891D9C481304597F0464E5F8A8AA_gshared)(___0_propertyBag, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SystemVersionPropertyBag__ctor_mC0D23BC1F975382571DFA70EC5B52B24083BD4AF (SystemVersionPropertyBag_t2BC09BA510A4DE999004F35E9930AA414DF46E8A* __this, const RuntimeMethod* method) ;
inline void PropertyBag_Register_TisVersion_tE426DB5655D0F22920AE16A2AA9AB7781B8255A7_m976EBCE3744148CDF5AF1174347D890DF7B63A6D (PropertyBag_1_t206E61AF5C231793E92CCCFEA28B724EEE20722D* ___0_propertyBag, const RuntimeMethod* method)
{
	((  void (*) (PropertyBag_1_t206E61AF5C231793E92CCCFEA28B724EEE20722D*, const RuntimeMethod*))PropertyBag_Register_TisRuntimeObject_m6780E7E03BC7A9FE3EBB2CED5A8250D914FFC1AE_gshared)(___0_propertyBag, method);
}
inline void ContainerPropertyBag_1__ctor_m664143BCBE736CBB716E37C4DC7CFEC21BB17D92 (ContainerPropertyBag_1_t4B3135B801859CF3F6877AEC19D1DA289FFC301D* __this, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t4B3135B801859CF3F6877AEC19D1DA289FFC301D*, const RuntimeMethod*))ContainerPropertyBag_1__ctor_m664143BCBE736CBB716E37C4DC7CFEC21BB17D92_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RProperty__ctor_m5B48CD3CF7C1DD802022B9AF99DAEEAB2A25B430 (RProperty_t8729556DDC5FF3CC31717171450B4EA749328E5D* __this, const RuntimeMethod* method) ;
inline void ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mA9C2ACFCDCD7269581FBF0D648DE6495840A79F4 (ContainerPropertyBag_1_t4B3135B801859CF3F6877AEC19D1DA289FFC301D* __this, Property_2_tBEC632CE0FBA6F323B365C780F1F4C4C62A850ED* ___0_property, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t4B3135B801859CF3F6877AEC19D1DA289FFC301D*, Property_2_tBEC632CE0FBA6F323B365C780F1F4C4C62A850ED*, const RuntimeMethod*))ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mA9C2ACFCDCD7269581FBF0D648DE6495840A79F4_gshared)(__this, ___0_property, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GProperty__ctor_m2E28D044AC6B8D72EA0595E1290599DC5F91EC35 (GProperty_t59EEA3B6DFE305EF122097DBA3B950684B224ACD* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BProperty__ctor_m3FA7E12A9E2B2B2AFFF6FC5C96AE4EB5BF9CCFA8 (BProperty_t8C06F5F0A1928368B508116004897B283E10E5C4* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AProperty__ctor_mAED58387B9563DED80A12595C6A11B5B2BEA6D2A (AProperty_t71D13C040377A5A0BDD9E1614DC61B65689411AC* __this, const RuntimeMethod* method) ;
inline void Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F (Property_2_tBEC632CE0FBA6F323B365C780F1F4C4C62A850ED* __this, const RuntimeMethod* method)
{
	((  void (*) (Property_2_tBEC632CE0FBA6F323B365C780F1F4C4C62A850ED*, const RuntimeMethod*))Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F_gshared)(__this, method);
}
inline void ContainerPropertyBag_1__ctor_m48D10232A046CC68EF83CE5D3B5454C195078055 (ContainerPropertyBag_1_tFC736A3382282864C67252047BC56B960AD3C486* __this, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_tFC736A3382282864C67252047BC56B960AD3C486*, const RuntimeMethod*))ContainerPropertyBag_1__ctor_m48D10232A046CC68EF83CE5D3B5454C195078055_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_m21350D1E93A69FD0D148521382FD3B438C450692 (XProperty_t8C4D7E192D5B86997EE4863E5A6999B9543590E1* __this, const RuntimeMethod* method) ;
inline void ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m6DBD44004CEA7C31A6639E946F82B44E3797E8AE (ContainerPropertyBag_1_tFC736A3382282864C67252047BC56B960AD3C486* __this, Property_2_t4CA8B837C8B5E5C163CFFA715548D9980679EC46* ___0_property, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_tFC736A3382282864C67252047BC56B960AD3C486*, Property_2_t4CA8B837C8B5E5C163CFFA715548D9980679EC46*, const RuntimeMethod*))ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m6DBD44004CEA7C31A6639E946F82B44E3797E8AE_gshared)(__this, ___0_property, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_mB32B0091E33FBD963435FB9973DB0DD000C3FD5C (YProperty_tBA23A08DCCCC01ED187D24048C43EAA5AB648E7D* __this, const RuntimeMethod* method) ;
inline void Property_2__ctor_mE2253FF920B6B8BA104EB3F4C6E8A871A34892E7 (Property_2_t4CA8B837C8B5E5C163CFFA715548D9980679EC46* __this, const RuntimeMethod* method)
{
	((  void (*) (Property_2_t4CA8B837C8B5E5C163CFFA715548D9980679EC46*, const RuntimeMethod*))Property_2__ctor_mE2253FF920B6B8BA104EB3F4C6E8A871A34892E7_gshared)(__this, method);
}
inline void ContainerPropertyBag_1__ctor_mED3672BE63BBCAE82B5E328167D256B540E9C287 (ContainerPropertyBag_1_tA7C51CF32C3F7B0B198B114B368B2E5BA498D33E* __this, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_tA7C51CF32C3F7B0B198B114B368B2E5BA498D33E*, const RuntimeMethod*))ContainerPropertyBag_1__ctor_mED3672BE63BBCAE82B5E328167D256B540E9C287_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_m46FEC277D299B82A75C8C20B0AEEACC479E3AEEB (XProperty_t012028338C3B87093E42427325BC449033296278* __this, const RuntimeMethod* method) ;
inline void ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m582D8F897B3FA9A209026D9CB0D2CBCA2091CD7A (ContainerPropertyBag_1_tA7C51CF32C3F7B0B198B114B368B2E5BA498D33E* __this, Property_2_tDFB93AAC56F8B372E59EEACF0AD971295487CCEA* ___0_property, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_tA7C51CF32C3F7B0B198B114B368B2E5BA498D33E*, Property_2_tDFB93AAC56F8B372E59EEACF0AD971295487CCEA*, const RuntimeMethod*))ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m582D8F897B3FA9A209026D9CB0D2CBCA2091CD7A_gshared)(__this, ___0_property, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_m4269E5BB6FCDFD8F849755DCA434EE9ACF9BE2E5 (YProperty_tE39DDA65525AD937577DFBB83BD942BDE5D3650F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZProperty__ctor_mE1AC4D86341B82F2792FD53417D5ABE36E37B10E (ZProperty_tCE2945E21C8A6EDBDED46B6106BDBB6852C1FB9B* __this, const RuntimeMethod* method) ;
inline void Property_2__ctor_m00887E3260117C76E2592EEB152472A442E5ECFF (Property_2_tDFB93AAC56F8B372E59EEACF0AD971295487CCEA* __this, const RuntimeMethod* method)
{
	((  void (*) (Property_2_tDFB93AAC56F8B372E59EEACF0AD971295487CCEA*, const RuntimeMethod*))Property_2__ctor_m00887E3260117C76E2592EEB152472A442E5ECFF_gshared)(__this, method);
}
inline void ContainerPropertyBag_1__ctor_mD88378C16AD1F8C2A20F3521D02DA3B6811B1FB5 (ContainerPropertyBag_1_t827E6E22E54C7702E357022F57F04833A6FAF09E* __this, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t827E6E22E54C7702E357022F57F04833A6FAF09E*, const RuntimeMethod*))ContainerPropertyBag_1__ctor_mD88378C16AD1F8C2A20F3521D02DA3B6811B1FB5_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_mBE4223E008541E2DD13ADC1707FBB73E51631CBD (XProperty_t7D24F952200020A50C34C5525355F6308A3B9CDC* __this, const RuntimeMethod* method) ;
inline void ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m321C399F1891C5BDC7012B34EC2670D56995AB6D (ContainerPropertyBag_1_t827E6E22E54C7702E357022F57F04833A6FAF09E* __this, Property_2_tD234CECBCBBAF0E5115582B104A904CB400D927F* ___0_property, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t827E6E22E54C7702E357022F57F04833A6FAF09E*, Property_2_tD234CECBCBBAF0E5115582B104A904CB400D927F*, const RuntimeMethod*))ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m321C399F1891C5BDC7012B34EC2670D56995AB6D_gshared)(__this, ___0_property, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_m26384F8C7D674046A6311567F6BD57C05C1E508A (YProperty_t140E8C4F8AB9EF9D6F3B7EBFDADFA838271CB395* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZProperty__ctor_mBE30B28C0B1F18C02DAEBD5354086C5E7412C795 (ZProperty_t431D10EB8A38306B642314AB1AD505F3BB355322* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WProperty__ctor_mE6F8FD652CBFF130392D33791BD2961B09A2EFF4 (WProperty_t8C921D95C0FC58C4F39423D4E530BBEDE6447841* __this, const RuntimeMethod* method) ;
inline void Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA (Property_2_tD234CECBCBBAF0E5115582B104A904CB400D927F* __this, const RuntimeMethod* method)
{
	((  void (*) (Property_2_tD234CECBCBBAF0E5115582B104A904CB400D927F*, const RuntimeMethod*))Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA_gshared)(__this, method);
}
inline void ContainerPropertyBag_1__ctor_m0D4393716F0234CD9550422C6CB0FCDAEE0376E4 (ContainerPropertyBag_1_t5E98E44A087C66C99B6B48FE49F35A9460817892* __this, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t5E98E44A087C66C99B6B48FE49F35A9460817892*, const RuntimeMethod*))ContainerPropertyBag_1__ctor_m0D4393716F0234CD9550422C6CB0FCDAEE0376E4_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_m850B5684A9419E1A388FA41A041AD667EB1B2589 (XProperty_t975009BA2182514E2B728FF83159426523F5C743* __this, const RuntimeMethod* method) ;
inline void ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m354E48D6271D3BE0511C80D3BBE3389C3DAA0538 (ContainerPropertyBag_1_t5E98E44A087C66C99B6B48FE49F35A9460817892* __this, Property_2_tC1A1D28677DB791F77E60F16E0A734BE3616141F* ___0_property, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t5E98E44A087C66C99B6B48FE49F35A9460817892*, Property_2_tC1A1D28677DB791F77E60F16E0A734BE3616141F*, const RuntimeMethod*))ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m354E48D6271D3BE0511C80D3BBE3389C3DAA0538_gshared)(__this, ___0_property, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_mC8B0BA567D3BED03017F65512C6DE795C7F7558B (YProperty_t541C5E0B4E92F21E8E986589A75A6411FC7496ED* __this, const RuntimeMethod* method) ;
inline void Property_2__ctor_m0053E84A8D6B34BFDAB7795833421AF8A4F0BAEE (Property_2_tC1A1D28677DB791F77E60F16E0A734BE3616141F* __this, const RuntimeMethod* method)
{
	((  void (*) (Property_2_tC1A1D28677DB791F77E60F16E0A734BE3616141F*, const RuntimeMethod*))Property_2__ctor_m0053E84A8D6B34BFDAB7795833421AF8A4F0BAEE_gshared)(__this, method);
}
inline void ContainerPropertyBag_1__ctor_mB9D6850E283177CAED47EE19A760C168ADCCF35C (ContainerPropertyBag_1_tC5EF4ED9E953B090F0F1A7314A738165FB6277B8* __this, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_tC5EF4ED9E953B090F0F1A7314A738165FB6277B8*, const RuntimeMethod*))ContainerPropertyBag_1__ctor_mB9D6850E283177CAED47EE19A760C168ADCCF35C_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_m8A1CCE2C8F5179029876BD678965D6C211CB332C (XProperty_tB7813C282F53A8DE5C913CB5B66811EF57A86ACF* __this, const RuntimeMethod* method) ;
inline void ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m8C4C8B4E763DF565341D1474E761E3E4628E1960 (ContainerPropertyBag_1_tC5EF4ED9E953B090F0F1A7314A738165FB6277B8* __this, Property_2_t69BB6E67D83C59343BA1D7DCC6BDE82B297986D1* ___0_property, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_tC5EF4ED9E953B090F0F1A7314A738165FB6277B8*, Property_2_t69BB6E67D83C59343BA1D7DCC6BDE82B297986D1*, const RuntimeMethod*))ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m8C4C8B4E763DF565341D1474E761E3E4628E1960_gshared)(__this, ___0_property, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_m2233FD190B553BFBD445F8A2EE6BE3DDF0047DBC (YProperty_t737EB7504629EEBEB734ABDAF170EE06776D3C55* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZProperty__ctor_mBFB49B0AD7F5606851D8E8B29D0F3AC93B0E0FA7 (ZProperty_t6B9433EC1EB2C6EE71D8CD868ADA04EF3AFD90D7* __this, const RuntimeMethod* method) ;
inline void Property_2__ctor_m3D03E5489D8244E706F51006F1B0D0C5250A7A86 (Property_2_t69BB6E67D83C59343BA1D7DCC6BDE82B297986D1* __this, const RuntimeMethod* method)
{
	((  void (*) (Property_2_t69BB6E67D83C59343BA1D7DCC6BDE82B297986D1*, const RuntimeMethod*))Property_2__ctor_m3D03E5489D8244E706F51006F1B0D0C5250A7A86_gshared)(__this, method);
}
inline void ContainerPropertyBag_1__ctor_m002B18E65D2DE825AC92248D478593AD1A3F524F (ContainerPropertyBag_1_t2A9DE8C991C6C2537259B466807EEEFC4B68B0E6* __this, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t2A9DE8C991C6C2537259B466807EEEFC4B68B0E6*, const RuntimeMethod*))ContainerPropertyBag_1__ctor_m002B18E65D2DE825AC92248D478593AD1A3F524F_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_mB5A8967CB19EE4DFE4ECEBEB88D0E1E7B0E6A534 (XProperty_tF3F7B9087649DB0DA8ABA99553075498B0D9E9BC* __this, const RuntimeMethod* method) ;
inline void ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mE9A22EEC7A77E08AEF0D374E723786F7F1CB63E2 (ContainerPropertyBag_1_t2A9DE8C991C6C2537259B466807EEEFC4B68B0E6* __this, Property_2_t4395571972396AEE0D2671461D1603BB8D296451* ___0_property, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t2A9DE8C991C6C2537259B466807EEEFC4B68B0E6*, Property_2_t4395571972396AEE0D2671461D1603BB8D296451*, const RuntimeMethod*))ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mE9A22EEC7A77E08AEF0D374E723786F7F1CB63E2_gshared)(__this, ___0_property, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_m06A2AAA9690D1D2BFC24B0EB360DB3585A877441 (YProperty_t6ED0B1CD2A67BAC3E33FA1CFD4424E0469DED13D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WidthProperty__ctor_m94F29A6177829CC11F530347605BB4592547C72D (WidthProperty_t7CC0B7F8C816515B4ED673CBFFA702091CDA5CD7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void HeightProperty__ctor_m07DCB5A7DF246055CB471EF52A8B0C909C222F2E (HeightProperty_t412F58E91B9683344CB1007B485A4479A0698790* __this, const RuntimeMethod* method) ;
inline void Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0 (Property_2_t4395571972396AEE0D2671461D1603BB8D296451* __this, const RuntimeMethod* method)
{
	((  void (*) (Property_2_t4395571972396AEE0D2671461D1603BB8D296451*, const RuntimeMethod*))Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0_gshared)(__this, method);
}
inline void ContainerPropertyBag_1__ctor_mFDAE4DA91A1BCD387794384FABBE63CD54647DFB (ContainerPropertyBag_1_t34F580FC14E2EF6488F9B8CEE5D00BE38DBC2496* __this, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t34F580FC14E2EF6488F9B8CEE5D00BE38DBC2496*, const RuntimeMethod*))ContainerPropertyBag_1__ctor_mFDAE4DA91A1BCD387794384FABBE63CD54647DFB_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_mE83AE59E6CAFA7F55F968FD2D8DBD9DCD0284632 (XProperty_tE606E1E3830D04DAEB0CE238A511A8CD0AFCF91F* __this, const RuntimeMethod* method) ;
inline void ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m1A8D6D29FEC8E7BB998E685F8A0CDFF7685AA4D4 (ContainerPropertyBag_1_t34F580FC14E2EF6488F9B8CEE5D00BE38DBC2496* __this, Property_2_tE0D61821CEE6E9C49C6C1BCAD5F62D78A24F7A56* ___0_property, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t34F580FC14E2EF6488F9B8CEE5D00BE38DBC2496*, Property_2_tE0D61821CEE6E9C49C6C1BCAD5F62D78A24F7A56*, const RuntimeMethod*))ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m1A8D6D29FEC8E7BB998E685F8A0CDFF7685AA4D4_gshared)(__this, ___0_property, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_m484229CC408CD868ABA8D0DBE8B12BDEEE74E4C8 (YProperty_t405447FD0661D730A61A82DBB5322B7645C9C409* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WidthProperty__ctor_m83F6E008F488F8C6DEC0A7BCCF5118221C576DCD (WidthProperty_t342CF43E06CF1BB3A8B11095799F09CDA16C0080* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void HeightProperty__ctor_m9191C19D80EA3F2F645028F03A507E0D492DBB2A (HeightProperty_tA93FD47B23964C044C6A92A943AFA11CAFF3E661* __this, const RuntimeMethod* method) ;
inline void Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230 (Property_2_tE0D61821CEE6E9C49C6C1BCAD5F62D78A24F7A56* __this, const RuntimeMethod* method)
{
	((  void (*) (Property_2_tE0D61821CEE6E9C49C6C1BCAD5F62D78A24F7A56*, const RuntimeMethod*))Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230_gshared)(__this, method);
}
inline void ContainerPropertyBag_1__ctor_m0B3C26C575C4406E1478D8D4FFB946522E95F740 (ContainerPropertyBag_1_t2279C723A3D81E0BE90A787349C987FC490033EF* __this, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t2279C723A3D81E0BE90A787349C987FC490033EF*, const RuntimeMethod*))ContainerPropertyBag_1__ctor_m0B3C26C575C4406E1478D8D4FFB946522E95F740_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CenterProperty__ctor_m4ECB75EAEC52B0EDDC2EE91F4F46F12A7870420A (CenterProperty_tD6327DAAB6BB3A87B57B00FDBB79F5891F611272* __this, const RuntimeMethod* method) ;
inline void ContainerPropertyBag_1_AddProperty_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m2D875551D9368012276D3FDD3DC12FD9A4BE3987 (ContainerPropertyBag_1_t2279C723A3D81E0BE90A787349C987FC490033EF* __this, Property_2_tC4748332B44E20FE7C498E90E74A718F89F44471* ___0_property, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t2279C723A3D81E0BE90A787349C987FC490033EF*, Property_2_tC4748332B44E20FE7C498E90E74A718F89F44471*, const RuntimeMethod*))ContainerPropertyBag_1_AddProperty_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m2D875551D9368012276D3FDD3DC12FD9A4BE3987_gshared)(__this, ___0_property, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ExtentsProperty__ctor_mC3315D3C24FA257EE106E3F6460360D65EB0DB16 (ExtentsProperty_t59E99CC5DAA1A0F80F2344E6297BABFF27F0B88F* __this, const RuntimeMethod* method) ;
inline void Property_2__ctor_m6D93F660253A4C7EC344945B11B74A1EC5E2052B (Property_2_tC4748332B44E20FE7C498E90E74A718F89F44471* __this, const RuntimeMethod* method)
{
	((  void (*) (Property_2_tC4748332B44E20FE7C498E90E74A718F89F44471*, const RuntimeMethod*))Property_2__ctor_m6D93F660253A4C7EC344945B11B74A1EC5E2052B_gshared)(__this, method);
}
inline void ContainerPropertyBag_1__ctor_mDF062EDE672E0636FD2510B7574F918A7924BAE1 (ContainerPropertyBag_1_t3A0DBCD09BBF64291800B0275CC7E1F2EECA0FEF* __this, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t3A0DBCD09BBF64291800B0275CC7E1F2EECA0FEF*, const RuntimeMethod*))ContainerPropertyBag_1__ctor_mDF062EDE672E0636FD2510B7574F918A7924BAE1_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PositionProperty__ctor_mC574247EEEC6CFC189F13B0B961BD2A9C2CBB2CD (PositionProperty_t413AD67DF4061C4B811A40AA01E44568337B8FA5* __this, const RuntimeMethod* method) ;
inline void ContainerPropertyBag_1_AddProperty_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m3CD2BB7313CFB80B5C21BFB971910AC718564437 (ContainerPropertyBag_1_t3A0DBCD09BBF64291800B0275CC7E1F2EECA0FEF* __this, Property_2_tAB010676E84B6005B47BB524D152BA4859373D2D* ___0_property, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t3A0DBCD09BBF64291800B0275CC7E1F2EECA0FEF*, Property_2_tAB010676E84B6005B47BB524D152BA4859373D2D*, const RuntimeMethod*))ContainerPropertyBag_1_AddProperty_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m3CD2BB7313CFB80B5C21BFB971910AC718564437_gshared)(__this, ___0_property, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SizeProperty__ctor_m45114288E626A2F23A1035BDB8420DD663FE25BB (SizeProperty_t9A3CD66744B590FB53B427877E1E781350ADF5C9* __this, const RuntimeMethod* method) ;
inline void Property_2__ctor_m6CCA84842C767E1068B2F635177225E05FFE539B (Property_2_tAB010676E84B6005B47BB524D152BA4859373D2D* __this, const RuntimeMethod* method)
{
	((  void (*) (Property_2_tAB010676E84B6005B47BB524D152BA4859373D2D*, const RuntimeMethod*))Property_2__ctor_m6CCA84842C767E1068B2F635177225E05FFE539B_gshared)(__this, method);
}
inline void ContainerPropertyBag_1__ctor_m14BD526E7B6C44170A4EF0E46DD101AA7D1C558E (ContainerPropertyBag_1_t569DC4A1FC35A7A716C097213C60CC9FA986B5A9* __this, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t569DC4A1FC35A7A716C097213C60CC9FA986B5A9*, const RuntimeMethod*))ContainerPropertyBag_1__ctor_m3377C4A2AFE52C17B429C1A35C2F2C9E953455AA_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MajorProperty__ctor_m5FA801277D886789A8732CE2E86F27C49E371679 (MajorProperty_tBBB67865E97B18DD17B9FB742398C3A3F79E8A54* __this, const RuntimeMethod* method) ;
inline void ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m56A8F79AE5548660FD24207FA0D287E84B15BAF3 (ContainerPropertyBag_1_t569DC4A1FC35A7A716C097213C60CC9FA986B5A9* __this, Property_2_tDF56505DA6AA5557884F8A3D032C17337E8E3951* ___0_property, const RuntimeMethod* method)
{
	((  void (*) (ContainerPropertyBag_1_t569DC4A1FC35A7A716C097213C60CC9FA986B5A9*, Property_2_tDF56505DA6AA5557884F8A3D032C17337E8E3951*, const RuntimeMethod*))ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_mBEAC8DBB778A44CAFB1950D04453DAF312DEDE32_gshared)(__this, ___0_property, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MinorProperty__ctor_mF391B7B9C95C2B48FB07127E463F0B788F9B0F29 (MinorProperty_tE0CA1F38999206EF615F6DAAF085F12C45D6D7DC* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BuildProperty__ctor_mD5BCC6C2F665694E7711CE8742008F6F2434BDAC (BuildProperty_t7D4DAF6A9110609825BB8D6A15C94A6BAF28E100* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RevisionProperty__ctor_m0606FE463AAA1C106582277BB0115987861BE2FC (RevisionProperty_t50A6B465CC86B9998606B7D5F614AE674BE69B6B* __this, const RuntimeMethod* method) ;
inline void Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91 (Property_2_tDF56505DA6AA5557884F8A3D032C17337E8E3951* __this, const RuntimeMethod* method)
{
	((  void (*) (Property_2_tDF56505DA6AA5557884F8A3D032C17337E8E3951*, const RuntimeMethod*))Property_2__ctor_m8513F2C4ED671798A7491909C328236F38A1AEB3_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MinAttribute__ctor_mE569E9A31ED222B7128543394AAF44DA09839262 (MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8* __this, float ___0_min, const RuntimeMethod* method) ;
inline void Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17 (Property_2_tDF56505DA6AA5557884F8A3D032C17337E8E3951* __this, Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA* ___0_attribute, const RuntimeMethod* method)
{
	((  void (*) (Property_2_tDF56505DA6AA5557884F8A3D032C17337E8E3951*, Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA*, const RuntimeMethod*))Property_2_AddAttribute_m7BE72727942237415CA702C9A13E7BB9CB108E58_gshared)(__this, ___0_attribute, method);
}
inline void ConcurrentDictionary_2__ctor_m4D5B845B9E86D2F0D817BCD2FB496291F8B6734B (ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582* __this, const RuntimeMethod* method)
{
	((  void (*) (ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582*, const RuntimeMethod*))ConcurrentDictionary_2__ctor_m0891574E19148D39C27C179BF2787093757C5F57_gshared)(__this, method);
}
inline void List_1__ctor_mD9035342BE1CF0A3EEEE157D1796DA385C0C3F69 (List_1_t4B77DB8D00EC6CC4705EB5F2FCC506472734EA72* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t4B77DB8D00EC6CC4705EB5F2FCC506472734EA72*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ReflectedPropertyBagProvider__ctor_mF83A77394EF7205D41A784158583F084E8CE04C2 (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DefaultPropertyBagInitializer_Initialize_m8BA3D4950B9C07EF2A0978A88D908FCF297BD080 (const RuntimeMethod* method) ;
inline bool ConcurrentDictionary_2_TryGetValue_mDEA54BBDF227F094A9D476D95BC3A3DF5077048B (ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582* __this, Type_t* ___0_key, RuntimeObject** ___1_value, const RuntimeMethod* method)
{
	return ((  bool (*) (ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582*, Type_t*, RuntimeObject**, const RuntimeMethod*))ConcurrentDictionary_2_TryGetValue_mE794BBB2811B9244CF8ECB11994F70DEB6BEFDF7_gshared)(__this, ___0_key, ___1_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool TypeTraits_IsContainer_m46D04F3E3219371CC5F133E2CC54BCA46FD72505 (Type_t* ___0_type, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Type_get_IsArray_mB9B8CA713B2AA9D6AFECC24E05AF78D22532B673 (Type_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Type_get_IsAbstract_m16FA83463867635ED9DECAE1C5F6BE96B4579CE5 (Type_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* ReflectedPropertyBagProvider_CreatePropertyBag_m89CDB23B3D426162B7641A4BB14DB3D71FE56CB2 (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF* __this, Type_t* ___0_type, const RuntimeMethod* method) ;
inline bool ConcurrentDictionary_2_TryAdd_m2CEBA7D69347F97B298F46E19C8736585DA58317 (ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582* __this, Type_t* ___0_key, RuntimeObject* ___1_value, const RuntimeMethod* method)
{
	return ((  bool (*) (ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582*, Type_t*, RuntimeObject*, const RuntimeMethod*))ConcurrentDictionary_2_TryAdd_mA7D07DAB0E4F228BEDBEBA3BEA124A7C839B0DFC_gshared)(__this, ___0_key, ___1_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2 (Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MethodInfo_t* Type_GetMethod_m9E66B5053F150537A74C490C1DA5174A7875189D (Type_t* __this, String_t* ___0_name, int32_t ___1_bindingAttr, const RuntimeMethod* method) ;
inline void Func_2__ctor_m85EFD3541E8A8498FD05A6169ED11E00D408A2F0 (Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164*, RuntimeObject*, intptr_t, const RuntimeMethod*))Func_2__ctor_m13C0A7F33154D861E2A041B52E88461832DA1697_gshared)(__this, ___0_object, ___1_method, method);
}
inline MethodInfo_t* Enumerable_First_TisMethodInfo_t_mF25EDA3EE26D685AA41FF116BE54C056DD0D179B (RuntimeObject* ___0_source, Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164* ___1_predicate, const RuntimeMethod* method)
{
	return ((  MethodInfo_t* (*) (RuntimeObject*, Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164*, const RuntimeMethod*))Enumerable_First_TisRuntimeObject_mC6FA107934316BE28EA02DDB9EADA7AE8DD1916B_gshared)(___0_source, ___1_predicate, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CGetPropertyMembersU3Ed__22__ctor_mFCA3444C6665B9FD96AF049C35FAD5D1D330BBB2 (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool FieldInfo_get_IsStatic_mEBBEB7B19A48D3E11BE830F3704C131A681F6139 (FieldInfo_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ReflectedPropertyBagProvider_IsValidPropertyType_mFBA6F8CF9F5B94652229D6A151B5302C460F05BF (Type_t* ___0_type, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MethodInfo_op_Inequality_mB73597A1FCC2F906DBCADDEC68A1B7D5B7E89FA8 (MethodInfo_t* ___0_left, MethodInfo_t* ___1_right, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MethodBase_get_IsStatic_mD2921396167EC4F99E2ADC46C39CCCEC3CD0E16E (MethodBase_t* __this, const RuntimeMethod* method) ;
inline void Func_2__ctor_mAFDFA2B152082BBF5E0626BF143EDACD61DE9D74 (Func_2_t7AF8146EC94DFCBB0F1B3E70111C1FB21D39F00E* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Func_2_t7AF8146EC94DFCBB0F1B3E70111C1FB21D39F00E*, RuntimeObject*, intptr_t, const RuntimeMethod*))Func_2__ctor_m13C0A7F33154D861E2A041B52E88461832DA1697_gshared)(__this, ___0_object, ___1_method, method);
}
inline bool Enumerable_All_TisType_t_m9EA20839AE686AA0B1927379BF8642A005ACFDE5 (RuntimeObject* ___0_source, Func_2_t7AF8146EC94DFCBB0F1B3E70111C1FB21D39F00E* ___1_predicate, const RuntimeMethod* method)
{
	return ((  bool (*) (RuntimeObject*, Func_2_t7AF8146EC94DFCBB0F1B3E70111C1FB21D39F00E*, const RuntimeMethod*))Enumerable_All_TisRuntimeObject_m57CDB9DB99F9E77214B47367D213B06AC3ED97FF_gshared)(___0_source, ___1_predicate, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m1B468CA26848A620321355E672EED6B1E6713535 (U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1 (String_t* ___0_a, String_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Environment_get_CurrentManagedThreadId_m66483AADCCC13272EBDCD94D31D2E52603C24BDF (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CGetPropertyMembersU3Ed__22_U3CU3Em__Finally1_m199A76100E9211640529676C30A85BD1060498D4 (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CGetPropertyMembersU3Ed__22_System_IDisposable_Dispose_m9F9302585189A3F427CBA6992B1257DE344119C4 (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* __this, const RuntimeMethod* method) ;
inline void Func_2__ctor_mCDDD46750A45128C47D438D81E5E88C718599821 (Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4*, RuntimeObject*, intptr_t, const RuntimeMethod*))Func_2__ctor_mEB7603EDE6D79A62E5BD74A896F030D2C9F2A821_gshared)(__this, ___0_object, ___1_method, method);
}
inline RuntimeObject* Enumerable_OrderBy_TisMemberInfo_t_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_mE25DD086E1B3B32ABF6B1CC2AE0323EA6782FF91 (RuntimeObject* ___0_source, Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4* ___1_keySelector, const RuntimeMethod* method)
{
	return ((  RuntimeObject* (*) (RuntimeObject*, Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4*, const RuntimeMethod*))Enumerable_OrderBy_TisRuntimeObject_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m0CD0677A1278BFC7E13405D11D1EF30919C9E9A2_gshared)(___0_source, ___1_keySelector, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ReflectedPropertyBagProvider_IsValidMember_m942BF637D7B010D3702E74009ABAA35CEF0624F2 (MemberInfo_t* ___0_memberInfo, const RuntimeMethod* method) ;
inline DontCreatePropertyAttribute_t759A6C6F524F0E05DEFD69A827104FEC2B648C0E* CustomAttributeExtensions_GetCustomAttribute_TisDontCreatePropertyAttribute_t759A6C6F524F0E05DEFD69A827104FEC2B648C0E_m3C664BC58E8B561E56DB4E2FEB7BFA8FF2D4DFB8 (MemberInfo_t* ___0_element, const RuntimeMethod* method)
{
	return ((  DontCreatePropertyAttribute_t759A6C6F524F0E05DEFD69A827104FEC2B648C0E* (*) (MemberInfo_t*, const RuntimeMethod*))CustomAttributeExtensions_GetCustomAttribute_TisRuntimeObject_m5B4B712DB5F08EBF4518A1973C9F11C15A4BEAE9_gshared)(___0_element, method);
}
inline CreatePropertyAttribute_t481619BD84F5E29767718DE127DD1E781A3B6F92* CustomAttributeExtensions_GetCustomAttribute_TisCreatePropertyAttribute_t481619BD84F5E29767718DE127DD1E781A3B6F92_mD72676F02AEB5E69DC033D1585E2106D0A40B8DB (MemberInfo_t* ___0_element, const RuntimeMethod* method)
{
	return ((  CreatePropertyAttribute_t481619BD84F5E29767718DE127DD1E781A3B6F92* (*) (MemberInfo_t*, const RuntimeMethod*))CustomAttributeExtensions_GetCustomAttribute_TisRuntimeObject_m5B4B712DB5F08EBF4518A1973C9F11C15A4BEAE9_gshared)(___0_element, method);
}
inline NonSerializedAttribute_t3E57D1A7E7F31DE192694AB4C8927BAE7D1BE177* CustomAttributeExtensions_GetCustomAttribute_TisNonSerializedAttribute_t3E57D1A7E7F31DE192694AB4C8927BAE7D1BE177_m2ED36C703953CABE08AA5B122C7604D9C2733021 (MemberInfo_t* ___0_element, const RuntimeMethod* method)
{
	return ((  NonSerializedAttribute_t3E57D1A7E7F31DE192694AB4C8927BAE7D1BE177* (*) (MemberInfo_t*, const RuntimeMethod*))CustomAttributeExtensions_GetCustomAttribute_TisRuntimeObject_m5B4B712DB5F08EBF4518A1973C9F11C15A4BEAE9_gshared)(___0_element, method);
}
inline SerializeField_t18B4EDCAA7D3D570AAB8A5CCBF29B2CEA7A3CA9C* CustomAttributeExtensions_GetCustomAttribute_TisSerializeField_t18B4EDCAA7D3D570AAB8A5CCBF29B2CEA7A3CA9C_m8CA1E6E172CEFA60B58858DD7FFC13D340EC18E3 (MemberInfo_t* ___0_element, const RuntimeMethod* method)
{
	return ((  SerializeField_t18B4EDCAA7D3D570AAB8A5CCBF29B2CEA7A3CA9C* (*) (MemberInfo_t*, const RuntimeMethod*))CustomAttributeExtensions_GetCustomAttribute_TisRuntimeObject_m5B4B712DB5F08EBF4518A1973C9F11C15A4BEAE9_gshared)(___0_element, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool FieldInfo_get_IsPublic_m73C84BEEAE6C993FACC6199B81DBF2B80D3810E5 (FieldInfo_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CGetPropertyMembersU3Ed__22_System_Collections_Generic_IEnumerableU3CSystem_Reflection_MemberInfoU3E_GetEnumerator_mFB07F1A6491DB7EEA44C182930AD32C728599080 (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Replace_mABDB7003A1D0AEDCAE9FF85E3DFFFBA752D2A166 (String_t* __this, String_t* ___0_oldValue, String_t* ___1_newValue, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshal_pinvoke(const FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF& unmarshaled, FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshaled_pinvoke& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF____m_FieldInfo_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_FieldInfoException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF____m_FieldInfo_FieldInfo_var, FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_FieldInfoException, NULL);
}
IL2CPP_EXTERN_C void FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshal_pinvoke_back(const FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshaled_pinvoke& marshaled, FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF____m_FieldInfo_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_FieldInfoException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF____m_FieldInfo_FieldInfo_var, FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_FieldInfoException, NULL);
}
IL2CPP_EXTERN_C void FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshal_pinvoke_cleanup(FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshal_com(const FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF& unmarshaled, FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshaled_com& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF____m_FieldInfo_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_FieldInfoException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF____m_FieldInfo_FieldInfo_var, FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_FieldInfoException, NULL);
}
IL2CPP_EXTERN_C void FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshal_com_back(const FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshaled_com& marshaled, FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF____m_FieldInfo_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_FieldInfoException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF____m_FieldInfo_FieldInfo_var, FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_FieldInfoException, NULL);
}
IL2CPP_EXTERN_C void FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshal_com_cleanup(FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF_marshaled_com& marshaled)
{
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FieldMember__ctor_mA6F2BB809BD42F26804E1E9A78B80ECA6C65FF03 (FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* __this, FieldInfo_t* ___0_fieldInfo, const RuntimeMethod* method) 
{
	{
		FieldInfo_t* L_0 = ___0_fieldInfo;
		__this->___m_FieldInfo = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_FieldInfo), (void*)L_0);
		FieldInfo_t* L_1 = __this->___m_FieldInfo;
		String_t* L_2;
		L_2 = ReflectionUtilities_SanitizeMemberName_m00515F1FC752D0A52D90143E58D19A7D40D799AE(L_1, NULL);
		__this->___U3CNameU3Ek__BackingField = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CNameU3Ek__BackingField), (void*)L_2);
		return;
	}
}
IL2CPP_EXTERN_C  void FieldMember__ctor_mA6F2BB809BD42F26804E1E9A78B80ECA6C65FF03_AdjustorThunk (RuntimeObject* __this, FieldInfo_t* ___0_fieldInfo, const RuntimeMethod* method)
{
	FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF*>(__this + _offset);
	FieldMember__ctor_mA6F2BB809BD42F26804E1E9A78B80ECA6C65FF03(_thisAdjusted, ___0_fieldInfo, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* FieldMember_get_Name_m9301F65E796E51B6F4EF0837AEB3EB9AD25293D6 (FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___U3CNameU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  String_t* FieldMember_get_Name_m9301F65E796E51B6F4EF0837AEB3EB9AD25293D6_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = FieldMember_get_Name_m9301F65E796E51B6F4EF0837AEB3EB9AD25293D6_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool FieldMember_get_IsReadOnly_mF9CA4CBB2973908DA90F49DD86124FC769168FB0 (FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* __this, const RuntimeMethod* method) 
{
	{
		FieldInfo_t* L_0 = __this->___m_FieldInfo;
		NullCheck(L_0);
		bool L_1;
		L_1 = FieldInfo_get_IsInitOnly_m476BB9325A68BDD56B088D3E8407F75FA1388ED9(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C  bool FieldMember_get_IsReadOnly_mF9CA4CBB2973908DA90F49DD86124FC769168FB0_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF*>(__this + _offset);
	bool _returnValue;
	_returnValue = FieldMember_get_IsReadOnly_mF9CA4CBB2973908DA90F49DD86124FC769168FB0(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* FieldMember_get_ValueType_m76238497CE787FD26A79341A8F3207EFF31CE63A (FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* __this, const RuntimeMethod* method) 
{
	{
		FieldInfo_t* L_0 = __this->___m_FieldInfo;
		NullCheck(L_0);
		Type_t* L_1;
		L_1 = VirtualFuncInvoker0< Type_t* >::Invoke(17, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C  Type_t* FieldMember_get_ValueType_m76238497CE787FD26A79341A8F3207EFF31CE63A_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF*>(__this + _offset);
	Type_t* _returnValue;
	_returnValue = FieldMember_get_ValueType_m76238497CE787FD26A79341A8F3207EFF31CE63A(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* FieldMember_GetCustomAttributes_m3A1CF3269B1FD4C659FDEA8D51CFAE003612101D (FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* __this, const RuntimeMethod* method) 
{
	{
		FieldInfo_t* L_0 = __this->___m_FieldInfo;
		RuntimeObject* L_1;
		L_1 = CustomAttributeExtensions_GetCustomAttributes_m2851556A37AAF9A808EFB2C603D11E48635FA785(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C  RuntimeObject* FieldMember_GetCustomAttributes_m3A1CF3269B1FD4C659FDEA8D51CFAE003612101D_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF*>(__this + _offset);
	RuntimeObject* _returnValue;
	_returnValue = FieldMember_GetCustomAttributes_m3A1CF3269B1FD4C659FDEA8D51CFAE003612101D(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshal_pinvoke(const PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984& unmarshaled, PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshaled_pinvoke& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984____m_PropertyInfo_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_PropertyInfoException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984____m_PropertyInfo_FieldInfo_var, PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_PropertyInfoException, NULL);
}
IL2CPP_EXTERN_C void PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshal_pinvoke_back(const PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshaled_pinvoke& marshaled, PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984____m_PropertyInfo_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_PropertyInfoException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984____m_PropertyInfo_FieldInfo_var, PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_PropertyInfoException, NULL);
}
IL2CPP_EXTERN_C void PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshal_pinvoke_cleanup(PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshal_com(const PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984& unmarshaled, PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshaled_com& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984____m_PropertyInfo_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_PropertyInfoException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984____m_PropertyInfo_FieldInfo_var, PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_PropertyInfoException, NULL);
}
IL2CPP_EXTERN_C void PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshal_com_back(const PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshaled_com& marshaled, PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984____m_PropertyInfo_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___m_PropertyInfoException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984____m_PropertyInfo_FieldInfo_var, PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_PropertyInfoException, NULL);
}
IL2CPP_EXTERN_C void PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshal_com_cleanup(PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984_marshaled_com& marshaled)
{
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* PropertyMember_get_Name_mCB0EBF067D82D0871C8051116D34A7D82EBE67FB (PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___U3CNameU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  String_t* PropertyMember_get_Name_mCB0EBF067D82D0871C8051116D34A7D82EBE67FB_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = PropertyMember_get_Name_mCB0EBF067D82D0871C8051116D34A7D82EBE67FB_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PropertyMember_get_IsReadOnly_m595190E0A259DF7A4CD106BFF16949E939F511C5 (PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* __this, const RuntimeMethod* method) 
{
	{
		PropertyInfo_t* L_0 = __this->___m_PropertyInfo;
		NullCheck(L_0);
		bool L_1;
		L_1 = VirtualFuncInvoker0< bool >::Invoke(19, L_0);
		return (bool)((((int32_t)L_1) == ((int32_t)0))? 1 : 0);
	}
}
IL2CPP_EXTERN_C  bool PropertyMember_get_IsReadOnly_m595190E0A259DF7A4CD106BFF16949E939F511C5_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984*>(__this + _offset);
	bool _returnValue;
	_returnValue = PropertyMember_get_IsReadOnly_m595190E0A259DF7A4CD106BFF16949E939F511C5(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* PropertyMember_get_ValueType_m40F8CF0BBF20782A6C594365911716F386FE4E1A (PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* __this, const RuntimeMethod* method) 
{
	{
		PropertyInfo_t* L_0 = __this->___m_PropertyInfo;
		NullCheck(L_0);
		Type_t* L_1;
		L_1 = VirtualFuncInvoker0< Type_t* >::Invoke(16, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C  Type_t* PropertyMember_get_ValueType_m40F8CF0BBF20782A6C594365911716F386FE4E1A_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984*>(__this + _offset);
	Type_t* _returnValue;
	_returnValue = PropertyMember_get_ValueType_m40F8CF0BBF20782A6C594365911716F386FE4E1A(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyMember__ctor_m0B727AF7D87194ED8EA453CC4CF01A10563E2334 (PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* __this, PropertyInfo_t* ___0_propertyInfo, const RuntimeMethod* method) 
{
	{
		PropertyInfo_t* L_0 = ___0_propertyInfo;
		__this->___m_PropertyInfo = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_PropertyInfo), (void*)L_0);
		PropertyInfo_t* L_1 = __this->___m_PropertyInfo;
		String_t* L_2;
		L_2 = ReflectionUtilities_SanitizeMemberName_m00515F1FC752D0A52D90143E58D19A7D40D799AE(L_1, NULL);
		__this->___U3CNameU3Ek__BackingField = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CNameU3Ek__BackingField), (void*)L_2);
		return;
	}
}
IL2CPP_EXTERN_C  void PropertyMember__ctor_m0B727AF7D87194ED8EA453CC4CF01A10563E2334_AdjustorThunk (RuntimeObject* __this, PropertyInfo_t* ___0_propertyInfo, const RuntimeMethod* method)
{
	PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984*>(__this + _offset);
	PropertyMember__ctor_m0B727AF7D87194ED8EA453CC4CF01A10563E2334(_thisAdjusted, ___0_propertyInfo, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PropertyMember_GetCustomAttributes_mE4214E27A5A04DF047573888EDD9590FD6C85409 (PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* __this, const RuntimeMethod* method) 
{
	{
		PropertyInfo_t* L_0 = __this->___m_PropertyInfo;
		RuntimeObject* L_1;
		L_1 = CustomAttributeExtensions_GetCustomAttributes_m2851556A37AAF9A808EFB2C603D11E48635FA785(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C  RuntimeObject* PropertyMember_GetCustomAttributes_mE4214E27A5A04DF047573888EDD9590FD6C85409_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984*>(__this + _offset);
	RuntimeObject* _returnValue;
	_returnValue = PropertyMember_GetCustomAttributes_mE4214E27A5A04DF047573888EDD9590FD6C85409(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool TypeTraits_IsContainer_m46D04F3E3219371CC5F133E2CC54BCA46FD72505 (Type_t* ___0_type, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&String_t_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	int32_t G_B7_0 = 0;
	{
		Type_t* L_0 = ___0_type;
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC((Type_t*)NULL, L_0, NULL);
		V_0 = L_1;
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_0017;
		}
	}
	{
		ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129* L_3 = (ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentNullException_t327031E412FAB2351B0022DD5DAD47E67E597129_il2cpp_TypeInfo_var)));
		ArgumentNullException__ctor_m444AE141157E333844FC1A9500224C2F9FD24F4B(L_3, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF3C6C902DBF80139640F6554F0C3392016A8ADF7)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_3, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&TypeTraits_IsContainer_m46D04F3E3219371CC5F133E2CC54BCA46FD72505_RuntimeMethod_var)));
	}

IL_0017:
	{
		Type_t* L_4 = ___0_type;
		NullCheck(L_4);
		bool L_5;
		L_5 = Type_get_IsPrimitive_m46ACAAB8F754D37713E3E45437705F4F58FAFA18(L_4, NULL);
		if (L_5)
		{
			goto IL_0044;
		}
	}
	{
		Type_t* L_6 = ___0_type;
		NullCheck(L_6);
		bool L_7;
		L_7 = Type_get_IsPointer_mC8AAAFEC4E4CEA59DAD0032B85D1BB224763278B(L_6, NULL);
		if (L_7)
		{
			goto IL_0044;
		}
	}
	{
		Type_t* L_8 = ___0_type;
		NullCheck(L_8);
		bool L_9;
		L_9 = VirtualFuncInvoker0< bool >::Invoke(69, L_8);
		if (L_9)
		{
			goto IL_0044;
		}
	}
	{
		Type_t* L_10 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_11 = { reinterpret_cast<intptr_t> (String_t_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_12;
		L_12 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_11, NULL);
		bool L_13;
		L_13 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_10, L_12, NULL);
		G_B7_0 = ((((int32_t)L_13) == ((int32_t)0))? 1 : 0);
		goto IL_0045;
	}

IL_0044:
	{
		G_B7_0 = 0;
	}

IL_0045:
	{
		V_1 = (bool)G_B7_0;
		goto IL_0048;
	}

IL_0048:
	{
		bool L_14 = V_1;
		return L_14;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TypeUtility__cctor_mFCC4029AD0D2F3FBE3F7117AC485DF687DE409A5 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConcurrentDictionary_2__ctor_m2CD607D007C68544A77E46F721E1F7A20EFBD2BE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConcurrentDictionary_2__ctor_m6B09103AE0E5ACDEA8157B5E09343647B1A55E80_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Func_1_t067EFEB6C5607DEFF52B09F0A11295959E292D7C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Func_1_t367387BB2C476D3F32DB12161B5FDC128DC3231C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectPool_1__ctor_m35C71BF5CBC8DED0ED44D82D926F61E584CA5AD4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RuntimeObject_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeUtility_SetExplicitInstantiationMethod_TisString_t_m1BB80FFC484AB56A416E91F9E4EAB2FF75861EFE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3C_cctorU3Eb__11_0_m59D3FD88A1841D56C6A2D231E993AA6D2E6465E1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3C_cctorU3Eb__11_1_mCF36865D4A819F0C0DDA987F30077434E56BC826_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3C_cctorU3Eb__11_2_mBA773F3E344C145FE939B19C5EE690F9FEC60716_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral53CDB29A789F785BCE9C340E574B4877B0E4D7EB);
		s_Il2CppMethodInitialized = true;
	}
	MethodInfoU5BU5D_tDF3670604A0AECF814A0B0BA09B91FBF0D6A3265* V_0 = NULL;
	int32_t V_1 = 0;
	MethodInfo_t* V_2 = NULL;
	bool V_3 = false;
	bool V_4 = false;
	int32_t G_B4_0 = 0;
	{
		ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91* L_0 = (ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91*)il2cpp_codegen_object_new(ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91_il2cpp_TypeInfo_var);
		ConcurrentDictionary_2__ctor_m2CD607D007C68544A77E46F721E1F7A20EFBD2BE(L_0, ConcurrentDictionary_2__ctor_m2CD607D007C68544A77E46F721E1F7A20EFBD2BE_RuntimeMethod_var);
		((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_TypeConstructors = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_TypeConstructors), (void*)L_0);
		RuntimeObject* L_1 = (RuntimeObject*)il2cpp_codegen_object_new(RuntimeObject_il2cpp_TypeInfo_var);
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(L_1, NULL);
		((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___syncedPoolObject = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___syncedPoolObject), (void*)L_1);
		ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB* L_2 = (ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB*)il2cpp_codegen_object_new(ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB_il2cpp_TypeInfo_var);
		ConcurrentDictionary_2__ctor_m6B09103AE0E5ACDEA8157B5E09343647B1A55E80(L_2, ConcurrentDictionary_2__ctor_m6B09103AE0E5ACDEA8157B5E09343647B1A55E80_RuntimeMethod_var);
		((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_CachedResolvedName = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_CachedResolvedName), (void*)L_2);
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_il2cpp_TypeInfo_var);
		U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203* L_3 = ((U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_il2cpp_TypeInfo_var))->___U3CU3E9;
		Func_1_t067EFEB6C5607DEFF52B09F0A11295959E292D7C* L_4 = (Func_1_t067EFEB6C5607DEFF52B09F0A11295959E292D7C*)il2cpp_codegen_object_new(Func_1_t067EFEB6C5607DEFF52B09F0A11295959E292D7C_il2cpp_TypeInfo_var);
		Func_1__ctor_mD927C23DE447A096B2B1F7D75ECDFE610DD39F2A(L_4, L_3, (intptr_t)((void*)U3CU3Ec_U3C_cctorU3Eb__11_0_m59D3FD88A1841D56C6A2D231E993AA6D2E6465E1_RuntimeMethod_var), NULL);
		U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203* L_5 = ((U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_il2cpp_TypeInfo_var))->___U3CU3E9;
		Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6* L_6 = (Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6*)il2cpp_codegen_object_new(Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6_il2cpp_TypeInfo_var);
		Action_1__ctor_m1540C8C2516AEA9F0AEE87470E2815CA36C560C7(L_6, L_5, (intptr_t)((void*)U3CU3Ec_U3C_cctorU3Eb__11_1_mCF36865D4A819F0C0DDA987F30077434E56BC826_RuntimeMethod_var), NULL);
		ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A* L_7 = (ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A*)il2cpp_codegen_object_new(ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A_il2cpp_TypeInfo_var);
		ObjectPool_1__ctor_m35C71BF5CBC8DED0ED44D82D926F61E584CA5AD4(L_7, L_4, (Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6*)NULL, L_6, (Action_1_t9E1712DA0A3123C126566BD722BF0E60F1E0AAD6*)NULL, (bool)1, ((int32_t)10), ((int32_t)10000), ObjectPool_1__ctor_m35C71BF5CBC8DED0ED44D82D926F61E584CA5AD4_RuntimeMethod_var);
		((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_Builders = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_Builders), (void*)L_7);
		U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203* L_8 = ((U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_il2cpp_TypeInfo_var))->___U3CU3E9;
		Func_1_t367387BB2C476D3F32DB12161B5FDC128DC3231C* L_9 = (Func_1_t367387BB2C476D3F32DB12161B5FDC128DC3231C*)il2cpp_codegen_object_new(Func_1_t367387BB2C476D3F32DB12161B5FDC128DC3231C_il2cpp_TypeInfo_var);
		Func_1__ctor_m27A68E928C1D9158EAAD261086B9BC424339327B(L_9, L_8, (intptr_t)((void*)U3CU3Ec_U3C_cctorU3Eb__11_2_mBA773F3E344C145FE939B19C5EE690F9FEC60716_RuntimeMethod_var), NULL);
		TypeUtility_SetExplicitInstantiationMethod_TisString_t_m1BB80FFC484AB56A416E91F9E4EAB2FF75861EFE(L_9, TypeUtility_SetExplicitInstantiationMethod_TisString_t_m1BB80FFC484AB56A416E91F9E4EAB2FF75861EFE_RuntimeMethod_var);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_10 = { reinterpret_cast<intptr_t> (TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_11;
		L_11 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_10, NULL);
		NullCheck(L_11);
		MethodInfoU5BU5D_tDF3670604A0AECF814A0B0BA09B91FBF0D6A3265* L_12;
		L_12 = VirtualFuncInvoker1< MethodInfoU5BU5D_tDF3670604A0AECF814A0B0BA09B91FBF0D6A3265*, int32_t >::Invoke(100, L_11, ((int32_t)40));
		V_0 = L_12;
		V_1 = 0;
		goto IL_00b5;
	}

IL_0080:
	{
		MethodInfoU5BU5D_tDF3670604A0AECF814A0B0BA09B91FBF0D6A3265* L_13 = V_0;
		int32_t L_14 = V_1;
		NullCheck(L_13);
		int32_t L_15 = L_14;
		MethodInfo_t* L_16 = (L_13)->GetAt(static_cast<il2cpp_array_size_t>(L_15));
		V_2 = L_16;
		MethodInfo_t* L_17 = V_2;
		NullCheck(L_17);
		String_t* L_18;
		L_18 = VirtualFuncInvoker0< String_t* >::Invoke(8, L_17);
		bool L_19;
		L_19 = String_op_Inequality_m8C940F3CFC42866709D7CA931B3D77B4BE94BCB6(L_18, _stringLiteral53CDB29A789F785BCE9C340E574B4877B0E4D7EB, NULL);
		if (L_19)
		{
			goto IL_00a2;
		}
	}
	{
		MethodInfo_t* L_20 = V_2;
		NullCheck(L_20);
		bool L_21;
		L_21 = VirtualFuncInvoker0< bool >::Invoke(26, L_20);
		G_B4_0 = ((((int32_t)L_21) == ((int32_t)0))? 1 : 0);
		goto IL_00a3;
	}

IL_00a2:
	{
		G_B4_0 = 1;
	}

IL_00a3:
	{
		V_3 = (bool)G_B4_0;
		bool L_22 = V_3;
		if (!L_22)
		{
			goto IL_00a9;
		}
	}
	{
		goto IL_00b1;
	}

IL_00a9:
	{
		MethodInfo_t* L_23 = V_2;
		((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_CreateTypeConstructor = L_23;
		Il2CppCodeGenWriteBarrier((void**)(&((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_CreateTypeConstructor), (void*)L_23);
		goto IL_00bb;
	}

IL_00b1:
	{
		int32_t L_24 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_24, 1));
	}

IL_00b5:
	{
		int32_t L_25 = V_1;
		MethodInfoU5BU5D_tDF3670604A0AECF814A0B0BA09B91FBF0D6A3265* L_26 = V_0;
		NullCheck(L_26);
		if ((((int32_t)L_25) < ((int32_t)((int32_t)(((RuntimeArray*)L_26)->max_length)))))
		{
			goto IL_0080;
		}
	}

IL_00bb:
	{
		MethodInfo_t* L_27 = ((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_CreateTypeConstructor;
		bool L_28;
		L_28 = MethodInfo_op_Equality_m1466AB76300C9F07856E706E7E914062175189D1((MethodInfo_t*)NULL, L_27, NULL);
		V_4 = L_28;
		bool L_29 = V_4;
		if (!L_29)
		{
			goto IL_00d2;
		}
	}
	{
		InvalidProgramException_t5715C9ED8CFE88C0DB9CD5614BA3D72BA9259C9B* L_30 = (InvalidProgramException_t5715C9ED8CFE88C0DB9CD5614BA3D72BA9259C9B*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidProgramException_t5715C9ED8CFE88C0DB9CD5614BA3D72BA9259C9B_il2cpp_TypeInfo_var)));
		InvalidProgramException__ctor_m75BD70D9AEEE6B109A3FB51897615B6DAA992B28(L_30, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_30, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&TypeUtility__cctor_mFCC4029AD0D2F3FBE3F7117AC485DF687DE409A5_RuntimeMethod_var)));
	}

IL_00d2:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* TypeUtility_GetTypeDisplayName_m45C565A0737008926DD1D4CA6B453A475C8E77FD (Type_t* ___0_type, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConcurrentDictionary_2_TryGetValue_mC760AE4BCF6AFC3CB7C64E8A405B651395A9D238_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConcurrentDictionary_2_set_Item_m5EF95C43AE2498CE264FCEE432AE379AD39651C2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	String_t* V_0 = NULL;
	int32_t V_1 = 0;
	bool V_2 = false;
	String_t* V_3 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB* L_0 = ((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_CachedResolvedName;
		Type_t* L_1 = ___0_type;
		NullCheck(L_0);
		bool L_2;
		L_2 = ConcurrentDictionary_2_TryGetValue_mC760AE4BCF6AFC3CB7C64E8A405B651395A9D238(L_0, L_1, (&V_0), ConcurrentDictionary_2_TryGetValue_mC760AE4BCF6AFC3CB7C64E8A405B651395A9D238_RuntimeMethod_var);
		V_2 = L_2;
		bool L_3 = V_2;
		if (!L_3)
		{
			goto IL_0016;
		}
	}
	{
		String_t* L_4 = V_0;
		V_3 = L_4;
		goto IL_0038;
	}

IL_0016:
	{
		V_1 = 0;
		Type_t* L_5 = ___0_type;
		Type_t* L_6 = ___0_type;
		NullCheck(L_6);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_7;
		L_7 = VirtualFuncInvoker0< TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* >::Invoke(50, L_6);
		il2cpp_codegen_runtime_class_init_inline(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		String_t* L_8;
		L_8 = TypeUtility_GetTypeDisplayName_mCDE259ECEE5E6A4E7C52D4319D7E5D856E19F862(L_5, (RuntimeObject*)L_7, (&V_1), NULL);
		V_0 = L_8;
		ConcurrentDictionary_2_t889EC4BD07B5DCB6087E2226ACC8A5E5590E48AB* L_9 = ((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_CachedResolvedName;
		Type_t* L_10 = ___0_type;
		String_t* L_11 = V_0;
		NullCheck(L_9);
		ConcurrentDictionary_2_set_Item_m5EF95C43AE2498CE264FCEE432AE379AD39651C2(L_9, L_10, L_11, ConcurrentDictionary_2_set_Item_m5EF95C43AE2498CE264FCEE432AE379AD39651C2_RuntimeMethod_var);
		String_t* L_12 = V_0;
		V_3 = L_12;
		goto IL_0038;
	}

IL_0038:
	{
		String_t* L_13 = V_3;
		return L_13;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* TypeUtility_GetTypeDisplayName_mCDE259ECEE5E6A4E7C52D4319D7E5D856E19F862 (Type_t* ___0_type, RuntimeObject* ___1_args, int32_t* ___2_argIndex, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Char_t521A6F19B456D956AF452D926C32709DC03D6B17_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IReadOnlyCollection_1_tB0C1D5CC73DC8C9F615E80A005D312E046F4D762_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IReadOnlyList_1_tFAB749F93AC9273AC78377822795638920495BA2_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectPool_1_Get_m9A0C64A471F1134751E86CFABE36283B06E29F8D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectPool_1_Release_mD38ED7186BD4E9178B260810EEA654454B3BB478_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&String_t_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral007174B876FA7EECC4152046B9308966D3E2B5B8);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0BA8CB3B900ECEF5E697192B8CDA6B626EB0CE72);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral115036F179EA48E7662F9BE55E0E00A42DDE6DA7);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2F1705A1AA8BA6FCE863E7F2CBA4BC28458C77AE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral316316045E2DB71BB9C0748EE882DBF00C83FD8E);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral38FB386B58970DA493E868CAC2AC6550E559B5D7);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral673CC9996FD90AFE21BD8D0E6E6824353AF4BDA2);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral758733BDBED83CBFF4F635AC26CA92AAE477F75D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBD2F86A6F80C957D36F5792A1101E475BB128D86);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD890B2BC5E5200965CD02403ABB6C221A614A1B7);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE1E5CE10BE86E259146E8ADE82FB423C65C3FFD7);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE3165827D406DD8F354BE7515C630A771E98916A);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE3AB954C27345B5777E41817C31696D6AC0E87C1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE3DFC065B6A6D9931B797808DD066491AAB82B29);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		s_Il2CppMethodInitialized = true;
	}
	String_t* V_0 = NULL;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	StringBuilder_t* V_3 = NULL;
	bool V_4 = false;
	String_t* V_5 = NULL;
	bool V_6 = false;
	bool V_7 = false;
	bool V_8 = false;
	bool V_9 = false;
	bool V_10 = false;
	bool V_11 = false;
	bool V_12 = false;
	bool V_13 = false;
	bool V_14 = false;
	bool V_15 = false;
	bool V_16 = false;
	bool V_17 = false;
	bool V_18 = false;
	bool V_19 = false;
	bool V_20 = false;
	RuntimeObject* V_21 = NULL;
	bool V_22 = false;
	int32_t V_23 = 0;
	bool V_24 = false;
	bool V_25 = false;
	bool V_26 = false;
	RuntimeObject* V_27 = NULL;
	bool V_28 = false;
	int32_t G_B45_0 = 0;
	{
		Type_t* L_0 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_1 = { reinterpret_cast<intptr_t> (Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_2;
		L_2 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_1, NULL);
		bool L_3;
		L_3 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_0, L_2, NULL);
		V_4 = L_3;
		bool L_4 = V_4;
		if (!L_4)
		{
			goto IL_0023;
		}
	}
	{
		V_5 = _stringLiteral0BA8CB3B900ECEF5E697192B8CDA6B626EB0CE72;
		goto IL_030c;
	}

IL_0023:
	{
		Type_t* L_5 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_6 = { reinterpret_cast<intptr_t> (UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_7;
		L_7 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_6, NULL);
		bool L_8;
		L_8 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_5, L_7, NULL);
		V_6 = L_8;
		bool L_9 = V_6;
		if (!L_9)
		{
			goto IL_0045;
		}
	}
	{
		V_5 = _stringLiteralE3165827D406DD8F354BE7515C630A771E98916A;
		goto IL_030c;
	}

IL_0045:
	{
		Type_t* L_10 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_11 = { reinterpret_cast<intptr_t> (Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_12;
		L_12 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_11, NULL);
		bool L_13;
		L_13 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_10, L_12, NULL);
		V_7 = L_13;
		bool L_14 = V_7;
		if (!L_14)
		{
			goto IL_0067;
		}
	}
	{
		V_5 = _stringLiteral115036F179EA48E7662F9BE55E0E00A42DDE6DA7;
		goto IL_030c;
	}

IL_0067:
	{
		Type_t* L_15 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_16 = { reinterpret_cast<intptr_t> (UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_17;
		L_17 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_16, NULL);
		bool L_18;
		L_18 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_15, L_17, NULL);
		V_8 = L_18;
		bool L_19 = V_8;
		if (!L_19)
		{
			goto IL_0089;
		}
	}
	{
		V_5 = _stringLiteral316316045E2DB71BB9C0748EE882DBF00C83FD8E;
		goto IL_030c;
	}

IL_0089:
	{
		Type_t* L_20 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_21 = { reinterpret_cast<intptr_t> (Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_22;
		L_22 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_21, NULL);
		bool L_23;
		L_23 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_20, L_22, NULL);
		V_9 = L_23;
		bool L_24 = V_9;
		if (!L_24)
		{
			goto IL_00ab;
		}
	}
	{
		V_5 = _stringLiteralE1E5CE10BE86E259146E8ADE82FB423C65C3FFD7;
		goto IL_030c;
	}

IL_00ab:
	{
		Type_t* L_25 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_26 = { reinterpret_cast<intptr_t> (Char_t521A6F19B456D956AF452D926C32709DC03D6B17_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_27;
		L_27 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_26, NULL);
		bool L_28;
		L_28 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_25, L_27, NULL);
		V_10 = L_28;
		bool L_29 = V_10;
		if (!L_29)
		{
			goto IL_00cd;
		}
	}
	{
		V_5 = _stringLiteral38FB386B58970DA493E868CAC2AC6550E559B5D7;
		goto IL_030c;
	}

IL_00cd:
	{
		Type_t* L_30 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_31 = { reinterpret_cast<intptr_t> (Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_32;
		L_32 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_31, NULL);
		bool L_33;
		L_33 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_30, L_32, NULL);
		V_11 = L_33;
		bool L_34 = V_11;
		if (!L_34)
		{
			goto IL_00ef;
		}
	}
	{
		V_5 = _stringLiteral673CC9996FD90AFE21BD8D0E6E6824353AF4BDA2;
		goto IL_030c;
	}

IL_00ef:
	{
		Type_t* L_35 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_36 = { reinterpret_cast<intptr_t> (Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_37;
		L_37 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_36, NULL);
		bool L_38;
		L_38 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_35, L_37, NULL);
		V_12 = L_38;
		bool L_39 = V_12;
		if (!L_39)
		{
			goto IL_0111;
		}
	}
	{
		V_5 = _stringLiteralD890B2BC5E5200965CD02403ABB6C221A614A1B7;
		goto IL_030c;
	}

IL_0111:
	{
		Type_t* L_40 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_41 = { reinterpret_cast<intptr_t> (UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_42;
		L_42 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_41, NULL);
		bool L_43;
		L_43 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_40, L_42, NULL);
		V_13 = L_43;
		bool L_44 = V_13;
		if (!L_44)
		{
			goto IL_0133;
		}
	}
	{
		V_5 = _stringLiteralE3AB954C27345B5777E41817C31696D6AC0E87C1;
		goto IL_030c;
	}

IL_0133:
	{
		Type_t* L_45 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_46 = { reinterpret_cast<intptr_t> (Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_47;
		L_47 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_46, NULL);
		bool L_48;
		L_48 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_45, L_47, NULL);
		V_14 = L_48;
		bool L_49 = V_14;
		if (!L_49)
		{
			goto IL_0155;
		}
	}
	{
		V_5 = _stringLiteralE3DFC065B6A6D9931B797808DD066491AAB82B29;
		goto IL_030c;
	}

IL_0155:
	{
		Type_t* L_50 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_51 = { reinterpret_cast<intptr_t> (Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_52;
		L_52 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_51, NULL);
		bool L_53;
		L_53 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_50, L_52, NULL);
		V_15 = L_53;
		bool L_54 = V_15;
		if (!L_54)
		{
			goto IL_0177;
		}
	}
	{
		V_5 = _stringLiteral007174B876FA7EECC4152046B9308966D3E2B5B8;
		goto IL_030c;
	}

IL_0177:
	{
		Type_t* L_55 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_56 = { reinterpret_cast<intptr_t> (String_t_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_57;
		L_57 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_56, NULL);
		bool L_58;
		L_58 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_55, L_57, NULL);
		V_16 = L_58;
		bool L_59 = V_16;
		if (!L_59)
		{
			goto IL_0199;
		}
	}
	{
		V_5 = _stringLiteral2F1705A1AA8BA6FCE863E7F2CBA4BC28458C77AE;
		goto IL_030c;
	}

IL_0199:
	{
		Type_t* L_60 = ___0_type;
		NullCheck(L_60);
		String_t* L_61;
		L_61 = VirtualFuncInvoker0< String_t* >::Invoke(8, L_60);
		V_0 = L_61;
		Type_t* L_62 = ___0_type;
		NullCheck(L_62);
		bool L_63;
		L_63 = VirtualFuncInvoker0< bool >::Invoke(38, L_62);
		V_17 = L_63;
		bool L_64 = V_17;
		if (!L_64)
		{
			goto IL_01b5;
		}
	}
	{
		String_t* L_65 = V_0;
		V_5 = L_65;
		goto IL_030c;
	}

IL_01b5:
	{
		Type_t* L_66 = ___0_type;
		NullCheck(L_66);
		bool L_67;
		L_67 = Type_get_IsNested_mCF57E6A68BA5CEDDB9DA81CB34B6945F414FB3FD(L_66, NULL);
		V_18 = L_67;
		bool L_68 = V_18;
		if (!L_68)
		{
			goto IL_01dc;
		}
	}
	{
		Type_t* L_69 = ___0_type;
		NullCheck(L_69);
		Type_t* L_70;
		L_70 = VirtualFuncInvoker0< Type_t* >::Invoke(9, L_69);
		RuntimeObject* L_71 = ___1_args;
		int32_t* L_72 = ___2_argIndex;
		il2cpp_codegen_runtime_class_init_inline(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		String_t* L_73;
		L_73 = TypeUtility_GetTypeDisplayName_mCDE259ECEE5E6A4E7C52D4319D7E5D856E19F862(L_70, L_71, L_72, NULL);
		String_t* L_74 = V_0;
		String_t* L_75;
		L_75 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(L_73, _stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D, L_74, NULL);
		V_0 = L_75;
	}

IL_01dc:
	{
		Type_t* L_76 = ___0_type;
		NullCheck(L_76);
		bool L_77;
		L_77 = VirtualFuncInvoker0< bool >::Invoke(40, L_76);
		V_19 = (bool)((((int32_t)L_77) == ((int32_t)0))? 1 : 0);
		bool L_78 = V_19;
		if (!L_78)
		{
			goto IL_01f3;
		}
	}
	{
		String_t* L_79 = V_0;
		V_5 = L_79;
		goto IL_030c;
	}

IL_01f3:
	{
		String_t* L_80 = V_0;
		NullCheck(L_80);
		int32_t L_81;
		L_81 = String_IndexOf_mE21E78F35EF4A7768E385A72814C88D22B689966(L_80, ((int32_t)96), NULL);
		V_1 = L_81;
		Type_t* L_82 = ___0_type;
		NullCheck(L_82);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_83;
		L_83 = VirtualFuncInvoker0< TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* >::Invoke(50, L_82);
		NullCheck(L_83);
		V_2 = ((int32_t)(((RuntimeArray*)L_83)->max_length));
		int32_t L_84 = V_1;
		V_20 = (bool)((((int32_t)L_84) > ((int32_t)(-1)))? 1 : 0);
		bool L_85 = V_20;
		if (!L_85)
		{
			goto IL_0228;
		}
	}
	{
		String_t* L_86 = V_0;
		int32_t L_87 = V_1;
		NullCheck(L_86);
		String_t* L_88;
		L_88 = String_Substring_m6BA4A3FA3800FE92662D0847CC8E1EEF940DF472(L_86, ((int32_t)il2cpp_codegen_add(L_87, 1)), NULL);
		int32_t L_89;
		L_89 = Int32_Parse_m273CA1A9C7717C99641291A95C543711C0202AF0(L_88, NULL);
		V_2 = L_89;
		String_t* L_90 = V_0;
		int32_t L_91 = V_1;
		NullCheck(L_90);
		String_t* L_92;
		L_92 = String_Remove_m8266E0BF5D8565D4CDC850F21E9178AE254C3E85(L_90, L_91, NULL);
		V_0 = L_92;
	}

IL_0228:
	{
		V_3 = (StringBuilder_t*)NULL;
		il2cpp_codegen_runtime_class_init_inline(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		RuntimeObject* L_93 = ((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___syncedPoolObject;
		V_21 = L_93;
		V_22 = (bool)0;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_024d:
			{
				{
					bool L_94 = V_22;
					if (!L_94)
					{
						goto IL_0259;
					}
				}
				{
					RuntimeObject* L_95 = V_21;
					Monitor_Exit_m05B2CF037E2214B3208198C282490A2A475653FA(L_95, NULL);
				}

IL_0259:
				{
					return;
				}
			}
		});
		try
		{
			RuntimeObject* L_96 = V_21;
			Monitor_Enter_m3CDB589DA1300B513D55FDCFB52B63E879794149(L_96, (&V_22), NULL);
			il2cpp_codegen_runtime_class_init_inline(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
			ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A* L_97 = ((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_Builders;
			NullCheck(L_97);
			StringBuilder_t* L_98;
			L_98 = ObjectPool_1_Get_m9A0C64A471F1134751E86CFABE36283B06E29F8D(L_97, ObjectPool_1_Get_m9A0C64A471F1134751E86CFABE36283B06E29F8D_RuntimeMethod_var);
			V_3 = L_98;
			goto IL_025a;
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_025a:
	{
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_02d3:
			{
				{
					il2cpp_codegen_runtime_class_init_inline(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
					RuntimeObject* L_99 = ((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___syncedPoolObject;
					V_27 = L_99;
					V_28 = (bool)0;
				}
				{
					auto __finallyBlock = il2cpp::utils::Finally([&]
					{

FINALLY_02f8:
						{
							{
								bool L_100 = V_28;
								if (!L_100)
								{
									goto IL_0304;
								}
							}
							{
								RuntimeObject* L_101 = V_27;
								Monitor_Exit_m05B2CF037E2214B3208198C282490A2A475653FA(L_101, NULL);
							}

IL_0304:
							{
								return;
							}
						}
					});
					try
					{
						RuntimeObject* L_102 = V_27;
						Monitor_Enter_m3CDB589DA1300B513D55FDCFB52B63E879794149(L_102, (&V_28), NULL);
						il2cpp_codegen_runtime_class_init_inline(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
						ObjectPool_1_tA6230762362A6BA6FEB38B9912D0463FB059A05A* L_103 = ((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_Builders;
						StringBuilder_t* L_104 = V_3;
						NullCheck(L_103);
						ObjectPool_1_Release_mD38ED7186BD4E9178B260810EEA654454B3BB478(L_103, L_104, ObjectPool_1_Release_mD38ED7186BD4E9178B260810EEA654454B3BB478_RuntimeMethod_var);
						goto IL_0305;
					}
					catch(Il2CppExceptionWrapper& e)
					{
						__finallyBlock.StoreException(e.ex);
					}
				}

IL_0305:
				{
					return;
				}
			}
		});
		try
		{
			{
				V_23 = 0;
				goto IL_029a_1;
			}

IL_0261_1:
			{
				int32_t L_105 = V_23;
				V_24 = (bool)((!(((uint32_t)L_105) <= ((uint32_t)0)))? 1 : 0);
				bool L_106 = V_24;
				if (!L_106)
				{
					goto IL_0279_1;
				}
			}
			{
				StringBuilder_t* L_107 = V_3;
				NullCheck(L_107);
				StringBuilder_t* L_108;
				L_108 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_107, _stringLiteral758733BDBED83CBFF4F635AC26CA92AAE477F75D, NULL);
			}

IL_0279_1:
			{
				StringBuilder_t* L_109 = V_3;
				RuntimeObject* L_110 = ___1_args;
				int32_t* L_111 = ___2_argIndex;
				int32_t L_112 = *((int32_t*)L_111);
				NullCheck(L_110);
				Type_t* L_113;
				L_113 = InterfaceFuncInvoker1< Type_t*, int32_t >::Invoke(0, IReadOnlyList_1_tFAB749F93AC9273AC78377822795638920495BA2_il2cpp_TypeInfo_var, L_110, L_112);
				il2cpp_codegen_runtime_class_init_inline(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
				String_t* L_114;
				L_114 = TypeUtility_GetTypeDisplayName_m45C565A0737008926DD1D4CA6B453A475C8E77FD(L_113, NULL);
				NullCheck(L_109);
				StringBuilder_t* L_115;
				L_115 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_109, L_114, NULL);
				int32_t L_116 = V_23;
				V_23 = ((int32_t)il2cpp_codegen_add(L_116, 1));
				int32_t* L_117 = ___2_argIndex;
				int32_t* L_118 = ___2_argIndex;
				int32_t L_119 = *((int32_t*)L_118);
				*((int32_t*)L_117) = (int32_t)((int32_t)il2cpp_codegen_add(L_119, 1));
			}

IL_029a_1:
			{
				int32_t L_120 = V_23;
				int32_t L_121 = V_2;
				if ((((int32_t)L_120) >= ((int32_t)L_121)))
				{
					goto IL_02ab_1;
				}
			}
			{
				int32_t* L_122 = ___2_argIndex;
				int32_t L_123 = *((int32_t*)L_122);
				RuntimeObject* L_124 = ___1_args;
				NullCheck(L_124);
				int32_t L_125;
				L_125 = InterfaceFuncInvoker0< int32_t >::Invoke(0, IReadOnlyCollection_1_tB0C1D5CC73DC8C9F615E80A005D312E046F4D762_il2cpp_TypeInfo_var, L_124);
				G_B45_0 = ((((int32_t)L_123) < ((int32_t)L_125))? 1 : 0);
				goto IL_02ac_1;
			}

IL_02ab_1:
			{
				G_B45_0 = 0;
			}

IL_02ac_1:
			{
				V_25 = (bool)G_B45_0;
				bool L_126 = V_25;
				if (L_126)
				{
					goto IL_0261_1;
				}
			}
			{
				StringBuilder_t* L_127 = V_3;
				NullCheck(L_127);
				int32_t L_128;
				L_128 = StringBuilder_get_Length_mDEA041E7357C68CC3B5885276BB403676DAAE0D8(L_127, NULL);
				V_26 = (bool)((((int32_t)L_128) > ((int32_t)0))? 1 : 0);
				bool L_129 = V_26;
				if (!L_129)
				{
					goto IL_02d0_1;
				}
			}
			{
				String_t* L_130 = V_0;
				StringBuilder_t* L_131 = V_3;
				String_t* L_132;
				L_132 = String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987(_stringLiteralBD2F86A6F80C957D36F5792A1101E475BB128D86, L_130, L_131, NULL);
				V_0 = L_132;
			}

IL_02d0_1:
			{
				goto IL_0307;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0307:
	{
		String_t* L_133 = V_0;
		V_5 = L_133;
		goto IL_030c;
	}

IL_030c:
	{
		String_t* L_134 = V_5;
		return L_134;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* TypeUtility_GetRootType_mA1A5FDE24BC31C9CB9E4827160A1379BDEAEAB0B (Type_t* ___0_type, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RuntimeObject_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	Type_t* V_0 = NULL;
	bool V_1 = false;
	Type_t* V_2 = NULL;
	bool V_3 = false;
	Type_t* G_B5_0 = NULL;
	{
		Type_t* L_0 = ___0_type;
		NullCheck(L_0);
		bool L_1;
		L_1 = Type_get_IsInterface_m484A7D9321E72758EABE7F36AE266EB0905957EC(L_0, NULL);
		V_1 = L_1;
		bool L_2 = V_1;
		if (!L_2)
		{
			goto IL_000f;
		}
	}
	{
		V_2 = (Type_t*)NULL;
		goto IL_004e;
	}

IL_000f:
	{
		Type_t* L_3 = ___0_type;
		NullCheck(L_3);
		bool L_4;
		L_4 = Type_get_IsValueType_m59AE2E0439DC06347B8D6B38548F3CBA54D38318(L_3, NULL);
		if (L_4)
		{
			goto IL_0023;
		}
	}
	{
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_5 = { reinterpret_cast<intptr_t> (RuntimeObject_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_6;
		L_6 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_5, NULL);
		G_B5_0 = L_6;
		goto IL_002d;
	}

IL_0023:
	{
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_7 = { reinterpret_cast<intptr_t> (ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_8;
		L_8 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_7, NULL);
		G_B5_0 = L_8;
	}

IL_002d:
	{
		V_0 = G_B5_0;
		goto IL_003a;
	}

IL_0030:
	{
		Type_t* L_9 = ___0_type;
		NullCheck(L_9);
		Type_t* L_10;
		L_10 = VirtualFuncInvoker0< Type_t* >::Invoke(113, L_9);
		___0_type = L_10;
	}

IL_003a:
	{
		Type_t* L_11 = V_0;
		Type_t* L_12 = ___0_type;
		NullCheck(L_12);
		Type_t* L_13;
		L_13 = VirtualFuncInvoker0< Type_t* >::Invoke(113, L_12);
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		bool L_14;
		L_14 = Type_op_Inequality_m83209C7BB3C05DFBEA3B6199B0BEFE8037301172(L_11, L_13, NULL);
		V_3 = L_14;
		bool L_15 = V_3;
		if (L_15)
		{
			goto IL_0030;
		}
	}
	{
		Type_t* L_16 = ___0_type;
		V_2 = L_16;
		goto IL_004e;
	}

IL_004e:
	{
		Type_t* L_17 = V_2;
		return L_17;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* TypeUtility_CreateTypeConstructor_mCD83647C237EE7D7DC578EE0FFF12124F4670D74 (Type_t* ___0_type, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConcurrentDictionary_2_set_Item_m050171C2656C2982971EECEA9026CADF5AEF72B1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IPropertyBag_t602D45F19F9BFA3E370929AC5023258AAF62E8C2_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ITypeConstructor_tAEA6BC4B87F0F3739E4457F21908EF55F666EB08_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeConstructorVisitor_tFC61250F68BF43FE889F5B1002CBFF90648E9C7F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RuntimeObject* V_0 = NULL;
	bool V_1 = false;
	TypeConstructorVisitor_tFC61250F68BF43FE889F5B1002CBFF90648E9C7F* V_2 = NULL;
	RuntimeObject* V_3 = NULL;
	bool V_4 = false;
	NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE* V_5 = NULL;
	{
		Type_t* L_0 = ___0_type;
		il2cpp_codegen_runtime_class_init_inline(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var);
		RuntimeObject* L_1;
		L_1 = PropertyBagStore_GetPropertyBag_m90F2EBB48D60993594856358C572964C8011143E(L_0, NULL);
		V_0 = L_1;
		RuntimeObject* L_2 = V_0;
		V_1 = (bool)((!(((RuntimeObject*)(RuntimeObject*)L_2) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		bool L_3 = V_1;
		if (!L_3)
		{
			goto IL_0028;
		}
	}
	{
		TypeConstructorVisitor_tFC61250F68BF43FE889F5B1002CBFF90648E9C7F* L_4 = (TypeConstructorVisitor_tFC61250F68BF43FE889F5B1002CBFF90648E9C7F*)il2cpp_codegen_object_new(TypeConstructorVisitor_tFC61250F68BF43FE889F5B1002CBFF90648E9C7F_il2cpp_TypeInfo_var);
		TypeConstructorVisitor__ctor_m496AFA7B1A835D40C4BEDBD5A7D1804CF196DE23(L_4, NULL);
		V_2 = L_4;
		RuntimeObject* L_5 = V_0;
		TypeConstructorVisitor_tFC61250F68BF43FE889F5B1002CBFF90648E9C7F* L_6 = V_2;
		NullCheck(L_5);
		InterfaceActionInvoker1< RuntimeObject* >::Invoke(0, IPropertyBag_t602D45F19F9BFA3E370929AC5023258AAF62E8C2_il2cpp_TypeInfo_var, L_5, L_6);
		TypeConstructorVisitor_tFC61250F68BF43FE889F5B1002CBFF90648E9C7F* L_7 = V_2;
		NullCheck(L_7);
		RuntimeObject* L_8 = L_7->___TypeConstructor;
		V_3 = L_8;
		goto IL_0072;
	}

IL_0028:
	{
		Type_t* L_9 = ___0_type;
		NullCheck(L_9);
		bool L_10;
		L_10 = VirtualFuncInvoker0< bool >::Invoke(20, L_9);
		V_4 = L_10;
		bool L_11 = V_4;
		if (!L_11)
		{
			goto IL_004f;
		}
	}
	{
		NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE* L_12 = (NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE*)il2cpp_codegen_object_new(NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE_il2cpp_TypeInfo_var);
		NonConstructable__ctor_m6B6DB41FBB71A153AFAC3B10CF35D2503D8CA982(L_12, NULL);
		V_5 = L_12;
		il2cpp_codegen_runtime_class_init_inline(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91* L_13 = ((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_TypeConstructors;
		Type_t* L_14 = ___0_type;
		NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE* L_15 = V_5;
		NullCheck(L_13);
		ConcurrentDictionary_2_set_Item_m050171C2656C2982971EECEA9026CADF5AEF72B1(L_13, L_14, L_15, ConcurrentDictionary_2_set_Item_m050171C2656C2982971EECEA9026CADF5AEF72B1_RuntimeMethod_var);
		NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE* L_16 = V_5;
		V_3 = L_16;
		goto IL_0072;
	}

IL_004f:
	{
		il2cpp_codegen_runtime_class_init_inline(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		MethodInfo_t* L_17 = ((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_CreateTypeConstructor;
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_18 = (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)SZArrayNew(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var, (uint32_t)1);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_19 = L_18;
		Type_t* L_20 = ___0_type;
		NullCheck(L_19);
		ArrayElementTypeCheck (L_19, L_20);
		(L_19)->SetAt(static_cast<il2cpp_array_size_t>(0), (Type_t*)L_20);
		NullCheck(L_17);
		MethodInfo_t* L_21;
		L_21 = VirtualFuncInvoker1< MethodInfo_t*, TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* >::Invoke(42, L_17, L_19);
		NullCheck(L_21);
		RuntimeObject* L_22;
		L_22 = MethodBase_Invoke_mEEF3218648F111A8C338001A7804091A0747C826(L_21, NULL, (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)NULL, NULL);
		V_3 = ((RuntimeObject*)IsInst((RuntimeObject*)L_22, ITypeConstructor_tAEA6BC4B87F0F3739E4457F21908EF55F666EB08_il2cpp_TypeInfo_var));
		goto IL_0072;
	}

IL_0072:
	{
		RuntimeObject* L_23 = V_3;
		return L_23;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* TypeUtility_GetTypeConstructor_m46157C15601C266EE0DB2324E834E1CE4CBFE1DC (Type_t* ___0_type, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConcurrentDictionary_2_TryGetValue_mBD42E58E3B97D8884F8ABD2ED7FB00AE05C3D7B3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RuntimeObject* V_0 = NULL;
	RuntimeObject* V_1 = NULL;
	RuntimeObject* G_B3_0 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		ConcurrentDictionary_2_tE5A8639D8AE0AF65ED6A8B7C1089E9F12ADD7B91* L_0 = ((TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_StaticFields*)il2cpp_codegen_static_fields_for(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var))->___s_TypeConstructors;
		Type_t* L_1 = ___0_type;
		NullCheck(L_0);
		bool L_2;
		L_2 = ConcurrentDictionary_2_TryGetValue_mBD42E58E3B97D8884F8ABD2ED7FB00AE05C3D7B3(L_0, L_1, (&V_0), ConcurrentDictionary_2_TryGetValue_mBD42E58E3B97D8884F8ABD2ED7FB00AE05C3D7B3_RuntimeMethod_var);
		if (L_2)
		{
			goto IL_0018;
		}
	}
	{
		Type_t* L_3 = ___0_type;
		il2cpp_codegen_runtime_class_init_inline(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		RuntimeObject* L_4;
		L_4 = TypeUtility_CreateTypeConstructor_mCD83647C237EE7D7DC578EE0FFF12124F4670D74(L_3, NULL);
		G_B3_0 = L_4;
		goto IL_0019;
	}

IL_0018:
	{
		RuntimeObject* L_5 = V_0;
		G_B3_0 = L_5;
	}

IL_0019:
	{
		V_1 = G_B3_0;
		goto IL_001c;
	}

IL_001c:
	{
		RuntimeObject* L_6 = V_1;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool TypeUtility_CanBeInstantiated_m02649AACC5170FAB0419B5FA22FF5DE524E23828 (Type_t* ___0_type, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ITypeConstructor_tAEA6BC4B87F0F3739E4457F21908EF55F666EB08_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Type_t* L_0 = ___0_type;
		il2cpp_codegen_runtime_class_init_inline(TypeUtility_t62F146E51B66BB18FA0E1EFA84A564FF2B8AAD03_il2cpp_TypeInfo_var);
		RuntimeObject* L_1;
		L_1 = TypeUtility_GetTypeConstructor_m46157C15601C266EE0DB2324E834E1CE4CBFE1DC(L_0, NULL);
		NullCheck(L_1);
		bool L_2;
		L_2 = InterfaceFuncInvoker0< bool >::Invoke(0, ITypeConstructor_tAEA6BC4B87F0F3739E4457F21908EF55F666EB08_il2cpp_TypeInfo_var, L_1);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TypeUtility_CheckIsAssignableFrom_m284900A7E86ACD68227E5E3DCC9430EEC7E1E03E (Type_t* ___0_type, Type_t* ___1_derivedType, const RuntimeMethod* method) 
{
	bool V_0 = false;
	{
		Type_t* L_0 = ___0_type;
		Type_t* L_1 = ___1_derivedType;
		NullCheck(L_0);
		bool L_2;
		L_2 = VirtualFuncInvoker1< bool, Type_t* >::Invoke(22, L_0, L_1);
		V_0 = (bool)((((int32_t)L_2) == ((int32_t)0))? 1 : 0);
		bool L_3 = V_0;
		if (!L_3)
		{
			goto IL_004a;
		}
	}
	{
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_4 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var)), (uint32_t)5);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_5 = L_4;
		NullCheck(L_5);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral704C5FCE5B7025BF4463EAB8CEF61810FB875636)));
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_6 = L_5;
		Type_t* L_7 = ___1_derivedType;
		NullCheck(L_7);
		String_t* L_8;
		L_8 = VirtualFuncInvoker0< String_t* >::Invoke(8, L_7);
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)L_8);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_9 = L_6;
		NullCheck(L_9);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(2), (String_t*)((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral30EAC41CDC5339E618C57498BD0319713142D96E)));
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_10 = L_9;
		Type_t* L_11 = ___0_type;
		NullCheck(L_11);
		String_t* L_12;
		L_12 = VirtualFuncInvoker0< String_t* >::Invoke(8, L_11);
		NullCheck(L_10);
		(L_10)->SetAt(static_cast<il2cpp_array_size_t>(3), (String_t*)L_12);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_13 = L_10;
		NullCheck(L_13);
		(L_13)->SetAt(static_cast<il2cpp_array_size_t>(4), (String_t*)((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral40915EBE202C5DCE0A0658C8080663943D342AF3)));
		String_t* L_14;
		L_14 = String_Concat_m647EBF831F54B6DF7D5AFA5FD012CF4EE7571B6A(L_13, NULL);
		ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* L_15 = (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465(L_15, L_14, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_15, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&TypeUtility_CheckIsAssignableFrom_m284900A7E86ACD68227E5E3DCC9430EEC7E1E03E_RuntimeMethod_var)));
	}

IL_004a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TypeUtility_CheckCanBeInstantiated_mB2C921D41DC94D83758FC5CFDFEE26911F62B14F (RuntimeObject* ___0_constructor, Type_t* ___1_type, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ITypeConstructor_tAEA6BC4B87F0F3739E4457F21908EF55F666EB08_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	{
		RuntimeObject* L_0 = ___0_constructor;
		NullCheck(L_0);
		bool L_1;
		L_1 = InterfaceFuncInvoker0< bool >::Invoke(0, ITypeConstructor_tAEA6BC4B87F0F3739E4457F21908EF55F666EB08_il2cpp_TypeInfo_var, L_0);
		V_0 = (bool)((((int32_t)L_1) == ((int32_t)0))? 1 : 0);
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_002a;
		}
	}
	{
		Type_t* L_3 = ___1_type;
		NullCheck(L_3);
		String_t* L_4;
		L_4 = VirtualFuncInvoker0< String_t* >::Invoke(8, L_3);
		String_t* L_5;
		L_5 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral3477F5F15DC74EADC79ED337F3988716EE1670E9)), L_4, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF1A902BAD5B9128E503E43473944346B65454351)), NULL);
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_6 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_6, L_5, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_6, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&TypeUtility_CheckCanBeInstantiated_mB2C921D41DC94D83758FC5CFDFEE26911F62B14F_RuntimeMethod_var)));
	}

IL_002a:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool NonConstructable_Unity_Properties_TypeUtility_ITypeConstructor_get_CanBeInstantiated_m12234E46124842FE494AB4C0B55045C1954F9A89 (NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE* __this, const RuntimeMethod* method) 
{
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* NonConstructable_Instantiate_m9FE0A208C273DC31D79B29A334F4DA2BF63A8AF6 (NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE* __this, const RuntimeMethod* method) 
{
	{
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_0 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralCD5BCEE5AAB18574F8AD6E7DDC5232CE0BE6D6E3)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NonConstructable_Instantiate_m9FE0A208C273DC31D79B29A334F4DA2BF63A8AF6_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NonConstructable__ctor_m6B6DB41FBB71A153AFAC3B10CF35D2503D8CA982 (NonConstructable_t395D64520723F74FB164AF8F15194433EBE077DE* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TypeConstructorVisitor__ctor_m496AFA7B1A835D40C4BEDBD5A7D1804CF196DE23 (TypeConstructorVisitor_tFC61250F68BF43FE889F5B1002CBFF90648E9C7F* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__cctor_m1E3F4DC530F8CB5240E8B5FFD5E5477BB7300FC2 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203* L_0 = (U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203*)il2cpp_codegen_object_new(U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_il2cpp_TypeInfo_var);
		U3CU3Ec__ctor_mD06BD054A2682CAA505B6F75C45B8D4736FBFA39(L_0, NULL);
		((U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_il2cpp_TypeInfo_var))->___U3CU3E9 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203_il2cpp_TypeInfo_var))->___U3CU3E9), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_mD06BD054A2682CAA505B6F75C45B8D4736FBFA39 (U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringBuilder_t* U3CU3Ec_U3C_cctorU3Eb__11_0_m59D3FD88A1841D56C6A2D231E993AA6D2E6465E1 (U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringBuilder_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		StringBuilder_t* L_0 = (StringBuilder_t*)il2cpp_codegen_object_new(StringBuilder_t_il2cpp_TypeInfo_var);
		StringBuilder__ctor_m1D99713357DE05DAFA296633639DB55F8C30587D(L_0, NULL);
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec_U3C_cctorU3Eb__11_1_mCF36865D4A819F0C0DDA987F30077434E56BC826 (U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203* __this, StringBuilder_t* ___0_sb, const RuntimeMethod* method) 
{
	{
		StringBuilder_t* L_0 = ___0_sb;
		NullCheck(L_0);
		StringBuilder_t* L_1;
		L_1 = StringBuilder_Clear_m2D76F6533574F40A4E3E2DC4E730277CBD0AF8F6(L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* U3CU3Ec_U3C_cctorU3Eb__11_2_mBA773F3E344C145FE939B19C5EE690F9FEC60716 (U3CU3Ec_tDCB9F8AC3255E399638FABB1600CE5E855145203* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&String_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ((String_t_StaticFields*)il2cpp_codegen_static_fields_for(String_t_il2cpp_TypeInfo_var))->___Empty;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DefaultPropertyBagInitializer_Initialize_m8BA3D4950B9C07EF2A0978A88D908FCF297BD080 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BoundsIntPropertyBag_t36236827A488493B27B94BF7EB678949732F2052_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BoundsPropertyBag_tA22B9256F487C40279FFFA58AD0E8D6CF29BA83F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ColorPropertyBag_tFA63F917A9DAA7F376DD37FF114B3D548E00BCC6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBag_Register_TisBoundsInt_t4E757DE5EFF9FCB42000F173360DDC63B5585485_m2606075F145B891D9C481304597F0464E5F8A8AA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBag_Register_TisBounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_m8A39CB42251835524F1068E49B6CF4BE5AD65032_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBag_Register_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1904BF66773B1294832B629C1FD5C32920AEEFDD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBag_Register_TisRectInt_t1744D10E1063135DA9D574F95205B98DAC600CB8_mF48E3B0570AD4D40331B885BCECF6A7A199327E4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBag_Register_TisRect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_m0EE819FDFD53BD79B74A1D270244805D2BD796A8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBag_Register_TisVector2Int_t69B2886EBAB732D9B880565E18E7568F3DE0CE6A_mF3979F0F1B3DEBC415368280E538AE95E7277EE3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBag_Register_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m9D4158376507E88B5DB59B60B56E2EBAC5567282_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBag_Register_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m74735F036C3540DFAF301D182A48C6EEB836F4E1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBag_Register_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m920D0EDA2A222D333EC898C0E4405F9D0A9000EF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBag_Register_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m1952774FD6803DED13AB832F702970F5F588917C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBag_Register_TisVersion_tE426DB5655D0F22920AE16A2AA9AB7781B8255A7_m976EBCE3744148CDF5AF1174347D890DF7B63A6D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectIntPropertyBag_t523552E9F6C38079E84B8F2ADE24828B4BC9DE73_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectPropertyBag_tA405FA98439EEF5D80DA2FC601583B49CCB638EC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SystemVersionPropertyBag_t2BC09BA510A4DE999004F35E9930AA414DF46E8A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2IntPropertyBag_t2C43760B9A4208AD87AEF2615AEF035A489502AF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2PropertyBag_t566CAC975576D5F03B957E62DBA553ECE659AB49_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3IntPropertyBag_t534CCCF8AD649649C930E5F3DDA3C0A734F932C7_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3PropertyBag_t4FDB90B998E0F82C33B88823F8108862054CED41_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector4PropertyBag_t648481977877F79BDB6095EE5B491D8757A9DB5C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ColorPropertyBag_tFA63F917A9DAA7F376DD37FF114B3D548E00BCC6* L_0 = (ColorPropertyBag_tFA63F917A9DAA7F376DD37FF114B3D548E00BCC6*)il2cpp_codegen_object_new(ColorPropertyBag_tFA63F917A9DAA7F376DD37FF114B3D548E00BCC6_il2cpp_TypeInfo_var);
		ColorPropertyBag__ctor_m695D2E7503EA97F7BB7252A882546C1C8B11D1BC(L_0, NULL);
		PropertyBag_Register_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1904BF66773B1294832B629C1FD5C32920AEEFDD(L_0, PropertyBag_Register_TisColor_tD001788D726C3A7F1379BEED0260B9591F440C1F_m1904BF66773B1294832B629C1FD5C32920AEEFDD_RuntimeMethod_var);
		Vector2PropertyBag_t566CAC975576D5F03B957E62DBA553ECE659AB49* L_1 = (Vector2PropertyBag_t566CAC975576D5F03B957E62DBA553ECE659AB49*)il2cpp_codegen_object_new(Vector2PropertyBag_t566CAC975576D5F03B957E62DBA553ECE659AB49_il2cpp_TypeInfo_var);
		Vector2PropertyBag__ctor_mF30206E01ECFEDF6726878BEDA3DA53A2D24D00F(L_1, NULL);
		PropertyBag_Register_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m9D4158376507E88B5DB59B60B56E2EBAC5567282(L_1, PropertyBag_Register_TisVector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_m9D4158376507E88B5DB59B60B56E2EBAC5567282_RuntimeMethod_var);
		Vector3PropertyBag_t4FDB90B998E0F82C33B88823F8108862054CED41* L_2 = (Vector3PropertyBag_t4FDB90B998E0F82C33B88823F8108862054CED41*)il2cpp_codegen_object_new(Vector3PropertyBag_t4FDB90B998E0F82C33B88823F8108862054CED41_il2cpp_TypeInfo_var);
		Vector3PropertyBag__ctor_mC00E39D196C789D68E6F910494E433E729E0E4B6(L_2, NULL);
		PropertyBag_Register_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m920D0EDA2A222D333EC898C0E4405F9D0A9000EF(L_2, PropertyBag_Register_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m920D0EDA2A222D333EC898C0E4405F9D0A9000EF_RuntimeMethod_var);
		Vector4PropertyBag_t648481977877F79BDB6095EE5B491D8757A9DB5C* L_3 = (Vector4PropertyBag_t648481977877F79BDB6095EE5B491D8757A9DB5C*)il2cpp_codegen_object_new(Vector4PropertyBag_t648481977877F79BDB6095EE5B491D8757A9DB5C_il2cpp_TypeInfo_var);
		Vector4PropertyBag__ctor_m6B7DFA6F651C0DC6522B1AB7E179752BA836AE10(L_3, NULL);
		PropertyBag_Register_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m1952774FD6803DED13AB832F702970F5F588917C(L_3, PropertyBag_Register_TisVector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_m1952774FD6803DED13AB832F702970F5F588917C_RuntimeMethod_var);
		Vector2IntPropertyBag_t2C43760B9A4208AD87AEF2615AEF035A489502AF* L_4 = (Vector2IntPropertyBag_t2C43760B9A4208AD87AEF2615AEF035A489502AF*)il2cpp_codegen_object_new(Vector2IntPropertyBag_t2C43760B9A4208AD87AEF2615AEF035A489502AF_il2cpp_TypeInfo_var);
		Vector2IntPropertyBag__ctor_mA5E766EC29B1E37CAA759CBB09997D25FAFB0631(L_4, NULL);
		PropertyBag_Register_TisVector2Int_t69B2886EBAB732D9B880565E18E7568F3DE0CE6A_mF3979F0F1B3DEBC415368280E538AE95E7277EE3(L_4, PropertyBag_Register_TisVector2Int_t69B2886EBAB732D9B880565E18E7568F3DE0CE6A_mF3979F0F1B3DEBC415368280E538AE95E7277EE3_RuntimeMethod_var);
		Vector3IntPropertyBag_t534CCCF8AD649649C930E5F3DDA3C0A734F932C7* L_5 = (Vector3IntPropertyBag_t534CCCF8AD649649C930E5F3DDA3C0A734F932C7*)il2cpp_codegen_object_new(Vector3IntPropertyBag_t534CCCF8AD649649C930E5F3DDA3C0A734F932C7_il2cpp_TypeInfo_var);
		Vector3IntPropertyBag__ctor_mECB1D0CC81ACF263C6C0D126BB285A11CBACB2BC(L_5, NULL);
		PropertyBag_Register_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m74735F036C3540DFAF301D182A48C6EEB836F4E1(L_5, PropertyBag_Register_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m74735F036C3540DFAF301D182A48C6EEB836F4E1_RuntimeMethod_var);
		RectPropertyBag_tA405FA98439EEF5D80DA2FC601583B49CCB638EC* L_6 = (RectPropertyBag_tA405FA98439EEF5D80DA2FC601583B49CCB638EC*)il2cpp_codegen_object_new(RectPropertyBag_tA405FA98439EEF5D80DA2FC601583B49CCB638EC_il2cpp_TypeInfo_var);
		RectPropertyBag__ctor_m4C5D1C260902338AC90DB837C39FE28695ED0110(L_6, NULL);
		PropertyBag_Register_TisRect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_m0EE819FDFD53BD79B74A1D270244805D2BD796A8(L_6, PropertyBag_Register_TisRect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D_m0EE819FDFD53BD79B74A1D270244805D2BD796A8_RuntimeMethod_var);
		RectIntPropertyBag_t523552E9F6C38079E84B8F2ADE24828B4BC9DE73* L_7 = (RectIntPropertyBag_t523552E9F6C38079E84B8F2ADE24828B4BC9DE73*)il2cpp_codegen_object_new(RectIntPropertyBag_t523552E9F6C38079E84B8F2ADE24828B4BC9DE73_il2cpp_TypeInfo_var);
		RectIntPropertyBag__ctor_mFFAC4C410B920C26B29CB6A309FB7EEF90E75A4F(L_7, NULL);
		PropertyBag_Register_TisRectInt_t1744D10E1063135DA9D574F95205B98DAC600CB8_mF48E3B0570AD4D40331B885BCECF6A7A199327E4(L_7, PropertyBag_Register_TisRectInt_t1744D10E1063135DA9D574F95205B98DAC600CB8_mF48E3B0570AD4D40331B885BCECF6A7A199327E4_RuntimeMethod_var);
		BoundsPropertyBag_tA22B9256F487C40279FFFA58AD0E8D6CF29BA83F* L_8 = (BoundsPropertyBag_tA22B9256F487C40279FFFA58AD0E8D6CF29BA83F*)il2cpp_codegen_object_new(BoundsPropertyBag_tA22B9256F487C40279FFFA58AD0E8D6CF29BA83F_il2cpp_TypeInfo_var);
		BoundsPropertyBag__ctor_mB3B92E349452661D037A9AB2A8B6581ECFEBB111(L_8, NULL);
		PropertyBag_Register_TisBounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_m8A39CB42251835524F1068E49B6CF4BE5AD65032(L_8, PropertyBag_Register_TisBounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3_m8A39CB42251835524F1068E49B6CF4BE5AD65032_RuntimeMethod_var);
		BoundsIntPropertyBag_t36236827A488493B27B94BF7EB678949732F2052* L_9 = (BoundsIntPropertyBag_t36236827A488493B27B94BF7EB678949732F2052*)il2cpp_codegen_object_new(BoundsIntPropertyBag_t36236827A488493B27B94BF7EB678949732F2052_il2cpp_TypeInfo_var);
		BoundsIntPropertyBag__ctor_mE2B2BB212558FE1C221E01D8FCEC4C41B02C6BF5(L_9, NULL);
		PropertyBag_Register_TisBoundsInt_t4E757DE5EFF9FCB42000F173360DDC63B5585485_m2606075F145B891D9C481304597F0464E5F8A8AA(L_9, PropertyBag_Register_TisBoundsInt_t4E757DE5EFF9FCB42000F173360DDC63B5585485_m2606075F145B891D9C481304597F0464E5F8A8AA_RuntimeMethod_var);
		SystemVersionPropertyBag_t2BC09BA510A4DE999004F35E9930AA414DF46E8A* L_10 = (SystemVersionPropertyBag_t2BC09BA510A4DE999004F35E9930AA414DF46E8A*)il2cpp_codegen_object_new(SystemVersionPropertyBag_t2BC09BA510A4DE999004F35E9930AA414DF46E8A_il2cpp_TypeInfo_var);
		SystemVersionPropertyBag__ctor_mC0D23BC1F975382571DFA70EC5B52B24083BD4AF(L_10, NULL);
		PropertyBag_Register_TisVersion_tE426DB5655D0F22920AE16A2AA9AB7781B8255A7_m976EBCE3744148CDF5AF1174347D890DF7B63A6D(L_10, PropertyBag_Register_TisVersion_tE426DB5655D0F22920AE16A2AA9AB7781B8255A7_m976EBCE3744148CDF5AF1174347D890DF7B63A6D_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ColorPropertyBag__ctor_m695D2E7503EA97F7BB7252A882546C1C8B11D1BC (ColorPropertyBag_tFA63F917A9DAA7F376DD37FF114B3D548E00BCC6* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AProperty_t71D13C040377A5A0BDD9E1614DC61B65689411AC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BProperty_t8C06F5F0A1928368B508116004897B283E10E5C4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mA9C2ACFCDCD7269581FBF0D648DE6495840A79F4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1__ctor_m664143BCBE736CBB716E37C4DC7CFEC21BB17D92_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_t4B3135B801859CF3F6877AEC19D1DA289FFC301D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GProperty_t59EEA3B6DFE305EF122097DBA3B950684B224ACD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RProperty_t8729556DDC5FF3CC31717171450B4EA749328E5D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ContainerPropertyBag_1_t4B3135B801859CF3F6877AEC19D1DA289FFC301D_il2cpp_TypeInfo_var);
		ContainerPropertyBag_1__ctor_m664143BCBE736CBB716E37C4DC7CFEC21BB17D92(__this, ContainerPropertyBag_1__ctor_m664143BCBE736CBB716E37C4DC7CFEC21BB17D92_RuntimeMethod_var);
		RProperty_t8729556DDC5FF3CC31717171450B4EA749328E5D* L_0 = (RProperty_t8729556DDC5FF3CC31717171450B4EA749328E5D*)il2cpp_codegen_object_new(RProperty_t8729556DDC5FF3CC31717171450B4EA749328E5D_il2cpp_TypeInfo_var);
		RProperty__ctor_m5B48CD3CF7C1DD802022B9AF99DAEEAB2A25B430(L_0, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mA9C2ACFCDCD7269581FBF0D648DE6495840A79F4(__this, L_0, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mA9C2ACFCDCD7269581FBF0D648DE6495840A79F4_RuntimeMethod_var);
		GProperty_t59EEA3B6DFE305EF122097DBA3B950684B224ACD* L_1 = (GProperty_t59EEA3B6DFE305EF122097DBA3B950684B224ACD*)il2cpp_codegen_object_new(GProperty_t59EEA3B6DFE305EF122097DBA3B950684B224ACD_il2cpp_TypeInfo_var);
		GProperty__ctor_m2E28D044AC6B8D72EA0595E1290599DC5F91EC35(L_1, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mA9C2ACFCDCD7269581FBF0D648DE6495840A79F4(__this, L_1, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mA9C2ACFCDCD7269581FBF0D648DE6495840A79F4_RuntimeMethod_var);
		BProperty_t8C06F5F0A1928368B508116004897B283E10E5C4* L_2 = (BProperty_t8C06F5F0A1928368B508116004897B283E10E5C4*)il2cpp_codegen_object_new(BProperty_t8C06F5F0A1928368B508116004897B283E10E5C4_il2cpp_TypeInfo_var);
		BProperty__ctor_m3FA7E12A9E2B2B2AFFF6FC5C96AE4EB5BF9CCFA8(L_2, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mA9C2ACFCDCD7269581FBF0D648DE6495840A79F4(__this, L_2, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mA9C2ACFCDCD7269581FBF0D648DE6495840A79F4_RuntimeMethod_var);
		AProperty_t71D13C040377A5A0BDD9E1614DC61B65689411AC* L_3 = (AProperty_t71D13C040377A5A0BDD9E1614DC61B65689411AC*)il2cpp_codegen_object_new(AProperty_t71D13C040377A5A0BDD9E1614DC61B65689411AC_il2cpp_TypeInfo_var);
		AProperty__ctor_mAED58387B9563DED80A12595C6A11B5B2BEA6D2A(L_3, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mA9C2ACFCDCD7269581FBF0D648DE6495840A79F4(__this, L_3, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mA9C2ACFCDCD7269581FBF0D648DE6495840A79F4_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* RProperty_get_Name_m768BEB2E8FA1995F41236DBA5C68C41FB75510BA (RProperty_t8729556DDC5FF3CC31717171450B4EA749328E5D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA33A5CAE02B786C2060461DF8C6764B4C05E9423);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteralA33A5CAE02B786C2060461DF8C6764B4C05E9423;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RProperty__ctor_m5B48CD3CF7C1DD802022B9AF99DAEEAB2A25B430 (RProperty_t8729556DDC5FF3CC31717171450B4EA749328E5D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F(__this, Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* GProperty_get_Name_mB1B8C4B15616728B9B97AE53213189FBC4E2420B (GProperty_t59EEA3B6DFE305EF122097DBA3B950684B224ACD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2B9B6C84CC15492CCB290C4B79418FA6D7DD24C1);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral2B9B6C84CC15492CCB290C4B79418FA6D7DD24C1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GProperty__ctor_m2E28D044AC6B8D72EA0595E1290599DC5F91EC35 (GProperty_t59EEA3B6DFE305EF122097DBA3B950684B224ACD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F(__this, Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* BProperty_get_Name_m43E6F7094BC817269A225E688C418557587C23D2 (BProperty_t8C06F5F0A1928368B508116004897B283E10E5C4* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4609D79FE2FAD95C38B6DA64FC671E8594984D4C);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral4609D79FE2FAD95C38B6DA64FC671E8594984D4C;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BProperty__ctor_m3FA7E12A9E2B2B2AFFF6FC5C96AE4EB5BF9CCFA8 (BProperty_t8C06F5F0A1928368B508116004897B283E10E5C4* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F(__this, Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* AProperty_get_Name_m82413DC018A41D152B3574D620AC576392D913A5 (AProperty_t71D13C040377A5A0BDD9E1614DC61B65689411AC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0A04B971B03DA607CE6C455184037B660CA89F78);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral0A04B971B03DA607CE6C455184037B660CA89F78;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AProperty__ctor_mAED58387B9563DED80A12595C6A11B5B2BEA6D2A (AProperty_t71D13C040377A5A0BDD9E1614DC61B65689411AC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F(__this, Property_2__ctor_m9DFCB7BFC31E1148DCB249D7707DF37707F6268F_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector2PropertyBag__ctor_mF30206E01ECFEDF6726878BEDA3DA53A2D24D00F (Vector2PropertyBag_t566CAC975576D5F03B957E62DBA553ECE659AB49* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m6DBD44004CEA7C31A6639E946F82B44E3797E8AE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1__ctor_m48D10232A046CC68EF83CE5D3B5454C195078055_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_tFC736A3382282864C67252047BC56B960AD3C486_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&XProperty_t8C4D7E192D5B86997EE4863E5A6999B9543590E1_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&YProperty_tBA23A08DCCCC01ED187D24048C43EAA5AB648E7D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ContainerPropertyBag_1_tFC736A3382282864C67252047BC56B960AD3C486_il2cpp_TypeInfo_var);
		ContainerPropertyBag_1__ctor_m48D10232A046CC68EF83CE5D3B5454C195078055(__this, ContainerPropertyBag_1__ctor_m48D10232A046CC68EF83CE5D3B5454C195078055_RuntimeMethod_var);
		XProperty_t8C4D7E192D5B86997EE4863E5A6999B9543590E1* L_0 = (XProperty_t8C4D7E192D5B86997EE4863E5A6999B9543590E1*)il2cpp_codegen_object_new(XProperty_t8C4D7E192D5B86997EE4863E5A6999B9543590E1_il2cpp_TypeInfo_var);
		XProperty__ctor_m21350D1E93A69FD0D148521382FD3B438C450692(L_0, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m6DBD44004CEA7C31A6639E946F82B44E3797E8AE(__this, L_0, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m6DBD44004CEA7C31A6639E946F82B44E3797E8AE_RuntimeMethod_var);
		YProperty_tBA23A08DCCCC01ED187D24048C43EAA5AB648E7D* L_1 = (YProperty_tBA23A08DCCCC01ED187D24048C43EAA5AB648E7D*)il2cpp_codegen_object_new(YProperty_tBA23A08DCCCC01ED187D24048C43EAA5AB648E7D_il2cpp_TypeInfo_var);
		YProperty__ctor_mB32B0091E33FBD963435FB9973DB0DD000C3FD5C(L_1, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m6DBD44004CEA7C31A6639E946F82B44E3797E8AE(__this, L_1, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m6DBD44004CEA7C31A6639E946F82B44E3797E8AE_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* XProperty_get_Name_m0C83B1AD112C546FBAAE21B64AA11F723AC30FE5 (XProperty_t8C4D7E192D5B86997EE4863E5A6999B9543590E1* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_m21350D1E93A69FD0D148521382FD3B438C450692 (XProperty_t8C4D7E192D5B86997EE4863E5A6999B9543590E1* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_mE2253FF920B6B8BA104EB3F4C6E8A871A34892E7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_mE2253FF920B6B8BA104EB3F4C6E8A871A34892E7(__this, Property_2__ctor_mE2253FF920B6B8BA104EB3F4C6E8A871A34892E7_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* YProperty_get_Name_m6B355E40C67C04DD438964ED91CCCFEC87863BEC (YProperty_tBA23A08DCCCC01ED187D24048C43EAA5AB648E7D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_mB32B0091E33FBD963435FB9973DB0DD000C3FD5C (YProperty_tBA23A08DCCCC01ED187D24048C43EAA5AB648E7D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_mE2253FF920B6B8BA104EB3F4C6E8A871A34892E7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_mE2253FF920B6B8BA104EB3F4C6E8A871A34892E7(__this, Property_2__ctor_mE2253FF920B6B8BA104EB3F4C6E8A871A34892E7_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector3PropertyBag__ctor_mC00E39D196C789D68E6F910494E433E729E0E4B6 (Vector3PropertyBag_t4FDB90B998E0F82C33B88823F8108862054CED41* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m582D8F897B3FA9A209026D9CB0D2CBCA2091CD7A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1__ctor_mED3672BE63BBCAE82B5E328167D256B540E9C287_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_tA7C51CF32C3F7B0B198B114B368B2E5BA498D33E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&XProperty_t012028338C3B87093E42427325BC449033296278_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&YProperty_tE39DDA65525AD937577DFBB83BD942BDE5D3650F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ZProperty_tCE2945E21C8A6EDBDED46B6106BDBB6852C1FB9B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ContainerPropertyBag_1_tA7C51CF32C3F7B0B198B114B368B2E5BA498D33E_il2cpp_TypeInfo_var);
		ContainerPropertyBag_1__ctor_mED3672BE63BBCAE82B5E328167D256B540E9C287(__this, ContainerPropertyBag_1__ctor_mED3672BE63BBCAE82B5E328167D256B540E9C287_RuntimeMethod_var);
		XProperty_t012028338C3B87093E42427325BC449033296278* L_0 = (XProperty_t012028338C3B87093E42427325BC449033296278*)il2cpp_codegen_object_new(XProperty_t012028338C3B87093E42427325BC449033296278_il2cpp_TypeInfo_var);
		XProperty__ctor_m46FEC277D299B82A75C8C20B0AEEACC479E3AEEB(L_0, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m582D8F897B3FA9A209026D9CB0D2CBCA2091CD7A(__this, L_0, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m582D8F897B3FA9A209026D9CB0D2CBCA2091CD7A_RuntimeMethod_var);
		YProperty_tE39DDA65525AD937577DFBB83BD942BDE5D3650F* L_1 = (YProperty_tE39DDA65525AD937577DFBB83BD942BDE5D3650F*)il2cpp_codegen_object_new(YProperty_tE39DDA65525AD937577DFBB83BD942BDE5D3650F_il2cpp_TypeInfo_var);
		YProperty__ctor_m4269E5BB6FCDFD8F849755DCA434EE9ACF9BE2E5(L_1, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m582D8F897B3FA9A209026D9CB0D2CBCA2091CD7A(__this, L_1, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m582D8F897B3FA9A209026D9CB0D2CBCA2091CD7A_RuntimeMethod_var);
		ZProperty_tCE2945E21C8A6EDBDED46B6106BDBB6852C1FB9B* L_2 = (ZProperty_tCE2945E21C8A6EDBDED46B6106BDBB6852C1FB9B*)il2cpp_codegen_object_new(ZProperty_tCE2945E21C8A6EDBDED46B6106BDBB6852C1FB9B_il2cpp_TypeInfo_var);
		ZProperty__ctor_mE1AC4D86341B82F2792FD53417D5ABE36E37B10E(L_2, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m582D8F897B3FA9A209026D9CB0D2CBCA2091CD7A(__this, L_2, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m582D8F897B3FA9A209026D9CB0D2CBCA2091CD7A_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* XProperty_get_Name_m57E924B79868E3450299B50CF2E86F6EC4D2DB9D (XProperty_t012028338C3B87093E42427325BC449033296278* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_m46FEC277D299B82A75C8C20B0AEEACC479E3AEEB (XProperty_t012028338C3B87093E42427325BC449033296278* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m00887E3260117C76E2592EEB152472A442E5ECFF_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m00887E3260117C76E2592EEB152472A442E5ECFF(__this, Property_2__ctor_m00887E3260117C76E2592EEB152472A442E5ECFF_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* YProperty_get_Name_mEC582AC60E11C6187A86D3EB8E90DA232D654D8D (YProperty_tE39DDA65525AD937577DFBB83BD942BDE5D3650F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_m4269E5BB6FCDFD8F849755DCA434EE9ACF9BE2E5 (YProperty_tE39DDA65525AD937577DFBB83BD942BDE5D3650F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m00887E3260117C76E2592EEB152472A442E5ECFF_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m00887E3260117C76E2592EEB152472A442E5ECFF(__this, Property_2__ctor_m00887E3260117C76E2592EEB152472A442E5ECFF_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ZProperty_get_Name_m2F0978F5D89C0A4269B629FF604403D5C6AB7E18 (ZProperty_tCE2945E21C8A6EDBDED46B6106BDBB6852C1FB9B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9CE1604D659135925CCC4DD1F526AFFE42E689F1);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral9CE1604D659135925CCC4DD1F526AFFE42E689F1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZProperty__ctor_mE1AC4D86341B82F2792FD53417D5ABE36E37B10E (ZProperty_tCE2945E21C8A6EDBDED46B6106BDBB6852C1FB9B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m00887E3260117C76E2592EEB152472A442E5ECFF_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m00887E3260117C76E2592EEB152472A442E5ECFF(__this, Property_2__ctor_m00887E3260117C76E2592EEB152472A442E5ECFF_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector4PropertyBag__ctor_m6B7DFA6F651C0DC6522B1AB7E179752BA836AE10 (Vector4PropertyBag_t648481977877F79BDB6095EE5B491D8757A9DB5C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m321C399F1891C5BDC7012B34EC2670D56995AB6D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1__ctor_mD88378C16AD1F8C2A20F3521D02DA3B6811B1FB5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_t827E6E22E54C7702E357022F57F04833A6FAF09E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WProperty_t8C921D95C0FC58C4F39423D4E530BBEDE6447841_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&XProperty_t7D24F952200020A50C34C5525355F6308A3B9CDC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&YProperty_t140E8C4F8AB9EF9D6F3B7EBFDADFA838271CB395_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ZProperty_t431D10EB8A38306B642314AB1AD505F3BB355322_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ContainerPropertyBag_1_t827E6E22E54C7702E357022F57F04833A6FAF09E_il2cpp_TypeInfo_var);
		ContainerPropertyBag_1__ctor_mD88378C16AD1F8C2A20F3521D02DA3B6811B1FB5(__this, ContainerPropertyBag_1__ctor_mD88378C16AD1F8C2A20F3521D02DA3B6811B1FB5_RuntimeMethod_var);
		XProperty_t7D24F952200020A50C34C5525355F6308A3B9CDC* L_0 = (XProperty_t7D24F952200020A50C34C5525355F6308A3B9CDC*)il2cpp_codegen_object_new(XProperty_t7D24F952200020A50C34C5525355F6308A3B9CDC_il2cpp_TypeInfo_var);
		XProperty__ctor_mBE4223E008541E2DD13ADC1707FBB73E51631CBD(L_0, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m321C399F1891C5BDC7012B34EC2670D56995AB6D(__this, L_0, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m321C399F1891C5BDC7012B34EC2670D56995AB6D_RuntimeMethod_var);
		YProperty_t140E8C4F8AB9EF9D6F3B7EBFDADFA838271CB395* L_1 = (YProperty_t140E8C4F8AB9EF9D6F3B7EBFDADFA838271CB395*)il2cpp_codegen_object_new(YProperty_t140E8C4F8AB9EF9D6F3B7EBFDADFA838271CB395_il2cpp_TypeInfo_var);
		YProperty__ctor_m26384F8C7D674046A6311567F6BD57C05C1E508A(L_1, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m321C399F1891C5BDC7012B34EC2670D56995AB6D(__this, L_1, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m321C399F1891C5BDC7012B34EC2670D56995AB6D_RuntimeMethod_var);
		ZProperty_t431D10EB8A38306B642314AB1AD505F3BB355322* L_2 = (ZProperty_t431D10EB8A38306B642314AB1AD505F3BB355322*)il2cpp_codegen_object_new(ZProperty_t431D10EB8A38306B642314AB1AD505F3BB355322_il2cpp_TypeInfo_var);
		ZProperty__ctor_mBE30B28C0B1F18C02DAEBD5354086C5E7412C795(L_2, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m321C399F1891C5BDC7012B34EC2670D56995AB6D(__this, L_2, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m321C399F1891C5BDC7012B34EC2670D56995AB6D_RuntimeMethod_var);
		WProperty_t8C921D95C0FC58C4F39423D4E530BBEDE6447841* L_3 = (WProperty_t8C921D95C0FC58C4F39423D4E530BBEDE6447841*)il2cpp_codegen_object_new(WProperty_t8C921D95C0FC58C4F39423D4E530BBEDE6447841_il2cpp_TypeInfo_var);
		WProperty__ctor_mE6F8FD652CBFF130392D33791BD2961B09A2EFF4(L_3, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m321C399F1891C5BDC7012B34EC2670D56995AB6D(__this, L_3, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_m321C399F1891C5BDC7012B34EC2670D56995AB6D_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* XProperty_get_Name_m17A1AA4B30199BAEF0B7072526E81C2B30014B4A (XProperty_t7D24F952200020A50C34C5525355F6308A3B9CDC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_mBE4223E008541E2DD13ADC1707FBB73E51631CBD (XProperty_t7D24F952200020A50C34C5525355F6308A3B9CDC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA(__this, Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* YProperty_get_Name_m0D5C29377B95EB977E61764CDD215197591DFFEA (YProperty_t140E8C4F8AB9EF9D6F3B7EBFDADFA838271CB395* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_m26384F8C7D674046A6311567F6BD57C05C1E508A (YProperty_t140E8C4F8AB9EF9D6F3B7EBFDADFA838271CB395* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA(__this, Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ZProperty_get_Name_mC39EB7495554C0D98CCB62566565F09D1E731124 (ZProperty_t431D10EB8A38306B642314AB1AD505F3BB355322* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9CE1604D659135925CCC4DD1F526AFFE42E689F1);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral9CE1604D659135925CCC4DD1F526AFFE42E689F1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZProperty__ctor_mBE30B28C0B1F18C02DAEBD5354086C5E7412C795 (ZProperty_t431D10EB8A38306B642314AB1AD505F3BB355322* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA(__this, Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* WProperty_get_Name_m16E79ED0A622375BCAE1FB7ACF221391A9849A10 (WProperty_t8C921D95C0FC58C4F39423D4E530BBEDE6447841* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA9FEAF5F50923952C1AC3A473DE3C7E17D23B907);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteralA9FEAF5F50923952C1AC3A473DE3C7E17D23B907;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WProperty__ctor_mE6F8FD652CBFF130392D33791BD2961B09A2EFF4 (WProperty_t8C921D95C0FC58C4F39423D4E530BBEDE6447841* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA(__this, Property_2__ctor_mB4377AC3DECCB9DF801451D8D3D612717EFA30BA_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector2IntPropertyBag__ctor_mA5E766EC29B1E37CAA759CBB09997D25FAFB0631 (Vector2IntPropertyBag_t2C43760B9A4208AD87AEF2615AEF035A489502AF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m354E48D6271D3BE0511C80D3BBE3389C3DAA0538_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1__ctor_m0D4393716F0234CD9550422C6CB0FCDAEE0376E4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_t5E98E44A087C66C99B6B48FE49F35A9460817892_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&XProperty_t975009BA2182514E2B728FF83159426523F5C743_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&YProperty_t541C5E0B4E92F21E8E986589A75A6411FC7496ED_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ContainerPropertyBag_1_t5E98E44A087C66C99B6B48FE49F35A9460817892_il2cpp_TypeInfo_var);
		ContainerPropertyBag_1__ctor_m0D4393716F0234CD9550422C6CB0FCDAEE0376E4(__this, ContainerPropertyBag_1__ctor_m0D4393716F0234CD9550422C6CB0FCDAEE0376E4_RuntimeMethod_var);
		XProperty_t975009BA2182514E2B728FF83159426523F5C743* L_0 = (XProperty_t975009BA2182514E2B728FF83159426523F5C743*)il2cpp_codegen_object_new(XProperty_t975009BA2182514E2B728FF83159426523F5C743_il2cpp_TypeInfo_var);
		XProperty__ctor_m850B5684A9419E1A388FA41A041AD667EB1B2589(L_0, NULL);
		ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m354E48D6271D3BE0511C80D3BBE3389C3DAA0538(__this, L_0, ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m354E48D6271D3BE0511C80D3BBE3389C3DAA0538_RuntimeMethod_var);
		YProperty_t541C5E0B4E92F21E8E986589A75A6411FC7496ED* L_1 = (YProperty_t541C5E0B4E92F21E8E986589A75A6411FC7496ED*)il2cpp_codegen_object_new(YProperty_t541C5E0B4E92F21E8E986589A75A6411FC7496ED_il2cpp_TypeInfo_var);
		YProperty__ctor_mC8B0BA567D3BED03017F65512C6DE795C7F7558B(L_1, NULL);
		ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m354E48D6271D3BE0511C80D3BBE3389C3DAA0538(__this, L_1, ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m354E48D6271D3BE0511C80D3BBE3389C3DAA0538_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* XProperty_get_Name_m6E8366D6ADA17D3A95511FE12147584A26159AFE (XProperty_t975009BA2182514E2B728FF83159426523F5C743* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_m850B5684A9419E1A388FA41A041AD667EB1B2589 (XProperty_t975009BA2182514E2B728FF83159426523F5C743* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m0053E84A8D6B34BFDAB7795833421AF8A4F0BAEE_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m0053E84A8D6B34BFDAB7795833421AF8A4F0BAEE(__this, Property_2__ctor_m0053E84A8D6B34BFDAB7795833421AF8A4F0BAEE_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* YProperty_get_Name_m27BEC9727316585FA4BF59F19B53ECC6DC458318 (YProperty_t541C5E0B4E92F21E8E986589A75A6411FC7496ED* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_mC8B0BA567D3BED03017F65512C6DE795C7F7558B (YProperty_t541C5E0B4E92F21E8E986589A75A6411FC7496ED* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m0053E84A8D6B34BFDAB7795833421AF8A4F0BAEE_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m0053E84A8D6B34BFDAB7795833421AF8A4F0BAEE(__this, Property_2__ctor_m0053E84A8D6B34BFDAB7795833421AF8A4F0BAEE_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector3IntPropertyBag__ctor_mECB1D0CC81ACF263C6C0D126BB285A11CBACB2BC (Vector3IntPropertyBag_t534CCCF8AD649649C930E5F3DDA3C0A734F932C7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m8C4C8B4E763DF565341D1474E761E3E4628E1960_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1__ctor_mB9D6850E283177CAED47EE19A760C168ADCCF35C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_tC5EF4ED9E953B090F0F1A7314A738165FB6277B8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&XProperty_tB7813C282F53A8DE5C913CB5B66811EF57A86ACF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&YProperty_t737EB7504629EEBEB734ABDAF170EE06776D3C55_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ZProperty_t6B9433EC1EB2C6EE71D8CD868ADA04EF3AFD90D7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ContainerPropertyBag_1_tC5EF4ED9E953B090F0F1A7314A738165FB6277B8_il2cpp_TypeInfo_var);
		ContainerPropertyBag_1__ctor_mB9D6850E283177CAED47EE19A760C168ADCCF35C(__this, ContainerPropertyBag_1__ctor_mB9D6850E283177CAED47EE19A760C168ADCCF35C_RuntimeMethod_var);
		XProperty_tB7813C282F53A8DE5C913CB5B66811EF57A86ACF* L_0 = (XProperty_tB7813C282F53A8DE5C913CB5B66811EF57A86ACF*)il2cpp_codegen_object_new(XProperty_tB7813C282F53A8DE5C913CB5B66811EF57A86ACF_il2cpp_TypeInfo_var);
		XProperty__ctor_m8A1CCE2C8F5179029876BD678965D6C211CB332C(L_0, NULL);
		ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m8C4C8B4E763DF565341D1474E761E3E4628E1960(__this, L_0, ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m8C4C8B4E763DF565341D1474E761E3E4628E1960_RuntimeMethod_var);
		YProperty_t737EB7504629EEBEB734ABDAF170EE06776D3C55* L_1 = (YProperty_t737EB7504629EEBEB734ABDAF170EE06776D3C55*)il2cpp_codegen_object_new(YProperty_t737EB7504629EEBEB734ABDAF170EE06776D3C55_il2cpp_TypeInfo_var);
		YProperty__ctor_m2233FD190B553BFBD445F8A2EE6BE3DDF0047DBC(L_1, NULL);
		ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m8C4C8B4E763DF565341D1474E761E3E4628E1960(__this, L_1, ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m8C4C8B4E763DF565341D1474E761E3E4628E1960_RuntimeMethod_var);
		ZProperty_t6B9433EC1EB2C6EE71D8CD868ADA04EF3AFD90D7* L_2 = (ZProperty_t6B9433EC1EB2C6EE71D8CD868ADA04EF3AFD90D7*)il2cpp_codegen_object_new(ZProperty_t6B9433EC1EB2C6EE71D8CD868ADA04EF3AFD90D7_il2cpp_TypeInfo_var);
		ZProperty__ctor_mBFB49B0AD7F5606851D8E8B29D0F3AC93B0E0FA7(L_2, NULL);
		ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m8C4C8B4E763DF565341D1474E761E3E4628E1960(__this, L_2, ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m8C4C8B4E763DF565341D1474E761E3E4628E1960_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* XProperty_get_Name_m214358C5224F9EB07B17E4BACEAE9079B50D815C (XProperty_tB7813C282F53A8DE5C913CB5B66811EF57A86ACF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_m8A1CCE2C8F5179029876BD678965D6C211CB332C (XProperty_tB7813C282F53A8DE5C913CB5B66811EF57A86ACF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m3D03E5489D8244E706F51006F1B0D0C5250A7A86_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m3D03E5489D8244E706F51006F1B0D0C5250A7A86(__this, Property_2__ctor_m3D03E5489D8244E706F51006F1B0D0C5250A7A86_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* YProperty_get_Name_m2C54CF468EE63E89D8B301BBB2D5FD02F19101D4 (YProperty_t737EB7504629EEBEB734ABDAF170EE06776D3C55* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_m2233FD190B553BFBD445F8A2EE6BE3DDF0047DBC (YProperty_t737EB7504629EEBEB734ABDAF170EE06776D3C55* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m3D03E5489D8244E706F51006F1B0D0C5250A7A86_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m3D03E5489D8244E706F51006F1B0D0C5250A7A86(__this, Property_2__ctor_m3D03E5489D8244E706F51006F1B0D0C5250A7A86_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ZProperty_get_Name_m2A323942BFFED99C2269258DBB195A8C75EC7032 (ZProperty_t6B9433EC1EB2C6EE71D8CD868ADA04EF3AFD90D7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9CE1604D659135925CCC4DD1F526AFFE42E689F1);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral9CE1604D659135925CCC4DD1F526AFFE42E689F1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZProperty__ctor_mBFB49B0AD7F5606851D8E8B29D0F3AC93B0E0FA7 (ZProperty_t6B9433EC1EB2C6EE71D8CD868ADA04EF3AFD90D7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m3D03E5489D8244E706F51006F1B0D0C5250A7A86_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m3D03E5489D8244E706F51006F1B0D0C5250A7A86(__this, Property_2__ctor_m3D03E5489D8244E706F51006F1B0D0C5250A7A86_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RectPropertyBag__ctor_m4C5D1C260902338AC90DB837C39FE28695ED0110 (RectPropertyBag_tA405FA98439EEF5D80DA2FC601583B49CCB638EC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mE9A22EEC7A77E08AEF0D374E723786F7F1CB63E2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1__ctor_m002B18E65D2DE825AC92248D478593AD1A3F524F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_t2A9DE8C991C6C2537259B466807EEEFC4B68B0E6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HeightProperty_t412F58E91B9683344CB1007B485A4479A0698790_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WidthProperty_t7CC0B7F8C816515B4ED673CBFFA702091CDA5CD7_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&XProperty_tF3F7B9087649DB0DA8ABA99553075498B0D9E9BC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&YProperty_t6ED0B1CD2A67BAC3E33FA1CFD4424E0469DED13D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ContainerPropertyBag_1_t2A9DE8C991C6C2537259B466807EEEFC4B68B0E6_il2cpp_TypeInfo_var);
		ContainerPropertyBag_1__ctor_m002B18E65D2DE825AC92248D478593AD1A3F524F(__this, ContainerPropertyBag_1__ctor_m002B18E65D2DE825AC92248D478593AD1A3F524F_RuntimeMethod_var);
		XProperty_tF3F7B9087649DB0DA8ABA99553075498B0D9E9BC* L_0 = (XProperty_tF3F7B9087649DB0DA8ABA99553075498B0D9E9BC*)il2cpp_codegen_object_new(XProperty_tF3F7B9087649DB0DA8ABA99553075498B0D9E9BC_il2cpp_TypeInfo_var);
		XProperty__ctor_mB5A8967CB19EE4DFE4ECEBEB88D0E1E7B0E6A534(L_0, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mE9A22EEC7A77E08AEF0D374E723786F7F1CB63E2(__this, L_0, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mE9A22EEC7A77E08AEF0D374E723786F7F1CB63E2_RuntimeMethod_var);
		YProperty_t6ED0B1CD2A67BAC3E33FA1CFD4424E0469DED13D* L_1 = (YProperty_t6ED0B1CD2A67BAC3E33FA1CFD4424E0469DED13D*)il2cpp_codegen_object_new(YProperty_t6ED0B1CD2A67BAC3E33FA1CFD4424E0469DED13D_il2cpp_TypeInfo_var);
		YProperty__ctor_m06A2AAA9690D1D2BFC24B0EB360DB3585A877441(L_1, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mE9A22EEC7A77E08AEF0D374E723786F7F1CB63E2(__this, L_1, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mE9A22EEC7A77E08AEF0D374E723786F7F1CB63E2_RuntimeMethod_var);
		WidthProperty_t7CC0B7F8C816515B4ED673CBFFA702091CDA5CD7* L_2 = (WidthProperty_t7CC0B7F8C816515B4ED673CBFFA702091CDA5CD7*)il2cpp_codegen_object_new(WidthProperty_t7CC0B7F8C816515B4ED673CBFFA702091CDA5CD7_il2cpp_TypeInfo_var);
		WidthProperty__ctor_m94F29A6177829CC11F530347605BB4592547C72D(L_2, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mE9A22EEC7A77E08AEF0D374E723786F7F1CB63E2(__this, L_2, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mE9A22EEC7A77E08AEF0D374E723786F7F1CB63E2_RuntimeMethod_var);
		HeightProperty_t412F58E91B9683344CB1007B485A4479A0698790* L_3 = (HeightProperty_t412F58E91B9683344CB1007B485A4479A0698790*)il2cpp_codegen_object_new(HeightProperty_t412F58E91B9683344CB1007B485A4479A0698790_il2cpp_TypeInfo_var);
		HeightProperty__ctor_m07DCB5A7DF246055CB471EF52A8B0C909C222F2E(L_3, NULL);
		ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mE9A22EEC7A77E08AEF0D374E723786F7F1CB63E2(__this, L_3, ContainerPropertyBag_1_AddProperty_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_mE9A22EEC7A77E08AEF0D374E723786F7F1CB63E2_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* XProperty_get_Name_m4847A49D2AA9C51AFF775ECDF8923AE2D9287C99 (XProperty_tF3F7B9087649DB0DA8ABA99553075498B0D9E9BC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_mB5A8967CB19EE4DFE4ECEBEB88D0E1E7B0E6A534 (XProperty_tF3F7B9087649DB0DA8ABA99553075498B0D9E9BC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0(__this, Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* YProperty_get_Name_m2ECB38AAD0B3AF556F16411B19A6E8051C7E6B2A (YProperty_t6ED0B1CD2A67BAC3E33FA1CFD4424E0469DED13D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_m06A2AAA9690D1D2BFC24B0EB360DB3585A877441 (YProperty_t6ED0B1CD2A67BAC3E33FA1CFD4424E0469DED13D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0(__this, Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* WidthProperty_get_Name_m418258980F9849C8B8974F1BFFE3B81A5A29205D (WidthProperty_t7CC0B7F8C816515B4ED673CBFFA702091CDA5CD7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEBF5FE0CBF3EEA67902EEA700B7216E3DB0D8E84);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteralEBF5FE0CBF3EEA67902EEA700B7216E3DB0D8E84;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WidthProperty__ctor_m94F29A6177829CC11F530347605BB4592547C72D (WidthProperty_t7CC0B7F8C816515B4ED673CBFFA702091CDA5CD7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0(__this, Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* HeightProperty_get_Name_mACC36167E0E725402D144CF52B5315FD01E17A3D (HeightProperty_t412F58E91B9683344CB1007B485A4479A0698790* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral647E0FB2B5E859CC4BD7C73623B82C8EFABA2563);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral647E0FB2B5E859CC4BD7C73623B82C8EFABA2563;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void HeightProperty__ctor_m07DCB5A7DF246055CB471EF52A8B0C909C222F2E (HeightProperty_t412F58E91B9683344CB1007B485A4479A0698790* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0(__this, Property_2__ctor_m5FD6858E5CA7D5DCC0B4B383932EA1FA640FD0C0_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RectIntPropertyBag__ctor_mFFAC4C410B920C26B29CB6A309FB7EEF90E75A4F (RectIntPropertyBag_t523552E9F6C38079E84B8F2ADE24828B4BC9DE73* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m1A8D6D29FEC8E7BB998E685F8A0CDFF7685AA4D4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1__ctor_mFDAE4DA91A1BCD387794384FABBE63CD54647DFB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_t34F580FC14E2EF6488F9B8CEE5D00BE38DBC2496_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&HeightProperty_tA93FD47B23964C044C6A92A943AFA11CAFF3E661_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&WidthProperty_t342CF43E06CF1BB3A8B11095799F09CDA16C0080_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&XProperty_tE606E1E3830D04DAEB0CE238A511A8CD0AFCF91F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&YProperty_t405447FD0661D730A61A82DBB5322B7645C9C409_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ContainerPropertyBag_1_t34F580FC14E2EF6488F9B8CEE5D00BE38DBC2496_il2cpp_TypeInfo_var);
		ContainerPropertyBag_1__ctor_mFDAE4DA91A1BCD387794384FABBE63CD54647DFB(__this, ContainerPropertyBag_1__ctor_mFDAE4DA91A1BCD387794384FABBE63CD54647DFB_RuntimeMethod_var);
		XProperty_tE606E1E3830D04DAEB0CE238A511A8CD0AFCF91F* L_0 = (XProperty_tE606E1E3830D04DAEB0CE238A511A8CD0AFCF91F*)il2cpp_codegen_object_new(XProperty_tE606E1E3830D04DAEB0CE238A511A8CD0AFCF91F_il2cpp_TypeInfo_var);
		XProperty__ctor_mE83AE59E6CAFA7F55F968FD2D8DBD9DCD0284632(L_0, NULL);
		ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m1A8D6D29FEC8E7BB998E685F8A0CDFF7685AA4D4(__this, L_0, ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m1A8D6D29FEC8E7BB998E685F8A0CDFF7685AA4D4_RuntimeMethod_var);
		YProperty_t405447FD0661D730A61A82DBB5322B7645C9C409* L_1 = (YProperty_t405447FD0661D730A61A82DBB5322B7645C9C409*)il2cpp_codegen_object_new(YProperty_t405447FD0661D730A61A82DBB5322B7645C9C409_il2cpp_TypeInfo_var);
		YProperty__ctor_m484229CC408CD868ABA8D0DBE8B12BDEEE74E4C8(L_1, NULL);
		ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m1A8D6D29FEC8E7BB998E685F8A0CDFF7685AA4D4(__this, L_1, ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m1A8D6D29FEC8E7BB998E685F8A0CDFF7685AA4D4_RuntimeMethod_var);
		WidthProperty_t342CF43E06CF1BB3A8B11095799F09CDA16C0080* L_2 = (WidthProperty_t342CF43E06CF1BB3A8B11095799F09CDA16C0080*)il2cpp_codegen_object_new(WidthProperty_t342CF43E06CF1BB3A8B11095799F09CDA16C0080_il2cpp_TypeInfo_var);
		WidthProperty__ctor_m83F6E008F488F8C6DEC0A7BCCF5118221C576DCD(L_2, NULL);
		ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m1A8D6D29FEC8E7BB998E685F8A0CDFF7685AA4D4(__this, L_2, ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m1A8D6D29FEC8E7BB998E685F8A0CDFF7685AA4D4_RuntimeMethod_var);
		HeightProperty_tA93FD47B23964C044C6A92A943AFA11CAFF3E661* L_3 = (HeightProperty_tA93FD47B23964C044C6A92A943AFA11CAFF3E661*)il2cpp_codegen_object_new(HeightProperty_tA93FD47B23964C044C6A92A943AFA11CAFF3E661_il2cpp_TypeInfo_var);
		HeightProperty__ctor_m9191C19D80EA3F2F645028F03A507E0D492DBB2A(L_3, NULL);
		ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m1A8D6D29FEC8E7BB998E685F8A0CDFF7685AA4D4(__this, L_3, ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m1A8D6D29FEC8E7BB998E685F8A0CDFF7685AA4D4_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* XProperty_get_Name_m6F3F6E0874574CA99F1067BC0CB937F03CBC84AB (XProperty_tE606E1E3830D04DAEB0CE238A511A8CD0AFCF91F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral062DB096C728515E033CF8C48A1C1F0B9A79384B;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void XProperty__ctor_mE83AE59E6CAFA7F55F968FD2D8DBD9DCD0284632 (XProperty_tE606E1E3830D04DAEB0CE238A511A8CD0AFCF91F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230(__this, Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* YProperty_get_Name_m2AE1DE95187BBD3082B9AEE36180867B7D0A9919 (YProperty_t405447FD0661D730A61A82DBB5322B7645C9C409* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral9384C6EF2DA5C0BD5274A0DACFF291D0ABBFD8B1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void YProperty__ctor_m484229CC408CD868ABA8D0DBE8B12BDEEE74E4C8 (YProperty_t405447FD0661D730A61A82DBB5322B7645C9C409* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230(__this, Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* WidthProperty_get_Name_mFEF2D1077A3A5D33C9B6BE8E4576FFE459B19EA2 (WidthProperty_t342CF43E06CF1BB3A8B11095799F09CDA16C0080* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEBF5FE0CBF3EEA67902EEA700B7216E3DB0D8E84);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteralEBF5FE0CBF3EEA67902EEA700B7216E3DB0D8E84;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WidthProperty__ctor_m83F6E008F488F8C6DEC0A7BCCF5118221C576DCD (WidthProperty_t342CF43E06CF1BB3A8B11095799F09CDA16C0080* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230(__this, Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* HeightProperty_get_Name_m0E2478B4BBEBFAFBB16F14DCC98730B7E79DC08F (HeightProperty_tA93FD47B23964C044C6A92A943AFA11CAFF3E661* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral647E0FB2B5E859CC4BD7C73623B82C8EFABA2563);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral647E0FB2B5E859CC4BD7C73623B82C8EFABA2563;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void HeightProperty__ctor_m9191C19D80EA3F2F645028F03A507E0D492DBB2A (HeightProperty_tA93FD47B23964C044C6A92A943AFA11CAFF3E661* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230(__this, Property_2__ctor_m9FB84C0814943DA6AF990699BADA5411E3D18230_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoundsPropertyBag__ctor_mB3B92E349452661D037A9AB2A8B6581ECFEBB111 (BoundsPropertyBag_tA22B9256F487C40279FFFA58AD0E8D6CF29BA83F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CenterProperty_tD6327DAAB6BB3A87B57B00FDBB79F5891F611272_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_AddProperty_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m2D875551D9368012276D3FDD3DC12FD9A4BE3987_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1__ctor_m0B3C26C575C4406E1478D8D4FFB946522E95F740_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_t2279C723A3D81E0BE90A787349C987FC490033EF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ExtentsProperty_t59E99CC5DAA1A0F80F2344E6297BABFF27F0B88F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ContainerPropertyBag_1_t2279C723A3D81E0BE90A787349C987FC490033EF_il2cpp_TypeInfo_var);
		ContainerPropertyBag_1__ctor_m0B3C26C575C4406E1478D8D4FFB946522E95F740(__this, ContainerPropertyBag_1__ctor_m0B3C26C575C4406E1478D8D4FFB946522E95F740_RuntimeMethod_var);
		CenterProperty_tD6327DAAB6BB3A87B57B00FDBB79F5891F611272* L_0 = (CenterProperty_tD6327DAAB6BB3A87B57B00FDBB79F5891F611272*)il2cpp_codegen_object_new(CenterProperty_tD6327DAAB6BB3A87B57B00FDBB79F5891F611272_il2cpp_TypeInfo_var);
		CenterProperty__ctor_m4ECB75EAEC52B0EDDC2EE91F4F46F12A7870420A(L_0, NULL);
		ContainerPropertyBag_1_AddProperty_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m2D875551D9368012276D3FDD3DC12FD9A4BE3987(__this, L_0, ContainerPropertyBag_1_AddProperty_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m2D875551D9368012276D3FDD3DC12FD9A4BE3987_RuntimeMethod_var);
		ExtentsProperty_t59E99CC5DAA1A0F80F2344E6297BABFF27F0B88F* L_1 = (ExtentsProperty_t59E99CC5DAA1A0F80F2344E6297BABFF27F0B88F*)il2cpp_codegen_object_new(ExtentsProperty_t59E99CC5DAA1A0F80F2344E6297BABFF27F0B88F_il2cpp_TypeInfo_var);
		ExtentsProperty__ctor_mC3315D3C24FA257EE106E3F6460360D65EB0DB16(L_1, NULL);
		ContainerPropertyBag_1_AddProperty_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m2D875551D9368012276D3FDD3DC12FD9A4BE3987(__this, L_1, ContainerPropertyBag_1_AddProperty_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_m2D875551D9368012276D3FDD3DC12FD9A4BE3987_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* CenterProperty_get_Name_mE2932C9FBF1FBC93325E98817C5D7B377DEB0CCF (CenterProperty_tD6327DAAB6BB3A87B57B00FDBB79F5891F611272* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF26F502B14F503952C33ADFF928357DED0388E8D);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteralF26F502B14F503952C33ADFF928357DED0388E8D;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CenterProperty__ctor_m4ECB75EAEC52B0EDDC2EE91F4F46F12A7870420A (CenterProperty_tD6327DAAB6BB3A87B57B00FDBB79F5891F611272* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m6D93F660253A4C7EC344945B11B74A1EC5E2052B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m6D93F660253A4C7EC344945B11B74A1EC5E2052B(__this, Property_2__ctor_m6D93F660253A4C7EC344945B11B74A1EC5E2052B_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ExtentsProperty_get_Name_m01B60324ABC14349CAB683087AA1617A61F428B2 (ExtentsProperty_t59E99CC5DAA1A0F80F2344E6297BABFF27F0B88F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral46A3E4CF4159352EA245C4FF7641E52B161DCFF3);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral46A3E4CF4159352EA245C4FF7641E52B161DCFF3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ExtentsProperty__ctor_mC3315D3C24FA257EE106E3F6460360D65EB0DB16 (ExtentsProperty_t59E99CC5DAA1A0F80F2344E6297BABFF27F0B88F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m6D93F660253A4C7EC344945B11B74A1EC5E2052B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m6D93F660253A4C7EC344945B11B74A1EC5E2052B(__this, Property_2__ctor_m6D93F660253A4C7EC344945B11B74A1EC5E2052B_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BoundsIntPropertyBag__ctor_mE2B2BB212558FE1C221E01D8FCEC4C41B02C6BF5 (BoundsIntPropertyBag_t36236827A488493B27B94BF7EB678949732F2052* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_AddProperty_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m3CD2BB7313CFB80B5C21BFB971910AC718564437_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1__ctor_mDF062EDE672E0636FD2510B7574F918A7924BAE1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_t3A0DBCD09BBF64291800B0275CC7E1F2EECA0FEF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PositionProperty_t413AD67DF4061C4B811A40AA01E44568337B8FA5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SizeProperty_t9A3CD66744B590FB53B427877E1E781350ADF5C9_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ContainerPropertyBag_1_t3A0DBCD09BBF64291800B0275CC7E1F2EECA0FEF_il2cpp_TypeInfo_var);
		ContainerPropertyBag_1__ctor_mDF062EDE672E0636FD2510B7574F918A7924BAE1(__this, ContainerPropertyBag_1__ctor_mDF062EDE672E0636FD2510B7574F918A7924BAE1_RuntimeMethod_var);
		PositionProperty_t413AD67DF4061C4B811A40AA01E44568337B8FA5* L_0 = (PositionProperty_t413AD67DF4061C4B811A40AA01E44568337B8FA5*)il2cpp_codegen_object_new(PositionProperty_t413AD67DF4061C4B811A40AA01E44568337B8FA5_il2cpp_TypeInfo_var);
		PositionProperty__ctor_mC574247EEEC6CFC189F13B0B961BD2A9C2CBB2CD(L_0, NULL);
		ContainerPropertyBag_1_AddProperty_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m3CD2BB7313CFB80B5C21BFB971910AC718564437(__this, L_0, ContainerPropertyBag_1_AddProperty_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m3CD2BB7313CFB80B5C21BFB971910AC718564437_RuntimeMethod_var);
		SizeProperty_t9A3CD66744B590FB53B427877E1E781350ADF5C9* L_1 = (SizeProperty_t9A3CD66744B590FB53B427877E1E781350ADF5C9*)il2cpp_codegen_object_new(SizeProperty_t9A3CD66744B590FB53B427877E1E781350ADF5C9_il2cpp_TypeInfo_var);
		SizeProperty__ctor_m45114288E626A2F23A1035BDB8420DD663FE25BB(L_1, NULL);
		ContainerPropertyBag_1_AddProperty_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m3CD2BB7313CFB80B5C21BFB971910AC718564437(__this, L_1, ContainerPropertyBag_1_AddProperty_TisVector3Int_t65CB06F557251D18A37BD71F3655BA836A357376_m3CD2BB7313CFB80B5C21BFB971910AC718564437_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* PositionProperty_get_Name_m39AE78CF5CD19081CF1B424CE3182BE78AE55F62 (PositionProperty_t413AD67DF4061C4B811A40AA01E44568337B8FA5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral88BDF3D0791A560245652E772545C49897854443);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral88BDF3D0791A560245652E772545C49897854443;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PositionProperty__ctor_mC574247EEEC6CFC189F13B0B961BD2A9C2CBB2CD (PositionProperty_t413AD67DF4061C4B811A40AA01E44568337B8FA5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m6CCA84842C767E1068B2F635177225E05FFE539B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m6CCA84842C767E1068B2F635177225E05FFE539B(__this, Property_2__ctor_m6CCA84842C767E1068B2F635177225E05FFE539B_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* SizeProperty_get_Name_m6941C93A954B441C5188389C079E699FD0CBC41C (SizeProperty_t9A3CD66744B590FB53B427877E1E781350ADF5C9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3EE5A4671A4E5AEBD31CA5F013A7773DC18ED22B);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral3EE5A4671A4E5AEBD31CA5F013A7773DC18ED22B;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SizeProperty__ctor_m45114288E626A2F23A1035BDB8420DD663FE25BB (SizeProperty_t9A3CD66744B590FB53B427877E1E781350ADF5C9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_m6CCA84842C767E1068B2F635177225E05FFE539B_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_m6CCA84842C767E1068B2F635177225E05FFE539B(__this, Property_2__ctor_m6CCA84842C767E1068B2F635177225E05FFE539B_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SystemVersionPropertyBag__ctor_mC0D23BC1F975382571DFA70EC5B52B24083BD4AF (SystemVersionPropertyBag_t2BC09BA510A4DE999004F35E9930AA414DF46E8A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BuildProperty_t7D4DAF6A9110609825BB8D6A15C94A6BAF28E100_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m56A8F79AE5548660FD24207FA0D287E84B15BAF3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1__ctor_m14BD526E7B6C44170A4EF0E46DD101AA7D1C558E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContainerPropertyBag_1_t569DC4A1FC35A7A716C097213C60CC9FA986B5A9_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MajorProperty_tBBB67865E97B18DD17B9FB742398C3A3F79E8A54_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MinorProperty_tE0CA1F38999206EF615F6DAAF085F12C45D6D7DC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RevisionProperty_t50A6B465CC86B9998606B7D5F614AE674BE69B6B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(ContainerPropertyBag_1_t569DC4A1FC35A7A716C097213C60CC9FA986B5A9_il2cpp_TypeInfo_var);
		ContainerPropertyBag_1__ctor_m14BD526E7B6C44170A4EF0E46DD101AA7D1C558E(__this, ContainerPropertyBag_1__ctor_m14BD526E7B6C44170A4EF0E46DD101AA7D1C558E_RuntimeMethod_var);
		MajorProperty_tBBB67865E97B18DD17B9FB742398C3A3F79E8A54* L_0 = (MajorProperty_tBBB67865E97B18DD17B9FB742398C3A3F79E8A54*)il2cpp_codegen_object_new(MajorProperty_tBBB67865E97B18DD17B9FB742398C3A3F79E8A54_il2cpp_TypeInfo_var);
		MajorProperty__ctor_m5FA801277D886789A8732CE2E86F27C49E371679(L_0, NULL);
		ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m56A8F79AE5548660FD24207FA0D287E84B15BAF3(__this, L_0, ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m56A8F79AE5548660FD24207FA0D287E84B15BAF3_RuntimeMethod_var);
		MinorProperty_tE0CA1F38999206EF615F6DAAF085F12C45D6D7DC* L_1 = (MinorProperty_tE0CA1F38999206EF615F6DAAF085F12C45D6D7DC*)il2cpp_codegen_object_new(MinorProperty_tE0CA1F38999206EF615F6DAAF085F12C45D6D7DC_il2cpp_TypeInfo_var);
		MinorProperty__ctor_mF391B7B9C95C2B48FB07127E463F0B788F9B0F29(L_1, NULL);
		ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m56A8F79AE5548660FD24207FA0D287E84B15BAF3(__this, L_1, ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m56A8F79AE5548660FD24207FA0D287E84B15BAF3_RuntimeMethod_var);
		BuildProperty_t7D4DAF6A9110609825BB8D6A15C94A6BAF28E100* L_2 = (BuildProperty_t7D4DAF6A9110609825BB8D6A15C94A6BAF28E100*)il2cpp_codegen_object_new(BuildProperty_t7D4DAF6A9110609825BB8D6A15C94A6BAF28E100_il2cpp_TypeInfo_var);
		BuildProperty__ctor_mD5BCC6C2F665694E7711CE8742008F6F2434BDAC(L_2, NULL);
		ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m56A8F79AE5548660FD24207FA0D287E84B15BAF3(__this, L_2, ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m56A8F79AE5548660FD24207FA0D287E84B15BAF3_RuntimeMethod_var);
		RevisionProperty_t50A6B465CC86B9998606B7D5F614AE674BE69B6B* L_3 = (RevisionProperty_t50A6B465CC86B9998606B7D5F614AE674BE69B6B*)il2cpp_codegen_object_new(RevisionProperty_t50A6B465CC86B9998606B7D5F614AE674BE69B6B_il2cpp_TypeInfo_var);
		RevisionProperty__ctor_m0606FE463AAA1C106582277BB0115987861BE2FC(L_3, NULL);
		ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m56A8F79AE5548660FD24207FA0D287E84B15BAF3(__this, L_3, ContainerPropertyBag_1_AddProperty_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m56A8F79AE5548660FD24207FA0D287E84B15BAF3_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MajorProperty__ctor_m5FA801277D886789A8732CE2E86F27C49E371679 (MajorProperty_tBBB67865E97B18DD17B9FB742398C3A3F79E8A54* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91(__this, Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91_RuntimeMethod_var);
		MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8* L_0 = (MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8*)il2cpp_codegen_object_new(MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8_il2cpp_TypeInfo_var);
		MinAttribute__ctor_mE569E9A31ED222B7128543394AAF44DA09839262(L_0, (0.0f), NULL);
		Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17(__this, L_0, Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* MajorProperty_get_Name_m95F7965B52F39B3620B82E47B30CF2893FA414EC (MajorProperty_tBBB67865E97B18DD17B9FB742398C3A3F79E8A54* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4D9AD25D7DFFA5DF4C19141B7788780E102E8D03);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral4D9AD25D7DFFA5DF4C19141B7788780E102E8D03;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MinorProperty__ctor_mF391B7B9C95C2B48FB07127E463F0B788F9B0F29 (MinorProperty_tE0CA1F38999206EF615F6DAAF085F12C45D6D7DC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91(__this, Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91_RuntimeMethod_var);
		MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8* L_0 = (MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8*)il2cpp_codegen_object_new(MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8_il2cpp_TypeInfo_var);
		MinAttribute__ctor_mE569E9A31ED222B7128543394AAF44DA09839262(L_0, (0.0f), NULL);
		Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17(__this, L_0, Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* MinorProperty_get_Name_mC00B9C231673E72545064F606F64027117280A09 (MinorProperty_tE0CA1F38999206EF615F6DAAF085F12C45D6D7DC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral53009C1E06A9EDAD1337D065B65F07A5745635D7);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral53009C1E06A9EDAD1337D065B65F07A5745635D7;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BuildProperty__ctor_mD5BCC6C2F665694E7711CE8742008F6F2434BDAC (BuildProperty_t7D4DAF6A9110609825BB8D6A15C94A6BAF28E100* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91(__this, Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91_RuntimeMethod_var);
		MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8* L_0 = (MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8*)il2cpp_codegen_object_new(MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8_il2cpp_TypeInfo_var);
		MinAttribute__ctor_mE569E9A31ED222B7128543394AAF44DA09839262(L_0, (0.0f), NULL);
		Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17(__this, L_0, Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* BuildProperty_get_Name_mE523A88F9890A84EA707B257639A18D3EC256E3A (BuildProperty_t7D4DAF6A9110609825BB8D6A15C94A6BAF28E100* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral45045578780FD0B9DC1862175DE1C8476691896A);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral45045578780FD0B9DC1862175DE1C8476691896A;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RevisionProperty__ctor_m0606FE463AAA1C106582277BB0115987861BE2FC (RevisionProperty_t50A6B465CC86B9998606B7D5F614AE674BE69B6B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91(__this, Property_2__ctor_mF6C86039FA91144F873F41892F0AF7C0103F4E91_RuntimeMethod_var);
		MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8* L_0 = (MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8*)il2cpp_codegen_object_new(MinAttribute_tAF42831245313E39FFE7BE7A2D24033E7A9F3DE8_il2cpp_TypeInfo_var);
		MinAttribute__ctor_mE569E9A31ED222B7128543394AAF44DA09839262(L_0, (0.0f), NULL);
		Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17(__this, L_0, Property_2_AddAttribute_m0AF414C2308561C1C1BAF1B3A170587B58060C17_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* RevisionProperty_get_Name_m3A7BF24E10B80CB5CD0F651215A73824BCF9D4CE (RevisionProperty_t50A6B465CC86B9998606B7D5F614AE674BE69B6B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCDEC055C1A4411C67143F959BC96DE196E510348);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteralCDEC055C1A4411C67143F959BC96DE196E510348;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PropertyBagStore__cctor_mF5E7248419BE8AEAB2356857AE4E19759082AFBD (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConcurrentDictionary_2__ctor_m4D5B845B9E86D2F0D817BCD2FB496291F8B6734B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mD9035342BE1CF0A3EEEE157D1796DA385C0C3F69_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t4B77DB8D00EC6CC4705EB5F2FCC506472734EA72_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582* L_0 = (ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582*)il2cpp_codegen_object_new(ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582_il2cpp_TypeInfo_var);
		ConcurrentDictionary_2__ctor_m4D5B845B9E86D2F0D817BCD2FB496291F8B6734B(L_0, ConcurrentDictionary_2__ctor_m4D5B845B9E86D2F0D817BCD2FB496291F8B6734B_RuntimeMethod_var);
		((PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_StaticFields*)il2cpp_codegen_static_fields_for(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var))->___s_PropertyBags = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_StaticFields*)il2cpp_codegen_static_fields_for(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var))->___s_PropertyBags), (void*)L_0);
		List_1_t4B77DB8D00EC6CC4705EB5F2FCC506472734EA72* L_1 = (List_1_t4B77DB8D00EC6CC4705EB5F2FCC506472734EA72*)il2cpp_codegen_object_new(List_1_t4B77DB8D00EC6CC4705EB5F2FCC506472734EA72_il2cpp_TypeInfo_var);
		List_1__ctor_mD9035342BE1CF0A3EEEE157D1796DA385C0C3F69(L_1, List_1__ctor_mD9035342BE1CF0A3EEEE157D1796DA385C0C3F69_RuntimeMethod_var);
		((PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_StaticFields*)il2cpp_codegen_static_fields_for(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var))->___s_RegisteredTypes = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_StaticFields*)il2cpp_codegen_static_fields_for(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var))->___s_RegisteredTypes), (void*)L_1);
		((PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_StaticFields*)il2cpp_codegen_static_fields_for(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var))->___s_PropertyBagProvider = (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&((PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_StaticFields*)il2cpp_codegen_static_fields_for(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var))->___s_PropertyBagProvider), (void*)(ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF*)NULL);
		ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF* L_2 = (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF*)il2cpp_codegen_object_new(ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_il2cpp_TypeInfo_var);
		ReflectedPropertyBagProvider__ctor_mF83A77394EF7205D41A784158583F084E8CE04C2(L_2, NULL);
		((PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_StaticFields*)il2cpp_codegen_static_fields_for(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var))->___s_PropertyBagProvider = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&((PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_StaticFields*)il2cpp_codegen_static_fields_for(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var))->___s_PropertyBagProvider), (void*)L_2);
		DefaultPropertyBagInitializer_Initialize_m8BA3D4950B9C07EF2A0978A88D908FCF297BD080(NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* PropertyBagStore_GetPropertyBag_m90F2EBB48D60993594856358C572964C8011143E (Type_t* ___0_type, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConcurrentDictionary_2_TryAdd_m2CEBA7D69347F97B298F46E19C8736585DA58317_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ConcurrentDictionary_2_TryGetValue_mDEA54BBDF227F094A9D476D95BC3A3DF5077048B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IPropertyBagRegister_tE78909517CE561497EC85687E3A2B6EA89519FF0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RuntimeObject_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RuntimeObject* V_0 = NULL;
	bool V_1 = false;
	RuntimeObject* V_2 = NULL;
	bool V_3 = false;
	bool V_4 = false;
	bool V_5 = false;
	bool V_6 = false;
	bool V_7 = false;
	bool V_8 = false;
	int32_t G_B7_0 = 0;
	int32_t G_B12_0 = 0;
	RuntimeObject* G_B21_0 = NULL;
	RuntimeObject* G_B20_0 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var);
		ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582* L_0 = ((PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_StaticFields*)il2cpp_codegen_static_fields_for(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var))->___s_PropertyBags;
		Type_t* L_1 = ___0_type;
		NullCheck(L_0);
		bool L_2;
		L_2 = ConcurrentDictionary_2_TryGetValue_mDEA54BBDF227F094A9D476D95BC3A3DF5077048B(L_0, L_1, (&V_0), ConcurrentDictionary_2_TryGetValue_mDEA54BBDF227F094A9D476D95BC3A3DF5077048B_RuntimeMethod_var);
		V_1 = L_2;
		bool L_3 = V_1;
		if (!L_3)
		{
			goto IL_001a;
		}
	}
	{
		RuntimeObject* L_4 = V_0;
		V_2 = L_4;
		goto IL_00dd;
	}

IL_001a:
	{
		Type_t* L_5 = ___0_type;
		bool L_6;
		L_6 = TypeTraits_IsContainer_m46D04F3E3219371CC5F133E2CC54BCA46FD72505(L_5, NULL);
		V_3 = (bool)((((int32_t)L_6) == ((int32_t)0))? 1 : 0);
		bool L_7 = V_3;
		if (!L_7)
		{
			goto IL_002f;
		}
	}
	{
		V_2 = (RuntimeObject*)NULL;
		goto IL_00dd;
	}

IL_002f:
	{
		Type_t* L_8 = ___0_type;
		NullCheck(L_8);
		bool L_9;
		L_9 = Type_get_IsArray_mB9B8CA713B2AA9D6AFECC24E05AF78D22532B673(L_8, NULL);
		if (!L_9)
		{
			goto IL_0045;
		}
	}
	{
		Type_t* L_10 = ___0_type;
		NullCheck(L_10);
		int32_t L_11;
		L_11 = VirtualFuncInvoker0< int32_t >::Invoke(47, L_10);
		G_B7_0 = ((((int32_t)((((int32_t)L_11) == ((int32_t)1))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		goto IL_0046;
	}

IL_0045:
	{
		G_B7_0 = 0;
	}

IL_0046:
	{
		V_4 = (bool)G_B7_0;
		bool L_12 = V_4;
		if (!L_12)
		{
			goto IL_0054;
		}
	}
	{
		V_2 = (RuntimeObject*)NULL;
		goto IL_00dd;
	}

IL_0054:
	{
		Type_t* L_13 = ___0_type;
		NullCheck(L_13);
		bool L_14;
		L_14 = Type_get_IsInterface_m484A7D9321E72758EABE7F36AE266EB0905957EC(L_13, NULL);
		if (L_14)
		{
			goto IL_0064;
		}
	}
	{
		Type_t* L_15 = ___0_type;
		NullCheck(L_15);
		bool L_16;
		L_16 = Type_get_IsAbstract_m16FA83463867635ED9DECAE1C5F6BE96B4579CE5(L_15, NULL);
		G_B12_0 = ((int32_t)(L_16));
		goto IL_0065;
	}

IL_0064:
	{
		G_B12_0 = 1;
	}

IL_0065:
	{
		V_5 = (bool)G_B12_0;
		bool L_17 = V_5;
		if (!L_17)
		{
			goto IL_0070;
		}
	}
	{
		V_2 = (RuntimeObject*)NULL;
		goto IL_00dd;
	}

IL_0070:
	{
		Type_t* L_18 = ___0_type;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_19 = { reinterpret_cast<intptr_t> (RuntimeObject_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_20;
		L_20 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_19, NULL);
		bool L_21;
		L_21 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_18, L_20, NULL);
		V_6 = L_21;
		bool L_22 = V_6;
		if (!L_22)
		{
			goto IL_008b;
		}
	}
	{
		V_2 = (RuntimeObject*)NULL;
		goto IL_00dd;
	}

IL_008b:
	{
		il2cpp_codegen_runtime_class_init_inline(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var);
		ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF* L_23 = ((PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_StaticFields*)il2cpp_codegen_static_fields_for(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var))->___s_PropertyBagProvider;
		V_7 = (bool)((!(((RuntimeObject*)(ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF*)L_23) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		bool L_24 = V_7;
		if (!L_24)
		{
			goto IL_00d9;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var);
		ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF* L_25 = ((PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_StaticFields*)il2cpp_codegen_static_fields_for(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var))->___s_PropertyBagProvider;
		Type_t* L_26 = ___0_type;
		NullCheck(L_25);
		RuntimeObject* L_27;
		L_27 = ReflectedPropertyBagProvider_CreatePropertyBag_m89CDB23B3D426162B7641A4BB14DB3D71FE56CB2(L_25, L_26, NULL);
		V_0 = L_27;
		RuntimeObject* L_28 = V_0;
		V_8 = (bool)((((RuntimeObject*)(RuntimeObject*)L_28) == ((RuntimeObject*)(RuntimeObject*)NULL))? 1 : 0);
		bool L_29 = V_8;
		if (!L_29)
		{
			goto IL_00c1;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var);
		ConcurrentDictionary_2_t289EA16AA3198E74A1ED327115892A1325FDE582* L_30 = ((PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_StaticFields*)il2cpp_codegen_static_fields_for(PropertyBagStore_tF76E67D0FF5460D8B59B033B6EB4E2A2E446024C_il2cpp_TypeInfo_var))->___s_PropertyBags;
		Type_t* L_31 = ___0_type;
		NullCheck(L_30);
		bool L_32;
		L_32 = ConcurrentDictionary_2_TryAdd_m2CEBA7D69347F97B298F46E19C8736585DA58317(L_30, L_31, (RuntimeObject*)NULL, ConcurrentDictionary_2_TryAdd_m2CEBA7D69347F97B298F46E19C8736585DA58317_RuntimeMethod_var);
		goto IL_00d8;
	}

IL_00c1:
	{
		RuntimeObject* L_33 = V_0;
		RuntimeObject* L_34 = ((RuntimeObject*)IsInst((RuntimeObject*)L_33, IPropertyBagRegister_tE78909517CE561497EC85687E3A2B6EA89519FF0_il2cpp_TypeInfo_var));
		if (L_34)
		{
			G_B21_0 = L_34;
			goto IL_00ce;
		}
		G_B20_0 = L_34;
	}
	{
		goto IL_00d4;
	}

IL_00ce:
	{
		NullCheck(G_B21_0);
		InterfaceActionInvoker0::Invoke(0, IPropertyBagRegister_tE78909517CE561497EC85687E3A2B6EA89519FF0_il2cpp_TypeInfo_var, G_B21_0);
	}

IL_00d4:
	{
		RuntimeObject* L_35 = V_0;
		V_2 = L_35;
		goto IL_00dd;
	}

IL_00d8:
	{
	}

IL_00d9:
	{
		V_2 = (RuntimeObject*)NULL;
		goto IL_00dd;
	}

IL_00dd:
	{
		RuntimeObject* L_36 = V_2;
		return L_36;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ReflectedPropertyBagAttribute__ctor_m630B43FC9EA5EACF97A960B10C6A4F28E2786B10 (ReflectedPropertyBagAttribute_tF96B3D583AE055F3962792F4B6DDBB973322792D* __this, const RuntimeMethod* method) 
{
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ReflectedPropertyBagProvider__ctor_mF83A77394EF7205D41A784158583F084E8CE04C2 (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerable_First_TisMethodInfo_t_mF25EDA3EE26D685AA41FF116BE54C056DD0D179B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3C_ctorU3Eb__10_0_m98A9F8B7DD2A14EBBB86606DCFE2CB988291758F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0F9BE39FE19AFE46EEE9236F011A9433EAA21F69);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4EC998918E53FB3C460B5C3148F7ABAADC6EF6A1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5596D6057770EA4D394A7BADC0C6729E361C1342);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5E2CF53B436B05607F3EE43FB81D75975A00662A);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralAAA7BDF9D882584A96DB5561B9FA7C14404B3824);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC8FA9ED3100EEB33720799E0BF36802352500BBE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCE876740FAE0F74584A26D196FFCDCF9055FFB6A);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF2C108AA83BE591285C6E2190E1EEB2F1ADBAB24);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFE09717218C9DB873D50817C8EE39FA626116789);
		s_Il2CppMethodInitialized = true;
	}
	Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164* G_B2_0 = NULL;
	MethodInfoU5BU5D_tDF3670604A0AECF814A0B0BA09B91FBF0D6A3265* G_B2_1 = NULL;
	ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF* G_B2_2 = NULL;
	Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164* G_B1_0 = NULL;
	MethodInfoU5BU5D_tDF3670604A0AECF814A0B0BA09B91FBF0D6A3265* G_B1_1 = NULL;
	ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF* G_B1_2 = NULL;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_0 = { reinterpret_cast<intptr_t> (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_1;
		L_1 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_0, NULL);
		NullCheck(L_1);
		MethodInfo_t* L_2;
		L_2 = Type_GetMethod_m9E66B5053F150537A74C490C1DA5174A7875189D(L_1, _stringLiteral5596D6057770EA4D394A7BADC0C6729E361C1342, ((int32_t)36), NULL);
		__this->___m_CreatePropertyMethod = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_CreatePropertyMethod), (void*)L_2);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_3 = { reinterpret_cast<intptr_t> (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_0_0_0_var) };
		Type_t* L_4;
		L_4 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_3, NULL);
		NullCheck(L_4);
		MethodInfoU5BU5D_tDF3670604A0AECF814A0B0BA09B91FBF0D6A3265* L_5;
		L_5 = VirtualFuncInvoker1< MethodInfoU5BU5D_tDF3670604A0AECF814A0B0BA09B91FBF0D6A3265*, int32_t >::Invoke(100, L_4, ((int32_t)20));
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var);
		Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164* L_6 = ((U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var))->___U3CU3E9__10_0;
		Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164* L_7 = L_6;
		if (L_7)
		{
			G_B2_0 = L_7;
			G_B2_1 = L_5;
			G_B2_2 = __this;
			goto IL_0055;
		}
		G_B1_0 = L_7;
		G_B1_1 = L_5;
		G_B1_2 = __this;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var);
		U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523* L_8 = ((U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var))->___U3CU3E9;
		Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164* L_9 = (Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164*)il2cpp_codegen_object_new(Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164_il2cpp_TypeInfo_var);
		Func_2__ctor_m85EFD3541E8A8498FD05A6169ED11E00D408A2F0(L_9, L_8, (intptr_t)((void*)U3CU3Ec_U3C_ctorU3Eb__10_0_m98A9F8B7DD2A14EBBB86606DCFE2CB988291758F_RuntimeMethod_var), NULL);
		Func_2_t48B62DF57727FFB990D76F189BB41D4DC86FF164* L_10 = L_9;
		((U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var))->___U3CU3E9__10_0 = L_10;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var))->___U3CU3E9__10_0), (void*)L_10);
		G_B2_0 = L_10;
		G_B2_1 = G_B1_1;
		G_B2_2 = G_B1_2;
	}

IL_0055:
	{
		MethodInfo_t* L_11;
		L_11 = Enumerable_First_TisMethodInfo_t_mF25EDA3EE26D685AA41FF116BE54C056DD0D179B((RuntimeObject*)G_B2_1, G_B2_0, Enumerable_First_TisMethodInfo_t_mF25EDA3EE26D685AA41FF116BE54C056DD0D179B_RuntimeMethod_var);
		NullCheck(G_B2_2);
		G_B2_2->___m_CreatePropertyBagMethod = L_11;
		Il2CppCodeGenWriteBarrier((void**)(&G_B2_2->___m_CreatePropertyBagMethod), (void*)L_11);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_12 = { reinterpret_cast<intptr_t> (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_13;
		L_13 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_12, NULL);
		NullCheck(L_13);
		MethodInfo_t* L_14;
		L_14 = Type_GetMethod_m9E66B5053F150537A74C490C1DA5174A7875189D(L_13, _stringLiteral4EC998918E53FB3C460B5C3148F7ABAADC6EF6A1, ((int32_t)36), NULL);
		__this->___m_CreateIndexedCollectionPropertyBagMethod = L_14;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_CreateIndexedCollectionPropertyBagMethod), (void*)L_14);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_15 = { reinterpret_cast<intptr_t> (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_0_0_0_var) };
		Type_t* L_16;
		L_16 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_15, NULL);
		NullCheck(L_16);
		MethodInfo_t* L_17;
		L_17 = Type_GetMethod_m9E66B5053F150537A74C490C1DA5174A7875189D(L_16, _stringLiteral0F9BE39FE19AFE46EEE9236F011A9433EAA21F69, ((int32_t)36), NULL);
		__this->___m_CreateSetPropertyBagMethod = L_17;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_CreateSetPropertyBagMethod), (void*)L_17);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_18 = { reinterpret_cast<intptr_t> (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_0_0_0_var) };
		Type_t* L_19;
		L_19 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_18, NULL);
		NullCheck(L_19);
		MethodInfo_t* L_20;
		L_20 = Type_GetMethod_m9E66B5053F150537A74C490C1DA5174A7875189D(L_19, _stringLiteralFE09717218C9DB873D50817C8EE39FA626116789, ((int32_t)36), NULL);
		__this->___m_CreateKeyValueCollectionPropertyBagMethod = L_20;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_CreateKeyValueCollectionPropertyBagMethod), (void*)L_20);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_21 = { reinterpret_cast<intptr_t> (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_0_0_0_var) };
		Type_t* L_22;
		L_22 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_21, NULL);
		NullCheck(L_22);
		MethodInfo_t* L_23;
		L_23 = Type_GetMethod_m9E66B5053F150537A74C490C1DA5174A7875189D(L_22, _stringLiteralF2C108AA83BE591285C6E2190E1EEB2F1ADBAB24, ((int32_t)36), NULL);
		__this->___m_CreateKeyValuePairPropertyBagMethod = L_23;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_CreateKeyValuePairPropertyBagMethod), (void*)L_23);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_24 = { reinterpret_cast<intptr_t> (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_0_0_0_var) };
		Type_t* L_25;
		L_25 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_24, NULL);
		NullCheck(L_25);
		MethodInfo_t* L_26;
		L_26 = Type_GetMethod_m9E66B5053F150537A74C490C1DA5174A7875189D(L_25, _stringLiteral5E2CF53B436B05607F3EE43FB81D75975A00662A, ((int32_t)36), NULL);
		__this->___m_CreateArrayPropertyBagMethod = L_26;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_CreateArrayPropertyBagMethod), (void*)L_26);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_27 = { reinterpret_cast<intptr_t> (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_0_0_0_var) };
		Type_t* L_28;
		L_28 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_27, NULL);
		NullCheck(L_28);
		MethodInfo_t* L_29;
		L_29 = Type_GetMethod_m9E66B5053F150537A74C490C1DA5174A7875189D(L_28, _stringLiteralC8FA9ED3100EEB33720799E0BF36802352500BBE, ((int32_t)36), NULL);
		__this->___m_CreateListPropertyBagMethod = L_29;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_CreateListPropertyBagMethod), (void*)L_29);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_30 = { reinterpret_cast<intptr_t> (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_0_0_0_var) };
		Type_t* L_31;
		L_31 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_30, NULL);
		NullCheck(L_31);
		MethodInfo_t* L_32;
		L_32 = Type_GetMethod_m9E66B5053F150537A74C490C1DA5174A7875189D(L_31, _stringLiteralAAA7BDF9D882584A96DB5561B9FA7C14404B3824, ((int32_t)36), NULL);
		__this->___m_CreateHashSetPropertyBagMethod = L_32;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_CreateHashSetPropertyBagMethod), (void*)L_32);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_33 = { reinterpret_cast<intptr_t> (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF_0_0_0_var) };
		Type_t* L_34;
		L_34 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_33, NULL);
		NullCheck(L_34);
		MethodInfo_t* L_35;
		L_35 = Type_GetMethod_m9E66B5053F150537A74C490C1DA5174A7875189D(L_34, _stringLiteralCE876740FAE0F74584A26D196FFCDCF9055FFB6A, ((int32_t)36), NULL);
		__this->___m_CreateDictionaryPropertyBagMethod = L_35;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_CreateDictionaryPropertyBagMethod), (void*)L_35);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* ReflectedPropertyBagProvider_CreatePropertyBag_m89CDB23B3D426162B7641A4BB14DB3D71FE56CB2 (ReflectedPropertyBagProvider_tFAAE287D4BED97ACD16EECAEA6DD5DE506D157CF* __this, Type_t* ___0_type, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IPropertyBag_t602D45F19F9BFA3E370929AC5023258AAF62E8C2_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	RuntimeObject* V_1 = NULL;
	{
		Type_t* L_0 = ___0_type;
		NullCheck(L_0);
		bool L_1;
		L_1 = VirtualFuncInvoker0< bool >::Invoke(41, L_0);
		V_0 = L_1;
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_000f;
		}
	}
	{
		V_1 = (RuntimeObject*)NULL;
		goto IL_0033;
	}

IL_000f:
	{
		MethodInfo_t* L_3 = __this->___m_CreatePropertyBagMethod;
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_4 = (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)SZArrayNew(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var, (uint32_t)1);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_5 = L_4;
		Type_t* L_6 = ___0_type;
		NullCheck(L_5);
		ArrayElementTypeCheck (L_5, L_6);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(0), (Type_t*)L_6);
		NullCheck(L_3);
		MethodInfo_t* L_7;
		L_7 = VirtualFuncInvoker1< MethodInfo_t*, TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* >::Invoke(42, L_3, L_5);
		NullCheck(L_7);
		RuntimeObject* L_8;
		L_8 = MethodBase_Invoke_mEEF3218648F111A8C338001A7804091A0747C826(L_7, __this, (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)NULL, NULL);
		V_1 = ((RuntimeObject*)Castclass((RuntimeObject*)L_8, IPropertyBag_t602D45F19F9BFA3E370929AC5023258AAF62E8C2_il2cpp_TypeInfo_var));
		goto IL_0033;
	}

IL_0033:
	{
		RuntimeObject* L_9 = V_1;
		return L_9;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* ReflectedPropertyBagProvider_GetPropertyMembers_mCCEF82F4B8D8F8416FFE7703B4C797CCA16436D5 (Type_t* ___0_type, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* L_0 = (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D*)il2cpp_codegen_object_new(U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D_il2cpp_TypeInfo_var);
		U3CGetPropertyMembersU3Ed__22__ctor_mFCA3444C6665B9FD96AF049C35FAD5D1D330BBB2(L_0, ((int32_t)-2), NULL);
		U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* L_1 = L_0;
		Type_t* L_2 = ___0_type;
		NullCheck(L_1);
		L_1->___U3CU3E3__type = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E3__type), (void*)L_2);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ReflectedPropertyBagProvider_IsValidMember_m942BF637D7B010D3702E74009ABAA35CEF0624F2 (MemberInfo_t* ___0_memberInfo, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FieldInfo_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PropertyInfo_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FieldInfo_t* V_0 = NULL;
	PropertyInfo_t* V_1 = NULL;
	MemberInfo_t* V_2 = NULL;
	MemberInfo_t* V_3 = NULL;
	bool V_4 = false;
	int32_t G_B7_0 = 0;
	int32_t G_B13_0 = 0;
	{
		MemberInfo_t* L_0 = ___0_memberInfo;
		V_3 = L_0;
		MemberInfo_t* L_1 = V_3;
		V_2 = L_1;
		MemberInfo_t* L_2 = V_2;
		V_0 = ((FieldInfo_t*)IsInstClass((RuntimeObject*)L_2, FieldInfo_t_il2cpp_TypeInfo_var));
		FieldInfo_t* L_3 = V_0;
		if (L_3)
		{
			goto IL_001b;
		}
	}
	{
		MemberInfo_t* L_4 = V_2;
		V_1 = ((PropertyInfo_t*)IsInstClass((RuntimeObject*)L_4, PropertyInfo_t_il2cpp_TypeInfo_var));
		PropertyInfo_t* L_5 = V_1;
		if (L_5)
		{
			goto IL_0037;
		}
	}
	{
		goto IL_0066;
	}

IL_001b:
	{
		goto IL_001d;
	}

IL_001d:
	{
		FieldInfo_t* L_6 = V_0;
		NullCheck(L_6);
		bool L_7;
		L_7 = FieldInfo_get_IsStatic_mEBBEB7B19A48D3E11BE830F3704C131A681F6139(L_6, NULL);
		if (L_7)
		{
			goto IL_0032;
		}
	}
	{
		FieldInfo_t* L_8 = V_0;
		NullCheck(L_8);
		Type_t* L_9;
		L_9 = VirtualFuncInvoker0< Type_t* >::Invoke(17, L_8);
		bool L_10;
		L_10 = ReflectedPropertyBagProvider_IsValidPropertyType_mFBA6F8CF9F5B94652229D6A151B5302C460F05BF(L_9, NULL);
		G_B7_0 = ((int32_t)(L_10));
		goto IL_0033;
	}

IL_0032:
	{
		G_B7_0 = 0;
	}

IL_0033:
	{
		V_4 = (bool)G_B7_0;
		goto IL_006b;
	}

IL_0037:
	{
		goto IL_0039;
	}

IL_0039:
	{
		PropertyInfo_t* L_11 = V_1;
		NullCheck(L_11);
		MethodInfo_t* L_12;
		L_12 = VirtualFuncInvoker0< MethodInfo_t* >::Invoke(20, L_11);
		bool L_13;
		L_13 = MethodInfo_op_Inequality_mB73597A1FCC2F906DBCADDEC68A1B7D5B7E89FA8((MethodInfo_t*)NULL, L_12, NULL);
		if (!L_13)
		{
			goto IL_0061;
		}
	}
	{
		PropertyInfo_t* L_14 = V_1;
		NullCheck(L_14);
		MethodInfo_t* L_15;
		L_15 = VirtualFuncInvoker0< MethodInfo_t* >::Invoke(20, L_14);
		NullCheck(L_15);
		bool L_16;
		L_16 = MethodBase_get_IsStatic_mD2921396167EC4F99E2ADC46C39CCCEC3CD0E16E(L_15, NULL);
		if (L_16)
		{
			goto IL_0061;
		}
	}
	{
		PropertyInfo_t* L_17 = V_1;
		NullCheck(L_17);
		Type_t* L_18;
		L_18 = VirtualFuncInvoker0< Type_t* >::Invoke(16, L_17);
		bool L_19;
		L_19 = ReflectedPropertyBagProvider_IsValidPropertyType_mFBA6F8CF9F5B94652229D6A151B5302C460F05BF(L_18, NULL);
		G_B13_0 = ((int32_t)(L_19));
		goto IL_0062;
	}

IL_0061:
	{
		G_B13_0 = 0;
	}

IL_0062:
	{
		V_4 = (bool)G_B13_0;
		goto IL_006b;
	}

IL_0066:
	{
		V_4 = (bool)0;
		goto IL_006b;
	}

IL_006b:
	{
		bool L_20 = V_4;
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ReflectedPropertyBagProvider_IsValidPropertyType_mFBA6F8CF9F5B94652229D6A151B5302C460F05BF (Type_t* ___0_type, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerable_All_TisType_t_m9EA20839AE686AA0B1927379BF8642A005ACFDE5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Func_2_t7AF8146EC94DFCBB0F1B3E70111C1FB21D39F00E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ReflectedPropertyBagProvider_IsValidPropertyType_mFBA6F8CF9F5B94652229D6A151B5302C460F05BF_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	int32_t G_B5_0 = 0;
	{
		Type_t* L_0 = ___0_type;
		NullCheck(L_0);
		bool L_1;
		L_1 = Type_get_IsPointer_mC8AAAFEC4E4CEA59DAD0032B85D1BB224763278B(L_0, NULL);
		V_0 = L_1;
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_000f;
		}
	}
	{
		V_1 = (bool)0;
		goto IL_0034;
	}

IL_000f:
	{
		Type_t* L_3 = ___0_type;
		NullCheck(L_3);
		bool L_4;
		L_4 = VirtualFuncInvoker0< bool >::Invoke(40, L_3);
		if (!L_4)
		{
			goto IL_0030;
		}
	}
	{
		Type_t* L_5 = ___0_type;
		NullCheck(L_5);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_6;
		L_6 = VirtualFuncInvoker0< TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* >::Invoke(50, L_5);
		Func_2_t7AF8146EC94DFCBB0F1B3E70111C1FB21D39F00E* L_7 = (Func_2_t7AF8146EC94DFCBB0F1B3E70111C1FB21D39F00E*)il2cpp_codegen_object_new(Func_2_t7AF8146EC94DFCBB0F1B3E70111C1FB21D39F00E_il2cpp_TypeInfo_var);
		Func_2__ctor_mAFDFA2B152082BBF5E0626BF143EDACD61DE9D74(L_7, NULL, (intptr_t)((void*)ReflectedPropertyBagProvider_IsValidPropertyType_mFBA6F8CF9F5B94652229D6A151B5302C460F05BF_RuntimeMethod_var), NULL);
		bool L_8;
		L_8 = Enumerable_All_TisType_t_m9EA20839AE686AA0B1927379BF8642A005ACFDE5((RuntimeObject*)L_6, L_7, Enumerable_All_TisType_t_m9EA20839AE686AA0B1927379BF8642A005ACFDE5_RuntimeMethod_var);
		G_B5_0 = ((int32_t)(L_8));
		goto IL_0031;
	}

IL_0030:
	{
		G_B5_0 = 1;
	}

IL_0031:
	{
		V_1 = (bool)G_B5_0;
		goto IL_0034;
	}

IL_0034:
	{
		bool L_9 = V_1;
		return L_9;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__cctor_mB26D9D572137F1C808D3D94430FC54F4BE1E2079 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523* L_0 = (U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523*)il2cpp_codegen_object_new(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var);
		U3CU3Ec__ctor_m1B468CA26848A620321355E672EED6B1E6713535(L_0, NULL);
		((U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var))->___U3CU3E9 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var))->___U3CU3E9), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m1B468CA26848A620321355E672EED6B1E6713535 (U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CU3Ec_U3C_ctorU3Eb__10_0_m98A9F8B7DD2A14EBBB86606DCFE2CB988291758F (U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523* __this, MethodInfo_t* ___0_x, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7E03D76FD473371698842C3641D0E7A9547686ED);
		s_Il2CppMethodInitialized = true;
	}
	int32_t G_B3_0 = 0;
	{
		MethodInfo_t* L_0 = ___0_x;
		NullCheck(L_0);
		String_t* L_1;
		L_1 = VirtualFuncInvoker0< String_t* >::Invoke(8, L_0);
		bool L_2;
		L_2 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_1, _stringLiteral7E03D76FD473371698842C3641D0E7A9547686ED, NULL);
		if (!L_2)
		{
			goto IL_001a;
		}
	}
	{
		MethodInfo_t* L_3 = ___0_x;
		NullCheck(L_3);
		bool L_4;
		L_4 = VirtualFuncInvoker0< bool >::Invoke(26, L_3);
		G_B3_0 = ((int32_t)(L_4));
		goto IL_001b;
	}

IL_001a:
	{
		G_B3_0 = 0;
	}

IL_001b:
	{
		return (bool)G_B3_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t U3CU3Ec_U3CGetPropertyMembersU3Eb__22_0_m50914E0FCA3654CF9091BFCA37780956B2CA0136 (U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523* __this, MemberInfo_t* ___0_x, const RuntimeMethod* method) 
{
	{
		MemberInfo_t* L_0 = ___0_x;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = VirtualFuncInvoker0< int32_t >::Invoke(15, L_0);
		return L_1;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CGetPropertyMembersU3Ed__22__ctor_mFCA3444C6665B9FD96AF049C35FAD5D1D330BBB2 (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		int32_t L_1;
		L_1 = Environment_get_CurrentManagedThreadId_m66483AADCCC13272EBDCD94D31D2E52603C24BDF(NULL);
		__this->___U3CU3El__initialThreadId = L_1;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CGetPropertyMembersU3Ed__22_System_IDisposable_Dispose_m9F9302585189A3F427CBA6992B1257DE344119C4 (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		int32_t L_1 = V_0;
		if ((((int32_t)L_1) == ((int32_t)((int32_t)-3))))
		{
			goto IL_0016;
		}
	}
	{
		goto IL_000e;
	}

IL_000e:
	{
		int32_t L_2 = V_0;
		if ((!(((uint32_t)((int32_t)il2cpp_codegen_subtract(L_2, 1))) > ((uint32_t)2))))
		{
			goto IL_0016;
		}
	}
	{
		goto IL_0022;
	}

IL_0016:
	{
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_0019:
			{
				U3CGetPropertyMembersU3Ed__22_U3CU3Em__Finally1_m199A76100E9211640529676C30A85BD1060498D4(__this, NULL);
				return;
			}
		});
		try
		{
			goto IL_0020;
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0020:
	{
		goto IL_0022;
	}

IL_0022:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3CGetPropertyMembersU3Ed__22_MoveNext_m729AF376AC4F43C28BD1BAA7792CE1D0C9C2EDB9 (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CustomAttributeExtensions_GetCustomAttribute_TisCreatePropertyAttribute_t481619BD84F5E29767718DE127DD1E781A3B6F92_mD72676F02AEB5E69DC033D1585E2106D0A40B8DB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CustomAttributeExtensions_GetCustomAttribute_TisDontCreatePropertyAttribute_t759A6C6F524F0E05DEFD69A827104FEC2B648C0E_m3C664BC58E8B561E56DB4E2FEB7BFA8FF2D4DFB8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CustomAttributeExtensions_GetCustomAttribute_TisNonSerializedAttribute_t3E57D1A7E7F31DE192694AB4C8927BAE7D1BE177_m2ED36C703953CABE08AA5B122C7604D9C2733021_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CustomAttributeExtensions_GetCustomAttribute_TisSerializeField_t18B4EDCAA7D3D570AAB8A5CCBF29B2CEA7A3CA9C_m8CA1E6E172CEFA60B58858DD7FFC13D340EC18E3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerable_OrderBy_TisMemberInfo_t_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_mE25DD086E1B3B32ABF6B1CC2AE0323EA6782FF91_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FieldInfo_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IEnumerable_1_t9BFC4EA32B04B96A5BB13A056B7E299ADC431143_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IEnumerator_1_t17A98E9C91AD59AC8DCA7D9C70E659E9F6583901_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RuntimeObject_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3CGetPropertyMembersU3Eb__22_0_m50914E0FCA3654CF9091BFCA37780956B2CA0136_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	int32_t V_1 = 0;
	bool V_2 = false;
	bool V_3 = false;
	bool V_4 = false;
	bool V_5 = false;
	bool V_6 = false;
	bool V_7 = false;
	bool V_8 = false;
	bool V_9 = false;
	bool V_10 = false;
	Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4* G_B10_0 = NULL;
	MemberInfoU5BU5D_t4CB6970BB166E8E1CFB06152B2A2284971873053* G_B10_1 = NULL;
	U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* G_B10_2 = NULL;
	Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4* G_B9_0 = NULL;
	MemberInfoU5BU5D_t4CB6970BB166E8E1CFB06152B2A2284971873053* G_B9_1 = NULL;
	U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* G_B9_2 = NULL;
	int32_t G_B14_0 = 0;
	int32_t G_B33_0 = 0;
	int32_t G_B41_0 = 0;
	{
		auto __finallyBlock = il2cpp::utils::Fault([&]
		{

FAULT_02b2:
			{
				U3CGetPropertyMembersU3Ed__22_System_IDisposable_Dispose_m9F9302585189A3F427CBA6992B1257DE344119C4(__this, NULL);
				return;
			}
		});
		try
		{
			{
				int32_t L_0 = __this->___U3CU3E1__state;
				V_1 = L_0;
				int32_t L_1 = V_1;
				switch (L_1)
				{
					case 0:
					{
						goto IL_001f_1;
					}
					case 1:
					{
						goto IL_0021_1;
					}
					case 2:
					{
						goto IL_0026_1;
					}
					case 3:
					{
						goto IL_002b_1;
					}
				}
			}
			{
				goto IL_0030_1;
			}

IL_001f_1:
			{
				goto IL_0037_1;
			}

IL_0021_1:
			{
				goto IL_0198_1;
			}

IL_0026_1:
			{
				goto IL_01de_1;
			}

IL_002b_1:
			{
				goto IL_0230_1;
			}

IL_0030_1:
			{
				V_0 = (bool)0;
				goto IL_02ba;
			}

IL_0037_1:
			{
				__this->___U3CU3E1__state = (-1);
			}

IL_003f_1:
			{
				Type_t* L_2 = __this->___type;
				NullCheck(L_2);
				MemberInfoU5BU5D_t4CB6970BB166E8E1CFB06152B2A2284971873053* L_3;
				L_3 = VirtualFuncInvoker1< MemberInfoU5BU5D_t4CB6970BB166E8E1CFB06152B2A2284971873053*, int32_t >::Invoke(91, L_2, ((int32_t)52));
				il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var);
				Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4* L_4 = ((U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var))->___U3CU3E9__22_0;
				Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4* L_5 = L_4;
				if (L_5)
				{
					G_B10_0 = L_5;
					G_B10_1 = L_3;
					G_B10_2 = __this;
					goto IL_006d_1;
				}
				G_B9_0 = L_5;
				G_B9_1 = L_3;
				G_B9_2 = __this;
			}
			{
				il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var);
				U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523* L_6 = ((U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var))->___U3CU3E9;
				Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4* L_7 = (Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4*)il2cpp_codegen_object_new(Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4_il2cpp_TypeInfo_var);
				Func_2__ctor_mCDDD46750A45128C47D438D81E5E88C718599821(L_7, L_6, (intptr_t)((void*)U3CU3Ec_U3CGetPropertyMembersU3Eb__22_0_m50914E0FCA3654CF9091BFCA37780956B2CA0136_RuntimeMethod_var), NULL);
				Func_2_t365AD77BFE11D4A2334553DF8D920010057AE7D4* L_8 = L_7;
				((U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var))->___U3CU3E9__22_0 = L_8;
				Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_t6D8C492F99FC36E8E3C273660058AF34514E0523_il2cpp_TypeInfo_var))->___U3CU3E9__22_0), (void*)L_8);
				G_B10_0 = L_8;
				G_B10_1 = G_B9_1;
				G_B10_2 = G_B9_2;
			}

IL_006d_1:
			{
				RuntimeObject* L_9;
				L_9 = Enumerable_OrderBy_TisMemberInfo_t_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_mE25DD086E1B3B32ABF6B1CC2AE0323EA6782FF91((RuntimeObject*)G_B10_1, G_B10_0, Enumerable_OrderBy_TisMemberInfo_t_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_mE25DD086E1B3B32ABF6B1CC2AE0323EA6782FF91_RuntimeMethod_var);
				NullCheck(G_B10_2);
				G_B10_2->___U3CmembersU3E5__1 = L_9;
				Il2CppCodeGenWriteBarrier((void**)(&G_B10_2->___U3CmembersU3E5__1), (void*)L_9);
				RuntimeObject* L_10 = __this->___U3CmembersU3E5__1;
				NullCheck(L_10);
				RuntimeObject* L_11;
				L_11 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(0, IEnumerable_1_t9BFC4EA32B04B96A5BB13A056B7E299ADC431143_il2cpp_TypeInfo_var, L_10);
				__this->___U3CU3Es__2 = L_11;
				Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3Es__2), (void*)L_11);
				__this->___U3CU3E1__state = ((int32_t)-3);
				goto IL_0248_1;
			}

IL_0096_1:
			{
				RuntimeObject* L_12 = __this->___U3CU3Es__2;
				NullCheck(L_12);
				MemberInfo_t* L_13;
				L_13 = InterfaceFuncInvoker0< MemberInfo_t* >::Invoke(0, IEnumerator_1_t17A98E9C91AD59AC8DCA7D9C70E659E9F6583901_il2cpp_TypeInfo_var, L_12);
				__this->___U3CmemberU3E5__3 = L_13;
				Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CmemberU3E5__3), (void*)L_13);
				MemberInfo_t* L_14 = __this->___U3CmemberU3E5__3;
				NullCheck(L_14);
				int32_t L_15;
				L_15 = VirtualFuncInvoker0< int32_t >::Invoke(7, L_14);
				if ((((int32_t)L_15) == ((int32_t)4)))
				{
					goto IL_00ca_1;
				}
			}
			{
				MemberInfo_t* L_16 = __this->___U3CmemberU3E5__3;
				NullCheck(L_16);
				int32_t L_17;
				L_17 = VirtualFuncInvoker0< int32_t >::Invoke(7, L_16);
				G_B14_0 = ((((int32_t)((((int32_t)L_17) == ((int32_t)((int32_t)16)))? 1 : 0)) == ((int32_t)0))? 1 : 0);
				goto IL_00cb_1;
			}

IL_00ca_1:
			{
				G_B14_0 = 0;
			}

IL_00cb_1:
			{
				V_2 = (bool)G_B14_0;
				bool L_18 = V_2;
				if (!L_18)
				{
					goto IL_00d5_1;
				}
			}
			{
				goto IL_0248_1;
			}

IL_00d5_1:
			{
				MemberInfo_t* L_19 = __this->___U3CmemberU3E5__3;
				NullCheck(L_19);
				Type_t* L_20;
				L_20 = VirtualFuncInvoker0< Type_t* >::Invoke(9, L_19);
				Type_t* L_21 = __this->___type;
				il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
				bool L_22;
				L_22 = Type_op_Inequality_m83209C7BB3C05DFBEA3B6199B0BEFE8037301172(L_20, L_21, NULL);
				V_3 = L_22;
				bool L_23 = V_3;
				if (!L_23)
				{
					goto IL_00f5_1;
				}
			}
			{
				goto IL_0248_1;
			}

IL_00f5_1:
			{
				MemberInfo_t* L_24 = __this->___U3CmemberU3E5__3;
				bool L_25;
				L_25 = ReflectedPropertyBagProvider_IsValidMember_m942BF637D7B010D3702E74009ABAA35CEF0624F2(L_24, NULL);
				V_4 = (bool)((((int32_t)L_25) == ((int32_t)0))? 1 : 0);
				bool L_26 = V_4;
				if (!L_26)
				{
					goto IL_010f_1;
				}
			}
			{
				goto IL_0248_1;
			}

IL_010f_1:
			{
				MemberInfo_t* L_27 = __this->___U3CmemberU3E5__3;
				DontCreatePropertyAttribute_t759A6C6F524F0E05DEFD69A827104FEC2B648C0E* L_28;
				L_28 = CustomAttributeExtensions_GetCustomAttribute_TisDontCreatePropertyAttribute_t759A6C6F524F0E05DEFD69A827104FEC2B648C0E_m3C664BC58E8B561E56DB4E2FEB7BFA8FF2D4DFB8(L_27, CustomAttributeExtensions_GetCustomAttribute_TisDontCreatePropertyAttribute_t759A6C6F524F0E05DEFD69A827104FEC2B648C0E_m3C664BC58E8B561E56DB4E2FEB7BFA8FF2D4DFB8_RuntimeMethod_var);
				__this->___U3ChasDontCreatePropertyAttributeU3E5__4 = (bool)((!(((RuntimeObject*)(DontCreatePropertyAttribute_t759A6C6F524F0E05DEFD69A827104FEC2B648C0E*)L_28) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
				MemberInfo_t* L_29 = __this->___U3CmemberU3E5__3;
				CreatePropertyAttribute_t481619BD84F5E29767718DE127DD1E781A3B6F92* L_30;
				L_30 = CustomAttributeExtensions_GetCustomAttribute_TisCreatePropertyAttribute_t481619BD84F5E29767718DE127DD1E781A3B6F92_mD72676F02AEB5E69DC033D1585E2106D0A40B8DB(L_29, CustomAttributeExtensions_GetCustomAttribute_TisCreatePropertyAttribute_t481619BD84F5E29767718DE127DD1E781A3B6F92_mD72676F02AEB5E69DC033D1585E2106D0A40B8DB_RuntimeMethod_var);
				__this->___U3ChasCreatePropertyAttributeU3E5__5 = (bool)((!(((RuntimeObject*)(CreatePropertyAttribute_t481619BD84F5E29767718DE127DD1E781A3B6F92*)L_30) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
				MemberInfo_t* L_31 = __this->___U3CmemberU3E5__3;
				NonSerializedAttribute_t3E57D1A7E7F31DE192694AB4C8927BAE7D1BE177* L_32;
				L_32 = CustomAttributeExtensions_GetCustomAttribute_TisNonSerializedAttribute_t3E57D1A7E7F31DE192694AB4C8927BAE7D1BE177_m2ED36C703953CABE08AA5B122C7604D9C2733021(L_31, CustomAttributeExtensions_GetCustomAttribute_TisNonSerializedAttribute_t3E57D1A7E7F31DE192694AB4C8927BAE7D1BE177_m2ED36C703953CABE08AA5B122C7604D9C2733021_RuntimeMethod_var);
				__this->___U3ChasNonSerializedAttributeU3E5__6 = (bool)((!(((RuntimeObject*)(NonSerializedAttribute_t3E57D1A7E7F31DE192694AB4C8927BAE7D1BE177*)L_32) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
				MemberInfo_t* L_33 = __this->___U3CmemberU3E5__3;
				SerializeField_t18B4EDCAA7D3D570AAB8A5CCBF29B2CEA7A3CA9C* L_34;
				L_34 = CustomAttributeExtensions_GetCustomAttribute_TisSerializeField_t18B4EDCAA7D3D570AAB8A5CCBF29B2CEA7A3CA9C_m8CA1E6E172CEFA60B58858DD7FFC13D340EC18E3(L_33, CustomAttributeExtensions_GetCustomAttribute_TisSerializeField_t18B4EDCAA7D3D570AAB8A5CCBF29B2CEA7A3CA9C_m8CA1E6E172CEFA60B58858DD7FFC13D340EC18E3_RuntimeMethod_var);
				__this->___U3ChasSerializedFieldAttributeU3E5__7 = (bool)((!(((RuntimeObject*)(SerializeField_t18B4EDCAA7D3D570AAB8A5CCBF29B2CEA7A3CA9C*)L_34) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
				bool L_35 = __this->___U3ChasDontCreatePropertyAttributeU3E5__4;
				V_5 = L_35;
				bool L_36 = V_5;
				if (!L_36)
				{
					goto IL_0171_1;
				}
			}
			{
				goto IL_0248_1;
			}

IL_0171_1:
			{
				bool L_37 = __this->___U3ChasCreatePropertyAttributeU3E5__5;
				V_6 = L_37;
				bool L_38 = V_6;
				if (!L_38)
				{
					goto IL_01a5_1;
				}
			}
			{
				MemberInfo_t* L_39 = __this->___U3CmemberU3E5__3;
				__this->___U3CU3E2__current = L_39;
				Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_39);
				__this->___U3CU3E1__state = 1;
				V_0 = (bool)1;
				goto IL_02ba;
			}

IL_0198_1:
			{
				__this->___U3CU3E1__state = ((int32_t)-3);
				goto IL_0248_1;
			}

IL_01a5_1:
			{
				bool L_40 = __this->___U3ChasNonSerializedAttributeU3E5__6;
				V_7 = L_40;
				bool L_41 = V_7;
				if (!L_41)
				{
					goto IL_01b7_1;
				}
			}
			{
				goto IL_0248_1;
			}

IL_01b7_1:
			{
				bool L_42 = __this->___U3ChasSerializedFieldAttributeU3E5__7;
				V_8 = L_42;
				bool L_43 = V_8;
				if (!L_43)
				{
					goto IL_01e8_1;
				}
			}
			{
				MemberInfo_t* L_44 = __this->___U3CmemberU3E5__3;
				__this->___U3CU3E2__current = L_44;
				Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_44);
				__this->___U3CU3E1__state = 2;
				V_0 = (bool)1;
				goto IL_02ba;
			}

IL_01de_1:
			{
				__this->___U3CU3E1__state = ((int32_t)-3);
				goto IL_0248_1;
			}

IL_01e8_1:
			{
				MemberInfo_t* L_45 = __this->___U3CmemberU3E5__3;
				__this->___U3CfieldU3E5__8 = ((FieldInfo_t*)IsInstClass((RuntimeObject*)L_45, FieldInfo_t_il2cpp_TypeInfo_var));
				Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CfieldU3E5__8), (void*)((FieldInfo_t*)IsInstClass((RuntimeObject*)L_45, FieldInfo_t_il2cpp_TypeInfo_var)));
				FieldInfo_t* L_46 = __this->___U3CfieldU3E5__8;
				if (!L_46)
				{
					goto IL_020e_1;
				}
			}
			{
				FieldInfo_t* L_47 = __this->___U3CfieldU3E5__8;
				NullCheck(L_47);
				bool L_48;
				L_48 = FieldInfo_get_IsPublic_m73C84BEEAE6C993FACC6199B81DBF2B80D3810E5(L_47, NULL);
				G_B33_0 = ((int32_t)(L_48));
				goto IL_020f_1;
			}

IL_020e_1:
			{
				G_B33_0 = 0;
			}

IL_020f_1:
			{
				V_9 = (bool)G_B33_0;
				bool L_49 = V_9;
				if (!L_49)
				{
					goto IL_0239_1;
				}
			}
			{
				MemberInfo_t* L_50 = __this->___U3CmemberU3E5__3;
				__this->___U3CU3E2__current = L_50;
				Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_50);
				__this->___U3CU3E1__state = 3;
				V_0 = (bool)1;
				goto IL_02ba;
			}

IL_0230_1:
			{
				__this->___U3CU3E1__state = ((int32_t)-3);
			}

IL_0239_1:
			{
				__this->___U3CfieldU3E5__8 = (FieldInfo_t*)NULL;
				Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CfieldU3E5__8), (void*)(FieldInfo_t*)NULL);
				__this->___U3CmemberU3E5__3 = (MemberInfo_t*)NULL;
				Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CmemberU3E5__3), (void*)(MemberInfo_t*)NULL);
			}

IL_0248_1:
			{
				RuntimeObject* L_51 = __this->___U3CU3Es__2;
				NullCheck(L_51);
				bool L_52;
				L_52 = InterfaceFuncInvoker0< bool >::Invoke(0, IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA_il2cpp_TypeInfo_var, L_51);
				if (L_52)
				{
					goto IL_0096_1;
				}
			}
			{
				U3CGetPropertyMembersU3Ed__22_U3CU3Em__Finally1_m199A76100E9211640529676C30A85BD1060498D4(__this, NULL);
				__this->___U3CU3Es__2 = (RuntimeObject*)NULL;
				Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3Es__2), (void*)(RuntimeObject*)NULL);
				Type_t* L_53 = __this->___type;
				NullCheck(L_53);
				Type_t* L_54;
				L_54 = VirtualFuncInvoker0< Type_t* >::Invoke(113, L_53);
				__this->___type = L_54;
				Il2CppCodeGenWriteBarrier((void**)(&__this->___type), (void*)L_54);
				__this->___U3CmembersU3E5__1 = (RuntimeObject*)NULL;
				Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CmembersU3E5__1), (void*)(RuntimeObject*)NULL);
				Type_t* L_55 = __this->___type;
				il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
				bool L_56;
				L_56 = Type_op_Inequality_m83209C7BB3C05DFBEA3B6199B0BEFE8037301172(L_55, (Type_t*)NULL, NULL);
				if (!L_56)
				{
					goto IL_02a4_1;
				}
			}
			{
				Type_t* L_57 = __this->___type;
				RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_58 = { reinterpret_cast<intptr_t> (RuntimeObject_0_0_0_var) };
				il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
				Type_t* L_59;
				L_59 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_58, NULL);
				bool L_60;
				L_60 = Type_op_Inequality_m83209C7BB3C05DFBEA3B6199B0BEFE8037301172(L_57, L_59, NULL);
				G_B41_0 = ((int32_t)(L_60));
				goto IL_02a5_1;
			}

IL_02a4_1:
			{
				G_B41_0 = 0;
			}

IL_02a5_1:
			{
				V_10 = (bool)G_B41_0;
				bool L_61 = V_10;
				if (L_61)
				{
					goto IL_003f_1;
				}
			}
			{
				V_0 = (bool)0;
				goto IL_02ba;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_02ba:
	{
		bool L_62 = V_0;
		return L_62;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CGetPropertyMembersU3Ed__22_U3CU3Em__Finally1_m199A76100E9211640529676C30A85BD1060498D4 (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->___U3CU3E1__state = (-1);
		RuntimeObject* L_0 = __this->___U3CU3Es__2;
		if (!L_0)
		{
			goto IL_001b;
		}
	}
	{
		RuntimeObject* L_1 = __this->___U3CU3Es__2;
		NullCheck(L_1);
		InterfaceActionInvoker0::Invoke(0, IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var, L_1);
	}

IL_001b:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MemberInfo_t* U3CGetPropertyMembersU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_Reflection_MemberInfoU3E_get_Current_mB28BEAF4EB3FAB5E771DC9A6454DCE6A67741FD9 (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* __this, const RuntimeMethod* method) 
{
	{
		MemberInfo_t* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CGetPropertyMembersU3Ed__22_System_Collections_IEnumerator_Reset_mBF507A053052F0001483795394E045C4E2EB826C (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3CGetPropertyMembersU3Ed__22_System_Collections_IEnumerator_Reset_mBF507A053052F0001483795394E045C4E2EB826C_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CGetPropertyMembersU3Ed__22_System_Collections_IEnumerator_get_Current_mC30C3E93C92C552E330B9A3D6D239E72A0586BA0 (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* __this, const RuntimeMethod* method) 
{
	{
		MemberInfo_t* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CGetPropertyMembersU3Ed__22_System_Collections_Generic_IEnumerableU3CSystem_Reflection_MemberInfoU3E_GetEnumerator_mFB07F1A6491DB7EEA44C182930AD32C728599080 (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* V_0 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		if ((!(((uint32_t)L_0) == ((uint32_t)((int32_t)-2)))))
		{
			goto IL_0022;
		}
	}
	{
		int32_t L_1 = __this->___U3CU3El__initialThreadId;
		int32_t L_2;
		L_2 = Environment_get_CurrentManagedThreadId_m66483AADCCC13272EBDCD94D31D2E52603C24BDF(NULL);
		if ((!(((uint32_t)L_1) == ((uint32_t)L_2))))
		{
			goto IL_0022;
		}
	}
	{
		__this->___U3CU3E1__state = 0;
		V_0 = __this;
		goto IL_0029;
	}

IL_0022:
	{
		U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* L_3 = (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D*)il2cpp_codegen_object_new(U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D_il2cpp_TypeInfo_var);
		U3CGetPropertyMembersU3Ed__22__ctor_mFCA3444C6665B9FD96AF049C35FAD5D1D330BBB2(L_3, 0, NULL);
		V_0 = L_3;
	}

IL_0029:
	{
		U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* L_4 = V_0;
		Type_t* L_5 = __this->___U3CU3E3__type;
		NullCheck(L_4);
		L_4->___type = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_4->___type), (void*)L_5);
		U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3CGetPropertyMembersU3Ed__22_System_Collections_IEnumerable_GetEnumerator_mD6684E626A039A8014D140C3BEEBD24928F7D5ED (U3CGetPropertyMembersU3Ed__22_t6D8200BE8309AEA2B11C0A3857CB5C82C722CC9D* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0;
		L_0 = U3CGetPropertyMembersU3Ed__22_System_Collections_Generic_IEnumerableU3CSystem_Reflection_MemberInfoU3E_GetEnumerator_mFB07F1A6491DB7EEA44C182930AD32C728599080(__this, NULL);
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ReflectionUtilities_SanitizeMemberName_m00515F1FC752D0A52D90143E58D19A7D40D799AE (MemberInfo_t* ___0_info, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral20E39C3AB7068FAFD9E4B868E16D2E5BC64D4952);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral50639CAD49418C7B223CC529395C0E2A3892501C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral725B1CAFF9B49E1231FDA15B85166BBEFAA36A11);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEF8AE9E6CBCFDABA932FBEB4C85964F450F724F5);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D);
		s_Il2CppMethodInitialized = true;
	}
	String_t* V_0 = NULL;
	{
		MemberInfo_t* L_0 = ___0_info;
		NullCheck(L_0);
		String_t* L_1;
		L_1 = VirtualFuncInvoker0< String_t* >::Invoke(8, L_0);
		NullCheck(L_1);
		String_t* L_2;
		L_2 = String_Replace_mABDB7003A1D0AEDCAE9FF85E3DFFFBA752D2A166(L_1, _stringLiteralF3E84B722399601AD7E281754E917478AA9AD48D, _stringLiteral50639CAD49418C7B223CC529395C0E2A3892501C, NULL);
		NullCheck(L_2);
		String_t* L_3;
		L_3 = String_Replace_mABDB7003A1D0AEDCAE9FF85E3DFFFBA752D2A166(L_2, _stringLiteral725B1CAFF9B49E1231FDA15B85166BBEFAA36A11, _stringLiteral50639CAD49418C7B223CC529395C0E2A3892501C, NULL);
		NullCheck(L_3);
		String_t* L_4;
		L_4 = String_Replace_mABDB7003A1D0AEDCAE9FF85E3DFFFBA752D2A166(L_3, _stringLiteralEF8AE9E6CBCFDABA932FBEB4C85964F450F724F5, _stringLiteral50639CAD49418C7B223CC529395C0E2A3892501C, NULL);
		NullCheck(L_4);
		String_t* L_5;
		L_5 = String_Replace_mABDB7003A1D0AEDCAE9FF85E3DFFFBA752D2A166(L_4, _stringLiteral20E39C3AB7068FAFD9E4B868E16D2E5BC64D4952, _stringLiteral50639CAD49418C7B223CC529395C0E2A3892501C, NULL);
		V_0 = L_5;
		goto IL_0046;
	}

IL_0046:
	{
		String_t* L_6 = V_0;
		return L_6;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* FieldMember_get_Name_m9301F65E796E51B6F4EF0837AEB3EB9AD25293D6_inline (FieldMember_tFAEBD5181D8BB7C912174317D5AAE15D23B378BF* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___U3CNameU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* PropertyMember_get_Name_mCB0EBF067D82D0871C8051116D34A7D82EBE67FB_inline (PropertyMember_t4A07D86E6B1554D28A1679F276FA9F9A6E1FD984* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___U3CNameU3Ek__BackingField;
		return L_0;
	}
}
