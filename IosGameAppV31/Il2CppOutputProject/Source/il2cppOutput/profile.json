{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {"os": "Unix 15.5.0", "cpuCount": "12", "mappedPhysicalMemory": "147mb", "commandLine": "/Users/<USER>/Game_Projects/Backup_M2/字卡/AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/build/deploy_arm64/il2cpp.dll --compile-cpp --platform=iOS --baselib-directory=/Users/<USER>/Game_Projects/Backup_M2/字卡/AppCode/FlashCardApp/IosGameAppV31/Libraries --additional-defines=IL2CPP_DEBUG=0 --incremental-g-c-time-slice=3 --dotnetprofile=unityaot-macos --profiler-report --print-command-line --external-lib-il2-cpp=/Users/<USER>/Game_Projects/Backup_M2/字卡/AppCode/FlashCardApp/IosGameAppV31/Libraries/libil2cpp.a --generatedcppdir=Il2CppOutputProject/Source/il2cppOutput --architecture=arm64 --outputpath=/Users/<USER>/Library/Developer/Xcode/DerivedData/Unity-iPhone-cagjkwhygrnlrdhjvwgopjvjjsdo/Build/Products/ReleaseForRunning-iphoneos/libGameAssembly.a --cachedirectory=/Users/<USER>/Library/Developer/Xcode/DerivedData/Unity-iPhone-cagjkwhygrnlrdhjvwgopjvjjsdo/Build/Intermediates.noindex/Unity-iPhone.build/ReleaseForRunning-iphoneos/artifacts/arm64 --configuration=Release"}, "traceEvents": [{"pid": 36539, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "il2cpp_outer"}}, {"pid": 36539, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 36539, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "GC - Gen2"}}, {"pid": 36539, "tid": 12884901888, "ts": 1755217823195632, "dur": 6824, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Blocking GC"}}, {"pid": 36539, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "GC - Gen1"}}, {"pid": 36539, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "GC - Gen0"}}, {"pid": 36539, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": "Main thread"}}, {"pid": 36539, "tid": 1, "ts": 1755217818925813, "dur": 7607754, "ph": "X", "name": "il2cpp.exe", "args": {"analytics": "1"}}, {"pid": 36539, "tid": 1, "ts": 1755217818926933, "dur": 37097, "ph": "X", "name": "ParseArguments", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217818964031, "dur": 4784, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217818978335, "dur": 7546051, "ph": "X", "name": "BeeDriverRunner.ExecuteIl2Cpp", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217826527076, "dur": 5358, "ph": "X", "name": "Write Analytics", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217826533568, "dur": 12146, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217826547361, "dur": 1236, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 36539, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 36539, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 36539, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": "Main thread"}}, {"pid": 36539, "tid": 1, "ts": 1755217819041411, "dur": 7478043, "ph": "X", "name": "Build FinalProgram", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217819042655, "dur": 6373, "ph": "X", "name": "Writing build program input data", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217819049042, "dur": 61653, "ph": "X", "name": "Running build system backend", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217819110711, "dur": 753230, "ph": "X", "name": "Running build program", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217819863944, "dur": 6655448, "ph": "X", "name": "Running build system backend", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217826519414, "dur": 41, "ph": "X", "name": "Finishing", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217826524933, "dur": 1231, "ph": "X", "name": "Main thread", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1755217819085227, "dur": 1623, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755217819087019, "dur": 6500, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755217819093545, "dur": 90, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755217819093640, "dur": 510, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755217819094151, "dur": 223, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755217819094528, "dur": 6237, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1755217819093577, "dur": 578, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819094156, "dur": 184, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217819093577, "dur": 583, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217819094162, "dur": 65, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217819094227, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819093583, "dur": 581, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819094164, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755217819093590, "dur": 579, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755217819094169, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819093595, "dur": 576, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819094171, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819093601, "dur": 572, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819094173, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217819093606, "dur": 569, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217819094175, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819093612, "dur": 564, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819094176, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819093619, "dur": 560, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819094179, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819093625, "dur": 559, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819094184, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819093631, "dur": 557, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819094188, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819093636, "dur": 554, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819094190, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755217819101283, "dur": 65, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "il2cpp.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": "Main thread"}}, {"pid": 35942, "tid": 1, "ts": 1755217819127015, "dur": 729562, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1755217819127022, "dur": 70942, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1755217819275562, "dur": 8272, "ph": "X", "name": "SetupConversion", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1755217819701594, "dur": 127883, "ph": "X", "name": "AppPackage.Setup", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1755217819703443, "dur": 117965, "ph": "X", "name": "GeneratedCode.SetupSpecificConfiguration", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1755217819704083, "dur": 117187, "ph": "X", "name": "SetupSpecificConfigImpl libGameAssembly", "args": {"info": "release_iOS_arm64"}}, {"pid": 35942, "tid": 1, "ts": 1755217819836035, "dur": 2945, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1755217819838981, "dur": 17596, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1755217819840580, "dur": 14555, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1755217819861066, "dur": 1667, "ph": "X", "name": "Main thread", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1755217819860737, "dur": 2160, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1755217819901932, "dur": 19541, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755217819921478, "dur": 136, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755217819921638, "dur": 90, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755217819921733, "dur": 339, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755217819922073, "dur": 6582282, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755217826504378, "dur": 58, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755217826504533, "dur": 9117, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 0, "ts": 1755217826513650, "dur": 330, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "SaveScanCache"}}, {"pid": 12345, "tid": 1, "ts": 1755217819921659, "dur": 419, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819922079, "dur": 12352, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819934436, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819934862, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/AppleAuth.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819935002, "dur": 26554, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/AppleAuth.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819961766, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/type_traits.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819961823, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppSignature.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819962036, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-common.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819962088, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include/BaselibPlatformSpecificEnvironment.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819962158, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/ErrorCodes.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819962309, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/sparsehashtable.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819962383, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/sparseconfig.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819962479, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/sparsetable.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819962596, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/MetadataCache.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819962682, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Baselib.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819962753, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/String.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819962831, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/Finally.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819962966, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/Thread.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819963281, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_ReentrantLock.inl.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819963374, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppTypeHash.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819963444, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/MemoryUtils.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819963782, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericContextHash.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819963946, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_SystemFutex.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819964006, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Il2CppError.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819964136, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-metadata.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819964190, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericContextCompare.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819964301, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_WakeupFallbackStrategy.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819964443, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/VmStringUtils.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819964495, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_Lock_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819964654, "dur": 274, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Assert.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819964929, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/Debugger.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819965013, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericMethodHash.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819965189, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/KeyWrapper.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819965293, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Gcc.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819965364, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/ExceptionSupportStack.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819965424, "dur": 2255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Msvc.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819967833, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Assembly.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819961561, "dur": 6509, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819934861, "dur": 33209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/k3mzf4c7qjm4.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217819968095, "dur": 710, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__5.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819968805, "dur": 1535, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__5.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819970357, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System/Array.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819970544, "dur": 716, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System/Type.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819970343, "dur": 1053, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819968094, "dur": 3302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qtjl7ojk9avp.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217819971411, "dur": 3819, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819975235, "dur": 2318, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819977565, "dur": 310, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819971409, "dur": 6466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jqssrqarzwq7.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217819977892, "dur": 2552, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__16.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819980444, "dur": 994, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__16.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819981574, "dur": 320, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericInstCompare.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819981442, "dur": 454, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819977890, "dur": 4006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/eftoa8jx85ty.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217819981919, "dur": 533, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819982453, "dur": 1629, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819984108, "dur": 326, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Text/EncodingHelper.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819984550, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Text/Normalization.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217819984086, "dur": 695, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819981918, "dur": 2864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/loerukg8r1wh.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217819984827, "dur": 293, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/bouncywp71__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819985121, "dur": 494, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/bouncywp71__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819985619, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819984825, "dur": 913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vtpk8gsgzneh.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217819985752, "dur": 340, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/bouncywp71__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819986092, "dur": 725, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/bouncywp71__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819986823, "dur": 175, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819985750, "dur": 1248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/oqyrsijnp99y.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217819986998, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819987401, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityICallRegistration.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819987608, "dur": 832, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityICallRegistration.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819987399, "dur": 1142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jqcixe2eoey3.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217819988541, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819988734, "dur": 800, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819989534, "dur": 885, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819990424, "dur": 521, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819988733, "dur": 2212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/z49a2gns691v.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217819990973, "dur": 1005, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__9.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819991978, "dur": 1151, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__9.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819993133, "dur": 128, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819990972, "dur": 2289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/dzq66hwck0hc.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217819993278, "dur": 2903, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819996181, "dur": 1014, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819997199, "dur": 193, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819993277, "dur": 4115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/k8b9qusn3vkv.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217819997392, "dur": 575, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819997969, "dur": 946, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217819998915, "dur": 1297, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820000218, "dur": 156, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217819997968, "dur": 2406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ina1w3l7d1uc.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820000387, "dur": 2238, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820002626, "dur": 589, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820003289, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppSignature.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820003221, "dur": 315, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820000386, "dur": 3150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/j0xrcecpts8a.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820003555, "dur": 329, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820003884, "dur": 864, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820005032, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_Lock_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820004752, "dur": 405, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820003553, "dur": 1604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/6hss3hozbqzm.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820005226, "dur": 241, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ParticleSystemModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820005474, "dur": 307, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820005178, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/c78zr1n6kkjd.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820005822, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ImageConversionModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820005927, "dur": 329, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ImageConversionModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820006312, "dur": 317, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/VmThreadUtils.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820006263, "dur": 585, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820005819, "dur": 1030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/kcd4uuzsszem.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820006849, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820006931, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820007197, "dur": 288, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820007490, "dur": 286, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820006930, "dur": 847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qw18ex6urgkx.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820007818, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820007989, "dur": 370, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820008372, "dur": 539, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820007816, "dur": 1095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/drimggpn7qt1.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820008928, "dur": 641, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AssetBundleModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820009569, "dur": 166, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AssetBundleModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820009810, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_MsvcIntrinsics.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820010051, "dur": 299, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/libc_allocator_with_realloc.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820009738, "dur": 680, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820008926, "dur": 1492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/1saxyse7ywir.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820010435, "dur": 744, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__6.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820011179, "dur": 256, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__6.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820011577, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Assembly.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820011439, "dur": 240, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820010433, "dur": 1246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ckjktap6kjvk.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820011692, "dur": 431, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820012124, "dur": 782, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820012909, "dur": 140, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820011691, "dur": 1358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jt4mdnlh4ey3.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820013090, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820013341, "dur": 225, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820013573, "dur": 205, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820013088, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/obryrxj7iisj.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820013811, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Telemetry.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820013914, "dur": 427, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Telemetry.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820014538, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/MemoryUtils.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820014714, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Exception.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820014348, "dur": 516, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820013809, "dur": 1055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7hu4xwaghcu5.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820014900, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Environments.Internal.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820014994, "dur": 407, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Environments.Internal.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820015410, "dur": 270, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820014897, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/tplxvyebdixg.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820015681, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820015830, "dur": 459, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Analytics.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820016289, "dur": 765, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Analytics.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820017061, "dur": 238, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820015828, "dur": 1471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jbavk350ka3r.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820017300, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820017620, "dur": 4621, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__8.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820022241, "dur": 1065, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__8.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820023310, "dur": 130, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820017618, "dur": 5823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3g5w9d6ix3hf.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820023463, "dur": 275, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820023738, "dur": 2831, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820026673, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/mono/ThreadPool/threadpool-ms-io.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820026581, "dur": 340, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820023462, "dur": 3459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/aqqztq2tgzky.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820026957, "dur": 826, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__9.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820027783, "dur": 3081, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__9.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820030985, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/MarshalAlloc.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820031227, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Object.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820031481, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Gcc_Patch_PostInclude.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820030869, "dur": 726, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820026955, "dur": 4640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/j7nc7z7ml1c2.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820031626, "dur": 733, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820032359, "dur": 829, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820033192, "dur": 152, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820031624, "dur": 1720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qg9erc4x4wfl.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820033344, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820033521, "dur": 536, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__14.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820034057, "dur": 735, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__14.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820034796, "dur": 141, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820033520, "dur": 1417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3t4ngsqkzd5q.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820034954, "dur": 372, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820035326, "dur": 4324, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820039655, "dur": 86, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 1, "ts": 1755217820039966, "dur": 148, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820034953, "dur": 5161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/wqdv0thrpz08.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820040129, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Numerics.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820040378, "dur": 844, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Numerics.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820041228, "dur": 368, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820040128, "dur": 1469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/nr0eepvu3b1g.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820041638, "dur": 1330, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820042968, "dur": 1211, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820044292, "dur": 337, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/sparse_hash_map.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820044183, "dur": 504, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820041636, "dur": 3051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zubxv7ohy475.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820044688, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820044771, "dur": 4559, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Core__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820049331, "dur": 1470, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Core__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820050809, "dur": 314, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820044770, "dur": 6353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/002g8u40lxut.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820051135, "dur": 688, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820051823, "dur": 1148, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820053043, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tabledefs.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820052982, "dur": 415, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820051134, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bnwuj76a82p5.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820053431, "dur": 583, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppUnresolvedIndirectCallStubs.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820054014, "dur": 746, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppUnresolvedIndirectCallStubs.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820054943, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/MarshalAlloc.h"}}, {"pid": 12345, "tid": 1, "ts": 1755217820054767, "dur": 351, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820053429, "dur": 1690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/a66ojy59xjty.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820055119, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820055193, "dur": 338, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820055531, "dur": 1231, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820056770, "dur": 968, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820055191, "dur": 2548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qd2nmhtw5cy6.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820058374, "dur": 611770, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/qd2nmhtw5cy6.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820670243, "dur": 475, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__24.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820670718, "dur": 698, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__24.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217820671421, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217820670241, "dur": 1315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2ihgqom3vtmr.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217820674166, "dur": 1321123, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/2ihgqom3vtmr.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217821995398, "dur": 906, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__14.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217821996305, "dur": 605, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__14.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217821996915, "dur": 130, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217821995395, "dur": 1650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vghxh1wmtonw.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217821997091, "dur": 302, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__13.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217821997394, "dur": 527, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__13.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217821997925, "dur": 120, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217821997089, "dur": 957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/a1d91ripq0bd.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217821998064, "dur": 356, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__12.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217821998420, "dur": 715, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__12.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217821999139, "dur": 120, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217821998063, "dur": 1197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9stshc2qmyux.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217821999275, "dur": 371, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217821999646, "dur": 863, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822000512, "dur": 118, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217821999273, "dur": 1357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/odq412ntjaje.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217822000645, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__10.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822000840, "dur": 418, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__10.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822001261, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217822000643, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3xmgmt9vgm7u.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217822001397, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822001587, "dur": 369, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822001959, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217822001396, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zk6g68mglqjr.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217822002091, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822002271, "dur": 360, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822002634, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217822002089, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/064b1wwkcrp2.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217822002764, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822002814, "dur": 175, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822002992, "dur": 118, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217822002763, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/q6ujqn6cl2ft.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217822003122, "dur": 409, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822003531, "dur": 817, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822004351, "dur": 120, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217822003120, "dur": 1351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5qrvaesfie49.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217822004484, "dur": 398, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822004882, "dur": 809, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822005695, "dur": 120, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217822004483, "dur": 1332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4i7eqybhecnu.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217822005830, "dur": 459, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822006289, "dur": 1089, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822007381, "dur": 118, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217822005829, "dur": 1671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/j33qapxjbobb.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217822007514, "dur": 333, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822007847, "dur": 655, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1755217822008505, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755217822007513, "dur": 1111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/w8pf0tld2g1a.o"}}, {"pid": 12345, "tid": 1, "ts": 1755217822008863, "dur": 282, "ph": "X", "name": "CopyFiles", "args": {"detail": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Unity-iPhone-cagjkwhygrnlrdhjvwgopjvjjsdo/Build/Products/ReleaseForRunning-iphoneos/libil2cpp.a"}}, {"pid": 12345, "tid": 1, "ts": 1755217822009148, "dur": 4495176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217819921662, "dur": 421, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217819922085, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217819922145, "dur": 12309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217819934495, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1755217819934568, "dur": 8637, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1755217819943328, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config-api.h"}}, {"pid": 12345, "tid": 2, "ts": 1755217819943522, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 2, "ts": 1755217819943613, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 2, "ts": 1755217819943767, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 2, "ts": 1755217819943861, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 2, "ts": 1755217819943976, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 2, "ts": 1755217819944093, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 2, "ts": 1755217819944309, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/pch/pch-c.h"}}, {"pid": 12345, "tid": 2, "ts": 1755217819944363, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 2, "ts": 1755217819944457, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-metadata.h"}}, {"pid": 12345, "tid": 2, "ts": 1755217819943295, "dur": 1214, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217819934494, "dur": 10015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/powareejcmck.o"}}, {"pid": 12345, "tid": 2, "ts": 1755217819944509, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217819944691, "dur": 169, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1755217819944895, "dur": 56, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 2, "ts": 1755217819944664, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bqmaztylrhnp.o"}}, {"pid": 12345, "tid": 2, "ts": 1755217819945066, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1755217819945141, "dur": 390, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1755217819945660, "dur": 290, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217819945065, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qh5o819yhfyd.o"}}, {"pid": 12345, "tid": 2, "ts": 1755217819945950, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217819946051, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1755217819946272, "dur": 790, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1755217819947140, "dur": 188, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217819946050, "dur": 1278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qjxrxtwvz9bz.o"}}, {"pid": 12345, "tid": 2, "ts": 1755217819947665, "dur": 124, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217819948263, "dur": 143609, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/qjxrxtwvz9bz.o"}}, {"pid": 12345, "tid": 2, "ts": 1755217820091915, "dur": 4869, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1755217820096785, "dur": 1850, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1755217820098640, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217820091913, "dur": 6861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7y26f73wf8td.o"}}, {"pid": 12345, "tid": 2, "ts": 1755217820099723, "dur": 540038, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/7y26f73wf8td.o"}}, {"pid": 12345, "tid": 2, "ts": 1755217820639855, "dur": 691, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__27.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1755217820640547, "dur": 1005, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__27.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1755217820641557, "dur": 139, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755217820639853, "dur": 1844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pep1atxx6lw5.o"}}, {"pid": 12345, "tid": 2, "ts": 1755217820641942, "dur": 1461785, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/pep1atxx6lw5.o"}}, {"pid": 12345, "tid": 2, "ts": 1755217822103781, "dur": 4400489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819921668, "dur": 420, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819922088, "dur": 12439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819934541, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestTextureModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1755217819934599, "dur": 8617, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestTextureModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1755217819943345, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-blob.h"}}, {"pid": 12345, "tid": 3, "ts": 1755217819943428, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-platforms.h"}}, {"pid": 12345, "tid": 3, "ts": 1755217819943568, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 3, "ts": 1755217819943679, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 3, "ts": 1755217819943772, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 3, "ts": 1755217819943875, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 3, "ts": 1755217819944002, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 3, "ts": 1755217819944113, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 3, "ts": 1755217819944288, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 3, "ts": 1755217819944343, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 3, "ts": 1755217819944437, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 3, "ts": 1755217819943302, "dur": 1228, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819934535, "dur": 9995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/271rjj9hpanc.o"}}, {"pid": 12345, "tid": 3, "ts": 1755217819944531, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819944845, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1755217819944983, "dur": 434, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1755217819945530, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 3, "ts": 1755217819945433, "dur": 239, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819944844, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rel5070m6la2.o"}}, {"pid": 12345, "tid": 3, "ts": 1755217819945672, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819945765, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1755217819945845, "dur": 682, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1755217819946694, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config-api.h"}}, {"pid": 12345, "tid": 3, "ts": 1755217819946613, "dur": 273, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819945764, "dur": 1122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/z3zpm0gp5hyz.o"}}, {"pid": 12345, "tid": 3, "ts": 1755217819946886, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819946995, "dur": 243, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Scheduler_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1755217819947350, "dur": 192, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819946944, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/daf0lflx9vvu.o"}}, {"pid": 12345, "tid": 3, "ts": 1755217819947543, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819947649, "dur": 430, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1755217819948079, "dur": 632, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1755217819948909, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819947648, "dur": 1381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/wbbr5gnv01ta.o"}}, {"pid": 12345, "tid": 3, "ts": 1755217819949076, "dur": 393, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppMetadataRegistration.c"}}, {"pid": 12345, "tid": 3, "ts": 1755217819949513, "dur": 51, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 3, "ts": 1755217819949666, "dur": 51, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 3, "ts": 1755217819949750, "dur": 193, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217819949040, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/odolambilp1w.o"}}, {"pid": 12345, "tid": 3, "ts": 1755217819950661, "dur": 190100, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/odolambilp1w.o"}}, {"pid": 12345, "tid": 3, "ts": 1755217820140803, "dur": 318, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCTypeValuesTable.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1755217820141121, "dur": 402, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCTypeValuesTable.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1755217820141527, "dur": 1440, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217820140801, "dur": 2168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/o58p68u8gvai.o"}}, {"pid": 12345, "tid": 3, "ts": 1755217820143487, "dur": 498828, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/o58p68u8gvai.o"}}, {"pid": 12345, "tid": 3, "ts": 1755217820642351, "dur": 335, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__26.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1755217820642687, "dur": 757, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__26.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1755217820643448, "dur": 131, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755217820642349, "dur": 1231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/o64kqdhz2jfq.o"}}, {"pid": 12345, "tid": 3, "ts": 1755217820647201, "dur": 1393622, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/o64kqdhz2jfq.o"}}, {"pid": 12345, "tid": 3, "ts": 1755217822040871, "dur": 4463447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755217819921673, "dur": 420, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755217819922093, "dur": 12442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755217819934546, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1755217819934656, "dur": 8690, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1755217819943361, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-blob.h"}}, {"pid": 12345, "tid": 4, "ts": 1755217819943481, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 4, "ts": 1755217819943574, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 4, "ts": 1755217819943676, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 4, "ts": 1755217819943770, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 4, "ts": 1755217819943854, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 4, "ts": 1755217819943969, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 4, "ts": 1755217819944102, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 4, "ts": 1755217819944212, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 4, "ts": 1755217819944308, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/pch/pch-c.h"}}, {"pid": 12345, "tid": 4, "ts": 1755217819944396, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 4, "ts": 1755217819943355, "dur": 1175, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755217819934544, "dur": 9986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rnyv6whbdy7m.o"}}, {"pid": 12345, "tid": 4, "ts": 1755217819944531, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755217819944843, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1755217819944971, "dur": 522, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1755217819945590, "dur": 259, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755217819944842, "dur": 1007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gxogp8w60w5t.o"}}, {"pid": 12345, "tid": 4, "ts": 1755217819945850, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755217819946271, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AssetBundleModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1755217819946393, "dur": 748, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AssetBundleModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1755217819947347, "dur": 191, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755217819946269, "dur": 1270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5844u56e9ta6.o"}}, {"pid": 12345, "tid": 4, "ts": 1755217819947539, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755217819947630, "dur": 436, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1755217819948066, "dur": 481, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1755217819947629, "dur": 938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/fp9vzf2i6t4f.o"}}, {"pid": 12345, "tid": 4, "ts": 1755217819948567, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755217819948704, "dur": 1831, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppMetadataUsage.c"}}, {"pid": 12345, "tid": 4, "ts": 1755217819950535, "dur": 1684, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppMetadataUsage.c"}}, {"pid": 12345, "tid": 4, "ts": 1755217819948703, "dur": 3537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cww9hyjfw0gn.o"}}, {"pid": 12345, "tid": 4, "ts": 1755217819953367, "dur": 735432, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/cww9hyjfw0gn.o"}}, {"pid": 12345, "tid": 4, "ts": 1755217820688887, "dur": 1508, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__22.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1755217820690395, "dur": 1371, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__22.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1755217820691771, "dur": 131, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755217820688884, "dur": 3018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/wna4u5t8phzo.o"}}, {"pid": 12345, "tid": 4, "ts": 1755217820692337, "dur": 1404159, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/wna4u5t8phzo.o"}}, {"pid": 12345, "tid": 4, "ts": 1755217822096627, "dur": 4407693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819921685, "dur": 410, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819922095, "dur": 12455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819934553, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAssetBundleModule_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1755217819934651, "dur": 8439, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAssetBundleModule_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1755217819943120, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-sanitizers.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819943175, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-object-internals.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819943272, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config-api.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819943396, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-platforms.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819943504, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819943685, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819943803, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819943913, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819944034, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819944126, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819944179, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819944317, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/pch/pch-c.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819944414, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819943119, "dur": 1446, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819934551, "dur": 10015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5sr3d242cry5.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819944566, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819944822, "dur": 541, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1755217819945364, "dur": 1000, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1755217819946482, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config-api.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819946438, "dur": 319, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819944821, "dur": 1936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/i4gkg67otenh.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819946757, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819946827, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Threading_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1755217819946902, "dur": 287, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Threading_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1755217819946826, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qixqomw1slwt.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819947403, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Configuration_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1755217819947523, "dur": 321, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Configuration_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1755217819947849, "dur": 55, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819947402, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/8917z4imhdvh.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819947904, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819948041, "dur": 348, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1755217819948389, "dur": 976, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1755217819949583, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819949421, "dur": 312, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819948040, "dur": 1693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7kto4ft2y71x.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819949734, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819949803, "dur": 1462, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericInstDefinitions.c"}}, {"pid": 12345, "tid": 5, "ts": 1755217819951265, "dur": 1118, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericInstDefinitions.c"}}, {"pid": 12345, "tid": 5, "ts": 1755217819949802, "dur": 2628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/34800dujha0t.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819952431, "dur": 903, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819953336, "dur": 839, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__8.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819954176, "dur": 7422, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__8.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819961642, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Threading/OSSpecificSynchronizationContext.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819961755, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericMethodCompare.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819962018, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Cpp/ReentrantLock.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819962097, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include/BaselibPlatformSpecificEnvironment.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819962149, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/ErrorCodes.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819962273, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-string-types.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819962481, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/sparsetable.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819962619, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/MetadataCache.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819962755, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/String.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819962828, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/Finally.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819962955, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/Thread.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819963187, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Thread.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819963290, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_ReentrantLock.inl.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819963396, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Threading/Thread.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819963547, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Timer.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819963604, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppTypeCompare.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819963888, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_CappedSemaphore_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819963978, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/FastReaderReaderWriterLock.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819964063, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Threading/Monitor.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819964195, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericContextCompare.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819964280, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_WakeupFallbackStrategy.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819964338, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Output.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819964394, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/ThreadLocalValue.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819964481, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/VmStringUtils.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819964561, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/ArrayMetadata.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819964631, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Runtime.InteropServices/Marshal.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819964698, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Assert.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819964923, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/Debugger.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819965076, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_SystemSemaphore.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819965186, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/KeyWrapper.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819965410, "dur": 2197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Msvc.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819967608, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/Mutex.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819967677, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/MarshalAlloc.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819967836, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Assembly.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819967910, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Atomic.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819961602, "dur": 6471, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819953334, "dur": 14739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/dpr6rtuek343.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819968073, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819968366, "dur": 718, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__22.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819969084, "dur": 1327, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__22.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819970502, "dur": 677, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Globalization/CultureInfo.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819970415, "dur": 942, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819968363, "dur": 2994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/tyyqnnmny8cs.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819971376, "dur": 3823, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__20.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819975204, "dur": 2375, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__20.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819977589, "dur": 310, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819971375, "dur": 6524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7abmnyrrrikt.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819977929, "dur": 2902, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__15.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819980832, "dur": 2008, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__15.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819982905, "dur": 763, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Runtime.Remoting.Messaging/AsyncResult.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819983735, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/BasicTypes.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819983856, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/Thread.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819983969, "dur": 781, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Runtime.Remoting.Activation/ActivationServices.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819982848, "dur": 1953, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819977927, "dur": 6875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/d787ps3y43cj.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819984940, "dur": 515, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/bouncywp71__3.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819985455, "dur": 650, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/bouncywp71__3.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819986109, "dur": 141, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819984938, "dur": 1313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/aemws7djwz2x.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819986270, "dur": 408, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/bouncywp71.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819986678, "dur": 1092, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/bouncywp71.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819987774, "dur": 148, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819986268, "dur": 1654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/fo0uudow1vfh.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819987939, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestWWWModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819988267, "dur": 640, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestWWWModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819988946, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/dynamic_array.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819989333, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Class.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819989416, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/template_util.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819989518, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Assert.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217819988915, "dur": 780, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819987937, "dur": 1759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yueec2swctzp.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819989696, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819989755, "dur": 556, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__3.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819990311, "dur": 1128, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__3.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819991447, "dur": 333, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819989754, "dur": 2026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/63g9h78mal0t.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819991796, "dur": 1796, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__7.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819993592, "dur": 1436, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__7.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819995034, "dur": 122, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819991795, "dur": 3361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rb316s1gdapd.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819995176, "dur": 1272, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819996448, "dur": 1127, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819997578, "dur": 125, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819995175, "dur": 2528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ed5wvb0sxjxs.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217819997704, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819997970, "dur": 772, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__10.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217819998742, "dur": 3563, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__10.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820002313, "dur": 278, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217819997968, "dur": 4623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/x76chu79adjt.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820002592, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820002646, "dur": 563, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820003210, "dur": 1428, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820004749, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Gcc_Patch.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820004828, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericInstHash.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820004644, "dur": 423, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820002644, "dur": 2423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yku3w4xrq4wr.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820005085, "dur": 292, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820005377, "dur": 506, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820005964, "dur": 366, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/dynamic_array.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820005891, "dur": 674, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820005084, "dur": 1482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cwxgt8z9ma9n.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820006594, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820006653, "dur": 277, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820006942, "dur": 129, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/dummy"}}, {"pid": 12345, "tid": 5, "ts": 1755217820007189, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/GenericMethod.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820007250, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Array.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820007071, "dur": 537, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820006592, "dur": 1016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9bpym24zpygm.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820007627, "dur": 611, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820008238, "dur": 3164, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820011406, "dur": 141, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820007626, "dur": 3921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/e9br2otk01d5.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820011569, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820011950, "dur": 1632, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820013823, "dur": 356, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Thread.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820014223, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_ReentrantLock.inl.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820013592, "dur": 924, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820011567, "dur": 2949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/k1o3x21f8gfj.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820014532, "dur": 325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Registration.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820014857, "dur": 646, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Registration.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820015743, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/ArrayMetadata.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820015866, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Gcc.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820015510, "dur": 513, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820014531, "dur": 1492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pyahkgnjwkg0.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820016056, "dur": 1533, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UniWebView-CSharp.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820017589, "dur": 2839, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UniWebView-CSharp.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820020432, "dur": 132, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820016054, "dur": 4510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5o8sp27tymhr.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820020590, "dur": 1289, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__5.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820021879, "dur": 755, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__5.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820022638, "dur": 120, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820020589, "dur": 2169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/wde4tfxpnpdx.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820022776, "dur": 454, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__4.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820023230, "dur": 853, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__4.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820024087, "dur": 149, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820022775, "dur": 1461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/v5tn9zgrjnwh.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820024250, "dur": 508, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820024758, "dur": 1543, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820026309, "dur": 260, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820024249, "dur": 2320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9h6i9amvcx3i.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820026570, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820026801, "dur": 503, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820027304, "dur": 3673, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820031114, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_ReentrantLock.inl.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820031305, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/Mono.Unity/UnityTls.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820031487, "dur": 710, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/CustomAttributeDataReader.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820030983, "dur": 1305, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820026800, "dur": 5490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/fcohl8o0xp5m.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820032343, "dur": 347, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820032690, "dur": 600, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820033295, "dur": 265, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820032340, "dur": 1220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4slarn91x6mm.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820033610, "dur": 789, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__12.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820034399, "dur": 3642, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__12.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820038205, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_SourceLocation.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820038366, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/ScopedThreadAttacher.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820038431, "dur": 496, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Msvc.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820038929, "dur": 358, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/Mutex.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820038049, "dur": 1305, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820033608, "dur": 5748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/h6gjg3vosncs.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820039357, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820039581, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820039735, "dur": 852, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820040680, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-blob.h"}}, {"pid": 12345, "tid": 5, "ts": 1755217820040594, "dur": 357, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820039580, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/0gxojhn0q9wg.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820040988, "dur": 737, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__6.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820041725, "dur": 1270, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__6.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820043002, "dur": 292, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820040986, "dur": 2309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/liz1vwjd5u04.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820043345, "dur": 344, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820043689, "dur": 841, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820044535, "dur": 209, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820043343, "dur": 1401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/77jzaab7z3j6.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820044780, "dur": 3889, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Core__1.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820048669, "dur": 973, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Core__1.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820049646, "dur": 127, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820044779, "dur": 4994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zsi0nfo2hvde.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820049773, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820049879, "dur": 590, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__3.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820050469, "dur": 1044, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__3.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820051521, "dur": 299, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820049877, "dur": 1944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/uflrfznlgvy3.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820051859, "dur": 376, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/NLayer.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820052235, "dur": 1098, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/NLayer.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820053342, "dur": 295, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820051857, "dur": 1780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7qn2ue7hlt7p.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820053664, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppReversePInvokeWrapperTable.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820053729, "dur": 267, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppReversePInvokeWrapperTable.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820054006, "dur": 271, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820053661, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5fizaa0jn40d.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820054308, "dur": 226, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppInteropDataTable.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820054535, "dur": 624, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppInteropDataTable.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820055183, "dur": 276, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820054306, "dur": 1153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bcptnv06or0f.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820055495, "dur": 727, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820056222, "dur": 783, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820057008, "dur": 164, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820055493, "dur": 1680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5xyz67ez2omn.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820057885, "dur": 613163, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/5xyz67ez2omn.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820671169, "dur": 514, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__23.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820671684, "dur": 817, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__23.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217820672506, "dur": 127, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217820671167, "dur": 1466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/h3dsaf85s5a8.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217820674795, "dur": 1285259, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/h3dsaf85s5a8.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217821960152, "dur": 575, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__15.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217821960727, "dur": 811, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__15.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1755217821961542, "dur": 129, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755217821960149, "dur": 1522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gwklxabkwczf.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217821962141, "dur": 1140299, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/gwklxabkwczf.o"}}, {"pid": 12345, "tid": 5, "ts": 1755217823102490, "dur": 3401804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819921691, "dur": 406, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819922097, "dur": 12351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819934463, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/bouncywp71_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819934681, "dur": 8684, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/bouncywp71_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819943505, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819943587, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819943704, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819943879, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819943994, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819944173, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819944264, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819944466, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-metadata.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819943374, "dur": 1195, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819934450, "dur": 10120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3fpsr3ws0u80.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819944570, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819944856, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819945008, "dur": 429, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819945448, "dur": 329, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819944855, "dur": 937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gy9vuftim7uh.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819945792, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819945875, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819945967, "dur": 597, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819946716, "dur": 258, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819945873, "dur": 1101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/awi2mli23iis.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819946974, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819947099, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Internal_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819947217, "dur": 437, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Internal_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819947787, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819947098, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/hu1whpjn9bvt.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819947922, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819947994, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Numerics_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819948082, "dur": 463, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Numerics_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819947993, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vqspld2lzvse.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819948582, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/LitJson_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819948680, "dur": 823, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/LitJson_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819949539, "dur": 341, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819948581, "dur": 1299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9d4nvknvhyc3.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819949881, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819949966, "dur": 1271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericAdjustorThunkTable.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819951237, "dur": 1141, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericAdjustorThunkTable.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819949964, "dur": 2444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rikaetvi67fp.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819952408, "dur": 896, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819953306, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/AppleAuth_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819953367, "dur": 89, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/AppleAuth_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1755217819953304, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/o3e97ifszslv.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819953542, "dur": 550, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__7.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819954092, "dur": 6783, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__7.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819961508, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Array.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819961703, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ErrorCode.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819961764, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/type_traits.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819961884, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Thread.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819962034, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Cpp/ReentrantLock.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819962153, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/ErrorCodes.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819962457, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/sparsetable.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819962579, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/MetadataCache.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819962662, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Baselib.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819962713, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/String.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819962851, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Class.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819962954, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/Thread.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819963006, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Thread.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819963287, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_ReentrantLock.inl.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819963469, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/NonCopyable.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819963573, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Timer.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819963712, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819963867, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_CappedSemaphore_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819963938, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_SystemFutex.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819964007, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Il2CppError.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819964150, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericContextCompare.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819964240, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/PlatformDetection.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819964342, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Output.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819964397, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/ThreadLocalValue.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819964449, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/VmStringUtils.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819964501, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System/Delegate.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819964572, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/ArrayMetadata.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819964663, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Assert.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819964965, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/Debugger.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819965135, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System/MonoCustomAttrs.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819965260, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ReentrantLock.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819965320, "dur": 2377, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-tiny.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819967879, "dur": 314, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Atomic.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819960879, "dur": 7339, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819953540, "dur": 14678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ch7p7degg1no.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819968254, "dur": 1358, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819969613, "dur": 1735, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819971370, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System/DateTime.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819971356, "dur": 362, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819968253, "dur": 3466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/kyc9b6qp3y7m.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819971735, "dur": 3503, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__18.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819975241, "dur": 2916, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__18.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819978234, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Reflection/AssemblyName.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819978443, "dur": 1996, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Reflection/CustomAttributeData.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819980538, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Reflection/Assembly.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819978162, "dur": 2858, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819971734, "dur": 9287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/02gtaxetfwmx.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819981053, "dur": 527, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__12.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819981580, "dur": 619, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__12.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819982292, "dur": 51, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 6, "ts": 1755217819982380, "dur": 67, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/dummy"}}, {"pid": 12345, "tid": 6, "ts": 1755217819982447, "dur": 370, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819981051, "dur": 1766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/63hfauu5up80.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819982832, "dur": 2576, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819985409, "dur": 1452, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819987079, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System/String.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819987198, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/WaitStatus-c-api.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819987383, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/PlatformEnvironment.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819986873, "dur": 643, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819982831, "dur": 4686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yap4bhwtw8eo.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819987569, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819987635, "dur": 1011, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819988652, "dur": 167, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819987567, "dur": 1252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9yai1i5uplrb.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819988836, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAudioModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819989083, "dur": 657, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAudioModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819989748, "dur": 266, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819988834, "dur": 1180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/djbreftlk2rq.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819990015, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819990072, "dur": 636, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__2.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819990709, "dur": 919, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__2.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819991683, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-il2cpp.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217819991632, "dur": 463, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819990070, "dur": 2026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/lodl2xup7woa.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819992133, "dur": 2436, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819994569, "dur": 1061, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819995634, "dur": 122, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819992131, "dur": 3625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/6vzdrjnjamdu.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819995770, "dur": 263, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__15.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819996033, "dur": 535, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__15.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819996576, "dur": 295, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819995769, "dur": 1103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qylaxhbzrhga.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819996917, "dur": 474, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__14.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819997393, "dur": 824, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__14.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819998220, "dur": 168, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819996915, "dur": 1473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pq1tc2u0nfzu.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217819998406, "dur": 622, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217819999028, "dur": 998, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820000032, "dur": 243, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217819998405, "dur": 1870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qlmwgts9kand.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820000291, "dur": 1811, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820002103, "dur": 487, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820002598, "dur": 275, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820000290, "dur": 2583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pimdbz1it4n5.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820002874, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820002984, "dur": 310, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820003294, "dur": 561, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820003861, "dur": 163, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820002983, "dur": 1041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xcancphtm2fc.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820004024, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820004171, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820004223, "dur": 177, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820004404, "dur": 319, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820004170, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jjkc8w84z3e5.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820004725, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820004795, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820005036, "dur": 459, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820005837, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Assembly.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217820005500, "dur": 412, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820004793, "dur": 1120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/e7nv8ap6gw8b.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820005954, "dur": 370, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820006324, "dur": 830, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820007240, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217820007161, "dur": 271, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820005953, "dur": 1479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5zxbtrn29ub0.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820007445, "dur": 839, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820008285, "dur": 2834, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820011123, "dur": 150, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820007444, "dur": 3830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ph3csm9aq9f5.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820011290, "dur": 717, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820012007, "dur": 1976, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820013991, "dur": 401, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820011289, "dur": 3104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5gsrdcgdb5q2.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820014393, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820014701, "dur": 518, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Internal.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820015219, "dur": 1023, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Internal.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820016313, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217820016445, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericInstHash.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217820016249, "dur": 495, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820014700, "dur": 2045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/lszm8l6rol0v.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820016746, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820016800, "dur": 727, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UniExtensions.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820017527, "dur": 835, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UniExtensions.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820018433, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/sparsehashtable.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217820018366, "dur": 194, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820016798, "dur": 1762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/mzarddknnc3m.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820018573, "dur": 4011, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820022585, "dur": 2590, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820025183, "dur": 245, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820018572, "dur": 6856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/hths9obnqdpg.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820025463, "dur": 849, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__10.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820026312, "dur": 1791, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__10.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820028419, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Compiler/CompilerEnvironmentClang.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217820028498, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_SystemSemaphore.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217820028110, "dur": 573, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820025460, "dur": 3224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/e8e4c9vjwq08.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820028711, "dur": 2720, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820031431, "dur": 781, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820032218, "dur": 136, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820028709, "dur": 3645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/c4wjr1ytbn8j.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820032354, "dur": 732, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820033089, "dur": 727, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__15.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820033817, "dur": 1297, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__15.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820035118, "dur": 181, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820033087, "dur": 2212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/o97bw14h21ga.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820035311, "dur": 3980, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__10.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820039291, "dur": 983, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__10.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820040282, "dur": 321, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820035310, "dur": 5293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bba7kk3tmbpp.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820040631, "dur": 610, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__8.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820041241, "dur": 749, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__8.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820041993, "dur": 154, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820040629, "dur": 1518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/75m6vle9blt3.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820042161, "dur": 363, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820042524, "dur": 802, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820043330, "dur": 300, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820042159, "dur": 1472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ef88fyakvgwm.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820043632, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820043851, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Core__4.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820044038, "dur": 487, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Core__4.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820044723, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Msvc.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217820044530, "dur": 339, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820043849, "dur": 1021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/moql8jzwsnns.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820044871, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820045006, "dur": 3720, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Core.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820048726, "dur": 852, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Core.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820049582, "dur": 655, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820045005, "dur": 5233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gnitbvgm52ph.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820050289, "dur": 1228, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__2.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820051517, "dur": 1052, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__2.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820052572, "dur": 142, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820050287, "dur": 2427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9zh3zp9nd7mk.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820052730, "dur": 607, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Mono.Security.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820053337, "dur": 924, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Mono.Security.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820054266, "dur": 345, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820052729, "dur": 1883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5gufo5c8c11z.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820054612, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820054775, "dur": 843, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820055618, "dur": 2158, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820057782, "dur": 249, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820054774, "dur": 3258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gf75cpaiupon.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820058446, "dur": 610888, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/gf75cpaiupon.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820669415, "dur": 530, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__25.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820669945, "dur": 1020, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__25.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217820671126, "dur": 489, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/gc/WriteBarrier.h"}}, {"pid": 12345, "tid": 6, "ts": 1755217820670973, "dur": 810, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217820669412, "dur": 2372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3q1wa421zv36.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217820674437, "dur": 1260480, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/3q1wa421zv36.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217821935039, "dur": 770, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__17.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217821935809, "dur": 658, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__17.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217821936471, "dur": 139, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217821935036, "dur": 1575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2j8pxz5f32re.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217821936639, "dur": 368, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__16.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217821937007, "dur": 839, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__16.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1755217821937850, "dur": 121, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755217821936637, "dur": 1334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/h441hqri0egv.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217821938228, "dur": 1217508, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/h441hqri0egv.o"}}, {"pid": 12345, "tid": 6, "ts": 1755217823155788, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Lib_iOS_arm64 z7ln/libGameAssembly.a"}}, {"pid": 12345, "tid": 6, "ts": 1755217823156475, "dur": 3184605, "ph": "X", "name": "Lib_iOS_arm64", "args": {"detail": "z7ln/libGameAssembly.a"}}, {"pid": 12345, "tid": 6, "ts": 1755217826341142, "dur": 163084, "ph": "X", "name": "CopyFiles", "args": {"detail": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Unity-iPhone-cagjkwhygrnlrdhjvwgopjvjjsdo/Build/Products/ReleaseForRunning-iphoneos/libGameAssembly.a"}}, {"pid": 12345, "tid": 7, "ts": 1755217819921697, "dur": 402, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217819922099, "dur": 12352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217819934457, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/__Generated_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819934587, "dur": 8740, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/__Generated_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819943350, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-blob.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217819943442, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-platforms.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217819943527, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217819943600, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217819943701, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217819943888, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217819944063, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217819944116, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217819944318, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/pch/pch-c.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217819944465, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-metadata.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217819943342, "dur": 1233, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217819934453, "dur": 10122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5e48yzu9zbhy.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217819944575, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217819944833, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819944928, "dur": 121, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819945092, "dur": 103, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 7, "ts": 1755217819945205, "dur": 231, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217819944832, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/6j33sd36ju5l.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217819945486, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819945549, "dur": 572, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819945485, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/oexo4cj32xh9.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217819946214, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819946282, "dur": 332, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819946860, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217819946830, "dur": 191, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217819946213, "dur": 809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/tslqnkphudqf.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217819947022, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217819947227, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Device_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819947278, "dur": 557, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Device_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819947226, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/mzm2hz577l1j.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217819947872, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217819948038, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Drawing_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819948090, "dur": 350, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Drawing_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819948449, "dur": 56, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217819948037, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3zzp7c1nvpku.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217819948571, "dur": 577, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/NLayer_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819949276, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217819949430, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217819949153, "dur": 382, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217819948524, "dur": 1011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3oeuhlqjid2m.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217819949535, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217819949602, "dur": 609, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodDefinitions.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819950211, "dur": 870, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodDefinitions.c"}}, {"pid": 12345, "tid": 7, "ts": 1755217819949601, "dur": 1506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cg31onsbb06f.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217819951559, "dur": 229685, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/cg31onsbb06f.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820181324, "dur": 643, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__9.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820181967, "dur": 949, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__9.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820182921, "dur": 130, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820181321, "dur": 1731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/fr516ohkimjr.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820183053, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820183335, "dur": 342, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__8.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820183678, "dur": 765, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__8.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820184447, "dur": 120, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820183333, "dur": 1234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ub2drf3mfy77.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820184587, "dur": 325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__7.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820184912, "dur": 654, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__7.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820185569, "dur": 118, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820184585, "dur": 1102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9vypb6opcawo.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820185702, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__65.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820185779, "dur": 220, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__65.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820186002, "dur": 118, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820185700, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/r807rpy87pbe.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820186133, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__64.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820186404, "dur": 539, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__64.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820186946, "dur": 118, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820186132, "dur": 932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/6sxjcgkb51zu.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820187076, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__63.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820187370, "dur": 589, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__63.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820187963, "dur": 117, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820187075, "dur": 1005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5kd3r03c1zif.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820188091, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__62.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820188419, "dur": 622, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__62.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820189045, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820188090, "dur": 1074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/k59ecrpfyb7v.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820189180, "dur": 283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__61.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820189463, "dur": 587, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__61.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820190054, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820189179, "dur": 994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zp6qwhbivnvc.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820190185, "dur": 347, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__60.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820190532, "dur": 691, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__60.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820191226, "dur": 118, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820190184, "dur": 1160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yhmac2ujrlex.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820191344, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820191530, "dur": 348, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__6.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820191878, "dur": 2496, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__6.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820194378, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820191529, "dur": 2968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/8ne9qq8q87e9.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820194512, "dur": 465, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__59.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820194977, "dur": 1285, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__59.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820196270, "dur": 270, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820194511, "dur": 2030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cpv5eawiinsk.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820196541, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820196596, "dur": 1712, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__58.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820198308, "dur": 2885, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__58.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820201202, "dur": 246, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820196594, "dur": 4854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/r9rhz0hlfzg1.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820201449, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820201504, "dur": 778, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__57.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820202282, "dur": 1288, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__57.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820203578, "dur": 267, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820201502, "dur": 2343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/6totjr3ncb5h.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820203895, "dur": 552, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__56.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820204448, "dur": 841, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__56.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820205293, "dur": 123, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820203893, "dur": 1523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5l4czbnp88n2.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820205439, "dur": 342, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__54.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820205781, "dur": 710, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__54.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820206558, "dur": 229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/sparsehashtable.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217820206495, "dur": 469, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820205437, "dur": 1528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zqx03suu2w0z.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820206966, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820207042, "dur": 1095, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__52.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820208137, "dur": 1739, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__52.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820209880, "dur": 151, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820207040, "dur": 2991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/d39v33zyv670.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820210032, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820210386, "dur": 515, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__50.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820210901, "dur": 1535, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__50.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820212443, "dur": 246, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820210383, "dur": 2306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rfmr3e52ogf2.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820212735, "dur": 580, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__49.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820213315, "dur": 1356, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__49.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820214675, "dur": 160, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820212733, "dur": 2103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5mvm6m61fitp.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820214890, "dur": 1137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__46.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820216028, "dur": 1858, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__46.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820217894, "dur": 256, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820214888, "dur": 3263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yj4tqox853o7.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820218194, "dur": 901, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__44.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820219096, "dur": 824, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__44.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820219924, "dur": 264, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820218192, "dur": 1997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cwgpqz2czdw8.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820220220, "dur": 537, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__42.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820220757, "dur": 1116, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__42.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820221881, "dur": 271, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820220218, "dur": 1935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/609cvgait185.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820222154, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820222407, "dur": 1856, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__40.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820224264, "dur": 1406, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__40.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820225678, "dur": 271, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820222406, "dur": 3544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/kdm8urfmmbg7.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820225983, "dur": 477, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__38.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820226460, "dur": 1044, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__38.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820227513, "dur": 230, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820225981, "dur": 1762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xcbndk0m9adf.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820227761, "dur": 510, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__37.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820228271, "dur": 1068, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__37.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820229343, "dur": 287, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820227760, "dur": 1871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cx7yxamp6m1v.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820229682, "dur": 1156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__35.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820230839, "dur": 1127, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__35.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820231974, "dur": 264, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820229680, "dur": 2558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/q9juxlrtzw8w.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820232290, "dur": 732, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__33.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820233022, "dur": 1009, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__33.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820234035, "dur": 123, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820232288, "dur": 1871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/kedi22yytklu.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820234159, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820234392, "dur": 2179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__31.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820236571, "dur": 1361, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__31.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820237940, "dur": 123, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820234391, "dur": 3672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yqpd2d42musv.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820238085, "dur": 2665, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__3.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820240754, "dur": 2241, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__3.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820243000, "dur": 181, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820238083, "dur": 5099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/1kngbszxytar.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820243210, "dur": 2152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__28.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820245363, "dur": 943, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__28.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820246310, "dur": 128, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820243208, "dur": 3230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qab44wa665dk.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820246463, "dur": 392, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__27.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820246855, "dur": 1560, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__27.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820248425, "dur": 300, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820246462, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5u7fpnw83h20.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820248727, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820248787, "dur": 3713, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__25.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820252501, "dur": 678, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__25.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820253182, "dur": 126, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820248785, "dur": 4523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/j7jbpy1cxr91.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820253328, "dur": 497, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__23.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820253825, "dur": 1487, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__23.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820255319, "dur": 239, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820253327, "dur": 2232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xjw6o96gq1g3.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820255599, "dur": 4460, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__21.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820260059, "dur": 1592, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__21.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820261660, "dur": 261, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820255597, "dur": 6324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/roputqemkpt0.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820261975, "dur": 2267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__2.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820264242, "dur": 959, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__2.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820265249, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Alignment.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217820265205, "dur": 527, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820261972, "dur": 3761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/g5jsvil0v5y7.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820265787, "dur": 5052, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__17.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820270840, "dur": 1597, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__17.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820272442, "dur": 130, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820265785, "dur": 6787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ys7wg06r1c35.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820272598, "dur": 274, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__15.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820272873, "dur": 616, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__15.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820273493, "dur": 120, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820272596, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/c5jsgbcwuma7.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820273627, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__14.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820273876, "dur": 591, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__14.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820274471, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820273626, "dur": 965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bxzbwvvxqktr.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820274608, "dur": 241, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__13.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820274849, "dur": 572, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__13.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820275424, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820274607, "dur": 936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qhwngmh0nwh8.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820275544, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820275755, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__12.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820276026, "dur": 660, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__12.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820276690, "dur": 130, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820275754, "dur": 1066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/51sz0c32k85o.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820276842, "dur": 389, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__10.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820277231, "dur": 703, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__10.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820277938, "dur": 130, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820276841, "dur": 1227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/je5u0n86didq.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820278087, "dur": 421, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820278508, "dur": 1094, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820279606, "dur": 120, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820278085, "dur": 1641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/g7mj5d9tm64v.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820279744, "dur": 1282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__8.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820281026, "dur": 3076, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__8.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820284107, "dur": 121, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820279742, "dur": 4486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vzmt8vmoid8b.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820284248, "dur": 338, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__5.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820284586, "dur": 493, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__5.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820285085, "dur": 136, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820284247, "dur": 974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/owv0sxdv8y3t.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820285234, "dur": 643, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__4.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820285877, "dur": 1391, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__4.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820287273, "dur": 126, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820285233, "dur": 2166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/nobk8hxw4wme.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820287419, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__2.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820287665, "dur": 1368, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__2.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820289050, "dur": 128, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820287418, "dur": 1760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ht47cz41ykzb.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820289197, "dur": 3833, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__15.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820293031, "dur": 1222, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__15.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820294257, "dur": 137, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820289196, "dur": 5198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vw7f3vlamgdd.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820294417, "dur": 392, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__12.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820294809, "dur": 1020, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__12.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820295838, "dur": 256, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820294416, "dur": 1678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/otgnnxchc28u.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820296095, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820296175, "dur": 3040, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__11.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820299215, "dur": 806, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__11.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820300025, "dur": 118, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820296173, "dur": 3970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/nlw62hwi8zrf.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820300159, "dur": 592, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820300751, "dur": 1017, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820301771, "dur": 129, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820300158, "dur": 1742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/j6191fbnxvlh.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820301917, "dur": 2928, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/DOTween__1.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820304845, "dur": 2210, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/DOTween__1.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820307064, "dur": 404, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820301916, "dur": 5554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/t16kqw8jg43b.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820307471, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820307550, "dur": 544, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__8.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820308095, "dur": 1245, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__8.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820309491, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-tiny.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217820309546, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/ExceptionSupportStack.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217820309680, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/hashtable-common.h"}}, {"pid": 12345, "tid": 7, "ts": 1755217820309345, "dur": 434, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820307548, "dur": 2232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/y6rqhfj0763a.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820309780, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820309858, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__53.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820309945, "dur": 849, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__53.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820310798, "dur": 214, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820309856, "dur": 1157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/i2u54ifl1ydp.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820311510, "dur": 598571, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/i2u54ifl1ydp.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820910193, "dur": 435, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__20.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820910629, "dur": 937, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__20.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1755217820911574, "dur": 270, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755217820910191, "dur": 1654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/a0mzugzcjr6p.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217820912777, "dur": 1132928, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/a0mzugzcjr6p.o"}}, {"pid": 12345, "tid": 7, "ts": 1755217822045834, "dur": 4458498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819921705, "dur": 396, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819922101, "dur": 12404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819934521, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestWWWModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819934597, "dur": 8699, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestWWWModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819943395, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-platforms.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217819943478, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217819943566, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217819943653, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217819943762, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217819943858, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217819943963, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217819944098, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217819944164, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217819944256, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217819944341, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/pch/pch-c.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217819944397, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217819943324, "dur": 1210, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819934517, "dur": 10017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qddbwsbff2jk.o"}}, {"pid": 12345, "tid": 8, "ts": 1755217819944534, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819944826, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819944960, "dur": 632, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819945692, "dur": 170, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819944825, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xp0jw9spucks.o"}}, {"pid": 12345, "tid": 8, "ts": 1755217819945862, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819945923, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819946033, "dur": 288, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819946438, "dur": 243, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819945922, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/94qby8pgpy6b.o"}}, {"pid": 12345, "tid": 8, "ts": 1755217819946682, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819946753, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819946805, "dur": 385, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819947471, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-metadata.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217819947339, "dur": 194, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819946752, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4gjf1ugkqrg0.o"}}, {"pid": 12345, "tid": 8, "ts": 1755217819947565, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UniExtensions_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819947636, "dur": 242, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UniExtensions_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819947564, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/m04yqph95cly.o"}}, {"pid": 12345, "tid": 8, "ts": 1755217819947927, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819948094, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Configuration_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819948195, "dur": 256, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Configuration_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819948093, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ndxkqzex5wxn.o"}}, {"pid": 12345, "tid": 8, "ts": 1755217819948493, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819948682, "dur": 305, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppRgctxTable.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819948987, "dur": 770, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppRgctxTable.c"}}, {"pid": 12345, "tid": 8, "ts": 1755217819949875, "dur": 80, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819948682, "dur": 1274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/k3dqr80vsrc4.o"}}, {"pid": 12345, "tid": 8, "ts": 1755217819950144, "dur": 55, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217819950766, "dur": 190057, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/k3dqr80vsrc4.o"}}, {"pid": 12345, "tid": 8, "ts": 1755217820140904, "dur": 488, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCFieldValuesTable.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1755217820141393, "dur": 1548, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCFieldValuesTable.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1755217820143112, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/VerifyPlatformEnvironment.h"}}, {"pid": 12345, "tid": 8, "ts": 1755217820142947, "dur": 335, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217820140901, "dur": 2381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ilz5jj26nlth.o"}}, {"pid": 12345, "tid": 8, "ts": 1755217820143707, "dur": 471779, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/ilz5jj26nlth.o"}}, {"pid": 12345, "tid": 8, "ts": 1755217820615610, "dur": 565, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__28.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1755217820616176, "dur": 802, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__28.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1755217820616983, "dur": 138, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755217820615607, "dur": 1514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/auspq2bv997o.o"}}, {"pid": 12345, "tid": 8, "ts": 1755217820617760, "dur": 1437130, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/auspq2bv997o.o"}}, {"pid": 12345, "tid": 8, "ts": 1755217822054999, "dur": 4449265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819921709, "dur": 412, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819922123, "dur": 12313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819934437, "dur": 724, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819935161, "dur": 8181, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819943358, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-blob.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819943484, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819943580, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819943695, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819943778, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819943888, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819944048, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819944122, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819944260, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819944402, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819943353, "dur": 1220, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819934436, "dur": 10138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/djwvc15xtyb7.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819944574, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819944736, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819944895, "dur": 700, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819945690, "dur": 167, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819944735, "dur": 1122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7hsheeai4xwh.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819945904, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819946038, "dur": 250, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819946629, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819946396, "dur": 432, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819945904, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/6lcp2ydflqs4.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819946829, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819946882, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Telemetry_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819946961, "dur": 227, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Telemetry_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819947276, "dur": 110, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819946881, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7ym78gf6u0oc.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819947387, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819947497, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UniWebView-CSharp_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819947678, "dur": 196, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UniWebView-CSharp_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819947881, "dur": 60, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819947496, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/dgyhngk5abrd.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819947942, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819948048, "dur": 421, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Core_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819948469, "dur": 620, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Core_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819949278, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-platforms.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819949361, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819949192, "dur": 269, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819948047, "dur": 1414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ww1hh899w085.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819949462, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819949535, "dur": 3938, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodPointerTable.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819953473, "dur": 3239, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodPointerTable.c"}}, {"pid": 12345, "tid": 9, "ts": 1755217819949534, "dur": 7201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bqsga4x1yvpb.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819956751, "dur": 1833, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__6.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819958584, "dur": 3512, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__6.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819962124, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System/GC.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819962248, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System/RuntimeType.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819962397, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_SourceLocation.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819962515, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/sparsetable.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819962645, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/MetadataCache.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819962800, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Memory.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819962914, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Class.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819963033, "dur": 239, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Thread.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819963273, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/libc_allocator_with_realloc.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819963454, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/MemoryUtils.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819963512, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/NonCopyable.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819963851, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_CappedSemaphore_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819964064, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/PlatformEnvironment.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819964145, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericContextCompare.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819964200, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/PlatformDetection.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819964263, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_WakeupFallbackStrategy.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819964346, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Output.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819964474, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/VmStringUtils.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819964533, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_Lock_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819964676, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Assert.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819964935, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/Debugger.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819965097, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_SystemSemaphore.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819965268, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Gcc.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819965411, "dur": 2224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Msvc.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819967637, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/MarshalAlloc.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819967864, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Atomic.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819967980, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/hashtable-common.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819962104, "dur": 5929, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819956750, "dur": 11284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/r131r4ziddc5.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819968034, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819968252, "dur": 1428, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__4.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819969680, "dur": 1598, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__4.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819971287, "dur": 214, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819968251, "dur": 3251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5dgxrf5m6mj6.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819971518, "dur": 3936, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__19.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819975457, "dur": 2368, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__19.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819977834, "dur": 340, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819971516, "dur": 6658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/likhq6sqikyg.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819978185, "dur": 2900, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__14.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819981085, "dur": 2942, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__14.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819984039, "dur": 799, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Runtime.Remoting.Proxies/RealProxy.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819984950, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Class.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819985216, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Security.Principal/WindowsIdentity.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819985295, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Runtime.Remoting.Contexts/Context.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819984031, "dur": 1425, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819978184, "dur": 7273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/sr5jajp11f1z.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819985470, "dur": 432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/bouncywp71__2.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819985902, "dur": 996, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/bouncywp71__2.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819986902, "dur": 179, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819985469, "dur": 1613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pyi8cyzltbre.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819987082, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819987363, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/__Generated.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819987499, "dur": 273, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/__Generated.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819987776, "dur": 146, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819987362, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/94n25r997p3z.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819987938, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VideoModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819988232, "dur": 476, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VideoModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819988713, "dur": 161, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819987937, "dur": 937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/uw878yhrcbyp.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819988890, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAssetBundleModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819989070, "dur": 191, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAssetBundleModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819989266, "dur": 231, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819988889, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9ugd7rmlxext.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819989511, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819989639, "dur": 528, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819990296, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/template_util.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819990171, "dur": 295, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819989510, "dur": 957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bd1777dsez29.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819990483, "dur": 738, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__1.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819991221, "dur": 1159, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__1.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819992387, "dur": 316, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819990482, "dur": 2221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/089wmogs48cr.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819992723, "dur": 3378, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__5.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819996102, "dur": 1076, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__5.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819997182, "dur": 221, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819992722, "dur": 4681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/lgwpba9l7x1a.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217819997422, "dur": 578, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__12.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217819998001, "dur": 1923, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__12.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820000076, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-blob.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217819999937, "dur": 338, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217819997421, "dur": 2855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jvuhhj25h3bs.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820000303, "dur": 2010, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820002314, "dur": 981, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820003307, "dur": 302, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820000301, "dur": 3309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/is8qaik1259p.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820003650, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820003804, "dur": 314, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820004124, "dur": 427, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820003647, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/wcm4y5mupxrf.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820004570, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820004757, "dur": 589, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820005372, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Lock.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217820005358, "dur": 368, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820004569, "dur": 1158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yxeua6r0si6p.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820005739, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820005868, "dur": 420, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820006294, "dur": 271, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820005738, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2gc3ymyj4kw2.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820006566, "dur": 521, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820007090, "dur": 595, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__2.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820007685, "dur": 946, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__2.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820008636, "dur": 201, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820007089, "dur": 1749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ax607vn8sxro.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820008838, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820009013, "dur": 852, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityClassRegistration.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820009865, "dur": 624, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityClassRegistration.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820009012, "dur": 1482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gnkvbyngk7ht.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820010509, "dur": 1436, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__5.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820011946, "dur": 963, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__5.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820012913, "dur": 138, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820010507, "dur": 2544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/r4fwd1a9hvj1.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820013068, "dur": 790, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820013858, "dur": 1006, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820014872, "dur": 259, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820013066, "dur": 2065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gqwri8tx0ym4.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820015132, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820015203, "dur": 382, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Device.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820015586, "dur": 671, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Device.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820016312, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Debug.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217820016264, "dur": 351, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820015201, "dur": 1414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qxe4a8cxekng.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820016650, "dur": 289, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UniExtensions__1.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820016939, "dur": 448, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UniExtensions__1.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820017532, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ErrorCode.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217820017394, "dur": 371, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820016649, "dur": 1116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/d0kk8jxhahlx.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820017780, "dur": 4986, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__7.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820022766, "dur": 2069, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__7.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820024846, "dur": 315, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820017779, "dur": 7383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/28kgpypkt10z.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820025209, "dur": 350, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__11.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820025560, "dur": 1107, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__11.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820026719, "dur": 579, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Alignment.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217820027299, "dur": 439, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/ClassInlines.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217820027739, "dur": 397, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Atomic_Macros.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217820026672, "dur": 1717, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820025207, "dur": 3183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/znissiahzecs.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820028391, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820028480, "dur": 3019, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__7.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820031499, "dur": 1081, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__7.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820032588, "dur": 295, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820028479, "dur": 4405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xj076adrebk3.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820032921, "dur": 1237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__16.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820034158, "dur": 649, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__16.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820034811, "dur": 144, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820032919, "dur": 2036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/phssghlg63xp.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820034955, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820035331, "dur": 3104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__1.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820038435, "dur": 841, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__1.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820039282, "dur": 337, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820035328, "dur": 4292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jdimbev4uib3.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820039667, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Runtime.Serialization.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820039788, "dur": 539, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Runtime.Serialization.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820040344, "dur": 332, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Runtime.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217820040334, "dur": 476, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820039665, "dur": 1146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vbl51zl4z7hv.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820040840, "dur": 454, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__7.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820041294, "dur": 905, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__7.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820042203, "dur": 128, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820040838, "dur": 1493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/6x2pb6xa8ico.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820042351, "dur": 397, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__2.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820042748, "dur": 929, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__2.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820043690, "dur": 260, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820042350, "dur": 1601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/tz4k0owmssk3.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820043999, "dur": 845, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Core__3.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820044844, "dur": 4219, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Core__3.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820049071, "dur": 274, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820043997, "dur": 5349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/lg64n4iyol6c.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820049346, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820049406, "dur": 523, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__4.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820049929, "dur": 954, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__4.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820050904, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Lock.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217820051115, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/Thread.h"}}, {"pid": 12345, "tid": 9, "ts": 1755217820050891, "dur": 407, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820049404, "dur": 1895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vn57ze4jkxfj.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820051334, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/NLayer__1.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820051468, "dur": 320, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/NLayer__1.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820051795, "dur": 271, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820051332, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/8sjok50z11vw.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820052067, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820052267, "dur": 229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Mono.Security__1.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820052496, "dur": 602, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Mono.Security__1.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820053102, "dur": 156, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820052266, "dur": 993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qnltvmap90qw.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820053271, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/LitJson.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820053537, "dur": 504, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/LitJson.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820054048, "dur": 290, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820053270, "dur": 1068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/tgo6g34y4jtw.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820054365, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCodeRegistration.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820054415, "dur": 168, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCodeRegistration.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820054588, "dur": 249, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820054364, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2t7ip9h2z027.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820054838, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820054937, "dur": 3958, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820058896, "dur": 2821, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820061722, "dur": 147, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820054936, "dur": 6933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/v5udokzlfrzl.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820062535, "dur": 635750, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/v5udokzlfrzl.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820698385, "dur": 392, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__21.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820698777, "dur": 549, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__21.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217820699330, "dur": 129, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217820698382, "dur": 1077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vfqnaqdamfi9.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217820700276, "dur": 1081843, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/vfqnaqdamfi9.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217821782270, "dur": 562, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__18.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217821782833, "dur": 1221, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__18.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1755217821784062, "dur": 300, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755217821782267, "dur": 2095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3rs6hfme748t.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217821785274, "dur": 1092607, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/3rs6hfme748t.o"}}, {"pid": 12345, "tid": 9, "ts": 1755217822877950, "dur": 3626346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819921715, "dur": 421, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819922136, "dur": 12299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819934455, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819934866, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsModule_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819935001, "dur": 8352, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsModule_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819943364, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-blob.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819943487, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819943584, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819943707, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819943866, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819943980, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819944106, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819944318, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/pch/pch-c.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819944409, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819944505, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-metadata.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819943360, "dur": 1198, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819934864, "dur": 9695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/o0bx5hy6ym8c.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819944559, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819944816, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819944956, "dur": 345, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819945351, "dur": 232, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819944815, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/74sknfzo9v5i.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819945583, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819945651, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ParticleSystemModule_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819945709, "dur": 154, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ParticleSystemModule_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819946010, "dur": 109, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819945649, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/w7bbpdcws1lv.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819946119, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819946284, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819946350, "dur": 535, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819946958, "dur": 73, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819946283, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zqq99mgjkhns.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819947052, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Registration_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819947111, "dur": 541, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Registration_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819947661, "dur": 123, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819947051, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ty8ae8spbec2.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819947824, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819947899, "dur": 75, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819947977, "dur": 61, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819947823, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/crai3p70nhg2.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819948040, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819948127, "dur": 286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819948413, "dur": 1003, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819949426, "dur": 331, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819948126, "dur": 1632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/b4ku6ewz0hit.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819949758, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819949856, "dur": 735, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericClassTable.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819950591, "dur": 386, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericClassTable.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819949854, "dur": 1141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/nsx2u2lz6xg2.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819951012, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/DOTweenPro_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819951089, "dur": 124, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/DOTweenPro_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819951220, "dur": 73, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819951011, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/tllere39fah2.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819951294, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819951556, "dur": 326, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819951882, "dur": 513, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1755217819951554, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/g1az8soqnfln.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819952435, "dur": 885, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819953322, "dur": 1079, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__9.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819954402, "dur": 7885, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__9.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819962411, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_Semaphore_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819962519, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Compiler/CompilerEnvironmentGcc.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819962571, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Il2CppHStringReference.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819962810, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Cpp/Internal/ConditionVariable_FutexBased.inl.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819962896, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/dense_hash_map.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819962998, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringUtils.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819963267, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Threading/NativeEventCalls.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819963487, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Cpp/Internal/ConditionVariable_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819963851, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_CappedSemaphore_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819963971, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/FastReaderReaderWriterLock.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819964027, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Il2CppError.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819964146, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericContextCompare.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819964268, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/PlatformDetection.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819964466, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Cpp/Internal/ConditionVariableData_FutexBased.inl.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819964566, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Threading/Timer.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819964656, "dur": 285, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Assert.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819964942, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/Debugger.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819965042, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/google/sparsehash/sparse_hash_map.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819965412, "dur": 2198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/Mutex.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819967729, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Object.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819967791, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Gcc_Patch_PostInclude.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819967849, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Assembly.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819962292, "dur": 5806, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819953321, "dur": 14777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/y4uu728bnrgn.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819968099, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819968310, "dur": 568, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__23.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819968879, "dur": 419, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__23.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819969302, "dur": 123, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819968309, "dur": 1116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ee2e3w0k1wob.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819969440, "dur": 505, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__21.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819969946, "dur": 1545, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__21.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819971495, "dur": 283, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819969439, "dur": 2339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/sqea375ou6vc.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819971778, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819971977, "dur": 3933, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__17.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819975913, "dur": 2172, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__17.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819978282, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Runtime.InteropServices/GCHandle.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819978443, "dur": 1959, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Reflection/EventInfo.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819978094, "dur": 2550, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819971976, "dur": 8668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zuwcqhyubu5h.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819980690, "dur": 443, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__13.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819981134, "dur": 702, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__13.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819981841, "dur": 143, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819980688, "dur": 1296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7b2vu9a4yrpg.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819982000, "dur": 267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__10.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819982267, "dur": 1111, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib__10.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819983382, "dur": 129, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819981999, "dur": 1512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/08a3eb2apdly.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819983512, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819983785, "dur": 1515, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819985300, "dur": 1844, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/mscorlib.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819987198, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_StaticAssert.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819987495, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/Mono.Security.Cryptography/KeyPairPersistence.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819987619, "dur": 516, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/Mono/RuntimeMarshal.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819987152, "dur": 1203, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819983782, "dur": 4573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/tq9w5f30snmr.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819988395, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestTextureModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819988521, "dur": 515, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestTextureModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819989040, "dur": 235, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819988393, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ucvh59466pup.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819989317, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819989423, "dur": 642, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819990104, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Il2CppHStringReference.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217819990074, "dur": 454, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819989316, "dur": 1213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/obbt2f247suu.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819990575, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819990711, "dur": 498, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819991217, "dur": 270, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819990573, "dur": 934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/feqdsbjheavx.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819991508, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819991696, "dur": 1429, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__8.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819993126, "dur": 971, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__8.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819994101, "dur": 122, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819991694, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/sjnir8hfhc6s.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819994242, "dur": 1990, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__3.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819996233, "dur": 840, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__3.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819997077, "dur": 137, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819994240, "dur": 2974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zs8kbleq078b.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819997237, "dur": 349, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__13.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819997586, "dur": 574, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__13.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819998166, "dur": 128, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819997235, "dur": 1059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4ay5wsg3krnb.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217819998308, "dur": 598, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__1.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819998906, "dur": 909, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__1.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217819999819, "dur": 188, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217819998307, "dur": 1700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/d5kgbwjuthgd.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820000024, "dur": 2906, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820002930, "dur": 1094, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820004371, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Compiler/CompilerEnvironmentClang.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217820004079, "dur": 387, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820000023, "dur": 4444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jqbohcviixpb.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820004514, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820004731, "dur": 654, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820005565, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/Mutex.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217820005391, "dur": 242, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820004512, "dur": 1122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cemyxm83mupm.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820005662, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820005718, "dur": 489, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820006214, "dur": 283, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820005660, "dur": 838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/781tks6emoip.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820006538, "dur": 720, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820007258, "dur": 1333, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820008599, "dur": 201, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820006535, "dur": 2266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/eza7o08anej0.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820008801, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820009007, "dur": 622, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820009630, "dur": 618, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820010256, "dur": 331, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820009006, "dur": 1582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3czwye6a61fe.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820010601, "dur": 1049, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__4.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820011650, "dur": 925, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__4.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820013069, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217820013149, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/Event.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217820012579, "dur": 768, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820010600, "dur": 2748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qwvftluaet2l.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820013349, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820013407, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Threading.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820013482, "dur": 382, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Threading.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820013871, "dur": 258, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820013405, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ucqqncuc2sox.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820014130, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820014191, "dur": 358, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Scheduler.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820014549, "dur": 488, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Scheduler.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820015149, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-blob.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217820015046, "dur": 645, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820014189, "dur": 1502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xkx41l5gwdrw.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820015725, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Configuration.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820016022, "dur": 473, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Configuration.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820016820, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/Mutex.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217820016503, "dur": 413, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820015723, "dur": 1193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/0efgrw5vofcd.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820016932, "dur": 4863, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__9.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820021796, "dur": 3916, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__9.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820025719, "dur": 270, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820016931, "dur": 9059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ow8kpnkg0rty.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820025990, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820026050, "dur": 451, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__1.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820026501, "dur": 653, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System__1.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820027158, "dur": 125, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820026049, "dur": 1234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/o22ut17x98rz.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820027300, "dur": 444, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__8.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820027744, "dur": 874, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__8.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820028625, "dur": 242, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820027299, "dur": 1568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yrkorqtpid1i.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820028884, "dur": 2329, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__5.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820031214, "dur": 839, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__5.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820032056, "dur": 130, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820028883, "dur": 3303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rotvbskw8yw0.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820032200, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__3.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820032453, "dur": 626, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__3.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820033110, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Debug.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217820033083, "dur": 486, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820032198, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/505qb3p01831.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820033609, "dur": 634, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__13.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820034243, "dur": 874, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml__13.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820035320, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Include/Baselib.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217820035128, "dur": 385, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820033607, "dur": 1907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ie1z0icnwpt7.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820035515, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820035613, "dur": 3465, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820039079, "dur": 1148, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Xml.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820040235, "dur": 292, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820035612, "dur": 4916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/8baz31xywens.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820040570, "dur": 370, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Drawing.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820040941, "dur": 438, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Drawing.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820041385, "dur": 188, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820040568, "dur": 1005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zjd9uguj0kta.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820041588, "dur": 359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__5.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820041947, "dur": 807, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__5.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820042760, "dur": 173, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820041587, "dur": 1346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/02vdgn5f26ol.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820042948, "dur": 1348, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__1.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820044297, "dur": 1177, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Data__1.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820045478, "dur": 133, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820042946, "dur": 2665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/srk4f9fooq3n.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820045629, "dur": 1830, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Configuration.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820047459, "dur": 235, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Configuration.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820047698, "dur": 159, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820045627, "dur": 2230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/38g8j4193qlp.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820047871, "dur": 1487, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/SimpleSQL_Runtime.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820049359, "dur": 799, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/SimpleSQL_Runtime.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820050162, "dur": 129, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820047870, "dur": 2421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/flhf6g2wkvwl.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820050311, "dur": 1697, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__1.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820052008, "dur": 1528, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__1.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820053548, "dur": 247, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820050309, "dur": 3486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/okefkgvqajb1.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820053823, "dur": 4986, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppInvokerTable.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820058809, "dur": 4933, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppInvokerTable.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820063751, "dur": 327, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820053821, "dur": 10258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/c38idt0r72f6.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820064098, "dur": 2179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues1.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820066277, "dur": 1222, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues1.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820067561, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/gc/GarbageCollector.h"}}, {"pid": 12345, "tid": 10, "ts": 1755217820067507, "dur": 344, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820064096, "dur": 3778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4pejmsy0ym57.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820068454, "dur": 525208, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/4pejmsy0ym57.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820593767, "dur": 513, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__37.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820594281, "dur": 824, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__37.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820595110, "dur": 133, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820593765, "dur": 1478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/wgpkjgne9qv0.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820595266, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__36.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820595566, "dur": 686, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__36.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820596256, "dur": 122, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820595264, "dur": 1114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/v843y06f5oel.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820596395, "dur": 313, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__35.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820596708, "dur": 582, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__35.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820597294, "dur": 121, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820596394, "dur": 1022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/at1fjrsi2021.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820597433, "dur": 307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__34.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820597740, "dur": 662, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__34.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820598406, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820597432, "dur": 1093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pdyr3helzcri.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820598540, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__33.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820598837, "dur": 613, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__33.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820599453, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820598539, "dur": 1033, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/o5dbj2xmvaa3.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820599572, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820599772, "dur": 309, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__32.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820600082, "dur": 650, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__32.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820600736, "dur": 122, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820599771, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3pan3sjpsmyj.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820600875, "dur": 349, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__31.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820601224, "dur": 724, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__31.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820601952, "dur": 121, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820600873, "dur": 1200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ia7ynj5rpym0.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820602089, "dur": 292, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__30.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820602381, "dur": 701, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__30.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820603085, "dur": 121, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820602088, "dur": 1118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/h2mi33otlw5k.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820603220, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__3.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820603396, "dur": 394, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__3.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820603794, "dur": 120, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820603218, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5fdt1rfbr26t.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820603928, "dur": 385, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__29.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820604313, "dur": 882, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__29.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1755217820605199, "dur": 122, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755217820603926, "dur": 1396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/v9vd6bqscyj8.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217820605936, "dur": 1557203, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/v9vd6bqscyj8.o"}}, {"pid": 12345, "tid": 10, "ts": 1755217822163217, "dur": 4341070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819921722, "dur": 421, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819922143, "dur": 12348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819934497, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VideoModule_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819934570, "dur": 8688, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VideoModule_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819943353, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-blob.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217819943509, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217819943698, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217819943783, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217819943897, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217819944022, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217819944110, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217819944183, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217819944267, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217819944401, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217819943341, "dur": 1198, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819934496, "dur": 10043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ae28egouxwd0.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217819944540, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819944839, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819945016, "dur": 440, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819945491, "dur": 60, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 11, "ts": 1755217819945595, "dur": 56, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool"}}, {"pid": 12345, "tid": 11, "ts": 1755217819945687, "dur": 176, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819944838, "dur": 1026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/aaaify8b4lae.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217819945865, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819945957, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819946069, "dur": 818, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819945956, "dur": 1103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/m6zylmla8hv1.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217819947059, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819947134, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Environments.Internal_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819947198, "dur": 651, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Environments.Internal_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819947862, "dur": 61, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819947133, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pva62dl1x926.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217819947997, "dur": 428, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/System.Runtime.Serialization_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819948452, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217819948432, "dur": 80, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819947948, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3mi413i8sg9k.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217819948527, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Mono.Security_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819948586, "dur": 491, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Mono.Security_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819949094, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217819949081, "dur": 172, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819948525, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/981fy6g0e4s6.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217819949254, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819949351, "dur": 397, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodTable.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819949748, "dur": 716, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodTable.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819949349, "dur": 1134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/e314l67x7zoi.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217819950483, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819950670, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/DOTween_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819950787, "dur": 259, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/DOTween_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819951126, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217819951050, "dur": 197, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819950669, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/elds30fho650.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217819951248, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217819951521, "dur": 1420, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819952942, "dur": 1494, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1755217819951520, "dur": 2940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cxygqyxvvlzx.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217819954588, "dur": 250281, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/cxygqyxvvlzx.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820204941, "dur": 642, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__55.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820205583, "dur": 834, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__55.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820206420, "dur": 123, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820204939, "dur": 1605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/k60oj28r5kcj.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820206565, "dur": 717, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__53.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820207282, "dur": 1693, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__53.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820208979, "dur": 121, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820206563, "dur": 2537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yidkjdewyujk.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820209118, "dur": 466, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__51.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820209584, "dur": 1105, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__51.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820210697, "dur": 306, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820209117, "dur": 1887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cvpfjoj0r4ql.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820211004, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820211065, "dur": 743, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__5.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820211809, "dur": 1539, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__5.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820213352, "dur": 141, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820211063, "dur": 2430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/n8albhut5j3p.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820213514, "dur": 321, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__48.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820213835, "dur": 757, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__48.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820214596, "dur": 122, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820213513, "dur": 1205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/k7ryqds4cuh6.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820214732, "dur": 614, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__47.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820215347, "dur": 1665, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__47.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820217015, "dur": 243, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820214731, "dur": 2527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jz7h6xb68r0q.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820217276, "dur": 490, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__45.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820217766, "dur": 1286, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__45.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820219059, "dur": 526, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820217275, "dur": 2311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/d405afjb3fdo.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820219587, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820219643, "dur": 379, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__43.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820220022, "dur": 770, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__43.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820220796, "dur": 128, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820219641, "dur": 1284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rgd1aqofwpkp.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820220944, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__41.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820221267, "dur": 984, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__41.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820222258, "dur": 264, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820220943, "dur": 1579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5spwa1tjpzrq.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820222523, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820222580, "dur": 728, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__4.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820223308, "dur": 2236, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__4.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820225552, "dur": 259, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820222578, "dur": 3234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/m0q4q1dv1hma.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820225813, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820225939, "dur": 621, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__39.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820226560, "dur": 1074, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__39.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820227855, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericContextHash.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217820227644, "dur": 467, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820225938, "dur": 2173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ykv3xzgldduu.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820228124, "dur": 436, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__36.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820228560, "dur": 1196, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__36.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820229776, "dur": 215, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820228122, "dur": 1869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rh4ai17r3ypc.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820230011, "dur": 1008, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__34.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820231019, "dur": 1447, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__34.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820232471, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820230009, "dur": 2596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5tg6hcdww8wd.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820232632, "dur": 788, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__32.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820233421, "dur": 1177, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__32.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820234606, "dur": 271, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820232630, "dur": 2247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/j6s1hni0pi4e.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820234931, "dur": 2246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__30.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820237177, "dur": 1166, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__30.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820238347, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820234929, "dur": 3537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xi5whyn4isbg.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820238479, "dur": 6881, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__29.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820245361, "dur": 2512, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__29.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820247877, "dur": 127, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820238478, "dur": 9526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rivkpcr4a8uz.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820248026, "dur": 3141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__26.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820251168, "dur": 1384, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__26.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820252556, "dur": 286, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820248024, "dur": 4820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4mnz47fa0fll.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820252899, "dur": 563, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__24.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820253462, "dur": 1192, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__24.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820254662, "dur": 301, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820252897, "dur": 2067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yrgn9onfu3xi.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820255016, "dur": 774, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__22.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820255791, "dur": 1792, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__22.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820257588, "dur": 129, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820255014, "dur": 2704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/c4si9zsjnd5j.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820257744, "dur": 1679, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__20.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820259424, "dur": 1243, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__20.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820260675, "dur": 261, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820257743, "dur": 3194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qg5d9ayngncl.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820260938, "dur": 1376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820262317, "dur": 792, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__19.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820263109, "dur": 1325, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__19.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820264438, "dur": 125, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820262316, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4ne9bjzm7oq8.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820264585, "dur": 285, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__18.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820264870, "dur": 696, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__18.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820265569, "dur": 186, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820264584, "dur": 1171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/j8alw9kp40ep.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820265755, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820266277, "dur": 4583, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__16.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820270860, "dur": 5089, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__16.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820275953, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820266276, "dur": 9796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xkngwkqtxamq.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820276086, "dur": 419, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__11.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820276505, "dur": 832, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__11.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820277341, "dur": 124, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820276085, "dur": 1380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4t8igxm4g6o9.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820277480, "dur": 456, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__1.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820277936, "dur": 761, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Generics__1.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820278701, "dur": 125, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820277479, "dur": 1347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/fxzbb341r3hs.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820278841, "dur": 351, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__9.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820279194, "dur": 694, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__9.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820279891, "dur": 118, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820278840, "dur": 1170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gfqho50ll1jv.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820280022, "dur": 470, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__7.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820280492, "dur": 1428, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__7.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820281923, "dur": 123, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820280021, "dur": 2025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/akj5sm8qfpws.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820282061, "dur": 2505, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__6.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820284566, "dur": 735, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__6.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820285306, "dur": 131, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820282060, "dur": 3378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9bzo86rsbnfd.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820285459, "dur": 732, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__3.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820286191, "dur": 2893, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__3.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820289213, "dur": 709, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/vm/MarshalAlloc.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217820289089, "dur": 840, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820285458, "dur": 4471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/287vs8iz18av.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820289947, "dur": 2918, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__14.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820292866, "dur": 679, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__14.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820293549, "dur": 128, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820289945, "dur": 3732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ah4rnm7cjs1t.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820293677, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820293841, "dur": 2442, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__13.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820296283, "dur": 890, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__13.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820297177, "dur": 126, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820293839, "dur": 3464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/98jb36qefqke.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820297324, "dur": 517, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__10.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820297841, "dur": 1035, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__10.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820298880, "dur": 120, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820297323, "dur": 1677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/25sin8hl5bgw.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820299000, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820299212, "dur": 520, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__1.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820299733, "dur": 1719, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__1.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820301456, "dur": 136, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820299211, "dur": 2381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/glkrsjo3uiiw.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820301613, "dur": 2965, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/DOTween__2.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820304578, "dur": 268, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/DOTween__2.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820304850, "dur": 133, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820301612, "dur": 3371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/eha955mhjsfv.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820305009, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/DOTweenPro.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820305091, "dur": 667, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/DOTweenPro.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820305762, "dur": 120, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820305008, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/lyeloinodc92.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820305899, "dur": 598, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/DOTween.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820306497, "dur": 795, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/DOTween.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820307296, "dur": 159, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820305898, "dur": 1558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yr6gtu8jhqgg.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820307471, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__9.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820307672, "dur": 415, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__9.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820308090, "dur": 120, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820307470, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zec71uzyb9kq.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820308225, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__7.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820308432, "dur": 780, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__7.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820309218, "dur": 222, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820308223, "dur": 1218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/dro215424isr.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820309442, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820309502, "dur": 347, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__6.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820309850, "dur": 480, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__6.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820310334, "dur": 305, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820309499, "dur": 1141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cj4lhqo40gbc.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820310641, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820310699, "dur": 2393, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__52.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820313092, "dur": 1230, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__52.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820314331, "dur": 284, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820310697, "dur": 3919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xmrhp90nya25.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820314616, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820314884, "dur": 3497, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__51.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820318382, "dur": 1130, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__51.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820319516, "dur": 124, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820314883, "dur": 4757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/we704tkpu8kq.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820319668, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__50.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820319969, "dur": 703, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__50.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820320675, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820319667, "dur": 1127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bz3cpk6rrk96.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820320814, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__5.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820321008, "dur": 412, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__5.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820321424, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820320812, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/r30x1ga7xszs.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820321557, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__49.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820321800, "dur": 840, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__49.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820322649, "dur": 281, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820321555, "dur": 1375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/p6c8rs7qimnu.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820322931, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820323006, "dur": 462, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__48.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820323468, "dur": 778, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__48.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820324250, "dur": 121, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820323003, "dur": 1368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/oysr7crsek6e.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820324387, "dur": 388, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__47.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820324775, "dur": 1030, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__47.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820325810, "dur": 136, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820324386, "dur": 1560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3ip3r3cvbwuv.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820325975, "dur": 1430, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__46.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820327405, "dur": 737, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__46.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820328146, "dur": 122, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820325972, "dur": 2296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/i3lfcuyx87ic.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820328288, "dur": 327, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__45.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820328615, "dur": 698, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__45.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820329317, "dur": 118, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820328287, "dur": 1149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/hv1yreiz451w.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820329451, "dur": 336, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__44.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820329787, "dur": 694, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__44.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820330484, "dur": 118, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820329450, "dur": 1152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/wfyac5311hqd.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820330616, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__43.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820330913, "dur": 616, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__43.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820331532, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820330615, "dur": 1036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/b0umwvp4yteu.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820331662, "dur": 268, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__42.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820331930, "dur": 599, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__42.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820332532, "dur": 118, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820331661, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/asa6bg95urxe.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820332651, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820332835, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__41.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820333163, "dur": 1185, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__41.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820334357, "dur": 283, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820332833, "dur": 1808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pc7hw6u8r3lv.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820334641, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820334725, "dur": 947, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__40.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820335672, "dur": 983, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__40.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820336664, "dur": 304, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820334724, "dur": 2245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/o4m1breao14f.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820336970, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820337037, "dur": 573, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__4.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820337611, "dur": 840, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__4.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820338461, "dur": 383, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820337035, "dur": 1810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/6nx9l7v9u4n9.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820338846, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820338906, "dur": 1230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__39.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820340137, "dur": 1182, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__39.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820341383, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include/C/Baselib_ErrorState.inl.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217820341546, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/NonCopyable.h"}}, {"pid": 12345, "tid": 11, "ts": 1755217820341323, "dur": 472, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820338904, "dur": 2893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/i4cpj5haghoz.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820341798, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820341854, "dur": 661, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__38.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820342515, "dur": 1249, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__38.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1755217820343772, "dur": 307, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755217820341851, "dur": 2229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/hws8yp1bcf7j.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217820344636, "dur": 1718132, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/hws8yp1bcf7j.o"}}, {"pid": 12345, "tid": 11, "ts": 1755217822062885, "dur": 4441370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819921731, "dur": 417, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819922148, "dur": 12396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819934548, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAudioModule_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819934664, "dur": 8708, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAudioModule_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819943577, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 12, "ts": 1755217819943688, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 12, "ts": 1755217819943797, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 12, "ts": 1755217819943987, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 12, "ts": 1755217819944119, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 12, "ts": 1755217819944346, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/pch/pch-c.h"}}, {"pid": 12345, "tid": 12, "ts": 1755217819944400, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 12, "ts": 1755217819943385, "dur": 1186, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819934545, "dur": 10026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/1ur6vjxvsv4h.o"}}, {"pid": 12345, "tid": 12, "ts": 1755217819944571, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819944873, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819945043, "dur": 405, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819945661, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 12, "ts": 1755217819945715, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 12, "ts": 1755217819945528, "dur": 298, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819944872, "dur": 954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qor9ea1qo1ly.o"}}, {"pid": 12345, "tid": 12, "ts": 1755217819945827, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819945891, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ImageConversionModule_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819945991, "dur": 192, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ImageConversionModule_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819946216, "dur": 312, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819945889, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/0h9b0boaewh1.o"}}, {"pid": 12345, "tid": 12, "ts": 1755217819946528, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819946600, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819946739, "dur": 493, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819946599, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4p9g640z6oqe.o"}}, {"pid": 12345, "tid": 12, "ts": 1755217819947425, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Analytics_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819947496, "dur": 357, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Analytics_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819947863, "dur": 58, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819947424, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/z0n40fsf9eph.o"}}, {"pid": 12345, "tid": 12, "ts": 1755217819947921, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819948101, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/SimpleSQL_Runtime_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819948248, "dur": 196, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/SimpleSQL_Runtime_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819948100, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/b7e4rpvfv0n2.o"}}, {"pid": 12345, "tid": 12, "ts": 1755217819948505, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217819948678, "dur": 3694, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppTypeDefinitions.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819952372, "dur": 3619, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Il2CppTypeDefinitions.c"}}, {"pid": 12345, "tid": 12, "ts": 1755217819948677, "dur": 7336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/o6c5fkq5uwah.o"}}, {"pid": 12345, "tid": 12, "ts": 1755217819956573, "dur": 974056, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/o6c5fkq5uwah.o"}}, {"pid": 12345, "tid": 12, "ts": 1755217820930758, "dur": 365, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__2.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1755217820931123, "dur": 646, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__2.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1755217820931774, "dur": 132, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217820930756, "dur": 1150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/k094li1doeva.o"}}, {"pid": 12345, "tid": 12, "ts": 1755217820931932, "dur": 281, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__19.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1755217820932214, "dur": 595, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/Backup_M2//AppCode/FlashCardApp/IosGameAppV31/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__19.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1755217820932813, "dur": 121, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755217820931931, "dur": 1003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/o7vuxo0batkd.o"}}, {"pid": 12345, "tid": 12, "ts": 1755217820933396, "dur": 1246422, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/o7vuxo0batkd.o"}}, {"pid": 12345, "tid": 12, "ts": 1755217822179867, "dur": 4324485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755217826515576, "dur": 1451, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 36539, "tid": 1, "ts": 1755217826526332, "dur": 617, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend_profiler0.traceevents"}}, {"pid": 36539, "tid": 1, "ts": 1755217826527014, "dur": 18, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 36539, "tid": 1, "ts": 1755217826527054, "dur": 9, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend_profiler1.traceevents"}}, {"pid": 36539, "tid": 1, "ts": 1755217826526957, "dur": 56, "ph": "X", "name": "backend_profiler0.traceevents", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217826527037, "dur": 16, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217826527067, "dur": 449, "ph": "X", "name": "backend_profiler1.traceevents", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217826524569, "dur": 3316, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"pid": 36539, "tid": 1, "ts": 1755217826548598, "dur": 1246, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}