﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void DiagnosticsFactory__ctor_m6AFD4725FFA4F4054B13917A376B8BE059AF4D27 (void);
extern void MetricsFactory__ctor_mD15F90B2C781DE2B48F41556FECEFAB2347AFC0C (void);
static Il2CppMethodPointer s_methodPointers[2] = 
{
	DiagnosticsFactory__ctor_m6AFD4725FFA4F4054B13917A376B8BE059AF4D27,
	MetricsFactory__ctor_mD15F90B2C781DE2B48F41556FECEFAB2347AFC0C,
};
static const int32_t s_InvokerIndices[2] = 
{
	6521,
	6521,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Services_Core_Telemetry_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Services_Core_Telemetry_CodeGenModule = 
{
	"Unity.Services.Core.Telemetry.dll",
	2,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
