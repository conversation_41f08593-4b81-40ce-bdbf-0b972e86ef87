﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


struct VirtualActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename T1, typename T2, typename T3>
struct VirtualActionInvoker3
{
	typedef void (*Action)(void*, T1, T2, T3, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1, T2 p2, T3 p3)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, p2, p3, invokeData.method);
	}
};

struct Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct ConfigU5BU5D_tD729F9EBA728115FA89F6C0FC650A2B23234809C;
struct Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D;
struct Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910;
struct InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF;
struct InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0;
struct InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A;
struct Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50;
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct SemaphoreSlim_t0D5CB5685D9BFA5BF95CEC6E7395490F933E8DB2;
struct StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC;
struct Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE;
struct String_t;
struct Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2;
struct ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159;
struct Config_t7A2A728653D74B190FE89ACF3E4DD6A9B877764A;
struct ReadWriteTask_t0821BF49EE38596C7734E86E1A6A39D769BE2C05;

IL2CPP_EXTERN_C RuntimeClass* Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE0_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE1_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE2_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE3_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE4_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE5_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE6_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE7_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE8_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE9_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEA_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEB_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEC_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DED_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEE_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEF_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DF0_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DF1_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DF2_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral06F3358B23D666113A1020E1C9CFEBE16373BE40;
IL2CPP_EXTERN_C String_t* _stringLiteral13A5361A51002BE0AE3A86C6F54E7ADAC4F2CE94;
IL2CPP_EXTERN_C String_t* _stringLiteral260306369A04CA189E353A93EBB484ED8F9A9B43;
IL2CPP_EXTERN_C String_t* _stringLiteral359C7A1FB5CEBD929D7F11F5D3E96EDE7FF01384;
IL2CPP_EXTERN_C String_t* _stringLiteral527C1A81C9577E20EFCD218DE9B39383A8F64CD0;
IL2CPP_EXTERN_C String_t* _stringLiteral5A888468814C6717D8F1F53C27076E49BCF685AE;
IL2CPP_EXTERN_C String_t* _stringLiteral61CF8C6E69A5020616A55D8196F59FE4DE0129D6;
IL2CPP_EXTERN_C String_t* _stringLiteral6304F4645B5484ACF5D9DF2D847AE616393DC417;
IL2CPP_EXTERN_C String_t* _stringLiteral81B54C81CE5770A2FB716FE3138FA18CE998793D;
IL2CPP_EXTERN_C String_t* _stringLiteral90581047810EB87A7277461DDA1C1493B91DAAA4;
IL2CPP_EXTERN_C String_t* _stringLiteral96025B6397AAC8D06A75085B92AD0F0146044D16;
IL2CPP_EXTERN_C String_t* _stringLiteral9A971A9294400EA492DFEFCF8370FA1EBA838E06;
IL2CPP_EXTERN_C String_t* _stringLiteralA840F25536BE8295D00B8780BF11900F5EE6774E;
IL2CPP_EXTERN_C String_t* _stringLiteralBDD794DC7884A15D601FC8AD88E8B6637CF36948;
IL2CPP_EXTERN_C String_t* _stringLiteralC1E0482ABDB4530F47C01C2A81FB06ED6E98A110;
IL2CPP_EXTERN_C String_t* _stringLiteralCC98F8D5063D43F6A1D8B5158D9DE47EAC048113;
IL2CPP_EXTERN_C String_t* _stringLiteralCFBC3A862771D0485E915BD869029175AD24B07C;
IL2CPP_EXTERN_C String_t* _stringLiteralD3DEC6A6A3177F7D2965AAB68291E77977CF1E3E;
IL2CPP_EXTERN_C String_t* _stringLiteralDA84EF263557F5F56FABA93B2A6EC89E8F3E0102;
IL2CPP_EXTERN_C String_t* _stringLiteralDC4A06A0DE599F745DBDD44A6FDE6212859D3A5F;
IL2CPP_EXTERN_C String_t* _stringLiteralE960A05B0E3F3B1A832A46162FB0C2332497D8F4;
IL2CPP_EXTERN_C const RuntimeMethod* ZOutputStream_Finish_mB1EE1A4B30EA2CF845108C50B0D2655FA62E1EB6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZOutputStream_Read_m5CC1684F4D0FDD3D45F8A00A4832FFFC7C288BD2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZOutputStream_Seek_m84B5ED0E1557F2D0F865AE92E9E488B1E2584810_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZOutputStream_SetLength_m339EBB86CB8188B04EBC9576B7371BCC6BFBE8E9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZOutputStream_Write_m9E72C9FBE8DC2FF250209F4A20410E70C5B6CEB0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZOutputStream_get_Length_mB0EE6639C42CFE697F7AC0F459D6DE666C2167F6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZOutputStream_get_Position_m058592C46002ADA486B37A63B86751B57503A47A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZOutputStream_set_Position_mF3AB2E5F71D00FB0891C44BF4A731BE074F2C7BF_RuntimeMethod_var;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE  : public RuntimeObject
{
};
struct Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D  : public RuntimeObject
{
};
struct Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B  : public RuntimeObject
{
	ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___strm;
	int32_t ___status;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___pending_buf;
	int32_t ___pending_buf_size;
	int32_t ___pending_out;
	int32_t ___pending;
	int32_t ___noheader;
	uint8_t ___data_type;
	uint8_t ___method;
	int32_t ___last_flush;
	int32_t ___w_size;
	int32_t ___w_bits;
	int32_t ___w_mask;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___window;
	int32_t ___window_size;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___prev;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___head;
	int32_t ___ins_h;
	int32_t ___hash_size;
	int32_t ___hash_bits;
	int32_t ___hash_mask;
	int32_t ___hash_shift;
	int32_t ___block_start;
	int32_t ___match_length;
	int32_t ___prev_match;
	int32_t ___match_available;
	int32_t ___strstart;
	int32_t ___match_start;
	int32_t ___lookahead;
	int32_t ___prev_length;
	int32_t ___max_chain_length;
	int32_t ___max_lazy_match;
	int32_t ___level;
	int32_t ___strategy;
	int32_t ___good_match;
	int32_t ___nice_match;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___dyn_ltree;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___dyn_dtree;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___bl_tree;
	Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD* ___l_desc;
	Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD* ___d_desc;
	Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD* ___bl_desc;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___bl_count;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___heap;
	int32_t ___heap_len;
	int32_t ___heap_max;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___depth;
	int32_t ___l_buf;
	int32_t ___lit_bufsize;
	int32_t ___last_lit;
	int32_t ___d_buf;
	int32_t ___opt_len;
	int32_t ___static_len;
	int32_t ___matches;
	int32_t ___last_eob_len;
	uint32_t ___bi_buf;
	int32_t ___bi_valid;
};
struct InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF  : public RuntimeObject
{
	int32_t ___mode;
	int32_t ___left;
	int32_t ___table;
	int32_t ___index;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___blens;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___bb;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___tb;
	InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* ___codes;
	int32_t ___last;
	int32_t ___bitk;
	int32_t ___bitb;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___hufts;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___window;
	int32_t ___end;
	int32_t ___read;
	int32_t ___write;
	RuntimeObject* ___checkfn;
	int64_t ___check;
	InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* ___inftree;
};
struct InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0  : public RuntimeObject
{
	int32_t ___mode;
	int32_t ___len;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___tree;
	int32_t ___tree_index;
	int32_t ___need;
	int32_t ___lit;
	int32_t ___get;
	int32_t ___dist;
	uint8_t ___lbits;
	uint8_t ___dbits;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___ltree;
	int32_t ___ltree_index;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___dtree;
	int32_t ___dtree_index;
};
struct InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A  : public RuntimeObject
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___hn;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___v;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___c;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___r;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___u;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___x;
};
struct Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50  : public RuntimeObject
{
	int32_t ___mode;
	int32_t ___method;
	Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D* ___was;
	int64_t ___need;
	int32_t ___marker;
	int32_t ___nowrap;
	int32_t ___wbits;
	InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* ___blocks;
};
struct MarshalByRefObject_t8C2F4C5854177FD60439EB1FCCFC1B3CFAFE8DCE  : public RuntimeObject
{
	RuntimeObject* ____identity;
};
struct MarshalByRefObject_t8C2F4C5854177FD60439EB1FCCFC1B3CFAFE8DCE_marshaled_pinvoke
{
	Il2CppIUnknown* ____identity;
};
struct MarshalByRefObject_t8C2F4C5854177FD60439EB1FCCFC1B3CFAFE8DCE_marshaled_com
{
	Il2CppIUnknown* ____identity;
};
struct StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC  : public RuntimeObject
{
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___static_tree;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___extra_bits;
	int32_t ___extra_base;
	int32_t ___elems;
	int32_t ___max_length;
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD  : public RuntimeObject
{
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___dyn_tree;
	int32_t ___max_code;
	StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* ___stat_desc;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159  : public RuntimeObject
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___next_in;
	int32_t ___next_in_index;
	int32_t ___avail_in;
	int64_t ___total_in;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___next_out;
	int32_t ___next_out_index;
	int32_t ___avail_out;
	int64_t ___total_out;
	String_t* ___msg;
	Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* ___dstate;
	Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* ___istate;
	int32_t ___data_type;
	int64_t ___adler;
	Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D* ____adler;
};
struct Config_t7A2A728653D74B190FE89ACF3E4DD6A9B877764A  : public RuntimeObject
{
	int32_t ___good_length;
	int32_t ___max_lazy;
	int32_t ___nice_length;
	int32_t ___max_chain;
	int32_t ___func;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175 
{
	int16_t ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3 
{
	int64_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE  : public MarshalByRefObject_t8C2F4C5854177FD60439EB1FCCFC1B3CFAFE8DCE
{
	ReadWriteTask_t0821BF49EE38596C7734E86E1A6A39D769BE2C05* ____activeReadWriteTask;
	SemaphoreSlim_t0D5CB5685D9BFA5BF95CEC6E7395490F933E8DB2* ____asyncActiveSemaphore;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D1024_t5733EFBB33AADAEBEA2E1783970DE850A5288465 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D1024_t5733EFBB33AADAEBEA2E1783970DE850A5288465__padding[1024];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D1152_t39D1452637F9AF907A60FC21ACEE206634AFB738 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D1152_t39D1452637F9AF907A60FC21ACEE206634AFB738__padding[1152];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D116_tC6FDE6B220A159FB9CF58C0D0FF6D2A4C4DE6CCC 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D116_tC6FDE6B220A159FB9CF58C0D0FF6D2A4C4DE6CCC__padding[116];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A__padding[12];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D120_t1A602D921475849D96DBB778B8057A2B3AC1474C 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D120_t1A602D921475849D96DBB778B8057A2B3AC1474C__padding[120];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D124_tAE68731600756EA3FD4F269D615CE3F424CC877D 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D124_tAE68731600756EA3FD4F269D615CE3F424CC877D__padding[124];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D16_t4A7160F2804128725ADD66EE44CCDC1E5C178D20 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D16_t4A7160F2804128725ADD66EE44CCDC1E5C178D20__padding[16];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D20_t9833B7E9C1287A7397AA6BD95CCF5530FBED5F9F 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D20_t9833B7E9C1287A7397AA6BD95CCF5530FBED5F9F__padding[20];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6__padding[256];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D28_t08CFF36BD4C6BC1BD59ED5DE705AE93D543EB514 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D28_t08CFF36BD4C6BC1BD59ED5DE705AE93D543EB514__padding[28];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D32_tD203A6A0D182C636B7D23BCFC8D5433EAFB489A5 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D32_tD203A6A0D182C636B7D23BCFC8D5433EAFB489A5__padding[32];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D384_t3727E4CA87E3355899A26976C8A73A8728543C69 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D384_t3727E4CA87E3355899A26976C8A73A8728543C69__padding[384];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D48_t61DA1ED154F63DEF6BA965478A7B1812621A6289 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D48_t61DA1ED154F63DEF6BA965478A7B1812621A6289__padding[48];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D512_t70107955B3092D86116B10E2F36588BFE4E1E48B 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D512_t70107955B3092D86116B10E2F36588BFE4E1E48B__padding[512];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D56_tB49D07BE6A99CA9CD54727B585DA875C5145AA5E 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D56_tB49D07BE6A99CA9CD54727B585DA875C5145AA5E__padding[56];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D6144_t5EE9CCAD46AF386A6AA58B2ADDED93142CBBC3A8 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D6144_t5EE9CCAD46AF386A6AA58B2ADDED93142CBBC3A8__padding[6144];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D64_t71A3AEEE8AC1C03A42E847F3EB602CF14B656185 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D64_t71A3AEEE8AC1C03A42E847F3EB602CF14B656185__padding[64];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D640_t391A8446578A9C9781A4975AF9291517FD2FAA56 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D640_t391A8446578A9C9781A4975AF9291517FD2FAA56__padding[640];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D68_t48E3042FF11BF39DD53EF93DC6B4D438F813785E 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D68_t48E3042FF11BF39DD53EF93DC6B4D438F813785E__padding[68];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D76_tE79A9075CF09C8AF5245306C33C1ED33ACC19736 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D76_tE79A9075CF09C8AF5245306C33C1ED33ACC19736__padding[76];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D8_tA7BA3628CA83B4E39F669A9AA95DAC65A7DF7D8D 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D8_tA7BA3628CA83B4E39F669A9AA95DAC65A7DF7D8D__padding[8];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D96_t84D1083D431627FD02068042F4D9184AAE240FBF 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D96_t84D1083D431627FD02068042F4D9184AAE240FBF__padding[96];
	};
};
#pragma pack(pop, tp)
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct SeekOrigin_t7EB9AD0EDF26368A40F48FA2098F02160B1E8000 
{
	int32_t ___value__;
};
struct ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2  : public Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE
{
	ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___z;
	int32_t ___flushLevel;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___buf;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___buf1;
	bool ___compress;
	Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* ___output;
	bool ___closed;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE_StaticFields
{
	U24ArrayTypeU3D32_tD203A6A0D182C636B7D23BCFC8D5433EAFB489A5 ___U24fieldU2D0;
	U24ArrayTypeU3D32_tD203A6A0D182C636B7D23BCFC8D5433EAFB489A5 ___U24fieldU2D1;
	U24ArrayTypeU3D640_t391A8446578A9C9781A4975AF9291517FD2FAA56 ___U24fieldU2D9;
	U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 ___U24fieldU2DC;
	U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 ___U24fieldU2D19;
	U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 ___U24fieldU2D1A;
	U24ArrayTypeU3D32_tD203A6A0D182C636B7D23BCFC8D5433EAFB489A5 ___U24fieldU2D1B;
	U24ArrayTypeU3D1024_t5733EFBB33AADAEBEA2E1783970DE850A5288465 ___U24fieldU2D1C;
	U24ArrayTypeU3D1024_t5733EFBB33AADAEBEA2E1783970DE850A5288465 ___U24fieldU2D1D;
	U24ArrayTypeU3D1024_t5733EFBB33AADAEBEA2E1783970DE850A5288465 ___U24fieldU2D1E;
	U24ArrayTypeU3D1024_t5733EFBB33AADAEBEA2E1783970DE850A5288465 ___U24fieldU2D1F;
	U24ArrayTypeU3D1024_t5733EFBB33AADAEBEA2E1783970DE850A5288465 ___U24fieldU2D20;
	U24ArrayTypeU3D1024_t5733EFBB33AADAEBEA2E1783970DE850A5288465 ___U24fieldU2D21;
	U24ArrayTypeU3D1024_t5733EFBB33AADAEBEA2E1783970DE850A5288465 ___U24fieldU2D22;
	U24ArrayTypeU3D1024_t5733EFBB33AADAEBEA2E1783970DE850A5288465 ___U24fieldU2D23;
	U24ArrayTypeU3D16_t4A7160F2804128725ADD66EE44CCDC1E5C178D20 ___U24fieldU2D3C;
	U24ArrayTypeU3D96_t84D1083D431627FD02068042F4D9184AAE240FBF ___U24fieldU2D3D;
	U24ArrayTypeU3D56_tB49D07BE6A99CA9CD54727B585DA875C5145AA5E ___U24fieldU2D3E;
	U24ArrayTypeU3D16_t4A7160F2804128725ADD66EE44CCDC1E5C178D20 ___U24fieldU2D3F;
	U24ArrayTypeU3D48_t61DA1ED154F63DEF6BA965478A7B1812621A6289 ___U24fieldU2D40;
	U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 ___U24fieldU2D41;
	U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 ___U24fieldU2D42;
	U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 ___U24fieldU2D43;
	U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 ___U24fieldU2D44;
	U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 ___U24fieldU2D45;
	U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 ___U24fieldU2D46;
	U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 ___U24fieldU2D47;
	U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 ___U24fieldU2D48;
	U24ArrayTypeU3D28_t08CFF36BD4C6BC1BD59ED5DE705AE93D543EB514 ___U24fieldU2DA3;
	U24ArrayTypeU3D32_tD203A6A0D182C636B7D23BCFC8D5433EAFB489A5 ___U24fieldU2DA5;
	U24ArrayTypeU3D20_t9833B7E9C1287A7397AA6BD95CCF5530FBED5F9F ___U24fieldU2DA6;
	U24ArrayTypeU3D20_t9833B7E9C1287A7397AA6BD95CCF5530FBED5F9F ___U24fieldU2DA7;
	U24ArrayTypeU3D16_t4A7160F2804128725ADD66EE44CCDC1E5C178D20 ___U24fieldU2DA8;
	U24ArrayTypeU3D16_t4A7160F2804128725ADD66EE44CCDC1E5C178D20 ___U24fieldU2DA9;
	U24ArrayTypeU3D16_t4A7160F2804128725ADD66EE44CCDC1E5C178D20 ___U24fieldU2DAA;
	U24ArrayTypeU3D16_t4A7160F2804128725ADD66EE44CCDC1E5C178D20 ___U24fieldU2DAB;
	U24ArrayTypeU3D16_t4A7160F2804128725ADD66EE44CCDC1E5C178D20 ___U24fieldU2DAC;
	U24ArrayTypeU3D16_t4A7160F2804128725ADD66EE44CCDC1E5C178D20 ___U24fieldU2DAD;
	U24ArrayTypeU3D16_t4A7160F2804128725ADD66EE44CCDC1E5C178D20 ___U24fieldU2DAE;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DAF;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DB0;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DB1;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DB2;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DB3;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DB4;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DB5;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DB6;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DB7;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DB8;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DB9;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DBA;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DBB;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DBC;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DBD;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DBE;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DBF;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DC0;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DC1;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DC2;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DC3;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DC4;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DC5;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DC6;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DC7;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DC8;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DC9;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DCA;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DCB;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DCC;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DCD;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DCE;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DCF;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DD0;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DD1;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DD2;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DD3;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DD4;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DD5;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DD6;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DD7;
	U24ArrayTypeU3D12_t9E5180F8274B7532523610DDAA39D51402594A7A ___U24fieldU2DD8;
	U24ArrayTypeU3D8_tA7BA3628CA83B4E39F669A9AA95DAC65A7DF7D8D ___U24fieldU2DD9;
	U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 ___U24fieldU2DDA;
	U24ArrayTypeU3D64_t71A3AEEE8AC1C03A42E847F3EB602CF14B656185 ___U24fieldU2DDB;
	U24ArrayTypeU3D16_t4A7160F2804128725ADD66EE44CCDC1E5C178D20 ___U24fieldU2DDE;
	U24ArrayTypeU3D68_t48E3042FF11BF39DD53EF93DC6B4D438F813785E ___U24fieldU2DE0;
	U24ArrayTypeU3D76_tE79A9075CF09C8AF5245306C33C1ED33ACC19736 ___U24fieldU2DE1;
	U24ArrayTypeU3D68_t48E3042FF11BF39DD53EF93DC6B4D438F813785E ___U24fieldU2DE2;
	U24ArrayTypeU3D6144_t5EE9CCAD46AF386A6AA58B2ADDED93142CBBC3A8 ___U24fieldU2DE3;
	U24ArrayTypeU3D384_t3727E4CA87E3355899A26976C8A73A8728543C69 ___U24fieldU2DE4;
	U24ArrayTypeU3D124_tAE68731600756EA3FD4F269D615CE3F424CC877D ___U24fieldU2DE5;
	U24ArrayTypeU3D124_tAE68731600756EA3FD4F269D615CE3F424CC877D ___U24fieldU2DE6;
	U24ArrayTypeU3D120_t1A602D921475849D96DBB778B8057A2B3AC1474C ___U24fieldU2DE7;
	U24ArrayTypeU3D120_t1A602D921475849D96DBB778B8057A2B3AC1474C ___U24fieldU2DE8;
	U24ArrayTypeU3D1152_t39D1452637F9AF907A60FC21ACEE206634AFB738 ___U24fieldU2DE9;
	U24ArrayTypeU3D120_t1A602D921475849D96DBB778B8057A2B3AC1474C ___U24fieldU2DEA;
	U24ArrayTypeU3D116_tC6FDE6B220A159FB9CF58C0D0FF6D2A4C4DE6CCC ___U24fieldU2DEB;
	U24ArrayTypeU3D120_t1A602D921475849D96DBB778B8057A2B3AC1474C ___U24fieldU2DEC;
	U24ArrayTypeU3D76_tE79A9075CF09C8AF5245306C33C1ED33ACC19736 ___U24fieldU2DED;
	U24ArrayTypeU3D20_t9833B7E9C1287A7397AA6BD95CCF5530FBED5F9F ___U24fieldU2DEE;
	U24ArrayTypeU3D512_t70107955B3092D86116B10E2F36588BFE4E1E48B ___U24fieldU2DEF;
	U24ArrayTypeU3D256_tA0C9AEEAC4ABA69DFCB0DCDC750FF0577A1E19E6 ___U24fieldU2DF0;
	U24ArrayTypeU3D116_tC6FDE6B220A159FB9CF58C0D0FF6D2A4C4DE6CCC ___U24fieldU2DF1;
	U24ArrayTypeU3D120_t1A602D921475849D96DBB778B8057A2B3AC1474C ___U24fieldU2DF2;
};
struct Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B_StaticFields
{
	ConfigU5BU5D_tD729F9EBA728115FA89F6C0FC650A2B23234809C* ___config_table;
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ___z_errmsg;
};
struct InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_StaticFields
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___inflate_mask;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___border;
};
struct InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_StaticFields
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___inflate_mask;
};
struct InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___fixed_tl;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___fixed_td;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___cplens;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___cplext;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___cpdist;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___cpdext;
};
struct Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50_StaticFields
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___mark;
};
struct StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_StaticFields
{
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___static_ltree;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___static_dtree;
	StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* ___static_l_desc;
	StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* ___static_d_desc;
	StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* ___static_bl_desc;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___extra_lbits;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___extra_dbits;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___extra_blbits;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___bl_order;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ____dist_code;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ____length_code;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___base_length;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___base_dist;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE_StaticFields
{
	Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* ___Null;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C  : public RuntimeArray
{
	ALIGN_FIELD (8) int32_t m_Items[1];

	inline int32_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline int32_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, int32_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline int32_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline int32_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, int32_t value)
	{
		m_Items[index] = value;
	}
};
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D  : public RuntimeArray
{
	ALIGN_FIELD (8) int64_t m_Items[1];

	inline int64_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline int64_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, int64_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline int64_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline int64_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, int64_t value)
	{
		m_Items[index] = value;
	}
};
struct Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E  : public RuntimeArray
{
	ALIGN_FIELD (8) Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* m_Items[1];

	inline Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB  : public RuntimeArray
{
	ALIGN_FIELD (8) int16_t m_Items[1];

	inline int16_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline int16_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, int16_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline int16_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline int16_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, int16_t value)
	{
		m_Items[index] = value;
	}
};



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfCodes__ctor_mABD6546D31140B3D8BD034F3CFD66E51B82A3834 (InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfTree__ctor_mEC52C41356BFD818C5C776A7546F36E008825598 (InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfBlocks_reset_m287421B0D47CBB5CAB51FA76A930806BAFF87BC8 (InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D* ___1_c, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfCodes_free_m6FF96CFF077C86EAD34D40DAD8499E2ED55DC131 (InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t Adler32_adler32_m47B915EF9E1AD64F5504CC03340A1E09ED08A416 (Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D* __this, int64_t ___0_adler, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___1_buf, int32_t ___2_index, int32_t ___3_len, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA (InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, int32_t ___1_r, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfTree_inflate_trees_fixed_m7D58777D20FA8CACF92F4D42A45E0BCC42934D28 (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_bl, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___1_bd, Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* ___2_tl, Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* ___3_td, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___4_z, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfCodes_init_m573B577586A6B3D38F81FC3C570D29C319BEAFE4 (InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* __this, int32_t ___0_bl, int32_t ___1_bd, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___2_tl, int32_t ___3_tl_index, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___4_td, int32_t ___5_td_index, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___6_z, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41 (RuntimeArray* ___0_sourceArray, int32_t ___1_sourceIndex, RuntimeArray* ___2_destinationArray, int32_t ___3_destinationIndex, int32_t ___4_length, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfTree_inflate_trees_bits_m08FC677299F8FBF8D84B4E1AF802E5037586C15A (InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* __this, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_c, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___1_bb, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___2_tb, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___3_hp, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___4_z, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfTree_inflate_trees_dynamic_m3FC3C6E9606B5B5494CA4D6F13D4B23F292A177F (InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* __this, int32_t ___0_nl, int32_t ___1_nd, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___2_c, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___3_bl, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___4_bd, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___5_tl, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___6_td, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___7_hp, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___8_z, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfCodes_proc_m4E3ABBA8C356A0566899229FDE22203A513AC4D1 (InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* __this, InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* ___0_s, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___1_z, int32_t ___2_r, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfCodes_inflate_fast_m14E202BC92D499CF9473F320A0F1CD68ECEE1E37 (InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* __this, int32_t ___0_bl, int32_t ___1_bd, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___2_tl, int32_t ___3_tl_index, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___4_td, int32_t ___5_td_index, InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* ___6_s, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___7_z, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfBlocks_free_m9E8C460B7D27F7D5C5C72699646494819E754D78 (InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Inflate_inflateEnd_mF5A412434A2CE53A5D62205EFAF86FD8485CA85E (Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfBlocks__ctor_mBBFA4CFB361E5D892398BBBB40440E973CA65093 (InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, RuntimeObject* ___1_checkfn, int32_t ___2_w, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Inflate_inflateReset_m45912560840BFE24481A507D494957B89BBEF964 (Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfBlocks_proc_m8FB471432B90D42E736660EE3001F601B41966CA (InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, int32_t ___1_r, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfTree_initWorkArea_m6920B04FCABD2ED354A307F6B18A49D4421546F6 (InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* __this, int32_t ___0_vsize, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfTree_huft_build_m46F10893F99C616504F6CBB38F34995EBF61FCE7 (InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* __this, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_b, int32_t ___1_bindex, int32_t ___2_n, int32_t ___3_s, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___4_d, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___5_e, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___6_t, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___7_m, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___8_hp, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___9_hn, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___10_v, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StaticTree__ctor_mAE62D8BA87B612590731EDA50692FCC18CBF7B22 (StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* __this, Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___0_static_tree, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___1_extra_bits, int32_t ___2_extra_base, int32_t ___3_elems, int32_t ___4_max_length, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Deflate_pqdownheap_mB4A295AA13CEC78D449DEFD1E2CF668E83E7AE4E (Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* __this, Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___0_tree, int32_t ___1_k, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t Math_Max_m12FB4E1302123ADB441E3A7BDF52E8404DDE53A2 (uint8_t ___0_val1, uint8_t ___1_val2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Tree_gen_bitlen_mC805176D33CF7261132A5A8A1687E50A68A97675 (Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD* __this, Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* ___0_s, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Tree_gen_codes_m2D3268BCC85C606634411576B8C2F4FCF08A523F (Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___0_tree, int32_t ___1_max_code, Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___2_bl_count, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Tree_bi_reverse_m21930311CEEE30D0F936BDF649B5B7BC93DC2FFC (int32_t ___0_code, int32_t ___1_len, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZStream__ctor_m3F4505E8C1CCB3315F19DD3D4345AE22856E1F75 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Stream__ctor_mE8B074A0EBEB026FFF14062AB4B8A78E17EFFBF0 (Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_inflateInit_m143D4D0243CA113657D2588ECB76683FDB337CA4 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_deflateEnd_mF650C79EFE4A71E58A815EE840EDEFCB125B4C6E (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_inflateEnd_m5F7D9F5A98C6E0A114C74524939232063F1807ED (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZStream_free_mC316D8E6ECB2DA4AC952270C4D2A50456A214AE7 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_deflate_mC8B68294A59CED32C2FCEA8AA012D94932517CCB (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, int32_t ___0_flush, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_inflate_mB6AE79419A82CEBC6DDBB92BE75155EE6CE56DA7 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, int32_t ___0_f, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IOException__ctor_mE0612A16064F93C7EBB468D6874777BD70CB50CA (IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Adler32__ctor_m64048ACD59254C7F1277C4A8DA58FF141166A236 (Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_inflateInit_m6BAD260A712D100475C473BD11C65084E61AC9E5 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, int32_t ___0_w, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_inflateInit_mFEA1826439939858F3A3F6D9B23BA7FFC22A0F83 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, int32_t ___0_w, bool ___1_nowrap, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Inflate__ctor_mC3FDB4D5246A093EE60FEF5698867D51DFB27104 (Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Inflate_inflateInit_mE54CCCBA0F6A9571D5E2AA707A3240C286815C86 (Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, int32_t ___1_w, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Inflate_inflate_m51AE19089B0B082180C4E6C97A88561386E3821E (Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, int32_t ___1_f, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_deflateInit_m20294775020642696F4FB1210A3BAA40D0486B71 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, int32_t ___0_level, int32_t ___1_bits, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_deflateInit_m9175B61C308978E5262D36D2A0FDF726BCC83D5E (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, int32_t ___0_level, int32_t ___1_bits, bool ___2_nowrap, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Deflate__ctor_mE92BACC46F4569B41FA175D382097918B79362EB (Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Deflate_deflateInit_m9E749DAAA1A022941B744D9280C7610FAA4BEC1B (Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_strm, int32_t ___1_level, int32_t ___2_bits, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Deflate_deflate_m497186F3E3DA81CEFC3A249ACA43B7420FF84E2E (Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_strm, int32_t ___1_flush, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Deflate_deflateEnd_m9795852C677FC77B33A8008BEE56C8DE6CBA498B (Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Config__ctor_m338FA11A47241559E05C774E6BFE7839DB286EE3 (Config_t7A2A728653D74B190FE89ACF3E4DD6A9B877764A* __this, int32_t ___0_good_length, int32_t ___1_max_lazy, int32_t ___2_nice_length, int32_t ___3_max_chain, int32_t ___4_func, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_good_length;
		__this->___good_length = L_0;
		int32_t L_1 = ___1_max_lazy;
		__this->___max_lazy = L_1;
		int32_t L_2 = ___2_nice_length;
		__this->___nice_length = L_2;
		int32_t L_3 = ___3_max_chain;
		__this->___max_chain = L_3;
		int32_t L_4 = ___4_func;
		__this->___func = L_4;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfBlocks__ctor_mBBFA4CFB361E5D892398BBBB40440E973CA65093 (InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, RuntimeObject* ___1_checkfn, int32_t ___2_w, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)1);
		__this->___bb = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___bb), (void*)L_0);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)1);
		__this->___tb = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___tb), (void*)L_1);
		InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* L_2 = (InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0*)il2cpp_codegen_object_new(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		InfCodes__ctor_mABD6546D31140B3D8BD034F3CFD66E51B82A3834(L_2, NULL);
		__this->___codes = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___codes), (void*)L_2);
		InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* L_3 = (InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A*)il2cpp_codegen_object_new(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var);
		InfTree__ctor_mEC52C41356BFD818C5C776A7546F36E008825598(L_3, NULL);
		__this->___inftree = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___inftree), (void*)L_3);
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_4 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)4320));
		__this->___hufts = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___hufts), (void*)L_4);
		int32_t L_5 = ___2_w;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_6 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)L_5);
		__this->___window = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___window), (void*)L_6);
		int32_t L_7 = ___2_w;
		__this->___end = L_7;
		RuntimeObject* L_8 = ___1_checkfn;
		__this->___checkfn = L_8;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___checkfn), (void*)L_8);
		__this->___mode = 0;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_9 = ___0_z;
		InfBlocks_reset_m287421B0D47CBB5CAB51FA76A930806BAFF87BC8(__this, L_9, (Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D*)NULL, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfBlocks_reset_m287421B0D47CBB5CAB51FA76A930806BAFF87BC8 (InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D* ___1_c, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int64_t V_1 = 0;
	{
		Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D* L_0 = ___1_c;
		if (!L_0)
		{
			goto IL_000f;
		}
	}
	{
		Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D* L_1 = ___1_c;
		int64_t L_2 = __this->___check;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (int64_t)L_2);
	}

IL_000f:
	{
		int32_t L_3 = __this->___mode;
		if ((((int32_t)L_3) == ((int32_t)4)))
		{
			goto IL_0027;
		}
	}
	{
		int32_t L_4 = __this->___mode;
		if ((!(((uint32_t)L_4) == ((uint32_t)5))))
		{
			goto IL_0027;
		}
	}

IL_0027:
	{
		int32_t L_5 = __this->___mode;
		if ((!(((uint32_t)L_5) == ((uint32_t)6))))
		{
			goto IL_003f;
		}
	}
	{
		InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* L_6 = __this->___codes;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_7 = ___0_z;
		NullCheck(L_6);
		InfCodes_free_m6FF96CFF077C86EAD34D40DAD8499E2ED55DC131(L_6, L_7, NULL);
	}

IL_003f:
	{
		__this->___mode = 0;
		__this->___bitk = 0;
		__this->___bitb = 0;
		int32_t L_8 = 0;
		V_0 = L_8;
		__this->___write = L_8;
		int32_t L_9 = V_0;
		__this->___read = L_9;
		RuntimeObject* L_10 = __this->___checkfn;
		if (!L_10)
		{
			goto IL_008e;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_11 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_12 = ___0_z;
		NullCheck(L_12);
		Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D* L_13 = L_12->____adler;
		NullCheck(L_13);
		int64_t L_14;
		L_14 = Adler32_adler32_m47B915EF9E1AD64F5504CC03340A1E09ED08A416(L_13, ((int64_t)0), (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)NULL, 0, 0, NULL);
		int64_t L_15 = L_14;
		V_1 = L_15;
		__this->___check = L_15;
		int64_t L_16 = V_1;
		NullCheck(L_11);
		L_11->___adler = L_16;
	}

IL_008e:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfBlocks_proc_m8FB471432B90D42E736660EE3001F601B41966CA (InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, int32_t ___1_r, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral260306369A04CA189E353A93EBB484ED8F9A9B43);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral359C7A1FB5CEBD929D7F11F5D3E96EDE7FF01384);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9A971A9294400EA492DFEFCF8370FA1EBA838E06);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCC98F8D5063D43F6A1D8B5158D9DE47EAC048113);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	int32_t V_7 = 0;
	int32_t V_8 = 0;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* V_9 = NULL;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* V_10 = NULL;
	Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* V_11 = NULL;
	Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* V_12 = NULL;
	int32_t V_13 = 0;
	int32_t V_14 = 0;
	int32_t V_15 = 0;
	int32_t V_16 = 0;
	int32_t V_17 = 0;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* V_18 = NULL;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* V_19 = NULL;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* V_20 = NULL;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* V_21 = NULL;
	int32_t G_B3_0 = 0;
	InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* G_B29_0 = NULL;
	InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* G_B28_0 = NULL;
	int32_t G_B32_0 = 0;
	InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* G_B32_1 = NULL;
	InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* G_B31_0 = NULL;
	InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* G_B30_0 = NULL;
	int32_t G_B41_0 = 0;
	int32_t G_B46_0 = 0;
	int32_t G_B51_0 = 0;
	InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* G_B62_0 = NULL;
	InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* G_B61_0 = NULL;
	int32_t G_B63_0 = 0;
	InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* G_B63_1 = NULL;
	int32_t G_B111_0 = 0;
	int32_t G_B114_0 = 0;
	int32_t G_B127_0 = 0;
	int32_t G_B141_0 = 0;
	int32_t G_B147_0 = 0;
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_0 = ___0_z;
		NullCheck(L_0);
		int32_t L_1 = L_0->___next_in_index;
		V_3 = L_1;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_2 = ___0_z;
		NullCheck(L_2);
		int32_t L_3 = L_2->___avail_in;
		V_4 = L_3;
		int32_t L_4 = __this->___bitb;
		V_1 = L_4;
		int32_t L_5 = __this->___bitk;
		V_2 = L_5;
		int32_t L_6 = __this->___write;
		V_5 = L_6;
		int32_t L_7 = V_5;
		int32_t L_8 = __this->___read;
		if ((((int32_t)L_7) >= ((int32_t)L_8)))
		{
			goto IL_0042;
		}
	}
	{
		int32_t L_9 = __this->___read;
		int32_t L_10 = V_5;
		G_B3_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_9, L_10)), 1));
		goto IL_004b;
	}

IL_0042:
	{
		int32_t L_11 = __this->___end;
		int32_t L_12 = V_5;
		G_B3_0 = ((int32_t)il2cpp_codegen_subtract(L_11, L_12));
	}

IL_004b:
	{
		V_6 = G_B3_0;
	}

IL_004d:
	{
		int32_t L_13 = __this->___mode;
		V_7 = L_13;
		int32_t L_14 = V_7;
		switch (L_14)
		{
			case 0:
			{
				goto IL_0089;
			}
			case 1:
			{
				goto IL_023a;
			}
			case 2:
			{
				goto IL_036f;
			}
			case 3:
			{
				goto IL_0560;
			}
			case 4:
			{
				goto IL_06df;
			}
			case 5:
			{
				goto IL_0880;
			}
			case 6:
			{
				goto IL_0c72;
			}
			case 7:
			{
				goto IL_0d4c;
			}
			case 8:
			{
				goto IL_0def;
			}
			case 9:
			{
				goto IL_0e36;
			}
		}
	}
	{
		goto IL_0e7e;
	}

IL_0089:
	{
		goto IL_0105;
	}

IL_008e:
	{
		int32_t L_15 = V_4;
		if (!L_15)
		{
			goto IL_009d;
		}
	}
	{
		___1_r = 0;
		goto IL_00e1;
	}

IL_009d:
	{
		int32_t L_16 = V_1;
		__this->___bitb = L_16;
		int32_t L_17 = V_2;
		__this->___bitk = L_17;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_18 = ___0_z;
		int32_t L_19 = V_4;
		NullCheck(L_18);
		L_18->___avail_in = L_19;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_20 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_21 = L_20;
		NullCheck(L_21);
		int64_t L_22 = L_21->___total_in;
		int32_t L_23 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_24 = ___0_z;
		NullCheck(L_24);
		int32_t L_25 = L_24->___next_in_index;
		NullCheck(L_21);
		L_21->___total_in = ((int64_t)il2cpp_codegen_add(L_22, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_23, L_25)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_26 = ___0_z;
		int32_t L_27 = V_3;
		NullCheck(L_26);
		L_26->___next_in_index = L_27;
		int32_t L_28 = V_5;
		__this->___write = L_28;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_29 = ___0_z;
		int32_t L_30 = ___1_r;
		int32_t L_31;
		L_31 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_29, L_30, NULL);
		return L_31;
	}

IL_00e1:
	{
		int32_t L_32 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_32, 1));
		int32_t L_33 = V_1;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_34 = ___0_z;
		NullCheck(L_34);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_35 = L_34->___next_in;
		int32_t L_36 = V_3;
		int32_t L_37 = L_36;
		V_3 = ((int32_t)il2cpp_codegen_add(L_37, 1));
		NullCheck(L_35);
		int32_t L_38 = L_37;
		uint8_t L_39 = (L_35)->GetAt(static_cast<il2cpp_array_size_t>(L_38));
		int32_t L_40 = V_2;
		V_1 = ((int32_t)(L_33|((int32_t)(((int32_t)((int32_t)L_39&((int32_t)255)))<<((int32_t)(L_40&((int32_t)31)))))));
		int32_t L_41 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_41, 8));
	}

IL_0105:
	{
		int32_t L_42 = V_2;
		if ((((int32_t)L_42) < ((int32_t)3)))
		{
			goto IL_008e;
		}
	}
	{
		int32_t L_43 = V_1;
		V_0 = ((int32_t)(L_43&7));
		int32_t L_44 = V_0;
		__this->___last = ((int32_t)(L_44&1));
		int32_t L_45 = V_0;
		V_8 = ((int32_t)(L_45>>1));
		int32_t L_46 = V_8;
		switch (L_46)
		{
			case 0:
			{
				goto IL_013a;
			}
			case 1:
			{
				goto IL_015d;
			}
			case 2:
			{
				goto IL_01be;
			}
			case 3:
			{
				goto IL_01d2;
			}
		}
	}
	{
		goto IL_0235;
	}

IL_013a:
	{
		int32_t L_47 = V_1;
		V_1 = ((int32_t)(L_47>>3));
		int32_t L_48 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_subtract(L_48, 3));
		int32_t L_49 = V_2;
		V_0 = ((int32_t)(L_49&7));
		int32_t L_50 = V_1;
		int32_t L_51 = V_0;
		V_1 = ((int32_t)(L_50>>((int32_t)(L_51&((int32_t)31)))));
		int32_t L_52 = V_2;
		int32_t L_53 = V_0;
		V_2 = ((int32_t)il2cpp_codegen_subtract(L_52, L_53));
		__this->___mode = 1;
		goto IL_0235;
	}

IL_015d:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_54 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)1);
		V_9 = L_54;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_55 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)1);
		V_10 = L_55;
		Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* L_56 = (Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E*)(Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E*)SZArrayNew(Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E_il2cpp_TypeInfo_var, (uint32_t)1);
		V_11 = L_56;
		Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* L_57 = (Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E*)(Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E*)SZArrayNew(Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E_il2cpp_TypeInfo_var, (uint32_t)1);
		V_12 = L_57;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_58 = V_9;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_59 = V_10;
		Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* L_60 = V_11;
		Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* L_61 = V_12;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_62 = ___0_z;
		il2cpp_codegen_runtime_class_init_inline(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var);
		int32_t L_63;
		L_63 = InfTree_inflate_trees_fixed_m7D58777D20FA8CACF92F4D42A45E0BCC42934D28(L_58, L_59, L_60, L_61, L_62, NULL);
		InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* L_64 = __this->___codes;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_65 = V_9;
		NullCheck(L_65);
		int32_t L_66 = 0;
		int32_t L_67 = (L_65)->GetAt(static_cast<il2cpp_array_size_t>(L_66));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_68 = V_10;
		NullCheck(L_68);
		int32_t L_69 = 0;
		int32_t L_70 = (L_68)->GetAt(static_cast<il2cpp_array_size_t>(L_69));
		Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* L_71 = V_11;
		NullCheck(L_71);
		int32_t L_72 = 0;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_73 = (L_71)->GetAt(static_cast<il2cpp_array_size_t>(L_72));
		Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* L_74 = V_12;
		NullCheck(L_74);
		int32_t L_75 = 0;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_76 = (L_74)->GetAt(static_cast<il2cpp_array_size_t>(L_75));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_77 = ___0_z;
		NullCheck(L_64);
		InfCodes_init_m573B577586A6B3D38F81FC3C570D29C319BEAFE4(L_64, L_67, L_70, L_73, 0, L_76, 0, L_77, NULL);
		int32_t L_78 = V_1;
		V_1 = ((int32_t)(L_78>>3));
		int32_t L_79 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_subtract(L_79, 3));
		__this->___mode = 6;
		goto IL_0235;
	}

IL_01be:
	{
		int32_t L_80 = V_1;
		V_1 = ((int32_t)(L_80>>3));
		int32_t L_81 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_subtract(L_81, 3));
		__this->___mode = 3;
		goto IL_0235;
	}

IL_01d2:
	{
		int32_t L_82 = V_1;
		V_1 = ((int32_t)(L_82>>3));
		int32_t L_83 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_subtract(L_83, 3));
		__this->___mode = ((int32_t)9);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_84 = ___0_z;
		NullCheck(L_84);
		L_84->___msg = _stringLiteral9A971A9294400EA492DFEFCF8370FA1EBA838E06;
		Il2CppCodeGenWriteBarrier((void**)(&L_84->___msg), (void*)_stringLiteral9A971A9294400EA492DFEFCF8370FA1EBA838E06);
		___1_r = ((int32_t)-3);
		int32_t L_85 = V_1;
		__this->___bitb = L_85;
		int32_t L_86 = V_2;
		__this->___bitk = L_86;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_87 = ___0_z;
		int32_t L_88 = V_4;
		NullCheck(L_87);
		L_87->___avail_in = L_88;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_89 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_90 = L_89;
		NullCheck(L_90);
		int64_t L_91 = L_90->___total_in;
		int32_t L_92 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_93 = ___0_z;
		NullCheck(L_93);
		int32_t L_94 = L_93->___next_in_index;
		NullCheck(L_90);
		L_90->___total_in = ((int64_t)il2cpp_codegen_add(L_91, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_92, L_94)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_95 = ___0_z;
		int32_t L_96 = V_3;
		NullCheck(L_95);
		L_95->___next_in_index = L_96;
		int32_t L_97 = V_5;
		__this->___write = L_97;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_98 = ___0_z;
		int32_t L_99 = ___1_r;
		int32_t L_100;
		L_100 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_98, L_99, NULL);
		return L_100;
	}

IL_0235:
	{
		goto IL_0ec6;
	}

IL_023a:
	{
		goto IL_02b6;
	}

IL_023f:
	{
		int32_t L_101 = V_4;
		if (!L_101)
		{
			goto IL_024e;
		}
	}
	{
		___1_r = 0;
		goto IL_0292;
	}

IL_024e:
	{
		int32_t L_102 = V_1;
		__this->___bitb = L_102;
		int32_t L_103 = V_2;
		__this->___bitk = L_103;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_104 = ___0_z;
		int32_t L_105 = V_4;
		NullCheck(L_104);
		L_104->___avail_in = L_105;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_106 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_107 = L_106;
		NullCheck(L_107);
		int64_t L_108 = L_107->___total_in;
		int32_t L_109 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_110 = ___0_z;
		NullCheck(L_110);
		int32_t L_111 = L_110->___next_in_index;
		NullCheck(L_107);
		L_107->___total_in = ((int64_t)il2cpp_codegen_add(L_108, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_109, L_111)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_112 = ___0_z;
		int32_t L_113 = V_3;
		NullCheck(L_112);
		L_112->___next_in_index = L_113;
		int32_t L_114 = V_5;
		__this->___write = L_114;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_115 = ___0_z;
		int32_t L_116 = ___1_r;
		int32_t L_117;
		L_117 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_115, L_116, NULL);
		return L_117;
	}

IL_0292:
	{
		int32_t L_118 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_118, 1));
		int32_t L_119 = V_1;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_120 = ___0_z;
		NullCheck(L_120);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_121 = L_120->___next_in;
		int32_t L_122 = V_3;
		int32_t L_123 = L_122;
		V_3 = ((int32_t)il2cpp_codegen_add(L_123, 1));
		NullCheck(L_121);
		int32_t L_124 = L_123;
		uint8_t L_125 = (L_121)->GetAt(static_cast<il2cpp_array_size_t>(L_124));
		int32_t L_126 = V_2;
		V_1 = ((int32_t)(L_119|((int32_t)(((int32_t)((int32_t)L_125&((int32_t)255)))<<((int32_t)(L_126&((int32_t)31)))))));
		int32_t L_127 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_127, 8));
	}

IL_02b6:
	{
		int32_t L_128 = V_2;
		if ((((int32_t)L_128) < ((int32_t)((int32_t)32))))
		{
			goto IL_023f;
		}
	}
	{
		int32_t L_129 = V_1;
		int32_t L_130 = V_1;
		if ((((int32_t)((int32_t)(((int32_t)(((~L_129))>>((int32_t)16)))&((int32_t)65535)))) == ((int32_t)((int32_t)(L_130&((int32_t)65535))))))
		{
			goto IL_0330;
		}
	}
	{
		__this->___mode = ((int32_t)9);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_131 = ___0_z;
		NullCheck(L_131);
		L_131->___msg = _stringLiteralCC98F8D5063D43F6A1D8B5158D9DE47EAC048113;
		Il2CppCodeGenWriteBarrier((void**)(&L_131->___msg), (void*)_stringLiteralCC98F8D5063D43F6A1D8B5158D9DE47EAC048113);
		___1_r = ((int32_t)-3);
		int32_t L_132 = V_1;
		__this->___bitb = L_132;
		int32_t L_133 = V_2;
		__this->___bitk = L_133;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_134 = ___0_z;
		int32_t L_135 = V_4;
		NullCheck(L_134);
		L_134->___avail_in = L_135;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_136 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_137 = L_136;
		NullCheck(L_137);
		int64_t L_138 = L_137->___total_in;
		int32_t L_139 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_140 = ___0_z;
		NullCheck(L_140);
		int32_t L_141 = L_140->___next_in_index;
		NullCheck(L_137);
		L_137->___total_in = ((int64_t)il2cpp_codegen_add(L_138, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_139, L_141)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_142 = ___0_z;
		int32_t L_143 = V_3;
		NullCheck(L_142);
		L_142->___next_in_index = L_143;
		int32_t L_144 = V_5;
		__this->___write = L_144;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_145 = ___0_z;
		int32_t L_146 = ___1_r;
		int32_t L_147;
		L_147 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_145, L_146, NULL);
		return L_147;
	}

IL_0330:
	{
		int32_t L_148 = V_1;
		__this->___left = ((int32_t)(L_148&((int32_t)65535)));
		int32_t L_149 = 0;
		V_2 = L_149;
		V_1 = L_149;
		int32_t L_150 = __this->___left;
		if (!L_150)
		{
			G_B29_0 = __this;
			goto IL_0353;
		}
		G_B28_0 = __this;
	}
	{
		G_B32_0 = 2;
		G_B32_1 = G_B28_0;
		goto IL_0365;
	}

IL_0353:
	{
		int32_t L_151 = __this->___last;
		if (!L_151)
		{
			G_B31_0 = G_B29_0;
			goto IL_0364;
		}
		G_B30_0 = G_B29_0;
	}
	{
		G_B32_0 = 7;
		G_B32_1 = G_B30_0;
		goto IL_0365;
	}

IL_0364:
	{
		G_B32_0 = 0;
		G_B32_1 = G_B31_0;
	}

IL_0365:
	{
		NullCheck(G_B32_1);
		G_B32_1->___mode = G_B32_0;
		goto IL_0ec6;
	}

IL_036f:
	{
		int32_t L_152 = V_4;
		if (L_152)
		{
			goto IL_03ba;
		}
	}
	{
		int32_t L_153 = V_1;
		__this->___bitb = L_153;
		int32_t L_154 = V_2;
		__this->___bitk = L_154;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_155 = ___0_z;
		int32_t L_156 = V_4;
		NullCheck(L_155);
		L_155->___avail_in = L_156;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_157 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_158 = L_157;
		NullCheck(L_158);
		int64_t L_159 = L_158->___total_in;
		int32_t L_160 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_161 = ___0_z;
		NullCheck(L_161);
		int32_t L_162 = L_161->___next_in_index;
		NullCheck(L_158);
		L_158->___total_in = ((int64_t)il2cpp_codegen_add(L_159, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_160, L_162)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_163 = ___0_z;
		int32_t L_164 = V_3;
		NullCheck(L_163);
		L_163->___next_in_index = L_164;
		int32_t L_165 = V_5;
		__this->___write = L_165;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_166 = ___0_z;
		int32_t L_167 = ___1_r;
		int32_t L_168;
		L_168 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_166, L_167, NULL);
		return L_168;
	}

IL_03ba:
	{
		int32_t L_169 = V_6;
		if (L_169)
		{
			goto IL_04db;
		}
	}
	{
		int32_t L_170 = V_5;
		int32_t L_171 = __this->___end;
		if ((!(((uint32_t)L_170) == ((uint32_t)L_171))))
		{
			goto IL_0404;
		}
	}
	{
		int32_t L_172 = __this->___read;
		if (!L_172)
		{
			goto IL_0404;
		}
	}
	{
		V_5 = 0;
		int32_t L_173 = V_5;
		int32_t L_174 = __this->___read;
		if ((((int32_t)L_173) >= ((int32_t)L_174)))
		{
			goto IL_03f9;
		}
	}
	{
		int32_t L_175 = __this->___read;
		int32_t L_176 = V_5;
		G_B41_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_175, L_176)), 1));
		goto IL_0402;
	}

IL_03f9:
	{
		int32_t L_177 = __this->___end;
		int32_t L_178 = V_5;
		G_B41_0 = ((int32_t)il2cpp_codegen_subtract(L_177, L_178));
	}

IL_0402:
	{
		V_6 = G_B41_0;
	}

IL_0404:
	{
		int32_t L_179 = V_6;
		if (L_179)
		{
			goto IL_04db;
		}
	}
	{
		int32_t L_180 = V_5;
		__this->___write = L_180;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_181 = ___0_z;
		int32_t L_182 = ___1_r;
		int32_t L_183;
		L_183 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_181, L_182, NULL);
		___1_r = L_183;
		int32_t L_184 = __this->___write;
		V_5 = L_184;
		int32_t L_185 = V_5;
		int32_t L_186 = __this->___read;
		if ((((int32_t)L_185) >= ((int32_t)L_186)))
		{
			goto IL_0442;
		}
	}
	{
		int32_t L_187 = __this->___read;
		int32_t L_188 = V_5;
		G_B46_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_187, L_188)), 1));
		goto IL_044b;
	}

IL_0442:
	{
		int32_t L_189 = __this->___end;
		int32_t L_190 = V_5;
		G_B46_0 = ((int32_t)il2cpp_codegen_subtract(L_189, L_190));
	}

IL_044b:
	{
		V_6 = G_B46_0;
		int32_t L_191 = V_5;
		int32_t L_192 = __this->___end;
		if ((!(((uint32_t)L_191) == ((uint32_t)L_192))))
		{
			goto IL_0490;
		}
	}
	{
		int32_t L_193 = __this->___read;
		if (!L_193)
		{
			goto IL_0490;
		}
	}
	{
		V_5 = 0;
		int32_t L_194 = V_5;
		int32_t L_195 = __this->___read;
		if ((((int32_t)L_194) >= ((int32_t)L_195)))
		{
			goto IL_0485;
		}
	}
	{
		int32_t L_196 = __this->___read;
		int32_t L_197 = V_5;
		G_B51_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_196, L_197)), 1));
		goto IL_048e;
	}

IL_0485:
	{
		int32_t L_198 = __this->___end;
		int32_t L_199 = V_5;
		G_B51_0 = ((int32_t)il2cpp_codegen_subtract(L_198, L_199));
	}

IL_048e:
	{
		V_6 = G_B51_0;
	}

IL_0490:
	{
		int32_t L_200 = V_6;
		if (L_200)
		{
			goto IL_04db;
		}
	}
	{
		int32_t L_201 = V_1;
		__this->___bitb = L_201;
		int32_t L_202 = V_2;
		__this->___bitk = L_202;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_203 = ___0_z;
		int32_t L_204 = V_4;
		NullCheck(L_203);
		L_203->___avail_in = L_204;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_205 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_206 = L_205;
		NullCheck(L_206);
		int64_t L_207 = L_206->___total_in;
		int32_t L_208 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_209 = ___0_z;
		NullCheck(L_209);
		int32_t L_210 = L_209->___next_in_index;
		NullCheck(L_206);
		L_206->___total_in = ((int64_t)il2cpp_codegen_add(L_207, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_208, L_210)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_211 = ___0_z;
		int32_t L_212 = V_3;
		NullCheck(L_211);
		L_211->___next_in_index = L_212;
		int32_t L_213 = V_5;
		__this->___write = L_213;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_214 = ___0_z;
		int32_t L_215 = ___1_r;
		int32_t L_216;
		L_216 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_214, L_215, NULL);
		return L_216;
	}

IL_04db:
	{
		___1_r = 0;
		int32_t L_217 = __this->___left;
		V_0 = L_217;
		int32_t L_218 = V_0;
		int32_t L_219 = V_4;
		if ((((int32_t)L_218) <= ((int32_t)L_219)))
		{
			goto IL_04f0;
		}
	}
	{
		int32_t L_220 = V_4;
		V_0 = L_220;
	}

IL_04f0:
	{
		int32_t L_221 = V_0;
		int32_t L_222 = V_6;
		if ((((int32_t)L_221) <= ((int32_t)L_222)))
		{
			goto IL_04fb;
		}
	}
	{
		int32_t L_223 = V_6;
		V_0 = L_223;
	}

IL_04fb:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_224 = ___0_z;
		NullCheck(L_224);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_225 = L_224->___next_in;
		int32_t L_226 = V_3;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_227 = __this->___window;
		int32_t L_228 = V_5;
		int32_t L_229 = V_0;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_225, L_226, (RuntimeArray*)L_227, L_228, L_229, NULL);
		int32_t L_230 = V_3;
		int32_t L_231 = V_0;
		V_3 = ((int32_t)il2cpp_codegen_add(L_230, L_231));
		int32_t L_232 = V_4;
		int32_t L_233 = V_0;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_232, L_233));
		int32_t L_234 = V_5;
		int32_t L_235 = V_0;
		V_5 = ((int32_t)il2cpp_codegen_add(L_234, L_235));
		int32_t L_236 = V_6;
		int32_t L_237 = V_0;
		V_6 = ((int32_t)il2cpp_codegen_subtract(L_236, L_237));
		int32_t L_238 = __this->___left;
		int32_t L_239 = V_0;
		int32_t L_240 = ((int32_t)il2cpp_codegen_subtract(L_238, L_239));
		V_13 = L_240;
		__this->___left = L_240;
		int32_t L_241 = V_13;
		if (!L_241)
		{
			goto IL_0543;
		}
	}
	{
		goto IL_0ec6;
	}

IL_0543:
	{
		int32_t L_242 = __this->___last;
		if (!L_242)
		{
			G_B62_0 = __this;
			goto IL_0555;
		}
		G_B61_0 = __this;
	}
	{
		G_B63_0 = 7;
		G_B63_1 = G_B61_0;
		goto IL_0556;
	}

IL_0555:
	{
		G_B63_0 = 0;
		G_B63_1 = G_B62_0;
	}

IL_0556:
	{
		NullCheck(G_B63_1);
		G_B63_1->___mode = G_B63_0;
		goto IL_0ec6;
	}

IL_0560:
	{
		goto IL_05dc;
	}

IL_0565:
	{
		int32_t L_243 = V_4;
		if (!L_243)
		{
			goto IL_0574;
		}
	}
	{
		___1_r = 0;
		goto IL_05b8;
	}

IL_0574:
	{
		int32_t L_244 = V_1;
		__this->___bitb = L_244;
		int32_t L_245 = V_2;
		__this->___bitk = L_245;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_246 = ___0_z;
		int32_t L_247 = V_4;
		NullCheck(L_246);
		L_246->___avail_in = L_247;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_248 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_249 = L_248;
		NullCheck(L_249);
		int64_t L_250 = L_249->___total_in;
		int32_t L_251 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_252 = ___0_z;
		NullCheck(L_252);
		int32_t L_253 = L_252->___next_in_index;
		NullCheck(L_249);
		L_249->___total_in = ((int64_t)il2cpp_codegen_add(L_250, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_251, L_253)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_254 = ___0_z;
		int32_t L_255 = V_3;
		NullCheck(L_254);
		L_254->___next_in_index = L_255;
		int32_t L_256 = V_5;
		__this->___write = L_256;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_257 = ___0_z;
		int32_t L_258 = ___1_r;
		int32_t L_259;
		L_259 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_257, L_258, NULL);
		return L_259;
	}

IL_05b8:
	{
		int32_t L_260 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_260, 1));
		int32_t L_261 = V_1;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_262 = ___0_z;
		NullCheck(L_262);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_263 = L_262->___next_in;
		int32_t L_264 = V_3;
		int32_t L_265 = L_264;
		V_3 = ((int32_t)il2cpp_codegen_add(L_265, 1));
		NullCheck(L_263);
		int32_t L_266 = L_265;
		uint8_t L_267 = (L_263)->GetAt(static_cast<il2cpp_array_size_t>(L_266));
		int32_t L_268 = V_2;
		V_1 = ((int32_t)(L_261|((int32_t)(((int32_t)((int32_t)L_267&((int32_t)255)))<<((int32_t)(L_268&((int32_t)31)))))));
		int32_t L_269 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_269, 8));
	}

IL_05dc:
	{
		int32_t L_270 = V_2;
		if ((((int32_t)L_270) < ((int32_t)((int32_t)14))))
		{
			goto IL_0565;
		}
	}
	{
		int32_t L_271 = V_1;
		int32_t L_272 = ((int32_t)(L_271&((int32_t)16383)));
		V_0 = L_272;
		__this->___table = L_272;
		int32_t L_273 = V_0;
		if ((((int32_t)((int32_t)(L_273&((int32_t)31)))) > ((int32_t)((int32_t)29))))
		{
			goto IL_060b;
		}
	}
	{
		int32_t L_274 = V_0;
		if ((((int32_t)((int32_t)(((int32_t)(L_274>>5))&((int32_t)31)))) <= ((int32_t)((int32_t)29))))
		{
			goto IL_0666;
		}
	}

IL_060b:
	{
		__this->___mode = ((int32_t)9);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_275 = ___0_z;
		NullCheck(L_275);
		L_275->___msg = _stringLiteral260306369A04CA189E353A93EBB484ED8F9A9B43;
		Il2CppCodeGenWriteBarrier((void**)(&L_275->___msg), (void*)_stringLiteral260306369A04CA189E353A93EBB484ED8F9A9B43);
		___1_r = ((int32_t)-3);
		int32_t L_276 = V_1;
		__this->___bitb = L_276;
		int32_t L_277 = V_2;
		__this->___bitk = L_277;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_278 = ___0_z;
		int32_t L_279 = V_4;
		NullCheck(L_278);
		L_278->___avail_in = L_279;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_280 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_281 = L_280;
		NullCheck(L_281);
		int64_t L_282 = L_281->___total_in;
		int32_t L_283 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_284 = ___0_z;
		NullCheck(L_284);
		int32_t L_285 = L_284->___next_in_index;
		NullCheck(L_281);
		L_281->___total_in = ((int64_t)il2cpp_codegen_add(L_282, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_283, L_285)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_286 = ___0_z;
		int32_t L_287 = V_3;
		NullCheck(L_286);
		L_286->___next_in_index = L_287;
		int32_t L_288 = V_5;
		__this->___write = L_288;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_289 = ___0_z;
		int32_t L_290 = ___1_r;
		int32_t L_291;
		L_291 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_289, L_290, NULL);
		return L_291;
	}

IL_0666:
	{
		int32_t L_292 = V_0;
		int32_t L_293 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_add(((int32_t)258), ((int32_t)(L_292&((int32_t)31))))), ((int32_t)(((int32_t)(L_293>>5))&((int32_t)31)))));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_294 = __this->___blens;
		if (!L_294)
		{
			goto IL_0691;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_295 = __this->___blens;
		NullCheck(L_295);
		int32_t L_296 = V_0;
		if ((((int32_t)((int32_t)(((RuntimeArray*)L_295)->max_length))) >= ((int32_t)L_296)))
		{
			goto IL_06a2;
		}
	}

IL_0691:
	{
		int32_t L_297 = V_0;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_298 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)L_297);
		__this->___blens = L_298;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___blens), (void*)L_298);
		goto IL_06c2;
	}

IL_06a2:
	{
		V_14 = 0;
		goto IL_06ba;
	}

IL_06aa:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_299 = __this->___blens;
		int32_t L_300 = V_14;
		NullCheck(L_299);
		(L_299)->SetAt(static_cast<il2cpp_array_size_t>(L_300), (int32_t)0);
		int32_t L_301 = V_14;
		V_14 = ((int32_t)il2cpp_codegen_add(L_301, 1));
	}

IL_06ba:
	{
		int32_t L_302 = V_14;
		int32_t L_303 = V_0;
		if ((((int32_t)L_302) < ((int32_t)L_303)))
		{
			goto IL_06aa;
		}
	}

IL_06c2:
	{
		int32_t L_304 = V_1;
		V_1 = ((int32_t)(L_304>>((int32_t)14)));
		int32_t L_305 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_subtract(L_305, ((int32_t)14)));
		__this->___index = 0;
		__this->___mode = 4;
		goto IL_06df;
	}

IL_06df:
	{
		goto IL_0792;
	}

IL_06e4:
	{
		goto IL_0760;
	}

IL_06e9:
	{
		int32_t L_306 = V_4;
		if (!L_306)
		{
			goto IL_06f8;
		}
	}
	{
		___1_r = 0;
		goto IL_073c;
	}

IL_06f8:
	{
		int32_t L_307 = V_1;
		__this->___bitb = L_307;
		int32_t L_308 = V_2;
		__this->___bitk = L_308;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_309 = ___0_z;
		int32_t L_310 = V_4;
		NullCheck(L_309);
		L_309->___avail_in = L_310;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_311 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_312 = L_311;
		NullCheck(L_312);
		int64_t L_313 = L_312->___total_in;
		int32_t L_314 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_315 = ___0_z;
		NullCheck(L_315);
		int32_t L_316 = L_315->___next_in_index;
		NullCheck(L_312);
		L_312->___total_in = ((int64_t)il2cpp_codegen_add(L_313, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_314, L_316)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_317 = ___0_z;
		int32_t L_318 = V_3;
		NullCheck(L_317);
		L_317->___next_in_index = L_318;
		int32_t L_319 = V_5;
		__this->___write = L_319;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_320 = ___0_z;
		int32_t L_321 = ___1_r;
		int32_t L_322;
		L_322 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_320, L_321, NULL);
		return L_322;
	}

IL_073c:
	{
		int32_t L_323 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_323, 1));
		int32_t L_324 = V_1;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_325 = ___0_z;
		NullCheck(L_325);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_326 = L_325->___next_in;
		int32_t L_327 = V_3;
		int32_t L_328 = L_327;
		V_3 = ((int32_t)il2cpp_codegen_add(L_328, 1));
		NullCheck(L_326);
		int32_t L_329 = L_328;
		uint8_t L_330 = (L_326)->GetAt(static_cast<il2cpp_array_size_t>(L_329));
		int32_t L_331 = V_2;
		V_1 = ((int32_t)(L_324|((int32_t)(((int32_t)((int32_t)L_330&((int32_t)255)))<<((int32_t)(L_331&((int32_t)31)))))));
		int32_t L_332 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_332, 8));
	}

IL_0760:
	{
		int32_t L_333 = V_2;
		if ((((int32_t)L_333) < ((int32_t)3)))
		{
			goto IL_06e9;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_334 = __this->___blens;
		il2cpp_codegen_runtime_class_init_inline(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_335 = ((InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_StaticFields*)il2cpp_codegen_static_fields_for(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var))->___border;
		int32_t L_336 = __this->___index;
		int32_t L_337 = L_336;
		V_13 = L_337;
		__this->___index = ((int32_t)il2cpp_codegen_add(L_337, 1));
		int32_t L_338 = V_13;
		NullCheck(L_335);
		int32_t L_339 = L_338;
		int32_t L_340 = (L_335)->GetAt(static_cast<il2cpp_array_size_t>(L_339));
		int32_t L_341 = V_1;
		NullCheck(L_334);
		(L_334)->SetAt(static_cast<il2cpp_array_size_t>(L_340), (int32_t)((int32_t)(L_341&7)));
		int32_t L_342 = V_1;
		V_1 = ((int32_t)(L_342>>3));
		int32_t L_343 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_subtract(L_343, 3));
	}

IL_0792:
	{
		int32_t L_344 = __this->___index;
		int32_t L_345 = __this->___table;
		if ((((int32_t)L_344) < ((int32_t)((int32_t)il2cpp_codegen_add(4, ((int32_t)(L_345>>((int32_t)10))))))))
		{
			goto IL_06e4;
		}
	}
	{
		goto IL_07ce;
	}

IL_07ad:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_346 = __this->___blens;
		il2cpp_codegen_runtime_class_init_inline(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_347 = ((InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_StaticFields*)il2cpp_codegen_static_fields_for(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var))->___border;
		int32_t L_348 = __this->___index;
		int32_t L_349 = L_348;
		V_13 = L_349;
		__this->___index = ((int32_t)il2cpp_codegen_add(L_349, 1));
		int32_t L_350 = V_13;
		NullCheck(L_347);
		int32_t L_351 = L_350;
		int32_t L_352 = (L_347)->GetAt(static_cast<il2cpp_array_size_t>(L_351));
		NullCheck(L_346);
		(L_346)->SetAt(static_cast<il2cpp_array_size_t>(L_352), (int32_t)0);
	}

IL_07ce:
	{
		int32_t L_353 = __this->___index;
		if ((((int32_t)L_353) < ((int32_t)((int32_t)19))))
		{
			goto IL_07ad;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_354 = __this->___bb;
		NullCheck(L_354);
		(L_354)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)7);
		InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* L_355 = __this->___inftree;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_356 = __this->___blens;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_357 = __this->___bb;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_358 = __this->___tb;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_359 = __this->___hufts;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_360 = ___0_z;
		NullCheck(L_355);
		int32_t L_361;
		L_361 = InfTree_inflate_trees_bits_m08FC677299F8FBF8D84B4E1AF802E5037586C15A(L_355, L_356, L_357, L_358, L_359, L_360, NULL);
		V_0 = L_361;
		int32_t L_362 = V_0;
		if (!L_362)
		{
			goto IL_086d;
		}
	}
	{
		int32_t L_363 = V_0;
		___1_r = L_363;
		int32_t L_364 = ___1_r;
		if ((!(((uint32_t)L_364) == ((uint32_t)((int32_t)-3)))))
		{
			goto IL_0829;
		}
	}
	{
		__this->___blens = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___blens), (void*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL);
		__this->___mode = ((int32_t)9);
	}

IL_0829:
	{
		int32_t L_365 = V_1;
		__this->___bitb = L_365;
		int32_t L_366 = V_2;
		__this->___bitk = L_366;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_367 = ___0_z;
		int32_t L_368 = V_4;
		NullCheck(L_367);
		L_367->___avail_in = L_368;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_369 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_370 = L_369;
		NullCheck(L_370);
		int64_t L_371 = L_370->___total_in;
		int32_t L_372 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_373 = ___0_z;
		NullCheck(L_373);
		int32_t L_374 = L_373->___next_in_index;
		NullCheck(L_370);
		L_370->___total_in = ((int64_t)il2cpp_codegen_add(L_371, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_372, L_374)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_375 = ___0_z;
		int32_t L_376 = V_3;
		NullCheck(L_375);
		L_375->___next_in_index = L_376;
		int32_t L_377 = V_5;
		__this->___write = L_377;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_378 = ___0_z;
		int32_t L_379 = ___1_r;
		int32_t L_380;
		L_380 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_378, L_379, NULL);
		return L_380;
	}

IL_086d:
	{
		__this->___index = 0;
		__this->___mode = 5;
		goto IL_0880;
	}

IL_0880:
	{
		int32_t L_381 = __this->___table;
		V_0 = L_381;
		int32_t L_382 = __this->___index;
		int32_t L_383 = V_0;
		int32_t L_384 = V_0;
		if ((((int32_t)L_382) < ((int32_t)((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_add(((int32_t)258), ((int32_t)(L_383&((int32_t)31))))), ((int32_t)(((int32_t)(L_384>>5))&((int32_t)31))))))))
		{
			goto IL_08a8;
		}
	}
	{
		goto IL_0b6c;
	}

IL_08a8:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_385 = __this->___bb;
		NullCheck(L_385);
		int32_t L_386 = 0;
		int32_t L_387 = (L_385)->GetAt(static_cast<il2cpp_array_size_t>(L_386));
		V_0 = L_387;
		goto IL_092d;
	}

IL_08b6:
	{
		int32_t L_388 = V_4;
		if (!L_388)
		{
			goto IL_08c5;
		}
	}
	{
		___1_r = 0;
		goto IL_0909;
	}

IL_08c5:
	{
		int32_t L_389 = V_1;
		__this->___bitb = L_389;
		int32_t L_390 = V_2;
		__this->___bitk = L_390;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_391 = ___0_z;
		int32_t L_392 = V_4;
		NullCheck(L_391);
		L_391->___avail_in = L_392;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_393 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_394 = L_393;
		NullCheck(L_394);
		int64_t L_395 = L_394->___total_in;
		int32_t L_396 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_397 = ___0_z;
		NullCheck(L_397);
		int32_t L_398 = L_397->___next_in_index;
		NullCheck(L_394);
		L_394->___total_in = ((int64_t)il2cpp_codegen_add(L_395, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_396, L_398)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_399 = ___0_z;
		int32_t L_400 = V_3;
		NullCheck(L_399);
		L_399->___next_in_index = L_400;
		int32_t L_401 = V_5;
		__this->___write = L_401;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_402 = ___0_z;
		int32_t L_403 = ___1_r;
		int32_t L_404;
		L_404 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_402, L_403, NULL);
		return L_404;
	}

IL_0909:
	{
		int32_t L_405 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_405, 1));
		int32_t L_406 = V_1;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_407 = ___0_z;
		NullCheck(L_407);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_408 = L_407->___next_in;
		int32_t L_409 = V_3;
		int32_t L_410 = L_409;
		V_3 = ((int32_t)il2cpp_codegen_add(L_410, 1));
		NullCheck(L_408);
		int32_t L_411 = L_410;
		uint8_t L_412 = (L_408)->GetAt(static_cast<il2cpp_array_size_t>(L_411));
		int32_t L_413 = V_2;
		V_1 = ((int32_t)(L_406|((int32_t)(((int32_t)((int32_t)L_412&((int32_t)255)))<<((int32_t)(L_413&((int32_t)31)))))));
		int32_t L_414 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_414, 8));
	}

IL_092d:
	{
		int32_t L_415 = V_2;
		int32_t L_416 = V_0;
		if ((((int32_t)L_415) < ((int32_t)L_416)))
		{
			goto IL_08b6;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_417 = __this->___tb;
		NullCheck(L_417);
		int32_t L_418 = 0;
		int32_t L_419 = (L_417)->GetAt(static_cast<il2cpp_array_size_t>(L_418));
		if ((!(((uint32_t)L_419) == ((uint32_t)(-1)))))
		{
			goto IL_0942;
		}
	}

IL_0942:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_420 = __this->___hufts;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_421 = __this->___tb;
		NullCheck(L_421);
		int32_t L_422 = 0;
		int32_t L_423 = (L_421)->GetAt(static_cast<il2cpp_array_size_t>(L_422));
		int32_t L_424 = V_1;
		il2cpp_codegen_runtime_class_init_inline(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_425 = ((InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_StaticFields*)il2cpp_codegen_static_fields_for(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var))->___inflate_mask;
		int32_t L_426 = V_0;
		NullCheck(L_425);
		int32_t L_427 = L_426;
		int32_t L_428 = (L_425)->GetAt(static_cast<il2cpp_array_size_t>(L_427));
		NullCheck(L_420);
		int32_t L_429 = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(((int32_t)il2cpp_codegen_add(L_423, ((int32_t)(L_424&L_428)))), 3)), 1));
		int32_t L_430 = (L_420)->GetAt(static_cast<il2cpp_array_size_t>(L_429));
		V_0 = L_430;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_431 = __this->___hufts;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_432 = __this->___tb;
		NullCheck(L_432);
		int32_t L_433 = 0;
		int32_t L_434 = (L_432)->GetAt(static_cast<il2cpp_array_size_t>(L_433));
		int32_t L_435 = V_1;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_436 = ((InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_StaticFields*)il2cpp_codegen_static_fields_for(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var))->___inflate_mask;
		int32_t L_437 = V_0;
		NullCheck(L_436);
		int32_t L_438 = L_437;
		int32_t L_439 = (L_436)->GetAt(static_cast<il2cpp_array_size_t>(L_438));
		NullCheck(L_431);
		int32_t L_440 = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(((int32_t)il2cpp_codegen_add(L_434, ((int32_t)(L_435&L_439)))), 3)), 2));
		int32_t L_441 = (L_431)->GetAt(static_cast<il2cpp_array_size_t>(L_440));
		V_17 = L_441;
		int32_t L_442 = V_17;
		if ((((int32_t)L_442) >= ((int32_t)((int32_t)16))))
		{
			goto IL_09b4;
		}
	}
	{
		int32_t L_443 = V_1;
		int32_t L_444 = V_0;
		V_1 = ((int32_t)(L_443>>((int32_t)(L_444&((int32_t)31)))));
		int32_t L_445 = V_2;
		int32_t L_446 = V_0;
		V_2 = ((int32_t)il2cpp_codegen_subtract(L_445, L_446));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_447 = __this->___blens;
		int32_t L_448 = __this->___index;
		int32_t L_449 = L_448;
		V_13 = L_449;
		__this->___index = ((int32_t)il2cpp_codegen_add(L_449, 1));
		int32_t L_450 = V_13;
		int32_t L_451 = V_17;
		NullCheck(L_447);
		(L_447)->SetAt(static_cast<il2cpp_array_size_t>(L_450), (int32_t)L_451);
		goto IL_0b67;
	}

IL_09b4:
	{
		int32_t L_452 = V_17;
		if ((!(((uint32_t)L_452) == ((uint32_t)((int32_t)18)))))
		{
			goto IL_09c3;
		}
	}
	{
		G_B111_0 = 7;
		goto IL_09c8;
	}

IL_09c3:
	{
		int32_t L_453 = V_17;
		G_B111_0 = ((int32_t)il2cpp_codegen_subtract(L_453, ((int32_t)14)));
	}

IL_09c8:
	{
		V_15 = G_B111_0;
		int32_t L_454 = V_17;
		if ((!(((uint32_t)L_454) == ((uint32_t)((int32_t)18)))))
		{
			goto IL_09da;
		}
	}
	{
		G_B114_0 = ((int32_t)11);
		goto IL_09db;
	}

IL_09da:
	{
		G_B114_0 = 3;
	}

IL_09db:
	{
		V_16 = G_B114_0;
		goto IL_0a59;
	}

IL_09e2:
	{
		int32_t L_455 = V_4;
		if (!L_455)
		{
			goto IL_09f1;
		}
	}
	{
		___1_r = 0;
		goto IL_0a35;
	}

IL_09f1:
	{
		int32_t L_456 = V_1;
		__this->___bitb = L_456;
		int32_t L_457 = V_2;
		__this->___bitk = L_457;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_458 = ___0_z;
		int32_t L_459 = V_4;
		NullCheck(L_458);
		L_458->___avail_in = L_459;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_460 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_461 = L_460;
		NullCheck(L_461);
		int64_t L_462 = L_461->___total_in;
		int32_t L_463 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_464 = ___0_z;
		NullCheck(L_464);
		int32_t L_465 = L_464->___next_in_index;
		NullCheck(L_461);
		L_461->___total_in = ((int64_t)il2cpp_codegen_add(L_462, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_463, L_465)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_466 = ___0_z;
		int32_t L_467 = V_3;
		NullCheck(L_466);
		L_466->___next_in_index = L_467;
		int32_t L_468 = V_5;
		__this->___write = L_468;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_469 = ___0_z;
		int32_t L_470 = ___1_r;
		int32_t L_471;
		L_471 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_469, L_470, NULL);
		return L_471;
	}

IL_0a35:
	{
		int32_t L_472 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_472, 1));
		int32_t L_473 = V_1;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_474 = ___0_z;
		NullCheck(L_474);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_475 = L_474->___next_in;
		int32_t L_476 = V_3;
		int32_t L_477 = L_476;
		V_3 = ((int32_t)il2cpp_codegen_add(L_477, 1));
		NullCheck(L_475);
		int32_t L_478 = L_477;
		uint8_t L_479 = (L_475)->GetAt(static_cast<il2cpp_array_size_t>(L_478));
		int32_t L_480 = V_2;
		V_1 = ((int32_t)(L_473|((int32_t)(((int32_t)((int32_t)L_479&((int32_t)255)))<<((int32_t)(L_480&((int32_t)31)))))));
		int32_t L_481 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_481, 8));
	}

IL_0a59:
	{
		int32_t L_482 = V_2;
		int32_t L_483 = V_0;
		int32_t L_484 = V_15;
		if ((((int32_t)L_482) < ((int32_t)((int32_t)il2cpp_codegen_add(L_483, L_484)))))
		{
			goto IL_09e2;
		}
	}
	{
		int32_t L_485 = V_1;
		int32_t L_486 = V_0;
		V_1 = ((int32_t)(L_485>>((int32_t)(L_486&((int32_t)31)))));
		int32_t L_487 = V_2;
		int32_t L_488 = V_0;
		V_2 = ((int32_t)il2cpp_codegen_subtract(L_487, L_488));
		int32_t L_489 = V_16;
		int32_t L_490 = V_1;
		il2cpp_codegen_runtime_class_init_inline(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_491 = ((InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_StaticFields*)il2cpp_codegen_static_fields_for(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var))->___inflate_mask;
		int32_t L_492 = V_15;
		NullCheck(L_491);
		int32_t L_493 = L_492;
		int32_t L_494 = (L_491)->GetAt(static_cast<il2cpp_array_size_t>(L_493));
		V_16 = ((int32_t)il2cpp_codegen_add(L_489, ((int32_t)(L_490&L_494))));
		int32_t L_495 = V_1;
		int32_t L_496 = V_15;
		V_1 = ((int32_t)(L_495>>((int32_t)(L_496&((int32_t)31)))));
		int32_t L_497 = V_2;
		int32_t L_498 = V_15;
		V_2 = ((int32_t)il2cpp_codegen_subtract(L_497, L_498));
		int32_t L_499 = __this->___index;
		V_15 = L_499;
		int32_t L_500 = __this->___table;
		V_0 = L_500;
		int32_t L_501 = V_15;
		int32_t L_502 = V_16;
		int32_t L_503 = V_0;
		int32_t L_504 = V_0;
		if ((((int32_t)((int32_t)il2cpp_codegen_add(L_501, L_502))) > ((int32_t)((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_add(((int32_t)258), ((int32_t)(L_503&((int32_t)31))))), ((int32_t)(((int32_t)(L_504>>5))&((int32_t)31))))))))
		{
			goto IL_0ac5;
		}
	}
	{
		int32_t L_505 = V_17;
		if ((!(((uint32_t)L_505) == ((uint32_t)((int32_t)16)))))
		{
			goto IL_0b27;
		}
	}
	{
		int32_t L_506 = V_15;
		if ((((int32_t)L_506) >= ((int32_t)1)))
		{
			goto IL_0b27;
		}
	}

IL_0ac5:
	{
		__this->___blens = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___blens), (void*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL);
		__this->___mode = ((int32_t)9);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_507 = ___0_z;
		NullCheck(L_507);
		L_507->___msg = _stringLiteral359C7A1FB5CEBD929D7F11F5D3E96EDE7FF01384;
		Il2CppCodeGenWriteBarrier((void**)(&L_507->___msg), (void*)_stringLiteral359C7A1FB5CEBD929D7F11F5D3E96EDE7FF01384);
		___1_r = ((int32_t)-3);
		int32_t L_508 = V_1;
		__this->___bitb = L_508;
		int32_t L_509 = V_2;
		__this->___bitk = L_509;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_510 = ___0_z;
		int32_t L_511 = V_4;
		NullCheck(L_510);
		L_510->___avail_in = L_511;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_512 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_513 = L_512;
		NullCheck(L_513);
		int64_t L_514 = L_513->___total_in;
		int32_t L_515 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_516 = ___0_z;
		NullCheck(L_516);
		int32_t L_517 = L_516->___next_in_index;
		NullCheck(L_513);
		L_513->___total_in = ((int64_t)il2cpp_codegen_add(L_514, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_515, L_517)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_518 = ___0_z;
		int32_t L_519 = V_3;
		NullCheck(L_518);
		L_518->___next_in_index = L_519;
		int32_t L_520 = V_5;
		__this->___write = L_520;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_521 = ___0_z;
		int32_t L_522 = ___1_r;
		int32_t L_523;
		L_523 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_521, L_522, NULL);
		return L_523;
	}

IL_0b27:
	{
		int32_t L_524 = V_17;
		if ((!(((uint32_t)L_524) == ((uint32_t)((int32_t)16)))))
		{
			goto IL_0b40;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_525 = __this->___blens;
		int32_t L_526 = V_15;
		NullCheck(L_525);
		int32_t L_527 = ((int32_t)il2cpp_codegen_subtract(L_526, 1));
		int32_t L_528 = (L_525)->GetAt(static_cast<il2cpp_array_size_t>(L_527));
		G_B127_0 = L_528;
		goto IL_0b41;
	}

IL_0b40:
	{
		G_B127_0 = 0;
	}

IL_0b41:
	{
		V_17 = G_B127_0;
	}

IL_0b43:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_529 = __this->___blens;
		int32_t L_530 = V_15;
		int32_t L_531 = L_530;
		V_15 = ((int32_t)il2cpp_codegen_add(L_531, 1));
		int32_t L_532 = V_17;
		NullCheck(L_529);
		(L_529)->SetAt(static_cast<il2cpp_array_size_t>(L_531), (int32_t)L_532);
		int32_t L_533 = V_16;
		int32_t L_534 = ((int32_t)il2cpp_codegen_subtract(L_533, 1));
		V_16 = L_534;
		if (L_534)
		{
			goto IL_0b43;
		}
	}
	{
		int32_t L_535 = V_15;
		__this->___index = L_535;
	}

IL_0b67:
	{
		goto IL_0880;
	}

IL_0b6c:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_536 = __this->___tb;
		NullCheck(L_536);
		(L_536)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)(-1));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_537 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)1);
		V_18 = L_537;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_538 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)1);
		V_19 = L_538;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_539 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)1);
		V_20 = L_539;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_540 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)1);
		V_21 = L_540;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_541 = V_18;
		NullCheck(L_541);
		(L_541)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)((int32_t)9));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_542 = V_19;
		NullCheck(L_542);
		(L_542)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)6);
		int32_t L_543 = __this->___table;
		V_0 = L_543;
		InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* L_544 = __this->___inftree;
		int32_t L_545 = V_0;
		int32_t L_546 = V_0;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_547 = __this->___blens;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_548 = V_18;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_549 = V_19;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_550 = V_20;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_551 = V_21;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_552 = __this->___hufts;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_553 = ___0_z;
		NullCheck(L_544);
		int32_t L_554;
		L_554 = InfTree_inflate_trees_dynamic_m3FC3C6E9606B5B5494CA4D6F13D4B23F292A177F(L_544, ((int32_t)il2cpp_codegen_add(((int32_t)257), ((int32_t)(L_545&((int32_t)31))))), ((int32_t)il2cpp_codegen_add(1, ((int32_t)(((int32_t)(L_546>>5))&((int32_t)31))))), L_547, L_548, L_549, L_550, L_551, L_552, L_553, NULL);
		V_0 = L_554;
		int32_t L_555 = V_0;
		if (!L_555)
		{
			goto IL_0c3e;
		}
	}
	{
		int32_t L_556 = V_0;
		if ((!(((uint32_t)L_556) == ((uint32_t)((int32_t)-3)))))
		{
			goto IL_0bf7;
		}
	}
	{
		__this->___blens = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___blens), (void*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL);
		__this->___mode = ((int32_t)9);
	}

IL_0bf7:
	{
		int32_t L_557 = V_0;
		___1_r = L_557;
		int32_t L_558 = V_1;
		__this->___bitb = L_558;
		int32_t L_559 = V_2;
		__this->___bitk = L_559;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_560 = ___0_z;
		int32_t L_561 = V_4;
		NullCheck(L_560);
		L_560->___avail_in = L_561;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_562 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_563 = L_562;
		NullCheck(L_563);
		int64_t L_564 = L_563->___total_in;
		int32_t L_565 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_566 = ___0_z;
		NullCheck(L_566);
		int32_t L_567 = L_566->___next_in_index;
		NullCheck(L_563);
		L_563->___total_in = ((int64_t)il2cpp_codegen_add(L_564, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_565, L_567)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_568 = ___0_z;
		int32_t L_569 = V_3;
		NullCheck(L_568);
		L_568->___next_in_index = L_569;
		int32_t L_570 = V_5;
		__this->___write = L_570;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_571 = ___0_z;
		int32_t L_572 = ___1_r;
		int32_t L_573;
		L_573 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_571, L_572, NULL);
		return L_573;
	}

IL_0c3e:
	{
		InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* L_574 = __this->___codes;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_575 = V_18;
		NullCheck(L_575);
		int32_t L_576 = 0;
		int32_t L_577 = (L_575)->GetAt(static_cast<il2cpp_array_size_t>(L_576));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_578 = V_19;
		NullCheck(L_578);
		int32_t L_579 = 0;
		int32_t L_580 = (L_578)->GetAt(static_cast<il2cpp_array_size_t>(L_579));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_581 = __this->___hufts;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_582 = V_20;
		NullCheck(L_582);
		int32_t L_583 = 0;
		int32_t L_584 = (L_582)->GetAt(static_cast<il2cpp_array_size_t>(L_583));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_585 = __this->___hufts;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_586 = V_21;
		NullCheck(L_586);
		int32_t L_587 = 0;
		int32_t L_588 = (L_586)->GetAt(static_cast<il2cpp_array_size_t>(L_587));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_589 = ___0_z;
		NullCheck(L_574);
		InfCodes_init_m573B577586A6B3D38F81FC3C570D29C319BEAFE4(L_574, L_577, L_580, L_581, L_584, L_585, L_588, L_589, NULL);
		__this->___mode = 6;
		goto IL_0c72;
	}

IL_0c72:
	{
		int32_t L_590 = V_1;
		__this->___bitb = L_590;
		int32_t L_591 = V_2;
		__this->___bitk = L_591;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_592 = ___0_z;
		int32_t L_593 = V_4;
		NullCheck(L_592);
		L_592->___avail_in = L_593;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_594 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_595 = L_594;
		NullCheck(L_595);
		int64_t L_596 = L_595->___total_in;
		int32_t L_597 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_598 = ___0_z;
		NullCheck(L_598);
		int32_t L_599 = L_598->___next_in_index;
		NullCheck(L_595);
		L_595->___total_in = ((int64_t)il2cpp_codegen_add(L_596, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_597, L_599)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_600 = ___0_z;
		int32_t L_601 = V_3;
		NullCheck(L_600);
		L_600->___next_in_index = L_601;
		int32_t L_602 = V_5;
		__this->___write = L_602;
		InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* L_603 = __this->___codes;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_604 = ___0_z;
		int32_t L_605 = ___1_r;
		NullCheck(L_603);
		int32_t L_606;
		L_606 = InfCodes_proc_m4E3ABBA8C356A0566899229FDE22203A513AC4D1(L_603, __this, L_604, L_605, NULL);
		int32_t L_607 = L_606;
		___1_r = L_607;
		if ((((int32_t)L_607) == ((int32_t)1)))
		{
			goto IL_0ccd;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_608 = ___0_z;
		int32_t L_609 = ___1_r;
		int32_t L_610;
		L_610 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_608, L_609, NULL);
		return L_610;
	}

IL_0ccd:
	{
		___1_r = 0;
		InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* L_611 = __this->___codes;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_612 = ___0_z;
		NullCheck(L_611);
		InfCodes_free_m6FF96CFF077C86EAD34D40DAD8499E2ED55DC131(L_611, L_612, NULL);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_613 = ___0_z;
		NullCheck(L_613);
		int32_t L_614 = L_613->___next_in_index;
		V_3 = L_614;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_615 = ___0_z;
		NullCheck(L_615);
		int32_t L_616 = L_615->___avail_in;
		V_4 = L_616;
		int32_t L_617 = __this->___bitb;
		V_1 = L_617;
		int32_t L_618 = __this->___bitk;
		V_2 = L_618;
		int32_t L_619 = __this->___write;
		V_5 = L_619;
		int32_t L_620 = V_5;
		int32_t L_621 = __this->___read;
		if ((((int32_t)L_620) >= ((int32_t)L_621)))
		{
			goto IL_0d1e;
		}
	}
	{
		int32_t L_622 = __this->___read;
		int32_t L_623 = V_5;
		G_B141_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_622, L_623)), 1));
		goto IL_0d27;
	}

IL_0d1e:
	{
		int32_t L_624 = __this->___end;
		int32_t L_625 = V_5;
		G_B141_0 = ((int32_t)il2cpp_codegen_subtract(L_624, L_625));
	}

IL_0d27:
	{
		V_6 = G_B141_0;
		int32_t L_626 = __this->___last;
		if (L_626)
		{
			goto IL_0d40;
		}
	}
	{
		__this->___mode = 0;
		goto IL_0ec6;
	}

IL_0d40:
	{
		__this->___mode = 7;
		goto IL_0d4c;
	}

IL_0d4c:
	{
		int32_t L_627 = V_5;
		__this->___write = L_627;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_628 = ___0_z;
		int32_t L_629 = ___1_r;
		int32_t L_630;
		L_630 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_628, L_629, NULL);
		___1_r = L_630;
		int32_t L_631 = __this->___write;
		V_5 = L_631;
		int32_t L_632 = V_5;
		int32_t L_633 = __this->___read;
		if ((((int32_t)L_632) >= ((int32_t)L_633)))
		{
			goto IL_0d83;
		}
	}
	{
		int32_t L_634 = __this->___read;
		int32_t L_635 = V_5;
		G_B147_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_634, L_635)), 1));
		goto IL_0d8c;
	}

IL_0d83:
	{
		int32_t L_636 = __this->___end;
		int32_t L_637 = V_5;
		G_B147_0 = ((int32_t)il2cpp_codegen_subtract(L_636, L_637));
	}

IL_0d8c:
	{
		V_6 = G_B147_0;
		int32_t L_638 = __this->___read;
		int32_t L_639 = __this->___write;
		if ((((int32_t)L_638) == ((int32_t)L_639)))
		{
			goto IL_0de3;
		}
	}
	{
		int32_t L_640 = V_1;
		__this->___bitb = L_640;
		int32_t L_641 = V_2;
		__this->___bitk = L_641;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_642 = ___0_z;
		int32_t L_643 = V_4;
		NullCheck(L_642);
		L_642->___avail_in = L_643;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_644 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_645 = L_644;
		NullCheck(L_645);
		int64_t L_646 = L_645->___total_in;
		int32_t L_647 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_648 = ___0_z;
		NullCheck(L_648);
		int32_t L_649 = L_648->___next_in_index;
		NullCheck(L_645);
		L_645->___total_in = ((int64_t)il2cpp_codegen_add(L_646, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_647, L_649)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_650 = ___0_z;
		int32_t L_651 = V_3;
		NullCheck(L_650);
		L_650->___next_in_index = L_651;
		int32_t L_652 = V_5;
		__this->___write = L_652;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_653 = ___0_z;
		int32_t L_654 = ___1_r;
		int32_t L_655;
		L_655 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_653, L_654, NULL);
		return L_655;
	}

IL_0de3:
	{
		__this->___mode = 8;
		goto IL_0def;
	}

IL_0def:
	{
		___1_r = 1;
		int32_t L_656 = V_1;
		__this->___bitb = L_656;
		int32_t L_657 = V_2;
		__this->___bitk = L_657;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_658 = ___0_z;
		int32_t L_659 = V_4;
		NullCheck(L_658);
		L_658->___avail_in = L_659;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_660 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_661 = L_660;
		NullCheck(L_661);
		int64_t L_662 = L_661->___total_in;
		int32_t L_663 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_664 = ___0_z;
		NullCheck(L_664);
		int32_t L_665 = L_664->___next_in_index;
		NullCheck(L_661);
		L_661->___total_in = ((int64_t)il2cpp_codegen_add(L_662, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_663, L_665)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_666 = ___0_z;
		int32_t L_667 = V_3;
		NullCheck(L_666);
		L_666->___next_in_index = L_667;
		int32_t L_668 = V_5;
		__this->___write = L_668;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_669 = ___0_z;
		int32_t L_670 = ___1_r;
		int32_t L_671;
		L_671 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_669, L_670, NULL);
		return L_671;
	}

IL_0e36:
	{
		___1_r = ((int32_t)-3);
		int32_t L_672 = V_1;
		__this->___bitb = L_672;
		int32_t L_673 = V_2;
		__this->___bitk = L_673;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_674 = ___0_z;
		int32_t L_675 = V_4;
		NullCheck(L_674);
		L_674->___avail_in = L_675;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_676 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_677 = L_676;
		NullCheck(L_677);
		int64_t L_678 = L_677->___total_in;
		int32_t L_679 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_680 = ___0_z;
		NullCheck(L_680);
		int32_t L_681 = L_680->___next_in_index;
		NullCheck(L_677);
		L_677->___total_in = ((int64_t)il2cpp_codegen_add(L_678, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_679, L_681)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_682 = ___0_z;
		int32_t L_683 = V_3;
		NullCheck(L_682);
		L_682->___next_in_index = L_683;
		int32_t L_684 = V_5;
		__this->___write = L_684;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_685 = ___0_z;
		int32_t L_686 = ___1_r;
		int32_t L_687;
		L_687 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_685, L_686, NULL);
		return L_687;
	}

IL_0e7e:
	{
		___1_r = ((int32_t)-2);
		int32_t L_688 = V_1;
		__this->___bitb = L_688;
		int32_t L_689 = V_2;
		__this->___bitk = L_689;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_690 = ___0_z;
		int32_t L_691 = V_4;
		NullCheck(L_690);
		L_690->___avail_in = L_691;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_692 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_693 = L_692;
		NullCheck(L_693);
		int64_t L_694 = L_693->___total_in;
		int32_t L_695 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_696 = ___0_z;
		NullCheck(L_696);
		int32_t L_697 = L_696->___next_in_index;
		NullCheck(L_693);
		L_693->___total_in = ((int64_t)il2cpp_codegen_add(L_694, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_695, L_697)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_698 = ___0_z;
		int32_t L_699 = V_3;
		NullCheck(L_698);
		L_698->___next_in_index = L_699;
		int32_t L_700 = V_5;
		__this->___write = L_700;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_701 = ___0_z;
		int32_t L_702 = ___1_r;
		int32_t L_703;
		L_703 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(__this, L_701, L_702, NULL);
		return L_703;
	}

IL_0ec6:
	{
		goto IL_004d;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfBlocks_free_m9E8C460B7D27F7D5C5C72699646494819E754D78 (InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, const RuntimeMethod* method) 
{
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_0 = ___0_z;
		InfBlocks_reset_m287421B0D47CBB5CAB51FA76A930806BAFF87BC8(__this, L_0, (Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D*)NULL, NULL);
		__this->___window = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___window), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)NULL);
		__this->___hufts = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___hufts), (void*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA (InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, int32_t ___1_r, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int64_t V_3 = 0;
	int32_t G_B3_0 = 0;
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_0 = ___0_z;
		NullCheck(L_0);
		int32_t L_1 = L_0->___next_out_index;
		V_1 = L_1;
		int32_t L_2 = __this->___read;
		V_2 = L_2;
		int32_t L_3 = V_2;
		int32_t L_4 = __this->___write;
		if ((((int32_t)L_3) > ((int32_t)L_4)))
		{
			goto IL_0025;
		}
	}
	{
		int32_t L_5 = __this->___write;
		G_B3_0 = L_5;
		goto IL_002b;
	}

IL_0025:
	{
		int32_t L_6 = __this->___end;
		G_B3_0 = L_6;
	}

IL_002b:
	{
		int32_t L_7 = V_2;
		V_0 = ((int32_t)il2cpp_codegen_subtract(G_B3_0, L_7));
		int32_t L_8 = V_0;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_9 = ___0_z;
		NullCheck(L_9);
		int32_t L_10 = L_9->___avail_out;
		if ((((int32_t)L_8) <= ((int32_t)L_10)))
		{
			goto IL_0041;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_11 = ___0_z;
		NullCheck(L_11);
		int32_t L_12 = L_11->___avail_out;
		V_0 = L_12;
	}

IL_0041:
	{
		int32_t L_13 = V_0;
		if (!L_13)
		{
			goto IL_0052;
		}
	}
	{
		int32_t L_14 = ___1_r;
		if ((!(((uint32_t)L_14) == ((uint32_t)((int32_t)-5)))))
		{
			goto IL_0052;
		}
	}
	{
		___1_r = 0;
	}

IL_0052:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_15 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_16 = L_15;
		NullCheck(L_16);
		int32_t L_17 = L_16->___avail_out;
		int32_t L_18 = V_0;
		NullCheck(L_16);
		L_16->___avail_out = ((int32_t)il2cpp_codegen_subtract(L_17, L_18));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_19 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_20 = L_19;
		NullCheck(L_20);
		int64_t L_21 = L_20->___total_out;
		int32_t L_22 = V_0;
		NullCheck(L_20);
		L_20->___total_out = ((int64_t)il2cpp_codegen_add(L_21, ((int64_t)L_22)));
		RuntimeObject* L_23 = __this->___checkfn;
		if (!L_23)
		{
			goto IL_00a2;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_24 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_25 = ___0_z;
		NullCheck(L_25);
		Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D* L_26 = L_25->____adler;
		int64_t L_27 = __this->___check;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_28 = __this->___window;
		int32_t L_29 = V_2;
		int32_t L_30 = V_0;
		NullCheck(L_26);
		int64_t L_31;
		L_31 = Adler32_adler32_m47B915EF9E1AD64F5504CC03340A1E09ED08A416(L_26, L_27, L_28, L_29, L_30, NULL);
		int64_t L_32 = L_31;
		V_3 = L_32;
		__this->___check = L_32;
		int64_t L_33 = V_3;
		NullCheck(L_24);
		L_24->___adler = L_33;
	}

IL_00a2:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_34 = __this->___window;
		int32_t L_35 = V_2;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_36 = ___0_z;
		NullCheck(L_36);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_37 = L_36->___next_out;
		int32_t L_38 = V_1;
		int32_t L_39 = V_0;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_34, L_35, (RuntimeArray*)L_37, L_38, L_39, NULL);
		int32_t L_40 = V_1;
		int32_t L_41 = V_0;
		V_1 = ((int32_t)il2cpp_codegen_add(L_40, L_41));
		int32_t L_42 = V_2;
		int32_t L_43 = V_0;
		V_2 = ((int32_t)il2cpp_codegen_add(L_42, L_43));
		int32_t L_44 = V_2;
		int32_t L_45 = __this->___end;
		if ((!(((uint32_t)L_44) == ((uint32_t)L_45))))
		{
			goto IL_017d;
		}
	}
	{
		V_2 = 0;
		int32_t L_46 = __this->___write;
		int32_t L_47 = __this->___end;
		if ((!(((uint32_t)L_46) == ((uint32_t)L_47))))
		{
			goto IL_00e4;
		}
	}
	{
		__this->___write = 0;
	}

IL_00e4:
	{
		int32_t L_48 = __this->___write;
		int32_t L_49 = V_2;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_48, L_49));
		int32_t L_50 = V_0;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_51 = ___0_z;
		NullCheck(L_51);
		int32_t L_52 = L_51->___avail_out;
		if ((((int32_t)L_50) <= ((int32_t)L_52)))
		{
			goto IL_0100;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_53 = ___0_z;
		NullCheck(L_53);
		int32_t L_54 = L_53->___avail_out;
		V_0 = L_54;
	}

IL_0100:
	{
		int32_t L_55 = V_0;
		if (!L_55)
		{
			goto IL_0111;
		}
	}
	{
		int32_t L_56 = ___1_r;
		if ((!(((uint32_t)L_56) == ((uint32_t)((int32_t)-5)))))
		{
			goto IL_0111;
		}
	}
	{
		___1_r = 0;
	}

IL_0111:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_57 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_58 = L_57;
		NullCheck(L_58);
		int32_t L_59 = L_58->___avail_out;
		int32_t L_60 = V_0;
		NullCheck(L_58);
		L_58->___avail_out = ((int32_t)il2cpp_codegen_subtract(L_59, L_60));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_61 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_62 = L_61;
		NullCheck(L_62);
		int64_t L_63 = L_62->___total_out;
		int32_t L_64 = V_0;
		NullCheck(L_62);
		L_62->___total_out = ((int64_t)il2cpp_codegen_add(L_63, ((int64_t)L_64)));
		RuntimeObject* L_65 = __this->___checkfn;
		if (!L_65)
		{
			goto IL_0161;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_66 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_67 = ___0_z;
		NullCheck(L_67);
		Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D* L_68 = L_67->____adler;
		int64_t L_69 = __this->___check;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_70 = __this->___window;
		int32_t L_71 = V_2;
		int32_t L_72 = V_0;
		NullCheck(L_68);
		int64_t L_73;
		L_73 = Adler32_adler32_m47B915EF9E1AD64F5504CC03340A1E09ED08A416(L_68, L_69, L_70, L_71, L_72, NULL);
		int64_t L_74 = L_73;
		V_3 = L_74;
		__this->___check = L_74;
		int64_t L_75 = V_3;
		NullCheck(L_66);
		L_66->___adler = L_75;
	}

IL_0161:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_76 = __this->___window;
		int32_t L_77 = V_2;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_78 = ___0_z;
		NullCheck(L_78);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_79 = L_78->___next_out;
		int32_t L_80 = V_1;
		int32_t L_81 = V_0;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_76, L_77, (RuntimeArray*)L_79, L_80, L_81, NULL);
		int32_t L_82 = V_1;
		int32_t L_83 = V_0;
		V_1 = ((int32_t)il2cpp_codegen_add(L_82, L_83));
		int32_t L_84 = V_2;
		int32_t L_85 = V_0;
		V_2 = ((int32_t)il2cpp_codegen_add(L_84, L_85));
	}

IL_017d:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_86 = ___0_z;
		int32_t L_87 = V_1;
		NullCheck(L_86);
		L_86->___next_out_index = L_87;
		int32_t L_88 = V_2;
		__this->___read = L_88;
		int32_t L_89 = ___1_r;
		return L_89;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfBlocks__cctor_m134A7C21ADE0B2CA5873F7667541432974514578 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE0_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE1_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)17));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE0_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		((InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_StaticFields*)il2cpp_codegen_static_fields_for(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var))->___inflate_mask = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_StaticFields*)il2cpp_codegen_static_fields_for(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var))->___inflate_mask), (void*)L_1);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_3 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)19));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE1_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		((InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_StaticFields*)il2cpp_codegen_static_fields_for(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var))->___border = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&((InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_StaticFields*)il2cpp_codegen_static_fields_for(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var))->___border), (void*)L_4);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfCodes__ctor_mABD6546D31140B3D8BD034F3CFD66E51B82A3834 (InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfCodes_init_m573B577586A6B3D38F81FC3C570D29C319BEAFE4 (InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* __this, int32_t ___0_bl, int32_t ___1_bd, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___2_tl, int32_t ___3_tl_index, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___4_td, int32_t ___5_td_index, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___6_z, const RuntimeMethod* method) 
{
	{
		__this->___mode = 0;
		int32_t L_0 = ___0_bl;
		__this->___lbits = (uint8_t)((int32_t)(uint8_t)L_0);
		int32_t L_1 = ___1_bd;
		__this->___dbits = (uint8_t)((int32_t)(uint8_t)L_1);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_2 = ___2_tl;
		__this->___ltree = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___ltree), (void*)L_2);
		int32_t L_3 = ___3_tl_index;
		__this->___ltree_index = L_3;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_4 = ___4_td;
		__this->___dtree = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___dtree), (void*)L_4);
		int32_t L_5 = ___5_td_index;
		__this->___dtree_index = L_5;
		__this->___tree = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___tree), (void*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfCodes_proc_m4E3ABBA8C356A0566899229FDE22203A513AC4D1 (InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* __this, InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* ___0_s, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___1_z, int32_t ___2_r, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral96025B6397AAC8D06A75085B92AD0F0146044D16);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBDD794DC7884A15D601FC8AD88E8B6637CF36948);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	int32_t V_7 = 0;
	int32_t V_8 = 0;
	int32_t V_9 = 0;
	int32_t V_10 = 0;
	int32_t G_B3_0 = 0;
	int32_t G_B11_0 = 0;
	InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* G_B14_0 = NULL;
	InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* G_B13_0 = NULL;
	int32_t G_B15_0 = 0;
	InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* G_B15_1 = NULL;
	int32_t G_B67_0 = 0;
	int32_t G_B72_0 = 0;
	int32_t G_B77_0 = 0;
	int32_t G_B91_0 = 0;
	int32_t G_B96_0 = 0;
	int32_t G_B101_0 = 0;
	int32_t G_B110_0 = 0;
	{
		V_3 = 0;
		V_4 = 0;
		V_5 = 0;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_0 = ___1_z;
		NullCheck(L_0);
		int32_t L_1 = L_0->___next_in_index;
		V_5 = L_1;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_2 = ___1_z;
		NullCheck(L_2);
		int32_t L_3 = L_2->___avail_in;
		V_6 = L_3;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_4 = ___0_s;
		NullCheck(L_4);
		int32_t L_5 = L_4->___bitb;
		V_3 = L_5;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_6 = ___0_s;
		NullCheck(L_6);
		int32_t L_7 = L_6->___bitk;
		V_4 = L_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_8 = ___0_s;
		NullCheck(L_8);
		int32_t L_9 = L_8->___write;
		V_7 = L_9;
		int32_t L_10 = V_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_11 = ___0_s;
		NullCheck(L_11);
		int32_t L_12 = L_11->___read;
		if ((((int32_t)L_10) >= ((int32_t)L_12)))
		{
			goto IL_004c;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_13 = ___0_s;
		NullCheck(L_13);
		int32_t L_14 = L_13->___read;
		int32_t L_15 = V_7;
		G_B3_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_14, L_15)), 1));
		goto IL_0055;
	}

IL_004c:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_16 = ___0_s;
		NullCheck(L_16);
		int32_t L_17 = L_16->___end;
		int32_t L_18 = V_7;
		G_B3_0 = ((int32_t)il2cpp_codegen_subtract(L_17, L_18));
	}

IL_0055:
	{
		V_8 = G_B3_0;
	}

IL_0057:
	{
		int32_t L_19 = __this->___mode;
		V_10 = L_19;
		int32_t L_20 = V_10;
		switch (L_20)
		{
			case 0:
			{
				goto IL_0093;
			}
			case 1:
			{
				goto IL_01b3;
			}
			case 2:
			{
				goto IL_036e;
			}
			case 3:
			{
				goto IL_0454;
			}
			case 4:
			{
				goto IL_05d8;
			}
			case 5:
			{
				goto IL_069a;
			}
			case 6:
			{
				goto IL_083d;
			}
			case 7:
			{
				goto IL_098b;
			}
			case 8:
			{
				goto IL_0a4b;
			}
			case 9:
			{
				goto IL_0a95;
			}
		}
	}
	{
		goto IL_0ae0;
	}

IL_0093:
	{
		int32_t L_21 = V_8;
		if ((((int32_t)L_21) < ((int32_t)((int32_t)258))))
		{
			goto IL_0183;
		}
	}
	{
		int32_t L_22 = V_6;
		if ((((int32_t)L_22) < ((int32_t)((int32_t)10))))
		{
			goto IL_0183;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_23 = ___0_s;
		int32_t L_24 = V_3;
		NullCheck(L_23);
		L_23->___bitb = L_24;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_25 = ___0_s;
		int32_t L_26 = V_4;
		NullCheck(L_25);
		L_25->___bitk = L_26;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_27 = ___1_z;
		int32_t L_28 = V_6;
		NullCheck(L_27);
		L_27->___avail_in = L_28;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_29 = ___1_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_30 = L_29;
		NullCheck(L_30);
		int64_t L_31 = L_30->___total_in;
		int32_t L_32 = V_5;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_33 = ___1_z;
		NullCheck(L_33);
		int32_t L_34 = L_33->___next_in_index;
		NullCheck(L_30);
		L_30->___total_in = ((int64_t)il2cpp_codegen_add(L_31, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_32, L_34)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_35 = ___1_z;
		int32_t L_36 = V_5;
		NullCheck(L_35);
		L_35->___next_in_index = L_36;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_37 = ___0_s;
		int32_t L_38 = V_7;
		NullCheck(L_37);
		L_37->___write = L_38;
		uint8_t L_39 = __this->___lbits;
		uint8_t L_40 = __this->___dbits;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_41 = __this->___ltree;
		int32_t L_42 = __this->___ltree_index;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_43 = __this->___dtree;
		int32_t L_44 = __this->___dtree_index;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_45 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_46 = ___1_z;
		int32_t L_47;
		L_47 = InfCodes_inflate_fast_m14E202BC92D499CF9473F320A0F1CD68ECEE1E37(__this, L_39, L_40, L_41, L_42, L_43, L_44, L_45, L_46, NULL);
		___2_r = L_47;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_48 = ___1_z;
		NullCheck(L_48);
		int32_t L_49 = L_48->___next_in_index;
		V_5 = L_49;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_50 = ___1_z;
		NullCheck(L_50);
		int32_t L_51 = L_50->___avail_in;
		V_6 = L_51;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_52 = ___0_s;
		NullCheck(L_52);
		int32_t L_53 = L_52->___bitb;
		V_3 = L_53;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_54 = ___0_s;
		NullCheck(L_54);
		int32_t L_55 = L_54->___bitk;
		V_4 = L_55;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_56 = ___0_s;
		NullCheck(L_56);
		int32_t L_57 = L_56->___write;
		V_7 = L_57;
		int32_t L_58 = V_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_59 = ___0_s;
		NullCheck(L_59);
		int32_t L_60 = L_59->___read;
		if ((((int32_t)L_58) >= ((int32_t)L_60)))
		{
			goto IL_0158;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_61 = ___0_s;
		NullCheck(L_61);
		int32_t L_62 = L_61->___read;
		int32_t L_63 = V_7;
		G_B11_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_62, L_63)), 1));
		goto IL_0161;
	}

IL_0158:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_64 = ___0_s;
		NullCheck(L_64);
		int32_t L_65 = L_64->___end;
		int32_t L_66 = V_7;
		G_B11_0 = ((int32_t)il2cpp_codegen_subtract(L_65, L_66));
	}

IL_0161:
	{
		V_8 = G_B11_0;
		int32_t L_67 = ___2_r;
		if (!L_67)
		{
			goto IL_0183;
		}
	}
	{
		int32_t L_68 = ___2_r;
		if ((!(((uint32_t)L_68) == ((uint32_t)1))))
		{
			G_B14_0 = __this;
			goto IL_0177;
		}
		G_B13_0 = __this;
	}
	{
		G_B15_0 = 7;
		G_B15_1 = G_B13_0;
		goto IL_0179;
	}

IL_0177:
	{
		G_B15_0 = ((int32_t)9);
		G_B15_1 = G_B14_0;
	}

IL_0179:
	{
		NullCheck(G_B15_1);
		G_B15_1->___mode = G_B15_0;
		goto IL_0b2b;
	}

IL_0183:
	{
		uint8_t L_69 = __this->___lbits;
		__this->___need = L_69;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_70 = __this->___ltree;
		__this->___tree = L_70;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___tree), (void*)L_70);
		int32_t L_71 = __this->___ltree_index;
		__this->___tree_index = L_71;
		__this->___mode = 1;
		goto IL_01b3;
	}

IL_01b3:
	{
		int32_t L_72 = __this->___need;
		V_0 = L_72;
		goto IL_023e;
	}

IL_01bf:
	{
		int32_t L_73 = V_6;
		if (!L_73)
		{
			goto IL_01ce;
		}
	}
	{
		___2_r = 0;
		goto IL_0215;
	}

IL_01ce:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_74 = ___0_s;
		int32_t L_75 = V_3;
		NullCheck(L_74);
		L_74->___bitb = L_75;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_76 = ___0_s;
		int32_t L_77 = V_4;
		NullCheck(L_76);
		L_76->___bitk = L_77;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_78 = ___1_z;
		int32_t L_79 = V_6;
		NullCheck(L_78);
		L_78->___avail_in = L_79;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_80 = ___1_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_81 = L_80;
		NullCheck(L_81);
		int64_t L_82 = L_81->___total_in;
		int32_t L_83 = V_5;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_84 = ___1_z;
		NullCheck(L_84);
		int32_t L_85 = L_84->___next_in_index;
		NullCheck(L_81);
		L_81->___total_in = ((int64_t)il2cpp_codegen_add(L_82, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_83, L_85)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_86 = ___1_z;
		int32_t L_87 = V_5;
		NullCheck(L_86);
		L_86->___next_in_index = L_87;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_88 = ___0_s;
		int32_t L_89 = V_7;
		NullCheck(L_88);
		L_88->___write = L_89;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_90 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_91 = ___1_z;
		int32_t L_92 = ___2_r;
		NullCheck(L_90);
		int32_t L_93;
		L_93 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_90, L_91, L_92, NULL);
		return L_93;
	}

IL_0215:
	{
		int32_t L_94 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_subtract(L_94, 1));
		int32_t L_95 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_96 = ___1_z;
		NullCheck(L_96);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_97 = L_96->___next_in;
		int32_t L_98 = V_5;
		int32_t L_99 = L_98;
		V_5 = ((int32_t)il2cpp_codegen_add(L_99, 1));
		NullCheck(L_97);
		int32_t L_100 = L_99;
		uint8_t L_101 = (L_97)->GetAt(static_cast<il2cpp_array_size_t>(L_100));
		int32_t L_102 = V_4;
		V_3 = ((int32_t)(L_95|((int32_t)(((int32_t)((int32_t)L_101&((int32_t)255)))<<((int32_t)(L_102&((int32_t)31)))))));
		int32_t L_103 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_add(L_103, 8));
	}

IL_023e:
	{
		int32_t L_104 = V_4;
		int32_t L_105 = V_0;
		if ((((int32_t)L_104) < ((int32_t)L_105)))
		{
			goto IL_01bf;
		}
	}
	{
		int32_t L_106 = __this->___tree_index;
		int32_t L_107 = V_3;
		il2cpp_codegen_runtime_class_init_inline(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_108 = ((InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_StaticFields*)il2cpp_codegen_static_fields_for(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var))->___inflate_mask;
		int32_t L_109 = V_0;
		NullCheck(L_108);
		int32_t L_110 = L_109;
		int32_t L_111 = (L_108)->GetAt(static_cast<il2cpp_array_size_t>(L_110));
		V_1 = ((int32_t)il2cpp_codegen_multiply(((int32_t)il2cpp_codegen_add(L_106, ((int32_t)(L_107&L_111)))), 3));
		int32_t L_112 = V_3;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_113 = __this->___tree;
		int32_t L_114 = V_1;
		NullCheck(L_113);
		int32_t L_115 = ((int32_t)il2cpp_codegen_add(L_114, 1));
		int32_t L_116 = (L_113)->GetAt(static_cast<il2cpp_array_size_t>(L_115));
		V_3 = ((int32_t)(L_112>>((int32_t)(L_116&((int32_t)31)))));
		int32_t L_117 = V_4;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_118 = __this->___tree;
		int32_t L_119 = V_1;
		NullCheck(L_118);
		int32_t L_120 = ((int32_t)il2cpp_codegen_add(L_119, 1));
		int32_t L_121 = (L_118)->GetAt(static_cast<il2cpp_array_size_t>(L_120));
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_117, L_121));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_122 = __this->___tree;
		int32_t L_123 = V_1;
		NullCheck(L_122);
		int32_t L_124 = L_123;
		int32_t L_125 = (L_122)->GetAt(static_cast<il2cpp_array_size_t>(L_124));
		V_2 = L_125;
		int32_t L_126 = V_2;
		if (L_126)
		{
			goto IL_02a3;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_127 = __this->___tree;
		int32_t L_128 = V_1;
		NullCheck(L_127);
		int32_t L_129 = ((int32_t)il2cpp_codegen_add(L_128, 2));
		int32_t L_130 = (L_127)->GetAt(static_cast<il2cpp_array_size_t>(L_129));
		__this->___lit = L_130;
		__this->___mode = 6;
		goto IL_0b2b;
	}

IL_02a3:
	{
		int32_t L_131 = V_2;
		if (!((int32_t)(L_131&((int32_t)16))))
		{
			goto IL_02d2;
		}
	}
	{
		int32_t L_132 = V_2;
		__this->___get = ((int32_t)(L_132&((int32_t)15)));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_133 = __this->___tree;
		int32_t L_134 = V_1;
		NullCheck(L_133);
		int32_t L_135 = ((int32_t)il2cpp_codegen_add(L_134, 2));
		int32_t L_136 = (L_133)->GetAt(static_cast<il2cpp_array_size_t>(L_135));
		__this->___len = L_136;
		__this->___mode = 2;
		goto IL_0b2b;
	}

IL_02d2:
	{
		int32_t L_137 = V_2;
		if (((int32_t)(L_137&((int32_t)64))))
		{
			goto IL_02fb;
		}
	}
	{
		int32_t L_138 = V_2;
		__this->___need = L_138;
		int32_t L_139 = V_1;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_140 = __this->___tree;
		int32_t L_141 = V_1;
		NullCheck(L_140);
		int32_t L_142 = ((int32_t)il2cpp_codegen_add(L_141, 2));
		int32_t L_143 = (L_140)->GetAt(static_cast<il2cpp_array_size_t>(L_142));
		__this->___tree_index = ((int32_t)il2cpp_codegen_add(((int32_t)(L_139/3)), L_143));
		goto IL_0b2b;
	}

IL_02fb:
	{
		int32_t L_144 = V_2;
		if (!((int32_t)(L_144&((int32_t)32))))
		{
			goto IL_0310;
		}
	}
	{
		__this->___mode = 7;
		goto IL_0b2b;
	}

IL_0310:
	{
		__this->___mode = ((int32_t)9);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_145 = ___1_z;
		NullCheck(L_145);
		L_145->___msg = _stringLiteral96025B6397AAC8D06A75085B92AD0F0146044D16;
		Il2CppCodeGenWriteBarrier((void**)(&L_145->___msg), (void*)_stringLiteral96025B6397AAC8D06A75085B92AD0F0146044D16);
		___2_r = ((int32_t)-3);
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_146 = ___0_s;
		int32_t L_147 = V_3;
		NullCheck(L_146);
		L_146->___bitb = L_147;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_148 = ___0_s;
		int32_t L_149 = V_4;
		NullCheck(L_148);
		L_148->___bitk = L_149;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_150 = ___1_z;
		int32_t L_151 = V_6;
		NullCheck(L_150);
		L_150->___avail_in = L_151;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_152 = ___1_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_153 = L_152;
		NullCheck(L_153);
		int64_t L_154 = L_153->___total_in;
		int32_t L_155 = V_5;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_156 = ___1_z;
		NullCheck(L_156);
		int32_t L_157 = L_156->___next_in_index;
		NullCheck(L_153);
		L_153->___total_in = ((int64_t)il2cpp_codegen_add(L_154, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_155, L_157)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_158 = ___1_z;
		int32_t L_159 = V_5;
		NullCheck(L_158);
		L_158->___next_in_index = L_159;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_160 = ___0_s;
		int32_t L_161 = V_7;
		NullCheck(L_160);
		L_160->___write = L_161;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_162 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_163 = ___1_z;
		int32_t L_164 = ___2_r;
		NullCheck(L_162);
		int32_t L_165;
		L_165 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_162, L_163, L_164, NULL);
		return L_165;
	}

IL_036e:
	{
		int32_t L_166 = __this->___get;
		V_0 = L_166;
		goto IL_03f9;
	}

IL_037a:
	{
		int32_t L_167 = V_6;
		if (!L_167)
		{
			goto IL_0389;
		}
	}
	{
		___2_r = 0;
		goto IL_03d0;
	}

IL_0389:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_168 = ___0_s;
		int32_t L_169 = V_3;
		NullCheck(L_168);
		L_168->___bitb = L_169;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_170 = ___0_s;
		int32_t L_171 = V_4;
		NullCheck(L_170);
		L_170->___bitk = L_171;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_172 = ___1_z;
		int32_t L_173 = V_6;
		NullCheck(L_172);
		L_172->___avail_in = L_173;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_174 = ___1_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_175 = L_174;
		NullCheck(L_175);
		int64_t L_176 = L_175->___total_in;
		int32_t L_177 = V_5;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_178 = ___1_z;
		NullCheck(L_178);
		int32_t L_179 = L_178->___next_in_index;
		NullCheck(L_175);
		L_175->___total_in = ((int64_t)il2cpp_codegen_add(L_176, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_177, L_179)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_180 = ___1_z;
		int32_t L_181 = V_5;
		NullCheck(L_180);
		L_180->___next_in_index = L_181;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_182 = ___0_s;
		int32_t L_183 = V_7;
		NullCheck(L_182);
		L_182->___write = L_183;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_184 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_185 = ___1_z;
		int32_t L_186 = ___2_r;
		NullCheck(L_184);
		int32_t L_187;
		L_187 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_184, L_185, L_186, NULL);
		return L_187;
	}

IL_03d0:
	{
		int32_t L_188 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_subtract(L_188, 1));
		int32_t L_189 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_190 = ___1_z;
		NullCheck(L_190);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_191 = L_190->___next_in;
		int32_t L_192 = V_5;
		int32_t L_193 = L_192;
		V_5 = ((int32_t)il2cpp_codegen_add(L_193, 1));
		NullCheck(L_191);
		int32_t L_194 = L_193;
		uint8_t L_195 = (L_191)->GetAt(static_cast<il2cpp_array_size_t>(L_194));
		int32_t L_196 = V_4;
		V_3 = ((int32_t)(L_189|((int32_t)(((int32_t)((int32_t)L_195&((int32_t)255)))<<((int32_t)(L_196&((int32_t)31)))))));
		int32_t L_197 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_add(L_197, 8));
	}

IL_03f9:
	{
		int32_t L_198 = V_4;
		int32_t L_199 = V_0;
		if ((((int32_t)L_198) < ((int32_t)L_199)))
		{
			goto IL_037a;
		}
	}
	{
		int32_t L_200 = __this->___len;
		int32_t L_201 = V_3;
		il2cpp_codegen_runtime_class_init_inline(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_202 = ((InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_StaticFields*)il2cpp_codegen_static_fields_for(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var))->___inflate_mask;
		int32_t L_203 = V_0;
		NullCheck(L_202);
		int32_t L_204 = L_203;
		int32_t L_205 = (L_202)->GetAt(static_cast<il2cpp_array_size_t>(L_204));
		__this->___len = ((int32_t)il2cpp_codegen_add(L_200, ((int32_t)(L_201&L_205))));
		int32_t L_206 = V_3;
		int32_t L_207 = V_0;
		V_3 = ((int32_t)(L_206>>((int32_t)(L_207&((int32_t)31)))));
		int32_t L_208 = V_4;
		int32_t L_209 = V_0;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_208, L_209));
		uint8_t L_210 = __this->___dbits;
		__this->___need = L_210;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_211 = __this->___dtree;
		__this->___tree = L_211;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___tree), (void*)L_211);
		int32_t L_212 = __this->___dtree_index;
		__this->___tree_index = L_212;
		__this->___mode = 3;
		goto IL_0454;
	}

IL_0454:
	{
		int32_t L_213 = __this->___need;
		V_0 = L_213;
		goto IL_04df;
	}

IL_0460:
	{
		int32_t L_214 = V_6;
		if (!L_214)
		{
			goto IL_046f;
		}
	}
	{
		___2_r = 0;
		goto IL_04b6;
	}

IL_046f:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_215 = ___0_s;
		int32_t L_216 = V_3;
		NullCheck(L_215);
		L_215->___bitb = L_216;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_217 = ___0_s;
		int32_t L_218 = V_4;
		NullCheck(L_217);
		L_217->___bitk = L_218;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_219 = ___1_z;
		int32_t L_220 = V_6;
		NullCheck(L_219);
		L_219->___avail_in = L_220;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_221 = ___1_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_222 = L_221;
		NullCheck(L_222);
		int64_t L_223 = L_222->___total_in;
		int32_t L_224 = V_5;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_225 = ___1_z;
		NullCheck(L_225);
		int32_t L_226 = L_225->___next_in_index;
		NullCheck(L_222);
		L_222->___total_in = ((int64_t)il2cpp_codegen_add(L_223, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_224, L_226)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_227 = ___1_z;
		int32_t L_228 = V_5;
		NullCheck(L_227);
		L_227->___next_in_index = L_228;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_229 = ___0_s;
		int32_t L_230 = V_7;
		NullCheck(L_229);
		L_229->___write = L_230;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_231 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_232 = ___1_z;
		int32_t L_233 = ___2_r;
		NullCheck(L_231);
		int32_t L_234;
		L_234 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_231, L_232, L_233, NULL);
		return L_234;
	}

IL_04b6:
	{
		int32_t L_235 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_subtract(L_235, 1));
		int32_t L_236 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_237 = ___1_z;
		NullCheck(L_237);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_238 = L_237->___next_in;
		int32_t L_239 = V_5;
		int32_t L_240 = L_239;
		V_5 = ((int32_t)il2cpp_codegen_add(L_240, 1));
		NullCheck(L_238);
		int32_t L_241 = L_240;
		uint8_t L_242 = (L_238)->GetAt(static_cast<il2cpp_array_size_t>(L_241));
		int32_t L_243 = V_4;
		V_3 = ((int32_t)(L_236|((int32_t)(((int32_t)((int32_t)L_242&((int32_t)255)))<<((int32_t)(L_243&((int32_t)31)))))));
		int32_t L_244 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_add(L_244, 8));
	}

IL_04df:
	{
		int32_t L_245 = V_4;
		int32_t L_246 = V_0;
		if ((((int32_t)L_245) < ((int32_t)L_246)))
		{
			goto IL_0460;
		}
	}
	{
		int32_t L_247 = __this->___tree_index;
		int32_t L_248 = V_3;
		il2cpp_codegen_runtime_class_init_inline(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_249 = ((InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_StaticFields*)il2cpp_codegen_static_fields_for(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var))->___inflate_mask;
		int32_t L_250 = V_0;
		NullCheck(L_249);
		int32_t L_251 = L_250;
		int32_t L_252 = (L_249)->GetAt(static_cast<il2cpp_array_size_t>(L_251));
		V_1 = ((int32_t)il2cpp_codegen_multiply(((int32_t)il2cpp_codegen_add(L_247, ((int32_t)(L_248&L_252)))), 3));
		int32_t L_253 = V_3;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_254 = __this->___tree;
		int32_t L_255 = V_1;
		NullCheck(L_254);
		int32_t L_256 = ((int32_t)il2cpp_codegen_add(L_255, 1));
		int32_t L_257 = (L_254)->GetAt(static_cast<il2cpp_array_size_t>(L_256));
		V_3 = ((int32_t)(L_253>>((int32_t)(L_257&((int32_t)31)))));
		int32_t L_258 = V_4;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_259 = __this->___tree;
		int32_t L_260 = V_1;
		NullCheck(L_259);
		int32_t L_261 = ((int32_t)il2cpp_codegen_add(L_260, 1));
		int32_t L_262 = (L_259)->GetAt(static_cast<il2cpp_array_size_t>(L_261));
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_258, L_262));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_263 = __this->___tree;
		int32_t L_264 = V_1;
		NullCheck(L_263);
		int32_t L_265 = L_264;
		int32_t L_266 = (L_263)->GetAt(static_cast<il2cpp_array_size_t>(L_265));
		V_2 = L_266;
		int32_t L_267 = V_2;
		if (!((int32_t)(L_267&((int32_t)16))))
		{
			goto IL_0551;
		}
	}
	{
		int32_t L_268 = V_2;
		__this->___get = ((int32_t)(L_268&((int32_t)15)));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_269 = __this->___tree;
		int32_t L_270 = V_1;
		NullCheck(L_269);
		int32_t L_271 = ((int32_t)il2cpp_codegen_add(L_270, 2));
		int32_t L_272 = (L_269)->GetAt(static_cast<il2cpp_array_size_t>(L_271));
		__this->___dist = L_272;
		__this->___mode = 4;
		goto IL_0b2b;
	}

IL_0551:
	{
		int32_t L_273 = V_2;
		if (((int32_t)(L_273&((int32_t)64))))
		{
			goto IL_057a;
		}
	}
	{
		int32_t L_274 = V_2;
		__this->___need = L_274;
		int32_t L_275 = V_1;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_276 = __this->___tree;
		int32_t L_277 = V_1;
		NullCheck(L_276);
		int32_t L_278 = ((int32_t)il2cpp_codegen_add(L_277, 2));
		int32_t L_279 = (L_276)->GetAt(static_cast<il2cpp_array_size_t>(L_278));
		__this->___tree_index = ((int32_t)il2cpp_codegen_add(((int32_t)(L_275/3)), L_279));
		goto IL_0b2b;
	}

IL_057a:
	{
		__this->___mode = ((int32_t)9);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_280 = ___1_z;
		NullCheck(L_280);
		L_280->___msg = _stringLiteralBDD794DC7884A15D601FC8AD88E8B6637CF36948;
		Il2CppCodeGenWriteBarrier((void**)(&L_280->___msg), (void*)_stringLiteralBDD794DC7884A15D601FC8AD88E8B6637CF36948);
		___2_r = ((int32_t)-3);
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_281 = ___0_s;
		int32_t L_282 = V_3;
		NullCheck(L_281);
		L_281->___bitb = L_282;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_283 = ___0_s;
		int32_t L_284 = V_4;
		NullCheck(L_283);
		L_283->___bitk = L_284;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_285 = ___1_z;
		int32_t L_286 = V_6;
		NullCheck(L_285);
		L_285->___avail_in = L_286;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_287 = ___1_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_288 = L_287;
		NullCheck(L_288);
		int64_t L_289 = L_288->___total_in;
		int32_t L_290 = V_5;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_291 = ___1_z;
		NullCheck(L_291);
		int32_t L_292 = L_291->___next_in_index;
		NullCheck(L_288);
		L_288->___total_in = ((int64_t)il2cpp_codegen_add(L_289, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_290, L_292)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_293 = ___1_z;
		int32_t L_294 = V_5;
		NullCheck(L_293);
		L_293->___next_in_index = L_294;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_295 = ___0_s;
		int32_t L_296 = V_7;
		NullCheck(L_295);
		L_295->___write = L_296;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_297 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_298 = ___1_z;
		int32_t L_299 = ___2_r;
		NullCheck(L_297);
		int32_t L_300;
		L_300 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_297, L_298, L_299, NULL);
		return L_300;
	}

IL_05d8:
	{
		int32_t L_301 = __this->___get;
		V_0 = L_301;
		goto IL_0663;
	}

IL_05e4:
	{
		int32_t L_302 = V_6;
		if (!L_302)
		{
			goto IL_05f3;
		}
	}
	{
		___2_r = 0;
		goto IL_063a;
	}

IL_05f3:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_303 = ___0_s;
		int32_t L_304 = V_3;
		NullCheck(L_303);
		L_303->___bitb = L_304;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_305 = ___0_s;
		int32_t L_306 = V_4;
		NullCheck(L_305);
		L_305->___bitk = L_306;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_307 = ___1_z;
		int32_t L_308 = V_6;
		NullCheck(L_307);
		L_307->___avail_in = L_308;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_309 = ___1_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_310 = L_309;
		NullCheck(L_310);
		int64_t L_311 = L_310->___total_in;
		int32_t L_312 = V_5;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_313 = ___1_z;
		NullCheck(L_313);
		int32_t L_314 = L_313->___next_in_index;
		NullCheck(L_310);
		L_310->___total_in = ((int64_t)il2cpp_codegen_add(L_311, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_312, L_314)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_315 = ___1_z;
		int32_t L_316 = V_5;
		NullCheck(L_315);
		L_315->___next_in_index = L_316;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_317 = ___0_s;
		int32_t L_318 = V_7;
		NullCheck(L_317);
		L_317->___write = L_318;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_319 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_320 = ___1_z;
		int32_t L_321 = ___2_r;
		NullCheck(L_319);
		int32_t L_322;
		L_322 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_319, L_320, L_321, NULL);
		return L_322;
	}

IL_063a:
	{
		int32_t L_323 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_subtract(L_323, 1));
		int32_t L_324 = V_3;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_325 = ___1_z;
		NullCheck(L_325);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_326 = L_325->___next_in;
		int32_t L_327 = V_5;
		int32_t L_328 = L_327;
		V_5 = ((int32_t)il2cpp_codegen_add(L_328, 1));
		NullCheck(L_326);
		int32_t L_329 = L_328;
		uint8_t L_330 = (L_326)->GetAt(static_cast<il2cpp_array_size_t>(L_329));
		int32_t L_331 = V_4;
		V_3 = ((int32_t)(L_324|((int32_t)(((int32_t)((int32_t)L_330&((int32_t)255)))<<((int32_t)(L_331&((int32_t)31)))))));
		int32_t L_332 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_add(L_332, 8));
	}

IL_0663:
	{
		int32_t L_333 = V_4;
		int32_t L_334 = V_0;
		if ((((int32_t)L_333) < ((int32_t)L_334)))
		{
			goto IL_05e4;
		}
	}
	{
		int32_t L_335 = __this->___dist;
		int32_t L_336 = V_3;
		il2cpp_codegen_runtime_class_init_inline(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_337 = ((InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_StaticFields*)il2cpp_codegen_static_fields_for(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var))->___inflate_mask;
		int32_t L_338 = V_0;
		NullCheck(L_337);
		int32_t L_339 = L_338;
		int32_t L_340 = (L_337)->GetAt(static_cast<il2cpp_array_size_t>(L_339));
		__this->___dist = ((int32_t)il2cpp_codegen_add(L_335, ((int32_t)(L_336&L_340))));
		int32_t L_341 = V_3;
		int32_t L_342 = V_0;
		V_3 = ((int32_t)(L_341>>((int32_t)(L_342&((int32_t)31)))));
		int32_t L_343 = V_4;
		int32_t L_344 = V_0;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_343, L_344));
		__this->___mode = 5;
		goto IL_069a;
	}

IL_069a:
	{
		int32_t L_345 = V_7;
		int32_t L_346 = __this->___dist;
		V_9 = ((int32_t)il2cpp_codegen_subtract(L_345, L_346));
		goto IL_06b5;
	}

IL_06aa:
	{
		int32_t L_347 = V_9;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_348 = ___0_s;
		NullCheck(L_348);
		int32_t L_349 = L_348->___end;
		V_9 = ((int32_t)il2cpp_codegen_add(L_347, L_349));
	}

IL_06b5:
	{
		int32_t L_350 = V_9;
		if ((((int32_t)L_350) < ((int32_t)0)))
		{
			goto IL_06aa;
		}
	}
	{
		goto IL_0826;
	}

IL_06c2:
	{
		int32_t L_351 = V_8;
		if (L_351)
		{
			goto IL_07e6;
		}
	}
	{
		int32_t L_352 = V_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_353 = ___0_s;
		NullCheck(L_353);
		int32_t L_354 = L_353->___end;
		if ((!(((uint32_t)L_352) == ((uint32_t)L_354))))
		{
			goto IL_070c;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_355 = ___0_s;
		NullCheck(L_355);
		int32_t L_356 = L_355->___read;
		if (!L_356)
		{
			goto IL_070c;
		}
	}
	{
		V_7 = 0;
		int32_t L_357 = V_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_358 = ___0_s;
		NullCheck(L_358);
		int32_t L_359 = L_358->___read;
		if ((((int32_t)L_357) >= ((int32_t)L_359)))
		{
			goto IL_0701;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_360 = ___0_s;
		NullCheck(L_360);
		int32_t L_361 = L_360->___read;
		int32_t L_362 = V_7;
		G_B67_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_361, L_362)), 1));
		goto IL_070a;
	}

IL_0701:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_363 = ___0_s;
		NullCheck(L_363);
		int32_t L_364 = L_363->___end;
		int32_t L_365 = V_7;
		G_B67_0 = ((int32_t)il2cpp_codegen_subtract(L_364, L_365));
	}

IL_070a:
	{
		V_8 = G_B67_0;
	}

IL_070c:
	{
		int32_t L_366 = V_8;
		if (L_366)
		{
			goto IL_07e6;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_367 = ___0_s;
		int32_t L_368 = V_7;
		NullCheck(L_367);
		L_367->___write = L_368;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_369 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_370 = ___1_z;
		int32_t L_371 = ___2_r;
		NullCheck(L_369);
		int32_t L_372;
		L_372 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_369, L_370, L_371, NULL);
		___2_r = L_372;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_373 = ___0_s;
		NullCheck(L_373);
		int32_t L_374 = L_373->___write;
		V_7 = L_374;
		int32_t L_375 = V_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_376 = ___0_s;
		NullCheck(L_376);
		int32_t L_377 = L_376->___read;
		if ((((int32_t)L_375) >= ((int32_t)L_377)))
		{
			goto IL_074a;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_378 = ___0_s;
		NullCheck(L_378);
		int32_t L_379 = L_378->___read;
		int32_t L_380 = V_7;
		G_B72_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_379, L_380)), 1));
		goto IL_0753;
	}

IL_074a:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_381 = ___0_s;
		NullCheck(L_381);
		int32_t L_382 = L_381->___end;
		int32_t L_383 = V_7;
		G_B72_0 = ((int32_t)il2cpp_codegen_subtract(L_382, L_383));
	}

IL_0753:
	{
		V_8 = G_B72_0;
		int32_t L_384 = V_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_385 = ___0_s;
		NullCheck(L_385);
		int32_t L_386 = L_385->___end;
		if ((!(((uint32_t)L_384) == ((uint32_t)L_386))))
		{
			goto IL_0798;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_387 = ___0_s;
		NullCheck(L_387);
		int32_t L_388 = L_387->___read;
		if (!L_388)
		{
			goto IL_0798;
		}
	}
	{
		V_7 = 0;
		int32_t L_389 = V_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_390 = ___0_s;
		NullCheck(L_390);
		int32_t L_391 = L_390->___read;
		if ((((int32_t)L_389) >= ((int32_t)L_391)))
		{
			goto IL_078d;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_392 = ___0_s;
		NullCheck(L_392);
		int32_t L_393 = L_392->___read;
		int32_t L_394 = V_7;
		G_B77_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_393, L_394)), 1));
		goto IL_0796;
	}

IL_078d:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_395 = ___0_s;
		NullCheck(L_395);
		int32_t L_396 = L_395->___end;
		int32_t L_397 = V_7;
		G_B77_0 = ((int32_t)il2cpp_codegen_subtract(L_396, L_397));
	}

IL_0796:
	{
		V_8 = G_B77_0;
	}

IL_0798:
	{
		int32_t L_398 = V_8;
		if (L_398)
		{
			goto IL_07e6;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_399 = ___0_s;
		int32_t L_400 = V_3;
		NullCheck(L_399);
		L_399->___bitb = L_400;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_401 = ___0_s;
		int32_t L_402 = V_4;
		NullCheck(L_401);
		L_401->___bitk = L_402;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_403 = ___1_z;
		int32_t L_404 = V_6;
		NullCheck(L_403);
		L_403->___avail_in = L_404;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_405 = ___1_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_406 = L_405;
		NullCheck(L_406);
		int64_t L_407 = L_406->___total_in;
		int32_t L_408 = V_5;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_409 = ___1_z;
		NullCheck(L_409);
		int32_t L_410 = L_409->___next_in_index;
		NullCheck(L_406);
		L_406->___total_in = ((int64_t)il2cpp_codegen_add(L_407, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_408, L_410)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_411 = ___1_z;
		int32_t L_412 = V_5;
		NullCheck(L_411);
		L_411->___next_in_index = L_412;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_413 = ___0_s;
		int32_t L_414 = V_7;
		NullCheck(L_413);
		L_413->___write = L_414;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_415 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_416 = ___1_z;
		int32_t L_417 = ___2_r;
		NullCheck(L_415);
		int32_t L_418;
		L_418 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_415, L_416, L_417, NULL);
		return L_418;
	}

IL_07e6:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_419 = ___0_s;
		NullCheck(L_419);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_420 = L_419->___window;
		int32_t L_421 = V_7;
		int32_t L_422 = L_421;
		V_7 = ((int32_t)il2cpp_codegen_add(L_422, 1));
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_423 = ___0_s;
		NullCheck(L_423);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_424 = L_423->___window;
		int32_t L_425 = V_9;
		int32_t L_426 = L_425;
		V_9 = ((int32_t)il2cpp_codegen_add(L_426, 1));
		NullCheck(L_424);
		int32_t L_427 = L_426;
		uint8_t L_428 = (L_424)->GetAt(static_cast<il2cpp_array_size_t>(L_427));
		NullCheck(L_420);
		(L_420)->SetAt(static_cast<il2cpp_array_size_t>(L_422), (uint8_t)L_428);
		int32_t L_429 = V_8;
		V_8 = ((int32_t)il2cpp_codegen_subtract(L_429, 1));
		int32_t L_430 = V_9;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_431 = ___0_s;
		NullCheck(L_431);
		int32_t L_432 = L_431->___end;
		if ((!(((uint32_t)L_430) == ((uint32_t)L_432))))
		{
			goto IL_0818;
		}
	}
	{
		V_9 = 0;
	}

IL_0818:
	{
		int32_t L_433 = __this->___len;
		__this->___len = ((int32_t)il2cpp_codegen_subtract(L_433, 1));
	}

IL_0826:
	{
		int32_t L_434 = __this->___len;
		if (L_434)
		{
			goto IL_06c2;
		}
	}
	{
		__this->___mode = 0;
		goto IL_0b2b;
	}

IL_083d:
	{
		int32_t L_435 = V_8;
		if (L_435)
		{
			goto IL_0961;
		}
	}
	{
		int32_t L_436 = V_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_437 = ___0_s;
		NullCheck(L_437);
		int32_t L_438 = L_437->___end;
		if ((!(((uint32_t)L_436) == ((uint32_t)L_438))))
		{
			goto IL_0887;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_439 = ___0_s;
		NullCheck(L_439);
		int32_t L_440 = L_439->___read;
		if (!L_440)
		{
			goto IL_0887;
		}
	}
	{
		V_7 = 0;
		int32_t L_441 = V_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_442 = ___0_s;
		NullCheck(L_442);
		int32_t L_443 = L_442->___read;
		if ((((int32_t)L_441) >= ((int32_t)L_443)))
		{
			goto IL_087c;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_444 = ___0_s;
		NullCheck(L_444);
		int32_t L_445 = L_444->___read;
		int32_t L_446 = V_7;
		G_B91_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_445, L_446)), 1));
		goto IL_0885;
	}

IL_087c:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_447 = ___0_s;
		NullCheck(L_447);
		int32_t L_448 = L_447->___end;
		int32_t L_449 = V_7;
		G_B91_0 = ((int32_t)il2cpp_codegen_subtract(L_448, L_449));
	}

IL_0885:
	{
		V_8 = G_B91_0;
	}

IL_0887:
	{
		int32_t L_450 = V_8;
		if (L_450)
		{
			goto IL_0961;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_451 = ___0_s;
		int32_t L_452 = V_7;
		NullCheck(L_451);
		L_451->___write = L_452;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_453 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_454 = ___1_z;
		int32_t L_455 = ___2_r;
		NullCheck(L_453);
		int32_t L_456;
		L_456 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_453, L_454, L_455, NULL);
		___2_r = L_456;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_457 = ___0_s;
		NullCheck(L_457);
		int32_t L_458 = L_457->___write;
		V_7 = L_458;
		int32_t L_459 = V_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_460 = ___0_s;
		NullCheck(L_460);
		int32_t L_461 = L_460->___read;
		if ((((int32_t)L_459) >= ((int32_t)L_461)))
		{
			goto IL_08c5;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_462 = ___0_s;
		NullCheck(L_462);
		int32_t L_463 = L_462->___read;
		int32_t L_464 = V_7;
		G_B96_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_463, L_464)), 1));
		goto IL_08ce;
	}

IL_08c5:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_465 = ___0_s;
		NullCheck(L_465);
		int32_t L_466 = L_465->___end;
		int32_t L_467 = V_7;
		G_B96_0 = ((int32_t)il2cpp_codegen_subtract(L_466, L_467));
	}

IL_08ce:
	{
		V_8 = G_B96_0;
		int32_t L_468 = V_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_469 = ___0_s;
		NullCheck(L_469);
		int32_t L_470 = L_469->___end;
		if ((!(((uint32_t)L_468) == ((uint32_t)L_470))))
		{
			goto IL_0913;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_471 = ___0_s;
		NullCheck(L_471);
		int32_t L_472 = L_471->___read;
		if (!L_472)
		{
			goto IL_0913;
		}
	}
	{
		V_7 = 0;
		int32_t L_473 = V_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_474 = ___0_s;
		NullCheck(L_474);
		int32_t L_475 = L_474->___read;
		if ((((int32_t)L_473) >= ((int32_t)L_475)))
		{
			goto IL_0908;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_476 = ___0_s;
		NullCheck(L_476);
		int32_t L_477 = L_476->___read;
		int32_t L_478 = V_7;
		G_B101_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_477, L_478)), 1));
		goto IL_0911;
	}

IL_0908:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_479 = ___0_s;
		NullCheck(L_479);
		int32_t L_480 = L_479->___end;
		int32_t L_481 = V_7;
		G_B101_0 = ((int32_t)il2cpp_codegen_subtract(L_480, L_481));
	}

IL_0911:
	{
		V_8 = G_B101_0;
	}

IL_0913:
	{
		int32_t L_482 = V_8;
		if (L_482)
		{
			goto IL_0961;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_483 = ___0_s;
		int32_t L_484 = V_3;
		NullCheck(L_483);
		L_483->___bitb = L_484;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_485 = ___0_s;
		int32_t L_486 = V_4;
		NullCheck(L_485);
		L_485->___bitk = L_486;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_487 = ___1_z;
		int32_t L_488 = V_6;
		NullCheck(L_487);
		L_487->___avail_in = L_488;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_489 = ___1_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_490 = L_489;
		NullCheck(L_490);
		int64_t L_491 = L_490->___total_in;
		int32_t L_492 = V_5;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_493 = ___1_z;
		NullCheck(L_493);
		int32_t L_494 = L_493->___next_in_index;
		NullCheck(L_490);
		L_490->___total_in = ((int64_t)il2cpp_codegen_add(L_491, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_492, L_494)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_495 = ___1_z;
		int32_t L_496 = V_5;
		NullCheck(L_495);
		L_495->___next_in_index = L_496;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_497 = ___0_s;
		int32_t L_498 = V_7;
		NullCheck(L_497);
		L_497->___write = L_498;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_499 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_500 = ___1_z;
		int32_t L_501 = ___2_r;
		NullCheck(L_499);
		int32_t L_502;
		L_502 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_499, L_500, L_501, NULL);
		return L_502;
	}

IL_0961:
	{
		___2_r = 0;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_503 = ___0_s;
		NullCheck(L_503);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_504 = L_503->___window;
		int32_t L_505 = V_7;
		int32_t L_506 = L_505;
		V_7 = ((int32_t)il2cpp_codegen_add(L_506, 1));
		int32_t L_507 = __this->___lit;
		NullCheck(L_504);
		(L_504)->SetAt(static_cast<il2cpp_array_size_t>(L_506), (uint8_t)((int32_t)(uint8_t)L_507));
		int32_t L_508 = V_8;
		V_8 = ((int32_t)il2cpp_codegen_subtract(L_508, 1));
		__this->___mode = 0;
		goto IL_0b2b;
	}

IL_098b:
	{
		int32_t L_509 = V_4;
		if ((((int32_t)L_509) <= ((int32_t)7)))
		{
			goto IL_09a5;
		}
	}
	{
		int32_t L_510 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_510, 8));
		int32_t L_511 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_add(L_511, 1));
		int32_t L_512 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_subtract(L_512, 1));
	}

IL_09a5:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_513 = ___0_s;
		int32_t L_514 = V_7;
		NullCheck(L_513);
		L_513->___write = L_514;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_515 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_516 = ___1_z;
		int32_t L_517 = ___2_r;
		NullCheck(L_515);
		int32_t L_518;
		L_518 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_515, L_516, L_517, NULL);
		___2_r = L_518;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_519 = ___0_s;
		NullCheck(L_519);
		int32_t L_520 = L_519->___write;
		V_7 = L_520;
		int32_t L_521 = V_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_522 = ___0_s;
		NullCheck(L_522);
		int32_t L_523 = L_522->___read;
		if ((((int32_t)L_521) >= ((int32_t)L_523)))
		{
			goto IL_09dc;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_524 = ___0_s;
		NullCheck(L_524);
		int32_t L_525 = L_524->___read;
		int32_t L_526 = V_7;
		G_B110_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_525, L_526)), 1));
		goto IL_09e5;
	}

IL_09dc:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_527 = ___0_s;
		NullCheck(L_527);
		int32_t L_528 = L_527->___end;
		int32_t L_529 = V_7;
		G_B110_0 = ((int32_t)il2cpp_codegen_subtract(L_528, L_529));
	}

IL_09e5:
	{
		V_8 = G_B110_0;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_530 = ___0_s;
		NullCheck(L_530);
		int32_t L_531 = L_530->___read;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_532 = ___0_s;
		NullCheck(L_532);
		int32_t L_533 = L_532->___write;
		if ((((int32_t)L_531) == ((int32_t)L_533)))
		{
			goto IL_0a3f;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_534 = ___0_s;
		int32_t L_535 = V_3;
		NullCheck(L_534);
		L_534->___bitb = L_535;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_536 = ___0_s;
		int32_t L_537 = V_4;
		NullCheck(L_536);
		L_536->___bitk = L_537;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_538 = ___1_z;
		int32_t L_539 = V_6;
		NullCheck(L_538);
		L_538->___avail_in = L_539;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_540 = ___1_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_541 = L_540;
		NullCheck(L_541);
		int64_t L_542 = L_541->___total_in;
		int32_t L_543 = V_5;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_544 = ___1_z;
		NullCheck(L_544);
		int32_t L_545 = L_544->___next_in_index;
		NullCheck(L_541);
		L_541->___total_in = ((int64_t)il2cpp_codegen_add(L_542, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_543, L_545)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_546 = ___1_z;
		int32_t L_547 = V_5;
		NullCheck(L_546);
		L_546->___next_in_index = L_547;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_548 = ___0_s;
		int32_t L_549 = V_7;
		NullCheck(L_548);
		L_548->___write = L_549;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_550 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_551 = ___1_z;
		int32_t L_552 = ___2_r;
		NullCheck(L_550);
		int32_t L_553;
		L_553 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_550, L_551, L_552, NULL);
		return L_553;
	}

IL_0a3f:
	{
		__this->___mode = 8;
		goto IL_0a4b;
	}

IL_0a4b:
	{
		___2_r = 1;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_554 = ___0_s;
		int32_t L_555 = V_3;
		NullCheck(L_554);
		L_554->___bitb = L_555;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_556 = ___0_s;
		int32_t L_557 = V_4;
		NullCheck(L_556);
		L_556->___bitk = L_557;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_558 = ___1_z;
		int32_t L_559 = V_6;
		NullCheck(L_558);
		L_558->___avail_in = L_559;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_560 = ___1_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_561 = L_560;
		NullCheck(L_561);
		int64_t L_562 = L_561->___total_in;
		int32_t L_563 = V_5;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_564 = ___1_z;
		NullCheck(L_564);
		int32_t L_565 = L_564->___next_in_index;
		NullCheck(L_561);
		L_561->___total_in = ((int64_t)il2cpp_codegen_add(L_562, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_563, L_565)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_566 = ___1_z;
		int32_t L_567 = V_5;
		NullCheck(L_566);
		L_566->___next_in_index = L_567;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_568 = ___0_s;
		int32_t L_569 = V_7;
		NullCheck(L_568);
		L_568->___write = L_569;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_570 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_571 = ___1_z;
		int32_t L_572 = ___2_r;
		NullCheck(L_570);
		int32_t L_573;
		L_573 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_570, L_571, L_572, NULL);
		return L_573;
	}

IL_0a95:
	{
		___2_r = ((int32_t)-3);
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_574 = ___0_s;
		int32_t L_575 = V_3;
		NullCheck(L_574);
		L_574->___bitb = L_575;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_576 = ___0_s;
		int32_t L_577 = V_4;
		NullCheck(L_576);
		L_576->___bitk = L_577;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_578 = ___1_z;
		int32_t L_579 = V_6;
		NullCheck(L_578);
		L_578->___avail_in = L_579;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_580 = ___1_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_581 = L_580;
		NullCheck(L_581);
		int64_t L_582 = L_581->___total_in;
		int32_t L_583 = V_5;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_584 = ___1_z;
		NullCheck(L_584);
		int32_t L_585 = L_584->___next_in_index;
		NullCheck(L_581);
		L_581->___total_in = ((int64_t)il2cpp_codegen_add(L_582, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_583, L_585)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_586 = ___1_z;
		int32_t L_587 = V_5;
		NullCheck(L_586);
		L_586->___next_in_index = L_587;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_588 = ___0_s;
		int32_t L_589 = V_7;
		NullCheck(L_588);
		L_588->___write = L_589;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_590 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_591 = ___1_z;
		int32_t L_592 = ___2_r;
		NullCheck(L_590);
		int32_t L_593;
		L_593 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_590, L_591, L_592, NULL);
		return L_593;
	}

IL_0ae0:
	{
		___2_r = ((int32_t)-2);
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_594 = ___0_s;
		int32_t L_595 = V_3;
		NullCheck(L_594);
		L_594->___bitb = L_595;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_596 = ___0_s;
		int32_t L_597 = V_4;
		NullCheck(L_596);
		L_596->___bitk = L_597;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_598 = ___1_z;
		int32_t L_599 = V_6;
		NullCheck(L_598);
		L_598->___avail_in = L_599;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_600 = ___1_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_601 = L_600;
		NullCheck(L_601);
		int64_t L_602 = L_601->___total_in;
		int32_t L_603 = V_5;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_604 = ___1_z;
		NullCheck(L_604);
		int32_t L_605 = L_604->___next_in_index;
		NullCheck(L_601);
		L_601->___total_in = ((int64_t)il2cpp_codegen_add(L_602, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_603, L_605)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_606 = ___1_z;
		int32_t L_607 = V_5;
		NullCheck(L_606);
		L_606->___next_in_index = L_607;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_608 = ___0_s;
		int32_t L_609 = V_7;
		NullCheck(L_608);
		L_608->___write = L_609;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_610 = ___0_s;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_611 = ___1_z;
		int32_t L_612 = ___2_r;
		NullCheck(L_610);
		int32_t L_613;
		L_613 = InfBlocks_inflate_flush_m08781CA75797D578FADE95730D6B2DD3E6D558EA(L_610, L_611, L_612, NULL);
		return L_613;
	}

IL_0b2b:
	{
		goto IL_0057;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfCodes_free_m6FF96CFF077C86EAD34D40DAD8499E2ED55DC131 (InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfCodes_inflate_fast_m14E202BC92D499CF9473F320A0F1CD68ECEE1E37 (InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0* __this, int32_t ___0_bl, int32_t ___1_bd, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___2_tl, int32_t ___3_tl_index, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___4_td, int32_t ___5_td_index, InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* ___6_s, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___7_z, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral96025B6397AAC8D06A75085B92AD0F0146044D16);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBDD794DC7884A15D601FC8AD88E8B6637CF36948);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* V_1 = NULL;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	int32_t V_7 = 0;
	int32_t V_8 = 0;
	int32_t V_9 = 0;
	int32_t V_10 = 0;
	int32_t V_11 = 0;
	int32_t V_12 = 0;
	int32_t V_13 = 0;
	int32_t V_14 = 0;
	int32_t V_15 = 0;
	int32_t G_B3_0 = 0;
	int32_t G_B44_0 = 0;
	int32_t G_B55_0 = 0;
	int32_t G_B59_0 = 0;
	int32_t G_B66_0 = 0;
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_0 = ___7_z;
		NullCheck(L_0);
		int32_t L_1 = L_0->___next_in_index;
		V_6 = L_1;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_2 = ___7_z;
		NullCheck(L_2);
		int32_t L_3 = L_2->___avail_in;
		V_7 = L_3;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_4 = ___6_s;
		NullCheck(L_4);
		int32_t L_5 = L_4->___bitb;
		V_4 = L_5;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_6 = ___6_s;
		NullCheck(L_6);
		int32_t L_7 = L_6->___bitk;
		V_5 = L_7;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_8 = ___6_s;
		NullCheck(L_8);
		int32_t L_9 = L_8->___write;
		V_8 = L_9;
		int32_t L_10 = V_8;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_11 = ___6_s;
		NullCheck(L_11);
		int32_t L_12 = L_11->___read;
		if ((((int32_t)L_10) >= ((int32_t)L_12)))
		{
			goto IL_004c;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_13 = ___6_s;
		NullCheck(L_13);
		int32_t L_14 = L_13->___read;
		int32_t L_15 = V_8;
		G_B3_0 = ((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_14, L_15)), 1));
		goto IL_0056;
	}

IL_004c:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_16 = ___6_s;
		NullCheck(L_16);
		int32_t L_17 = L_16->___end;
		int32_t L_18 = V_8;
		G_B3_0 = ((int32_t)il2cpp_codegen_subtract(L_17, L_18));
	}

IL_0056:
	{
		V_9 = G_B3_0;
		il2cpp_codegen_runtime_class_init_inline(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_19 = ((InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_StaticFields*)il2cpp_codegen_static_fields_for(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var))->___inflate_mask;
		int32_t L_20 = ___0_bl;
		NullCheck(L_19);
		int32_t L_21 = L_20;
		int32_t L_22 = (L_19)->GetAt(static_cast<il2cpp_array_size_t>(L_21));
		V_10 = L_22;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_23 = ((InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_StaticFields*)il2cpp_codegen_static_fields_for(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var))->___inflate_mask;
		int32_t L_24 = ___1_bd;
		NullCheck(L_23);
		int32_t L_25 = L_24;
		int32_t L_26 = (L_23)->GetAt(static_cast<il2cpp_array_size_t>(L_25));
		V_11 = L_26;
	}

IL_006a:
	{
		goto IL_009b;
	}

IL_006f:
	{
		int32_t L_27 = V_7;
		V_7 = ((int32_t)il2cpp_codegen_subtract(L_27, 1));
		int32_t L_28 = V_4;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_29 = ___7_z;
		NullCheck(L_29);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_30 = L_29->___next_in;
		int32_t L_31 = V_6;
		int32_t L_32 = L_31;
		V_6 = ((int32_t)il2cpp_codegen_add(L_32, 1));
		NullCheck(L_30);
		int32_t L_33 = L_32;
		uint8_t L_34 = (L_30)->GetAt(static_cast<il2cpp_array_size_t>(L_33));
		int32_t L_35 = V_5;
		V_4 = ((int32_t)(L_28|((int32_t)(((int32_t)((int32_t)L_34&((int32_t)255)))<<((int32_t)(L_35&((int32_t)31)))))));
		int32_t L_36 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_36, 8));
	}

IL_009b:
	{
		int32_t L_37 = V_5;
		if ((((int32_t)L_37) < ((int32_t)((int32_t)20))))
		{
			goto IL_006f;
		}
	}
	{
		int32_t L_38 = V_4;
		int32_t L_39 = V_10;
		V_0 = ((int32_t)(L_38&L_39));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_40 = ___2_tl;
		V_1 = L_40;
		int32_t L_41 = ___3_tl_index;
		V_2 = L_41;
		int32_t L_42 = V_2;
		int32_t L_43 = V_0;
		V_15 = ((int32_t)il2cpp_codegen_multiply(((int32_t)il2cpp_codegen_add(L_42, L_43)), 3));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_44 = V_1;
		int32_t L_45 = V_15;
		NullCheck(L_44);
		int32_t L_46 = L_45;
		int32_t L_47 = (L_44)->GetAt(static_cast<il2cpp_array_size_t>(L_46));
		int32_t L_48 = L_47;
		V_3 = L_48;
		if (L_48)
		{
			goto IL_00fb;
		}
	}
	{
		int32_t L_49 = V_4;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_50 = V_1;
		int32_t L_51 = V_15;
		NullCheck(L_50);
		int32_t L_52 = ((int32_t)il2cpp_codegen_add(L_51, 1));
		int32_t L_53 = (L_50)->GetAt(static_cast<il2cpp_array_size_t>(L_52));
		V_4 = ((int32_t)(L_49>>((int32_t)(L_53&((int32_t)31)))));
		int32_t L_54 = V_5;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_55 = V_1;
		int32_t L_56 = V_15;
		NullCheck(L_55);
		int32_t L_57 = ((int32_t)il2cpp_codegen_add(L_56, 1));
		int32_t L_58 = (L_55)->GetAt(static_cast<il2cpp_array_size_t>(L_57));
		V_5 = ((int32_t)il2cpp_codegen_subtract(L_54, L_58));
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_59 = ___6_s;
		NullCheck(L_59);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_60 = L_59->___window;
		int32_t L_61 = V_8;
		int32_t L_62 = L_61;
		V_8 = ((int32_t)il2cpp_codegen_add(L_62, 1));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_63 = V_1;
		int32_t L_64 = V_15;
		NullCheck(L_63);
		int32_t L_65 = ((int32_t)il2cpp_codegen_add(L_64, 2));
		int32_t L_66 = (L_63)->GetAt(static_cast<il2cpp_array_size_t>(L_65));
		NullCheck(L_60);
		(L_60)->SetAt(static_cast<il2cpp_array_size_t>(L_62), (uint8_t)((int32_t)(uint8_t)L_66));
		int32_t L_67 = V_9;
		V_9 = ((int32_t)il2cpp_codegen_subtract(L_67, 1));
		goto IL_062b;
	}

IL_00fb:
	{
		int32_t L_68 = V_4;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_69 = V_1;
		int32_t L_70 = V_15;
		NullCheck(L_69);
		int32_t L_71 = ((int32_t)il2cpp_codegen_add(L_70, 1));
		int32_t L_72 = (L_69)->GetAt(static_cast<il2cpp_array_size_t>(L_71));
		V_4 = ((int32_t)(L_68>>((int32_t)(L_72&((int32_t)31)))));
		int32_t L_73 = V_5;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_74 = V_1;
		int32_t L_75 = V_15;
		NullCheck(L_74);
		int32_t L_76 = ((int32_t)il2cpp_codegen_add(L_75, 1));
		int32_t L_77 = (L_74)->GetAt(static_cast<il2cpp_array_size_t>(L_76));
		V_5 = ((int32_t)il2cpp_codegen_subtract(L_73, L_77));
		int32_t L_78 = V_3;
		if (!((int32_t)(L_78&((int32_t)16))))
		{
			goto IL_049a;
		}
	}
	{
		int32_t L_79 = V_3;
		V_3 = ((int32_t)(L_79&((int32_t)15)));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_80 = V_1;
		int32_t L_81 = V_15;
		NullCheck(L_80);
		int32_t L_82 = ((int32_t)il2cpp_codegen_add(L_81, 2));
		int32_t L_83 = (L_80)->GetAt(static_cast<il2cpp_array_size_t>(L_82));
		int32_t L_84 = V_4;
		il2cpp_codegen_runtime_class_init_inline(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_85 = ((InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_StaticFields*)il2cpp_codegen_static_fields_for(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var))->___inflate_mask;
		int32_t L_86 = V_3;
		NullCheck(L_85);
		int32_t L_87 = L_86;
		int32_t L_88 = (L_85)->GetAt(static_cast<il2cpp_array_size_t>(L_87));
		V_12 = ((int32_t)il2cpp_codegen_add(L_83, ((int32_t)(L_84&L_88))));
		int32_t L_89 = V_4;
		int32_t L_90 = V_3;
		V_4 = ((int32_t)(L_89>>((int32_t)(L_90&((int32_t)31)))));
		int32_t L_91 = V_5;
		int32_t L_92 = V_3;
		V_5 = ((int32_t)il2cpp_codegen_subtract(L_91, L_92));
		goto IL_0175;
	}

IL_0149:
	{
		int32_t L_93 = V_7;
		V_7 = ((int32_t)il2cpp_codegen_subtract(L_93, 1));
		int32_t L_94 = V_4;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_95 = ___7_z;
		NullCheck(L_95);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_96 = L_95->___next_in;
		int32_t L_97 = V_6;
		int32_t L_98 = L_97;
		V_6 = ((int32_t)il2cpp_codegen_add(L_98, 1));
		NullCheck(L_96);
		int32_t L_99 = L_98;
		uint8_t L_100 = (L_96)->GetAt(static_cast<il2cpp_array_size_t>(L_99));
		int32_t L_101 = V_5;
		V_4 = ((int32_t)(L_94|((int32_t)(((int32_t)((int32_t)L_100&((int32_t)255)))<<((int32_t)(L_101&((int32_t)31)))))));
		int32_t L_102 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_102, 8));
	}

IL_0175:
	{
		int32_t L_103 = V_5;
		if ((((int32_t)L_103) < ((int32_t)((int32_t)15))))
		{
			goto IL_0149;
		}
	}
	{
		int32_t L_104 = V_4;
		int32_t L_105 = V_11;
		V_0 = ((int32_t)(L_104&L_105));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_106 = ___4_td;
		V_1 = L_106;
		int32_t L_107 = ___5_td_index;
		V_2 = L_107;
		int32_t L_108 = V_2;
		int32_t L_109 = V_0;
		V_15 = ((int32_t)il2cpp_codegen_multiply(((int32_t)il2cpp_codegen_add(L_108, L_109)), 3));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_110 = V_1;
		int32_t L_111 = V_15;
		NullCheck(L_110);
		int32_t L_112 = L_111;
		int32_t L_113 = (L_110)->GetAt(static_cast<il2cpp_array_size_t>(L_112));
		V_3 = L_113;
	}

IL_0196:
	{
		int32_t L_114 = V_4;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_115 = V_1;
		int32_t L_116 = V_15;
		NullCheck(L_115);
		int32_t L_117 = ((int32_t)il2cpp_codegen_add(L_116, 1));
		int32_t L_118 = (L_115)->GetAt(static_cast<il2cpp_array_size_t>(L_117));
		V_4 = ((int32_t)(L_114>>((int32_t)(L_118&((int32_t)31)))));
		int32_t L_119 = V_5;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_120 = V_1;
		int32_t L_121 = V_15;
		NullCheck(L_120);
		int32_t L_122 = ((int32_t)il2cpp_codegen_add(L_121, 1));
		int32_t L_123 = (L_120)->GetAt(static_cast<il2cpp_array_size_t>(L_122));
		V_5 = ((int32_t)il2cpp_codegen_subtract(L_119, L_123));
		int32_t L_124 = V_3;
		if (!((int32_t)(L_124&((int32_t)16))))
		{
			goto IL_03d0;
		}
	}
	{
		int32_t L_125 = V_3;
		V_3 = ((int32_t)(L_125&((int32_t)15)));
		goto IL_01ee;
	}

IL_01c2:
	{
		int32_t L_126 = V_7;
		V_7 = ((int32_t)il2cpp_codegen_subtract(L_126, 1));
		int32_t L_127 = V_4;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_128 = ___7_z;
		NullCheck(L_128);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_129 = L_128->___next_in;
		int32_t L_130 = V_6;
		int32_t L_131 = L_130;
		V_6 = ((int32_t)il2cpp_codegen_add(L_131, 1));
		NullCheck(L_129);
		int32_t L_132 = L_131;
		uint8_t L_133 = (L_129)->GetAt(static_cast<il2cpp_array_size_t>(L_132));
		int32_t L_134 = V_5;
		V_4 = ((int32_t)(L_127|((int32_t)(((int32_t)((int32_t)L_133&((int32_t)255)))<<((int32_t)(L_134&((int32_t)31)))))));
		int32_t L_135 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_135, 8));
	}

IL_01ee:
	{
		int32_t L_136 = V_5;
		int32_t L_137 = V_3;
		if ((((int32_t)L_136) < ((int32_t)L_137)))
		{
			goto IL_01c2;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_138 = V_1;
		int32_t L_139 = V_15;
		NullCheck(L_138);
		int32_t L_140 = ((int32_t)il2cpp_codegen_add(L_139, 2));
		int32_t L_141 = (L_138)->GetAt(static_cast<il2cpp_array_size_t>(L_140));
		int32_t L_142 = V_4;
		il2cpp_codegen_runtime_class_init_inline(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_143 = ((InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_StaticFields*)il2cpp_codegen_static_fields_for(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var))->___inflate_mask;
		int32_t L_144 = V_3;
		NullCheck(L_143);
		int32_t L_145 = L_144;
		int32_t L_146 = (L_143)->GetAt(static_cast<il2cpp_array_size_t>(L_145));
		V_13 = ((int32_t)il2cpp_codegen_add(L_141, ((int32_t)(L_142&L_146))));
		int32_t L_147 = V_4;
		int32_t L_148 = V_3;
		V_4 = ((int32_t)(L_147>>((int32_t)(L_148&((int32_t)31)))));
		int32_t L_149 = V_5;
		int32_t L_150 = V_3;
		V_5 = ((int32_t)il2cpp_codegen_subtract(L_149, L_150));
		int32_t L_151 = V_9;
		int32_t L_152 = V_12;
		V_9 = ((int32_t)il2cpp_codegen_subtract(L_151, L_152));
		int32_t L_153 = V_8;
		int32_t L_154 = V_13;
		if ((((int32_t)L_153) < ((int32_t)L_154)))
		{
			goto IL_02bb;
		}
	}
	{
		int32_t L_155 = V_8;
		int32_t L_156 = V_13;
		V_14 = ((int32_t)il2cpp_codegen_subtract(L_155, L_156));
		int32_t L_157 = V_8;
		int32_t L_158 = V_14;
		if ((((int32_t)((int32_t)il2cpp_codegen_subtract(L_157, L_158))) <= ((int32_t)0)))
		{
			goto IL_028c;
		}
	}
	{
		int32_t L_159 = V_8;
		int32_t L_160 = V_14;
		if ((((int32_t)2) <= ((int32_t)((int32_t)il2cpp_codegen_subtract(L_159, L_160)))))
		{
			goto IL_028c;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_161 = ___6_s;
		NullCheck(L_161);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_162 = L_161->___window;
		int32_t L_163 = V_8;
		int32_t L_164 = L_163;
		V_8 = ((int32_t)il2cpp_codegen_add(L_164, 1));
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_165 = ___6_s;
		NullCheck(L_165);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_166 = L_165->___window;
		int32_t L_167 = V_14;
		int32_t L_168 = L_167;
		V_14 = ((int32_t)il2cpp_codegen_add(L_168, 1));
		NullCheck(L_166);
		int32_t L_169 = L_168;
		uint8_t L_170 = (L_166)->GetAt(static_cast<il2cpp_array_size_t>(L_169));
		NullCheck(L_162);
		(L_162)->SetAt(static_cast<il2cpp_array_size_t>(L_164), (uint8_t)L_170);
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_171 = ___6_s;
		NullCheck(L_171);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_172 = L_171->___window;
		int32_t L_173 = V_8;
		int32_t L_174 = L_173;
		V_8 = ((int32_t)il2cpp_codegen_add(L_174, 1));
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_175 = ___6_s;
		NullCheck(L_175);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_176 = L_175->___window;
		int32_t L_177 = V_14;
		int32_t L_178 = L_177;
		V_14 = ((int32_t)il2cpp_codegen_add(L_178, 1));
		NullCheck(L_176);
		int32_t L_179 = L_178;
		uint8_t L_180 = (L_176)->GetAt(static_cast<il2cpp_array_size_t>(L_179));
		NullCheck(L_172);
		(L_172)->SetAt(static_cast<il2cpp_array_size_t>(L_174), (uint8_t)L_180);
		int32_t L_181 = V_12;
		V_12 = ((int32_t)il2cpp_codegen_subtract(L_181, 2));
		goto IL_02b6;
	}

IL_028c:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_182 = ___6_s;
		NullCheck(L_182);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_183 = L_182->___window;
		int32_t L_184 = V_14;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_185 = ___6_s;
		NullCheck(L_185);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_186 = L_185->___window;
		int32_t L_187 = V_8;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_183, L_184, (RuntimeArray*)L_186, L_187, 2, NULL);
		int32_t L_188 = V_8;
		V_8 = ((int32_t)il2cpp_codegen_add(L_188, 2));
		int32_t L_189 = V_14;
		V_14 = ((int32_t)il2cpp_codegen_add(L_189, 2));
		int32_t L_190 = V_12;
		V_12 = ((int32_t)il2cpp_codegen_subtract(L_190, 2));
	}

IL_02b6:
	{
		goto IL_035b;
	}

IL_02bb:
	{
		int32_t L_191 = V_8;
		int32_t L_192 = V_13;
		V_14 = ((int32_t)il2cpp_codegen_subtract(L_191, L_192));
	}

IL_02c2:
	{
		int32_t L_193 = V_14;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_194 = ___6_s;
		NullCheck(L_194);
		int32_t L_195 = L_194->___end;
		V_14 = ((int32_t)il2cpp_codegen_add(L_193, L_195));
		int32_t L_196 = V_14;
		if ((((int32_t)L_196) < ((int32_t)0)))
		{
			goto IL_02c2;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_197 = ___6_s;
		NullCheck(L_197);
		int32_t L_198 = L_197->___end;
		int32_t L_199 = V_14;
		V_3 = ((int32_t)il2cpp_codegen_subtract(L_198, L_199));
		int32_t L_200 = V_12;
		int32_t L_201 = V_3;
		if ((((int32_t)L_200) <= ((int32_t)L_201)))
		{
			goto IL_035b;
		}
	}
	{
		int32_t L_202 = V_12;
		int32_t L_203 = V_3;
		V_12 = ((int32_t)il2cpp_codegen_subtract(L_202, L_203));
		int32_t L_204 = V_8;
		int32_t L_205 = V_14;
		if ((((int32_t)((int32_t)il2cpp_codegen_subtract(L_204, L_205))) <= ((int32_t)0)))
		{
			goto IL_0332;
		}
	}
	{
		int32_t L_206 = V_3;
		int32_t L_207 = V_8;
		int32_t L_208 = V_14;
		if ((((int32_t)L_206) <= ((int32_t)((int32_t)il2cpp_codegen_subtract(L_207, L_208)))))
		{
			goto IL_0332;
		}
	}

IL_0305:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_209 = ___6_s;
		NullCheck(L_209);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_210 = L_209->___window;
		int32_t L_211 = V_8;
		int32_t L_212 = L_211;
		V_8 = ((int32_t)il2cpp_codegen_add(L_212, 1));
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_213 = ___6_s;
		NullCheck(L_213);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_214 = L_213->___window;
		int32_t L_215 = V_14;
		int32_t L_216 = L_215;
		V_14 = ((int32_t)il2cpp_codegen_add(L_216, 1));
		NullCheck(L_214);
		int32_t L_217 = L_216;
		uint8_t L_218 = (L_214)->GetAt(static_cast<il2cpp_array_size_t>(L_217));
		NullCheck(L_210);
		(L_210)->SetAt(static_cast<il2cpp_array_size_t>(L_212), (uint8_t)L_218);
		int32_t L_219 = V_3;
		int32_t L_220 = ((int32_t)il2cpp_codegen_subtract(L_219, 1));
		V_3 = L_220;
		if (L_220)
		{
			goto IL_0305;
		}
	}
	{
		goto IL_0358;
	}

IL_0332:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_221 = ___6_s;
		NullCheck(L_221);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_222 = L_221->___window;
		int32_t L_223 = V_14;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_224 = ___6_s;
		NullCheck(L_224);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_225 = L_224->___window;
		int32_t L_226 = V_8;
		int32_t L_227 = V_3;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_222, L_223, (RuntimeArray*)L_225, L_226, L_227, NULL);
		int32_t L_228 = V_8;
		int32_t L_229 = V_3;
		V_8 = ((int32_t)il2cpp_codegen_add(L_228, L_229));
		int32_t L_230 = V_14;
		int32_t L_231 = V_3;
		V_14 = ((int32_t)il2cpp_codegen_add(L_230, L_231));
		V_3 = 0;
	}

IL_0358:
	{
		V_14 = 0;
	}

IL_035b:
	{
		int32_t L_232 = V_8;
		int32_t L_233 = V_14;
		if ((((int32_t)((int32_t)il2cpp_codegen_subtract(L_232, L_233))) <= ((int32_t)0)))
		{
			goto IL_03a1;
		}
	}
	{
		int32_t L_234 = V_12;
		int32_t L_235 = V_8;
		int32_t L_236 = V_14;
		if ((((int32_t)L_234) <= ((int32_t)((int32_t)il2cpp_codegen_subtract(L_235, L_236)))))
		{
			goto IL_03a1;
		}
	}

IL_0372:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_237 = ___6_s;
		NullCheck(L_237);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_238 = L_237->___window;
		int32_t L_239 = V_8;
		int32_t L_240 = L_239;
		V_8 = ((int32_t)il2cpp_codegen_add(L_240, 1));
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_241 = ___6_s;
		NullCheck(L_241);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_242 = L_241->___window;
		int32_t L_243 = V_14;
		int32_t L_244 = L_243;
		V_14 = ((int32_t)il2cpp_codegen_add(L_244, 1));
		NullCheck(L_242);
		int32_t L_245 = L_244;
		uint8_t L_246 = (L_242)->GetAt(static_cast<il2cpp_array_size_t>(L_245));
		NullCheck(L_238);
		(L_238)->SetAt(static_cast<il2cpp_array_size_t>(L_240), (uint8_t)L_246);
		int32_t L_247 = V_12;
		int32_t L_248 = ((int32_t)il2cpp_codegen_subtract(L_247, 1));
		V_12 = L_248;
		if (L_248)
		{
			goto IL_0372;
		}
	}
	{
		goto IL_03cb;
	}

IL_03a1:
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_249 = ___6_s;
		NullCheck(L_249);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_250 = L_249->___window;
		int32_t L_251 = V_14;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_252 = ___6_s;
		NullCheck(L_252);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_253 = L_252->___window;
		int32_t L_254 = V_8;
		int32_t L_255 = V_12;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_250, L_251, (RuntimeArray*)L_253, L_254, L_255, NULL);
		int32_t L_256 = V_8;
		int32_t L_257 = V_12;
		V_8 = ((int32_t)il2cpp_codegen_add(L_256, L_257));
		int32_t L_258 = V_14;
		int32_t L_259 = V_12;
		V_14 = ((int32_t)il2cpp_codegen_add(L_258, L_259));
		V_12 = 0;
	}

IL_03cb:
	{
		goto IL_0495;
	}

IL_03d0:
	{
		int32_t L_260 = V_3;
		if (((int32_t)(L_260&((int32_t)64))))
		{
			goto IL_0400;
		}
	}
	{
		int32_t L_261 = V_0;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_262 = V_1;
		int32_t L_263 = V_15;
		NullCheck(L_262);
		int32_t L_264 = ((int32_t)il2cpp_codegen_add(L_263, 2));
		int32_t L_265 = (L_262)->GetAt(static_cast<il2cpp_array_size_t>(L_264));
		V_0 = ((int32_t)il2cpp_codegen_add(L_261, L_265));
		int32_t L_266 = V_0;
		int32_t L_267 = V_4;
		il2cpp_codegen_runtime_class_init_inline(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_268 = ((InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_StaticFields*)il2cpp_codegen_static_fields_for(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var))->___inflate_mask;
		int32_t L_269 = V_3;
		NullCheck(L_268);
		int32_t L_270 = L_269;
		int32_t L_271 = (L_268)->GetAt(static_cast<il2cpp_array_size_t>(L_270));
		V_0 = ((int32_t)il2cpp_codegen_add(L_266, ((int32_t)(L_267&L_271))));
		int32_t L_272 = V_2;
		int32_t L_273 = V_0;
		V_15 = ((int32_t)il2cpp_codegen_multiply(((int32_t)il2cpp_codegen_add(L_272, L_273)), 3));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_274 = V_1;
		int32_t L_275 = V_15;
		NullCheck(L_274);
		int32_t L_276 = L_275;
		int32_t L_277 = (L_274)->GetAt(static_cast<il2cpp_array_size_t>(L_276));
		V_3 = L_277;
		goto IL_0490;
	}

IL_0400:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_278 = ___7_z;
		NullCheck(L_278);
		L_278->___msg = _stringLiteralBDD794DC7884A15D601FC8AD88E8B6637CF36948;
		Il2CppCodeGenWriteBarrier((void**)(&L_278->___msg), (void*)_stringLiteralBDD794DC7884A15D601FC8AD88E8B6637CF36948);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_279 = ___7_z;
		NullCheck(L_279);
		int32_t L_280 = L_279->___avail_in;
		int32_t L_281 = V_7;
		V_12 = ((int32_t)il2cpp_codegen_subtract(L_280, L_281));
		int32_t L_282 = V_5;
		int32_t L_283 = V_12;
		if ((((int32_t)((int32_t)(L_282>>3))) >= ((int32_t)L_283)))
		{
			goto IL_042c;
		}
	}
	{
		int32_t L_284 = V_5;
		G_B44_0 = ((int32_t)(L_284>>3));
		goto IL_042e;
	}

IL_042c:
	{
		int32_t L_285 = V_12;
		G_B44_0 = L_285;
	}

IL_042e:
	{
		V_12 = G_B44_0;
		int32_t L_286 = V_7;
		int32_t L_287 = V_12;
		V_7 = ((int32_t)il2cpp_codegen_add(L_286, L_287));
		int32_t L_288 = V_6;
		int32_t L_289 = V_12;
		V_6 = ((int32_t)il2cpp_codegen_subtract(L_288, L_289));
		int32_t L_290 = V_5;
		int32_t L_291 = V_12;
		V_5 = ((int32_t)il2cpp_codegen_subtract(L_290, ((int32_t)(L_291<<3))));
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_292 = ___6_s;
		int32_t L_293 = V_4;
		NullCheck(L_292);
		L_292->___bitb = L_293;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_294 = ___6_s;
		int32_t L_295 = V_5;
		NullCheck(L_294);
		L_294->___bitk = L_295;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_296 = ___7_z;
		int32_t L_297 = V_7;
		NullCheck(L_296);
		L_296->___avail_in = L_297;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_298 = ___7_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_299 = L_298;
		NullCheck(L_299);
		int64_t L_300 = L_299->___total_in;
		int32_t L_301 = V_6;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_302 = ___7_z;
		NullCheck(L_302);
		int32_t L_303 = L_302->___next_in_index;
		NullCheck(L_299);
		L_299->___total_in = ((int64_t)il2cpp_codegen_add(L_300, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_301, L_303)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_304 = ___7_z;
		int32_t L_305 = V_6;
		NullCheck(L_304);
		L_304->___next_in_index = L_305;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_306 = ___6_s;
		int32_t L_307 = V_8;
		NullCheck(L_306);
		L_306->___write = L_307;
		return ((int32_t)-3);
	}

IL_0490:
	{
		goto IL_0196;
	}

IL_0495:
	{
		goto IL_062b;
	}

IL_049a:
	{
		int32_t L_308 = V_3;
		if (((int32_t)(L_308&((int32_t)64))))
		{
			goto IL_050a;
		}
	}
	{
		int32_t L_309 = V_0;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_310 = V_1;
		int32_t L_311 = V_15;
		NullCheck(L_310);
		int32_t L_312 = ((int32_t)il2cpp_codegen_add(L_311, 2));
		int32_t L_313 = (L_310)->GetAt(static_cast<il2cpp_array_size_t>(L_312));
		V_0 = ((int32_t)il2cpp_codegen_add(L_309, L_313));
		int32_t L_314 = V_0;
		int32_t L_315 = V_4;
		il2cpp_codegen_runtime_class_init_inline(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_316 = ((InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_StaticFields*)il2cpp_codegen_static_fields_for(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var))->___inflate_mask;
		int32_t L_317 = V_3;
		NullCheck(L_316);
		int32_t L_318 = L_317;
		int32_t L_319 = (L_316)->GetAt(static_cast<il2cpp_array_size_t>(L_318));
		V_0 = ((int32_t)il2cpp_codegen_add(L_314, ((int32_t)(L_315&L_319))));
		int32_t L_320 = V_2;
		int32_t L_321 = V_0;
		V_15 = ((int32_t)il2cpp_codegen_multiply(((int32_t)il2cpp_codegen_add(L_320, L_321)), 3));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_322 = V_1;
		int32_t L_323 = V_15;
		NullCheck(L_322);
		int32_t L_324 = L_323;
		int32_t L_325 = (L_322)->GetAt(static_cast<il2cpp_array_size_t>(L_324));
		int32_t L_326 = L_325;
		V_3 = L_326;
		if (L_326)
		{
			goto IL_0505;
		}
	}
	{
		int32_t L_327 = V_4;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_328 = V_1;
		int32_t L_329 = V_15;
		NullCheck(L_328);
		int32_t L_330 = ((int32_t)il2cpp_codegen_add(L_329, 1));
		int32_t L_331 = (L_328)->GetAt(static_cast<il2cpp_array_size_t>(L_330));
		V_4 = ((int32_t)(L_327>>((int32_t)(L_331&((int32_t)31)))));
		int32_t L_332 = V_5;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_333 = V_1;
		int32_t L_334 = V_15;
		NullCheck(L_333);
		int32_t L_335 = ((int32_t)il2cpp_codegen_add(L_334, 1));
		int32_t L_336 = (L_333)->GetAt(static_cast<il2cpp_array_size_t>(L_335));
		V_5 = ((int32_t)il2cpp_codegen_subtract(L_332, L_336));
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_337 = ___6_s;
		NullCheck(L_337);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_338 = L_337->___window;
		int32_t L_339 = V_8;
		int32_t L_340 = L_339;
		V_8 = ((int32_t)il2cpp_codegen_add(L_340, 1));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_341 = V_1;
		int32_t L_342 = V_15;
		NullCheck(L_341);
		int32_t L_343 = ((int32_t)il2cpp_codegen_add(L_342, 2));
		int32_t L_344 = (L_341)->GetAt(static_cast<il2cpp_array_size_t>(L_343));
		NullCheck(L_338);
		(L_338)->SetAt(static_cast<il2cpp_array_size_t>(L_340), (uint8_t)((int32_t)(uint8_t)L_344));
		int32_t L_345 = V_9;
		V_9 = ((int32_t)il2cpp_codegen_subtract(L_345, 1));
		goto IL_062b;
	}

IL_0505:
	{
		goto IL_0626;
	}

IL_050a:
	{
		int32_t L_346 = V_3;
		if (!((int32_t)(L_346&((int32_t)32))))
		{
			goto IL_0596;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_347 = ___7_z;
		NullCheck(L_347);
		int32_t L_348 = L_347->___avail_in;
		int32_t L_349 = V_7;
		V_12 = ((int32_t)il2cpp_codegen_subtract(L_348, L_349));
		int32_t L_350 = V_5;
		int32_t L_351 = V_12;
		if ((((int32_t)((int32_t)(L_350>>3))) >= ((int32_t)L_351)))
		{
			goto IL_0533;
		}
	}
	{
		int32_t L_352 = V_5;
		G_B55_0 = ((int32_t)(L_352>>3));
		goto IL_0535;
	}

IL_0533:
	{
		int32_t L_353 = V_12;
		G_B55_0 = L_353;
	}

IL_0535:
	{
		V_12 = G_B55_0;
		int32_t L_354 = V_7;
		int32_t L_355 = V_12;
		V_7 = ((int32_t)il2cpp_codegen_add(L_354, L_355));
		int32_t L_356 = V_6;
		int32_t L_357 = V_12;
		V_6 = ((int32_t)il2cpp_codegen_subtract(L_356, L_357));
		int32_t L_358 = V_5;
		int32_t L_359 = V_12;
		V_5 = ((int32_t)il2cpp_codegen_subtract(L_358, ((int32_t)(L_359<<3))));
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_360 = ___6_s;
		int32_t L_361 = V_4;
		NullCheck(L_360);
		L_360->___bitb = L_361;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_362 = ___6_s;
		int32_t L_363 = V_5;
		NullCheck(L_362);
		L_362->___bitk = L_363;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_364 = ___7_z;
		int32_t L_365 = V_7;
		NullCheck(L_364);
		L_364->___avail_in = L_365;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_366 = ___7_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_367 = L_366;
		NullCheck(L_367);
		int64_t L_368 = L_367->___total_in;
		int32_t L_369 = V_6;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_370 = ___7_z;
		NullCheck(L_370);
		int32_t L_371 = L_370->___next_in_index;
		NullCheck(L_367);
		L_367->___total_in = ((int64_t)il2cpp_codegen_add(L_368, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_369, L_371)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_372 = ___7_z;
		int32_t L_373 = V_6;
		NullCheck(L_372);
		L_372->___next_in_index = L_373;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_374 = ___6_s;
		int32_t L_375 = V_8;
		NullCheck(L_374);
		L_374->___write = L_375;
		return 1;
	}

IL_0596:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_376 = ___7_z;
		NullCheck(L_376);
		L_376->___msg = _stringLiteral96025B6397AAC8D06A75085B92AD0F0146044D16;
		Il2CppCodeGenWriteBarrier((void**)(&L_376->___msg), (void*)_stringLiteral96025B6397AAC8D06A75085B92AD0F0146044D16);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_377 = ___7_z;
		NullCheck(L_377);
		int32_t L_378 = L_377->___avail_in;
		int32_t L_379 = V_7;
		V_12 = ((int32_t)il2cpp_codegen_subtract(L_378, L_379));
		int32_t L_380 = V_5;
		int32_t L_381 = V_12;
		if ((((int32_t)((int32_t)(L_380>>3))) >= ((int32_t)L_381)))
		{
			goto IL_05c2;
		}
	}
	{
		int32_t L_382 = V_5;
		G_B59_0 = ((int32_t)(L_382>>3));
		goto IL_05c4;
	}

IL_05c2:
	{
		int32_t L_383 = V_12;
		G_B59_0 = L_383;
	}

IL_05c4:
	{
		V_12 = G_B59_0;
		int32_t L_384 = V_7;
		int32_t L_385 = V_12;
		V_7 = ((int32_t)il2cpp_codegen_add(L_384, L_385));
		int32_t L_386 = V_6;
		int32_t L_387 = V_12;
		V_6 = ((int32_t)il2cpp_codegen_subtract(L_386, L_387));
		int32_t L_388 = V_5;
		int32_t L_389 = V_12;
		V_5 = ((int32_t)il2cpp_codegen_subtract(L_388, ((int32_t)(L_389<<3))));
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_390 = ___6_s;
		int32_t L_391 = V_4;
		NullCheck(L_390);
		L_390->___bitb = L_391;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_392 = ___6_s;
		int32_t L_393 = V_5;
		NullCheck(L_392);
		L_392->___bitk = L_393;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_394 = ___7_z;
		int32_t L_395 = V_7;
		NullCheck(L_394);
		L_394->___avail_in = L_395;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_396 = ___7_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_397 = L_396;
		NullCheck(L_397);
		int64_t L_398 = L_397->___total_in;
		int32_t L_399 = V_6;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_400 = ___7_z;
		NullCheck(L_400);
		int32_t L_401 = L_400->___next_in_index;
		NullCheck(L_397);
		L_397->___total_in = ((int64_t)il2cpp_codegen_add(L_398, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_399, L_401)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_402 = ___7_z;
		int32_t L_403 = V_6;
		NullCheck(L_402);
		L_402->___next_in_index = L_403;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_404 = ___6_s;
		int32_t L_405 = V_8;
		NullCheck(L_404);
		L_404->___write = L_405;
		return ((int32_t)-3);
	}

IL_0626:
	{
		goto IL_00fb;
	}

IL_062b:
	{
		int32_t L_406 = V_9;
		if ((((int32_t)L_406) < ((int32_t)((int32_t)258))))
		{
			goto IL_0640;
		}
	}
	{
		int32_t L_407 = V_7;
		if ((((int32_t)L_407) >= ((int32_t)((int32_t)10))))
		{
			goto IL_006a;
		}
	}

IL_0640:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_408 = ___7_z;
		NullCheck(L_408);
		int32_t L_409 = L_408->___avail_in;
		int32_t L_410 = V_7;
		V_12 = ((int32_t)il2cpp_codegen_subtract(L_409, L_410));
		int32_t L_411 = V_5;
		int32_t L_412 = V_12;
		if ((((int32_t)((int32_t)(L_411>>3))) >= ((int32_t)L_412)))
		{
			goto IL_0660;
		}
	}
	{
		int32_t L_413 = V_5;
		G_B66_0 = ((int32_t)(L_413>>3));
		goto IL_0662;
	}

IL_0660:
	{
		int32_t L_414 = V_12;
		G_B66_0 = L_414;
	}

IL_0662:
	{
		V_12 = G_B66_0;
		int32_t L_415 = V_7;
		int32_t L_416 = V_12;
		V_7 = ((int32_t)il2cpp_codegen_add(L_415, L_416));
		int32_t L_417 = V_6;
		int32_t L_418 = V_12;
		V_6 = ((int32_t)il2cpp_codegen_subtract(L_417, L_418));
		int32_t L_419 = V_5;
		int32_t L_420 = V_12;
		V_5 = ((int32_t)il2cpp_codegen_subtract(L_419, ((int32_t)(L_420<<3))));
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_421 = ___6_s;
		int32_t L_422 = V_4;
		NullCheck(L_421);
		L_421->___bitb = L_422;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_423 = ___6_s;
		int32_t L_424 = V_5;
		NullCheck(L_423);
		L_423->___bitk = L_424;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_425 = ___7_z;
		int32_t L_426 = V_7;
		NullCheck(L_425);
		L_425->___avail_in = L_426;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_427 = ___7_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_428 = L_427;
		NullCheck(L_428);
		int64_t L_429 = L_428->___total_in;
		int32_t L_430 = V_6;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_431 = ___7_z;
		NullCheck(L_431);
		int32_t L_432 = L_431->___next_in_index;
		NullCheck(L_428);
		L_428->___total_in = ((int64_t)il2cpp_codegen_add(L_429, ((int64_t)((int32_t)il2cpp_codegen_subtract(L_430, L_432)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_433 = ___7_z;
		int32_t L_434 = V_6;
		NullCheck(L_433);
		L_433->___next_in_index = L_434;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_435 = ___6_s;
		int32_t L_436 = V_8;
		NullCheck(L_435);
		L_435->___write = L_436;
		return 0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfCodes__cctor_m4D46AFE1F2DB6C2EF5BF855446FFBE9112C64BA5 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE2_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)17));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE2_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		((InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_StaticFields*)il2cpp_codegen_static_fields_for(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var))->___inflate_mask = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_StaticFields*)il2cpp_codegen_static_fields_for(InfCodes_t399051CEF6AACF300A00BEB119FA34376CDFC4F0_il2cpp_TypeInfo_var))->___inflate_mask), (void*)L_1);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Inflate__ctor_mC3FDB4D5246A093EE60FEF5698867D51DFB27104 (Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D* L_0 = (Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D*)(Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D*)SZArrayNew(Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D_il2cpp_TypeInfo_var, (uint32_t)1);
		__this->___was = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___was), (void*)L_0);
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Inflate_inflateReset_m45912560840BFE24481A507D494957B89BBEF964 (Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, const RuntimeMethod* method) 
{
	int64_t V_0 = 0;
	Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* G_B5_0 = NULL;
	Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* G_B4_0 = NULL;
	int32_t G_B6_0 = 0;
	Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* G_B6_1 = NULL;
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_0 = ___0_z;
		if (!L_0)
		{
			goto IL_0011;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_1 = ___0_z;
		NullCheck(L_1);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_2 = L_1->___istate;
		if (L_2)
		{
			goto IL_0014;
		}
	}

IL_0011:
	{
		return ((int32_t)-2);
	}

IL_0014:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_3 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_4 = ___0_z;
		int64_t L_5 = ((int64_t)0);
		V_0 = L_5;
		NullCheck(L_4);
		L_4->___total_out = L_5;
		int64_t L_6 = V_0;
		NullCheck(L_3);
		L_3->___total_in = L_6;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_7 = ___0_z;
		NullCheck(L_7);
		L_7->___msg = (String_t*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&L_7->___msg), (void*)(String_t*)NULL);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_8 = ___0_z;
		NullCheck(L_8);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_9 = L_8->___istate;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_10 = ___0_z;
		NullCheck(L_10);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_11 = L_10->___istate;
		NullCheck(L_11);
		int32_t L_12 = L_11->___nowrap;
		if (!L_12)
		{
			G_B5_0 = L_9;
			goto IL_0048;
		}
		G_B4_0 = L_9;
	}
	{
		G_B6_0 = 7;
		G_B6_1 = G_B4_0;
		goto IL_0049;
	}

IL_0048:
	{
		G_B6_0 = 0;
		G_B6_1 = G_B5_0;
	}

IL_0049:
	{
		NullCheck(G_B6_1);
		G_B6_1->___mode = G_B6_0;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_13 = ___0_z;
		NullCheck(L_13);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_14 = L_13->___istate;
		NullCheck(L_14);
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_15 = L_14->___blocks;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_16 = ___0_z;
		NullCheck(L_15);
		InfBlocks_reset_m287421B0D47CBB5CAB51FA76A930806BAFF87BC8(L_15, L_16, (Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D*)NULL, NULL);
		return 0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Inflate_inflateEnd_mF5A412434A2CE53A5D62205EFAF86FD8485CA85E (Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, const RuntimeMethod* method) 
{
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_0 = __this->___blocks;
		if (!L_0)
		{
			goto IL_0017;
		}
	}
	{
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_1 = __this->___blocks;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_2 = ___0_z;
		NullCheck(L_1);
		InfBlocks_free_m9E8C460B7D27F7D5C5C72699646494819E754D78(L_1, L_2, NULL);
	}

IL_0017:
	{
		__this->___blocks = (InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___blocks), (void*)(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF*)NULL);
		return 0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Inflate_inflateInit_mE54CCCBA0F6A9571D5E2AA707A3240C286815C86 (Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, int32_t ___1_w, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* G_B7_0 = NULL;
	Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* G_B7_1 = NULL;
	ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* G_B6_0 = NULL;
	Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* G_B6_1 = NULL;
	Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* G_B8_0 = NULL;
	ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* G_B8_1 = NULL;
	Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* G_B8_2 = NULL;
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_0 = ___0_z;
		NullCheck(L_0);
		L_0->___msg = (String_t*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&L_0->___msg), (void*)(String_t*)NULL);
		__this->___blocks = (InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___blocks), (void*)(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF*)NULL);
		__this->___nowrap = 0;
		int32_t L_1 = ___1_w;
		if ((((int32_t)L_1) >= ((int32_t)0)))
		{
			goto IL_0027;
		}
	}
	{
		int32_t L_2 = ___1_w;
		___1_w = ((-L_2));
		__this->___nowrap = 1;
	}

IL_0027:
	{
		int32_t L_3 = ___1_w;
		if ((((int32_t)L_3) < ((int32_t)8)))
		{
			goto IL_0036;
		}
	}
	{
		int32_t L_4 = ___1_w;
		if ((((int32_t)L_4) <= ((int32_t)((int32_t)15))))
		{
			goto IL_0041;
		}
	}

IL_0036:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_5 = ___0_z;
		int32_t L_6;
		L_6 = Inflate_inflateEnd_mF5A412434A2CE53A5D62205EFAF86FD8485CA85E(__this, L_5, NULL);
		return ((int32_t)-2);
	}

IL_0041:
	{
		int32_t L_7 = ___1_w;
		__this->___wbits = L_7;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_8 = ___0_z;
		NullCheck(L_8);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_9 = L_8->___istate;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_10 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_11 = ___0_z;
		NullCheck(L_11);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_12 = L_11->___istate;
		NullCheck(L_12);
		int32_t L_13 = L_12->___nowrap;
		if (!L_13)
		{
			G_B7_0 = L_10;
			G_B7_1 = L_9;
			goto IL_0065;
		}
		G_B6_0 = L_10;
		G_B6_1 = L_9;
	}
	{
		G_B8_0 = ((Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50*)(NULL));
		G_B8_1 = G_B6_0;
		G_B8_2 = G_B6_1;
		goto IL_0066;
	}

IL_0065:
	{
		G_B8_0 = __this;
		G_B8_1 = G_B7_0;
		G_B8_2 = G_B7_1;
	}

IL_0066:
	{
		int32_t L_14 = ___1_w;
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_15 = (InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF*)il2cpp_codegen_object_new(InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF_il2cpp_TypeInfo_var);
		InfBlocks__ctor_mBBFA4CFB361E5D892398BBBB40440E973CA65093(L_15, G_B8_1, G_B8_0, ((int32_t)(1<<((int32_t)(L_14&((int32_t)31))))), NULL);
		NullCheck(G_B8_2);
		G_B8_2->___blocks = L_15;
		Il2CppCodeGenWriteBarrier((void**)(&G_B8_2->___blocks), (void*)L_15);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_16 = ___0_z;
		int32_t L_17;
		L_17 = Inflate_inflateReset_m45912560840BFE24481A507D494957B89BBEF964(__this, L_16, NULL);
		return 0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Inflate_inflate_m51AE19089B0B082180C4E6C97A88561386E3821E (Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* __this, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___0_z, int32_t ___1_f, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral06F3358B23D666113A1020E1C9CFEBE16373BE40);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6304F4645B5484ACF5D9DF2D847AE616393DC417);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA840F25536BE8295D00B8780BF11900F5EE6774E);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD3DEC6A6A3177F7D2965AAB68291E77977CF1E3E);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA84EF263557F5F56FABA93B2A6EC89E8F3E0102);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t G_B7_0 = 0;
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_0 = ___0_z;
		if (!L_0)
		{
			goto IL_001c;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_1 = ___0_z;
		NullCheck(L_1);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_2 = L_1->___istate;
		if (!L_2)
		{
			goto IL_001c;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_3 = ___0_z;
		NullCheck(L_3);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = L_3->___next_in;
		if (L_4)
		{
			goto IL_001f;
		}
	}

IL_001c:
	{
		return ((int32_t)-2);
	}

IL_001f:
	{
		int32_t L_5 = ___1_f;
		if ((!(((uint32_t)L_5) == ((uint32_t)4))))
		{
			goto IL_002d;
		}
	}
	{
		G_B7_0 = ((int32_t)-5);
		goto IL_002e;
	}

IL_002d:
	{
		G_B7_0 = 0;
	}

IL_002e:
	{
		___1_f = G_B7_0;
		V_0 = ((int32_t)-5);
	}

IL_0033:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_6 = ___0_z;
		NullCheck(L_6);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_7 = L_6->___istate;
		NullCheck(L_7);
		int32_t L_8 = L_7->___mode;
		V_2 = L_8;
		int32_t L_9 = V_2;
		switch (L_9)
		{
			case 0:
			{
				goto IL_0082;
			}
			case 1:
			{
				goto IL_015f;
			}
			case 2:
			{
				goto IL_0215;
			}
			case 3:
			{
				goto IL_0286;
			}
			case 4:
			{
				goto IL_02fe;
			}
			case 5:
			{
				goto IL_0375;
			}
			case 6:
			{
				goto IL_03f2;
			}
			case 7:
			{
				goto IL_0419;
			}
			case 8:
			{
				goto IL_04b4;
			}
			case 9:
			{
				goto IL_0526;
			}
			case 10:
			{
				goto IL_059f;
			}
			case 11:
			{
				goto IL_0617;
			}
			case 12:
			{
				goto IL_06cf;
			}
			case 13:
			{
				goto IL_06d1;
			}
		}
	}
	{
		goto IL_06d4;
	}

IL_0082:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_10 = ___0_z;
		NullCheck(L_10);
		int32_t L_11 = L_10->___avail_in;
		if (L_11)
		{
			goto IL_008f;
		}
	}
	{
		int32_t L_12 = V_0;
		return L_12;
	}

IL_008f:
	{
		int32_t L_13 = ___1_f;
		V_0 = L_13;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_14 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_15 = L_14;
		NullCheck(L_15);
		int32_t L_16 = L_15->___avail_in;
		NullCheck(L_15);
		L_15->___avail_in = ((int32_t)il2cpp_codegen_subtract(L_16, 1));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_17 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_18 = L_17;
		NullCheck(L_18);
		int64_t L_19 = L_18->___total_in;
		NullCheck(L_18);
		L_18->___total_in = ((int64_t)il2cpp_codegen_add(L_19, ((int64_t)1)));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_20 = ___0_z;
		NullCheck(L_20);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_21 = L_20->___istate;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_22 = ___0_z;
		NullCheck(L_22);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_23 = L_22->___next_in;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_24 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_25 = L_24;
		NullCheck(L_25);
		int32_t L_26 = L_25->___next_in_index;
		int32_t L_27 = L_26;
		V_3 = L_27;
		NullCheck(L_25);
		L_25->___next_in_index = ((int32_t)il2cpp_codegen_add(L_27, 1));
		int32_t L_28 = V_3;
		NullCheck(L_23);
		int32_t L_29 = L_28;
		uint8_t L_30 = (L_23)->GetAt(static_cast<il2cpp_array_size_t>(L_29));
		uint8_t L_31 = L_30;
		V_3 = L_31;
		NullCheck(L_21);
		L_21->___method = L_31;
		int32_t L_32 = V_3;
		if ((((int32_t)((int32_t)(L_32&((int32_t)15)))) == ((int32_t)8)))
		{
			goto IL_0106;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_33 = ___0_z;
		NullCheck(L_33);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_34 = L_33->___istate;
		NullCheck(L_34);
		L_34->___mode = ((int32_t)13);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_35 = ___0_z;
		NullCheck(L_35);
		L_35->___msg = _stringLiteralA840F25536BE8295D00B8780BF11900F5EE6774E;
		Il2CppCodeGenWriteBarrier((void**)(&L_35->___msg), (void*)_stringLiteralA840F25536BE8295D00B8780BF11900F5EE6774E);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_36 = ___0_z;
		NullCheck(L_36);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_37 = L_36->___istate;
		NullCheck(L_37);
		L_37->___marker = 5;
		goto IL_06d7;
	}

IL_0106:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_38 = ___0_z;
		NullCheck(L_38);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_39 = L_38->___istate;
		NullCheck(L_39);
		int32_t L_40 = L_39->___method;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_41 = ___0_z;
		NullCheck(L_41);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_42 = L_41->___istate;
		NullCheck(L_42);
		int32_t L_43 = L_42->___wbits;
		if ((((int32_t)((int32_t)il2cpp_codegen_add(((int32_t)(L_40>>4)), 8))) <= ((int32_t)L_43)))
		{
			goto IL_014e;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_44 = ___0_z;
		NullCheck(L_44);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_45 = L_44->___istate;
		NullCheck(L_45);
		L_45->___mode = ((int32_t)13);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_46 = ___0_z;
		NullCheck(L_46);
		L_46->___msg = _stringLiteralDA84EF263557F5F56FABA93B2A6EC89E8F3E0102;
		Il2CppCodeGenWriteBarrier((void**)(&L_46->___msg), (void*)_stringLiteralDA84EF263557F5F56FABA93B2A6EC89E8F3E0102);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_47 = ___0_z;
		NullCheck(L_47);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_48 = L_47->___istate;
		NullCheck(L_48);
		L_48->___marker = 5;
		goto IL_06d7;
	}

IL_014e:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_49 = ___0_z;
		NullCheck(L_49);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_50 = L_49->___istate;
		NullCheck(L_50);
		L_50->___mode = 1;
		goto IL_015f;
	}

IL_015f:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_51 = ___0_z;
		NullCheck(L_51);
		int32_t L_52 = L_51->___avail_in;
		if (L_52)
		{
			goto IL_016c;
		}
	}
	{
		int32_t L_53 = V_0;
		return L_53;
	}

IL_016c:
	{
		int32_t L_54 = ___1_f;
		V_0 = L_54;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_55 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_56 = L_55;
		NullCheck(L_56);
		int32_t L_57 = L_56->___avail_in;
		NullCheck(L_56);
		L_56->___avail_in = ((int32_t)il2cpp_codegen_subtract(L_57, 1));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_58 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_59 = L_58;
		NullCheck(L_59);
		int64_t L_60 = L_59->___total_in;
		NullCheck(L_59);
		L_59->___total_in = ((int64_t)il2cpp_codegen_add(L_60, ((int64_t)1)));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_61 = ___0_z;
		NullCheck(L_61);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_62 = L_61->___next_in;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_63 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_64 = L_63;
		NullCheck(L_64);
		int32_t L_65 = L_64->___next_in_index;
		int32_t L_66 = L_65;
		V_3 = L_66;
		NullCheck(L_64);
		L_64->___next_in_index = ((int32_t)il2cpp_codegen_add(L_66, 1));
		int32_t L_67 = V_3;
		NullCheck(L_62);
		int32_t L_68 = L_67;
		uint8_t L_69 = (L_62)->GetAt(static_cast<il2cpp_array_size_t>(L_68));
		V_1 = ((int32_t)((int32_t)L_69&((int32_t)255)));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_70 = ___0_z;
		NullCheck(L_70);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_71 = L_70->___istate;
		NullCheck(L_71);
		int32_t L_72 = L_71->___method;
		int32_t L_73 = V_1;
		if (!((int32_t)(((int32_t)il2cpp_codegen_add(((int32_t)(L_72<<8)), L_73))%((int32_t)31))))
		{
			goto IL_01ea;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_74 = ___0_z;
		NullCheck(L_74);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_75 = L_74->___istate;
		NullCheck(L_75);
		L_75->___mode = ((int32_t)13);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_76 = ___0_z;
		NullCheck(L_76);
		L_76->___msg = _stringLiteral6304F4645B5484ACF5D9DF2D847AE616393DC417;
		Il2CppCodeGenWriteBarrier((void**)(&L_76->___msg), (void*)_stringLiteral6304F4645B5484ACF5D9DF2D847AE616393DC417);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_77 = ___0_z;
		NullCheck(L_77);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_78 = L_77->___istate;
		NullCheck(L_78);
		L_78->___marker = 5;
		goto IL_06d7;
	}

IL_01ea:
	{
		int32_t L_79 = V_1;
		if (((int32_t)(L_79&((int32_t)32))))
		{
			goto IL_0204;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_80 = ___0_z;
		NullCheck(L_80);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_81 = L_80->___istate;
		NullCheck(L_81);
		L_81->___mode = 7;
		goto IL_06d7;
	}

IL_0204:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_82 = ___0_z;
		NullCheck(L_82);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_83 = L_82->___istate;
		NullCheck(L_83);
		L_83->___mode = 2;
		goto IL_0215;
	}

IL_0215:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_84 = ___0_z;
		NullCheck(L_84);
		int32_t L_85 = L_84->___avail_in;
		if (L_85)
		{
			goto IL_0222;
		}
	}
	{
		int32_t L_86 = V_0;
		return L_86;
	}

IL_0222:
	{
		int32_t L_87 = ___1_f;
		V_0 = L_87;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_88 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_89 = L_88;
		NullCheck(L_89);
		int32_t L_90 = L_89->___avail_in;
		NullCheck(L_89);
		L_89->___avail_in = ((int32_t)il2cpp_codegen_subtract(L_90, 1));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_91 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_92 = L_91;
		NullCheck(L_92);
		int64_t L_93 = L_92->___total_in;
		NullCheck(L_92);
		L_92->___total_in = ((int64_t)il2cpp_codegen_add(L_93, ((int64_t)1)));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_94 = ___0_z;
		NullCheck(L_94);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_95 = L_94->___istate;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_96 = ___0_z;
		NullCheck(L_96);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_97 = L_96->___next_in;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_98 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_99 = L_98;
		NullCheck(L_99);
		int32_t L_100 = L_99->___next_in_index;
		int32_t L_101 = L_100;
		V_3 = L_101;
		NullCheck(L_99);
		L_99->___next_in_index = ((int32_t)il2cpp_codegen_add(L_101, 1));
		int32_t L_102 = V_3;
		NullCheck(L_97);
		int32_t L_103 = L_102;
		uint8_t L_104 = (L_97)->GetAt(static_cast<il2cpp_array_size_t>(L_103));
		NullCheck(L_95);
		L_95->___need = ((int64_t)(((int64_t)((int32_t)(((int32_t)((int32_t)L_104&((int32_t)255)))<<((int32_t)24))))&((int64_t)(uint64_t)((uint32_t)((int32_t)-16777216)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_105 = ___0_z;
		NullCheck(L_105);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_106 = L_105->___istate;
		NullCheck(L_106);
		L_106->___mode = 3;
		goto IL_0286;
	}

IL_0286:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_107 = ___0_z;
		NullCheck(L_107);
		int32_t L_108 = L_107->___avail_in;
		if (L_108)
		{
			goto IL_0293;
		}
	}
	{
		int32_t L_109 = V_0;
		return L_109;
	}

IL_0293:
	{
		int32_t L_110 = ___1_f;
		V_0 = L_110;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_111 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_112 = L_111;
		NullCheck(L_112);
		int32_t L_113 = L_112->___avail_in;
		NullCheck(L_112);
		L_112->___avail_in = ((int32_t)il2cpp_codegen_subtract(L_113, 1));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_114 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_115 = L_114;
		NullCheck(L_115);
		int64_t L_116 = L_115->___total_in;
		NullCheck(L_115);
		L_115->___total_in = ((int64_t)il2cpp_codegen_add(L_116, ((int64_t)1)));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_117 = ___0_z;
		NullCheck(L_117);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_118 = L_117->___istate;
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_119 = L_118;
		NullCheck(L_119);
		int64_t L_120 = L_119->___need;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_121 = ___0_z;
		NullCheck(L_121);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_122 = L_121->___next_in;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_123 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_124 = L_123;
		NullCheck(L_124);
		int32_t L_125 = L_124->___next_in_index;
		int32_t L_126 = L_125;
		V_3 = L_126;
		NullCheck(L_124);
		L_124->___next_in_index = ((int32_t)il2cpp_codegen_add(L_126, 1));
		int32_t L_127 = V_3;
		NullCheck(L_122);
		int32_t L_128 = L_127;
		uint8_t L_129 = (L_122)->GetAt(static_cast<il2cpp_array_size_t>(L_128));
		NullCheck(L_119);
		L_119->___need = ((int64_t)il2cpp_codegen_add(L_120, ((int64_t)(((int64_t)((int32_t)(((int32_t)((int32_t)L_129&((int32_t)255)))<<((int32_t)16))))&((int64_t)((int32_t)16711680))))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_130 = ___0_z;
		NullCheck(L_130);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_131 = L_130->___istate;
		NullCheck(L_131);
		L_131->___mode = 4;
		goto IL_02fe;
	}

IL_02fe:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_132 = ___0_z;
		NullCheck(L_132);
		int32_t L_133 = L_132->___avail_in;
		if (L_133)
		{
			goto IL_030b;
		}
	}
	{
		int32_t L_134 = V_0;
		return L_134;
	}

IL_030b:
	{
		int32_t L_135 = ___1_f;
		V_0 = L_135;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_136 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_137 = L_136;
		NullCheck(L_137);
		int32_t L_138 = L_137->___avail_in;
		NullCheck(L_137);
		L_137->___avail_in = ((int32_t)il2cpp_codegen_subtract(L_138, 1));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_139 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_140 = L_139;
		NullCheck(L_140);
		int64_t L_141 = L_140->___total_in;
		NullCheck(L_140);
		L_140->___total_in = ((int64_t)il2cpp_codegen_add(L_141, ((int64_t)1)));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_142 = ___0_z;
		NullCheck(L_142);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_143 = L_142->___istate;
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_144 = L_143;
		NullCheck(L_144);
		int64_t L_145 = L_144->___need;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_146 = ___0_z;
		NullCheck(L_146);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_147 = L_146->___next_in;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_148 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_149 = L_148;
		NullCheck(L_149);
		int32_t L_150 = L_149->___next_in_index;
		int32_t L_151 = L_150;
		V_3 = L_151;
		NullCheck(L_149);
		L_149->___next_in_index = ((int32_t)il2cpp_codegen_add(L_151, 1));
		int32_t L_152 = V_3;
		NullCheck(L_147);
		int32_t L_153 = L_152;
		uint8_t L_154 = (L_147)->GetAt(static_cast<il2cpp_array_size_t>(L_153));
		NullCheck(L_144);
		L_144->___need = ((int64_t)il2cpp_codegen_add(L_145, ((int64_t)(((int64_t)((int32_t)(((int32_t)((int32_t)L_154&((int32_t)255)))<<8)))&((int64_t)((int32_t)65280))))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_155 = ___0_z;
		NullCheck(L_155);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_156 = L_155->___istate;
		NullCheck(L_156);
		L_156->___mode = 5;
		goto IL_0375;
	}

IL_0375:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_157 = ___0_z;
		NullCheck(L_157);
		int32_t L_158 = L_157->___avail_in;
		if (L_158)
		{
			goto IL_0382;
		}
	}
	{
		int32_t L_159 = V_0;
		return L_159;
	}

IL_0382:
	{
		int32_t L_160 = ___1_f;
		V_0 = L_160;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_161 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_162 = L_161;
		NullCheck(L_162);
		int32_t L_163 = L_162->___avail_in;
		NullCheck(L_162);
		L_162->___avail_in = ((int32_t)il2cpp_codegen_subtract(L_163, 1));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_164 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_165 = L_164;
		NullCheck(L_165);
		int64_t L_166 = L_165->___total_in;
		NullCheck(L_165);
		L_165->___total_in = ((int64_t)il2cpp_codegen_add(L_166, ((int64_t)1)));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_167 = ___0_z;
		NullCheck(L_167);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_168 = L_167->___istate;
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_169 = L_168;
		NullCheck(L_169);
		int64_t L_170 = L_169->___need;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_171 = ___0_z;
		NullCheck(L_171);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_172 = L_171->___next_in;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_173 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_174 = L_173;
		NullCheck(L_174);
		int32_t L_175 = L_174->___next_in_index;
		int32_t L_176 = L_175;
		V_3 = L_176;
		NullCheck(L_174);
		L_174->___next_in_index = ((int32_t)il2cpp_codegen_add(L_176, 1));
		int32_t L_177 = V_3;
		NullCheck(L_172);
		int32_t L_178 = L_177;
		uint8_t L_179 = (L_172)->GetAt(static_cast<il2cpp_array_size_t>(L_178));
		NullCheck(L_169);
		L_169->___need = ((int64_t)il2cpp_codegen_add(L_170, ((int64_t)(((int64_t)L_179)&((int64_t)((int32_t)255))))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_180 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_181 = ___0_z;
		NullCheck(L_181);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_182 = L_181->___istate;
		NullCheck(L_182);
		int64_t L_183 = L_182->___need;
		NullCheck(L_180);
		L_180->___adler = L_183;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_184 = ___0_z;
		NullCheck(L_184);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_185 = L_184->___istate;
		NullCheck(L_185);
		L_185->___mode = 6;
		return 2;
	}

IL_03f2:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_186 = ___0_z;
		NullCheck(L_186);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_187 = L_186->___istate;
		NullCheck(L_187);
		L_187->___mode = ((int32_t)13);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_188 = ___0_z;
		NullCheck(L_188);
		L_188->___msg = _stringLiteralD3DEC6A6A3177F7D2965AAB68291E77977CF1E3E;
		Il2CppCodeGenWriteBarrier((void**)(&L_188->___msg), (void*)_stringLiteralD3DEC6A6A3177F7D2965AAB68291E77977CF1E3E);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_189 = ___0_z;
		NullCheck(L_189);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_190 = L_189->___istate;
		NullCheck(L_190);
		L_190->___marker = 0;
		return ((int32_t)-2);
	}

IL_0419:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_191 = ___0_z;
		NullCheck(L_191);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_192 = L_191->___istate;
		NullCheck(L_192);
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_193 = L_192->___blocks;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_194 = ___0_z;
		int32_t L_195 = V_0;
		NullCheck(L_193);
		int32_t L_196;
		L_196 = InfBlocks_proc_m8FB471432B90D42E736660EE3001F601B41966CA(L_193, L_194, L_195, NULL);
		V_0 = L_196;
		int32_t L_197 = V_0;
		if ((!(((uint32_t)L_197) == ((uint32_t)((int32_t)-3)))))
		{
			goto IL_0452;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_198 = ___0_z;
		NullCheck(L_198);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_199 = L_198->___istate;
		NullCheck(L_199);
		L_199->___mode = ((int32_t)13);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_200 = ___0_z;
		NullCheck(L_200);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_201 = L_200->___istate;
		NullCheck(L_201);
		L_201->___marker = 0;
		goto IL_06d7;
	}

IL_0452:
	{
		int32_t L_202 = V_0;
		if (L_202)
		{
			goto IL_045a;
		}
	}
	{
		int32_t L_203 = ___1_f;
		V_0 = L_203;
	}

IL_045a:
	{
		int32_t L_204 = V_0;
		if ((((int32_t)L_204) == ((int32_t)1)))
		{
			goto IL_0463;
		}
	}
	{
		int32_t L_205 = V_0;
		return L_205;
	}

IL_0463:
	{
		int32_t L_206 = ___1_f;
		V_0 = L_206;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_207 = ___0_z;
		NullCheck(L_207);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_208 = L_207->___istate;
		NullCheck(L_208);
		InfBlocks_t5548E391F2472E264398F5095A35EE13B59CD4EF* L_209 = L_208->___blocks;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_210 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_211 = ___0_z;
		NullCheck(L_211);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_212 = L_211->___istate;
		NullCheck(L_212);
		Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D* L_213 = L_212->___was;
		NullCheck(L_209);
		InfBlocks_reset_m287421B0D47CBB5CAB51FA76A930806BAFF87BC8(L_209, L_210, L_213, NULL);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_214 = ___0_z;
		NullCheck(L_214);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_215 = L_214->___istate;
		NullCheck(L_215);
		int32_t L_216 = L_215->___nowrap;
		if (!L_216)
		{
			goto IL_04a3;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_217 = ___0_z;
		NullCheck(L_217);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_218 = L_217->___istate;
		NullCheck(L_218);
		L_218->___mode = ((int32_t)12);
		goto IL_06d7;
	}

IL_04a3:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_219 = ___0_z;
		NullCheck(L_219);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_220 = L_219->___istate;
		NullCheck(L_220);
		L_220->___mode = 8;
		goto IL_04b4;
	}

IL_04b4:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_221 = ___0_z;
		NullCheck(L_221);
		int32_t L_222 = L_221->___avail_in;
		if (L_222)
		{
			goto IL_04c1;
		}
	}
	{
		int32_t L_223 = V_0;
		return L_223;
	}

IL_04c1:
	{
		int32_t L_224 = ___1_f;
		V_0 = L_224;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_225 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_226 = L_225;
		NullCheck(L_226);
		int32_t L_227 = L_226->___avail_in;
		NullCheck(L_226);
		L_226->___avail_in = ((int32_t)il2cpp_codegen_subtract(L_227, 1));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_228 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_229 = L_228;
		NullCheck(L_229);
		int64_t L_230 = L_229->___total_in;
		NullCheck(L_229);
		L_229->___total_in = ((int64_t)il2cpp_codegen_add(L_230, ((int64_t)1)));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_231 = ___0_z;
		NullCheck(L_231);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_232 = L_231->___istate;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_233 = ___0_z;
		NullCheck(L_233);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_234 = L_233->___next_in;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_235 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_236 = L_235;
		NullCheck(L_236);
		int32_t L_237 = L_236->___next_in_index;
		int32_t L_238 = L_237;
		V_3 = L_238;
		NullCheck(L_236);
		L_236->___next_in_index = ((int32_t)il2cpp_codegen_add(L_238, 1));
		int32_t L_239 = V_3;
		NullCheck(L_234);
		int32_t L_240 = L_239;
		uint8_t L_241 = (L_234)->GetAt(static_cast<il2cpp_array_size_t>(L_240));
		NullCheck(L_232);
		L_232->___need = ((int64_t)(((int64_t)((int32_t)(((int32_t)((int32_t)L_241&((int32_t)255)))<<((int32_t)24))))&((int64_t)(uint64_t)((uint32_t)((int32_t)-16777216)))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_242 = ___0_z;
		NullCheck(L_242);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_243 = L_242->___istate;
		NullCheck(L_243);
		L_243->___mode = ((int32_t)9);
		goto IL_0526;
	}

IL_0526:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_244 = ___0_z;
		NullCheck(L_244);
		int32_t L_245 = L_244->___avail_in;
		if (L_245)
		{
			goto IL_0533;
		}
	}
	{
		int32_t L_246 = V_0;
		return L_246;
	}

IL_0533:
	{
		int32_t L_247 = ___1_f;
		V_0 = L_247;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_248 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_249 = L_248;
		NullCheck(L_249);
		int32_t L_250 = L_249->___avail_in;
		NullCheck(L_249);
		L_249->___avail_in = ((int32_t)il2cpp_codegen_subtract(L_250, 1));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_251 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_252 = L_251;
		NullCheck(L_252);
		int64_t L_253 = L_252->___total_in;
		NullCheck(L_252);
		L_252->___total_in = ((int64_t)il2cpp_codegen_add(L_253, ((int64_t)1)));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_254 = ___0_z;
		NullCheck(L_254);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_255 = L_254->___istate;
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_256 = L_255;
		NullCheck(L_256);
		int64_t L_257 = L_256->___need;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_258 = ___0_z;
		NullCheck(L_258);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_259 = L_258->___next_in;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_260 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_261 = L_260;
		NullCheck(L_261);
		int32_t L_262 = L_261->___next_in_index;
		int32_t L_263 = L_262;
		V_3 = L_263;
		NullCheck(L_261);
		L_261->___next_in_index = ((int32_t)il2cpp_codegen_add(L_263, 1));
		int32_t L_264 = V_3;
		NullCheck(L_259);
		int32_t L_265 = L_264;
		uint8_t L_266 = (L_259)->GetAt(static_cast<il2cpp_array_size_t>(L_265));
		NullCheck(L_256);
		L_256->___need = ((int64_t)il2cpp_codegen_add(L_257, ((int64_t)(((int64_t)((int32_t)(((int32_t)((int32_t)L_266&((int32_t)255)))<<((int32_t)16))))&((int64_t)((int32_t)16711680))))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_267 = ___0_z;
		NullCheck(L_267);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_268 = L_267->___istate;
		NullCheck(L_268);
		L_268->___mode = ((int32_t)10);
		goto IL_059f;
	}

IL_059f:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_269 = ___0_z;
		NullCheck(L_269);
		int32_t L_270 = L_269->___avail_in;
		if (L_270)
		{
			goto IL_05ac;
		}
	}
	{
		int32_t L_271 = V_0;
		return L_271;
	}

IL_05ac:
	{
		int32_t L_272 = ___1_f;
		V_0 = L_272;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_273 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_274 = L_273;
		NullCheck(L_274);
		int32_t L_275 = L_274->___avail_in;
		NullCheck(L_274);
		L_274->___avail_in = ((int32_t)il2cpp_codegen_subtract(L_275, 1));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_276 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_277 = L_276;
		NullCheck(L_277);
		int64_t L_278 = L_277->___total_in;
		NullCheck(L_277);
		L_277->___total_in = ((int64_t)il2cpp_codegen_add(L_278, ((int64_t)1)));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_279 = ___0_z;
		NullCheck(L_279);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_280 = L_279->___istate;
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_281 = L_280;
		NullCheck(L_281);
		int64_t L_282 = L_281->___need;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_283 = ___0_z;
		NullCheck(L_283);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_284 = L_283->___next_in;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_285 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_286 = L_285;
		NullCheck(L_286);
		int32_t L_287 = L_286->___next_in_index;
		int32_t L_288 = L_287;
		V_3 = L_288;
		NullCheck(L_286);
		L_286->___next_in_index = ((int32_t)il2cpp_codegen_add(L_288, 1));
		int32_t L_289 = V_3;
		NullCheck(L_284);
		int32_t L_290 = L_289;
		uint8_t L_291 = (L_284)->GetAt(static_cast<il2cpp_array_size_t>(L_290));
		NullCheck(L_281);
		L_281->___need = ((int64_t)il2cpp_codegen_add(L_282, ((int64_t)(((int64_t)((int32_t)(((int32_t)((int32_t)L_291&((int32_t)255)))<<8)))&((int64_t)((int32_t)65280))))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_292 = ___0_z;
		NullCheck(L_292);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_293 = L_292->___istate;
		NullCheck(L_293);
		L_293->___mode = ((int32_t)11);
		goto IL_0617;
	}

IL_0617:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_294 = ___0_z;
		NullCheck(L_294);
		int32_t L_295 = L_294->___avail_in;
		if (L_295)
		{
			goto IL_0624;
		}
	}
	{
		int32_t L_296 = V_0;
		return L_296;
	}

IL_0624:
	{
		int32_t L_297 = ___1_f;
		V_0 = L_297;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_298 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_299 = L_298;
		NullCheck(L_299);
		int32_t L_300 = L_299->___avail_in;
		NullCheck(L_299);
		L_299->___avail_in = ((int32_t)il2cpp_codegen_subtract(L_300, 1));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_301 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_302 = L_301;
		NullCheck(L_302);
		int64_t L_303 = L_302->___total_in;
		NullCheck(L_302);
		L_302->___total_in = ((int64_t)il2cpp_codegen_add(L_303, ((int64_t)1)));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_304 = ___0_z;
		NullCheck(L_304);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_305 = L_304->___istate;
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_306 = L_305;
		NullCheck(L_306);
		int64_t L_307 = L_306->___need;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_308 = ___0_z;
		NullCheck(L_308);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_309 = L_308->___next_in;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_310 = ___0_z;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_311 = L_310;
		NullCheck(L_311);
		int32_t L_312 = L_311->___next_in_index;
		int32_t L_313 = L_312;
		V_3 = L_313;
		NullCheck(L_311);
		L_311->___next_in_index = ((int32_t)il2cpp_codegen_add(L_313, 1));
		int32_t L_314 = V_3;
		NullCheck(L_309);
		int32_t L_315 = L_314;
		uint8_t L_316 = (L_309)->GetAt(static_cast<il2cpp_array_size_t>(L_315));
		NullCheck(L_306);
		L_306->___need = ((int64_t)il2cpp_codegen_add(L_307, ((int64_t)(((int64_t)L_316)&((int64_t)((int32_t)255))))));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_317 = ___0_z;
		NullCheck(L_317);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_318 = L_317->___istate;
		NullCheck(L_318);
		Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D* L_319 = L_318->___was;
		NullCheck(L_319);
		int32_t L_320 = 0;
		int64_t L_321 = (L_319)->GetAt(static_cast<il2cpp_array_size_t>(L_320));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_322 = ___0_z;
		NullCheck(L_322);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_323 = L_322->___istate;
		NullCheck(L_323);
		int64_t L_324 = L_323->___need;
		if ((((int32_t)((int32_t)L_321)) == ((int32_t)((int32_t)L_324))))
		{
			goto IL_06bd;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_325 = ___0_z;
		NullCheck(L_325);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_326 = L_325->___istate;
		NullCheck(L_326);
		L_326->___mode = ((int32_t)13);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_327 = ___0_z;
		NullCheck(L_327);
		L_327->___msg = _stringLiteral06F3358B23D666113A1020E1C9CFEBE16373BE40;
		Il2CppCodeGenWriteBarrier((void**)(&L_327->___msg), (void*)_stringLiteral06F3358B23D666113A1020E1C9CFEBE16373BE40);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_328 = ___0_z;
		NullCheck(L_328);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_329 = L_328->___istate;
		NullCheck(L_329);
		L_329->___marker = 5;
		goto IL_06d7;
	}

IL_06bd:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_330 = ___0_z;
		NullCheck(L_330);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_331 = L_330->___istate;
		NullCheck(L_331);
		L_331->___mode = ((int32_t)12);
		goto IL_06cf;
	}

IL_06cf:
	{
		return 1;
	}

IL_06d1:
	{
		return ((int32_t)-3);
	}

IL_06d4:
	{
		return ((int32_t)-2);
	}

IL_06d7:
	{
		goto IL_0033;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Inflate__cctor_m0E0749B2F7A90021FD54B900E61D831B5C27DB07 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)4);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(2), (uint8_t)((int32_t)255));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_2 = L_1;
		NullCheck(L_2);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(3), (uint8_t)((int32_t)255));
		((Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50_StaticFields*)il2cpp_codegen_static_fields_for(Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50_il2cpp_TypeInfo_var))->___mark = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&((Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50_StaticFields*)il2cpp_codegen_static_fields_for(Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50_il2cpp_TypeInfo_var))->___mark), (void*)L_2);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfTree__ctor_mEC52C41356BFD818C5C776A7546F36E008825598 (InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfTree_huft_build_m46F10893F99C616504F6CBB38F34995EBF61FCE7 (InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* __this, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_b, int32_t ___1_bindex, int32_t ___2_n, int32_t ___3_s, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___4_d, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___5_e, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___6_t, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___7_m, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___8_hp, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___9_hn, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___10_v, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	int32_t V_7 = 0;
	int32_t V_8 = 0;
	int32_t V_9 = 0;
	int32_t V_10 = 0;
	int32_t V_11 = 0;
	int32_t V_12 = 0;
	int32_t V_13 = 0;
	int32_t V_14 = 0;
	int32_t V_15 = 0;
	int32_t G_B38_0 = 0;
	int32_t G_B56_0 = 0;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* G_B56_1 = NULL;
	int32_t G_B55_0 = 0;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* G_B55_1 = NULL;
	int32_t G_B57_0 = 0;
	int32_t G_B57_1 = 0;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* G_B57_2 = NULL;
	int32_t G_B75_0 = 0;
	{
		V_9 = 0;
		int32_t L_0 = ___2_n;
		V_4 = L_0;
	}

IL_0006:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = __this->___c;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_2 = ___0_b;
		int32_t L_3 = ___1_bindex;
		int32_t L_4 = V_9;
		NullCheck(L_2);
		int32_t L_5 = ((int32_t)il2cpp_codegen_add(L_3, L_4));
		int32_t L_6 = (L_2)->GetAt(static_cast<il2cpp_array_size_t>(L_5));
		NullCheck(L_1);
		int32_t* L_7 = ((L_1)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_6)));
		int32_t L_8 = *((int32_t*)L_7);
		*((int32_t*)L_7) = (int32_t)((int32_t)il2cpp_codegen_add(L_8, 1));
		int32_t L_9 = V_9;
		V_9 = ((int32_t)il2cpp_codegen_add(L_9, 1));
		int32_t L_10 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_10, 1));
		int32_t L_11 = V_4;
		if (L_11)
		{
			goto IL_0006;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_12 = __this->___c;
		NullCheck(L_12);
		int32_t L_13 = 0;
		int32_t L_14 = (L_12)->GetAt(static_cast<il2cpp_array_size_t>(L_13));
		int32_t L_15 = ___2_n;
		if ((!(((uint32_t)L_14) == ((uint32_t)L_15))))
		{
			goto IL_0049;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_16 = ___6_t;
		NullCheck(L_16);
		(L_16)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)(-1));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_17 = ___7_m;
		NullCheck(L_17);
		(L_17)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)0);
		return 0;
	}

IL_0049:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_18 = ___7_m;
		NullCheck(L_18);
		int32_t L_19 = 0;
		int32_t L_20 = (L_18)->GetAt(static_cast<il2cpp_array_size_t>(L_19));
		V_7 = L_20;
		V_5 = 1;
		goto IL_0070;
	}

IL_0057:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_21 = __this->___c;
		int32_t L_22 = V_5;
		NullCheck(L_21);
		int32_t L_23 = L_22;
		int32_t L_24 = (L_21)->GetAt(static_cast<il2cpp_array_size_t>(L_23));
		if (!L_24)
		{
			goto IL_006a;
		}
	}
	{
		goto IL_0079;
	}

IL_006a:
	{
		int32_t L_25 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_25, 1));
	}

IL_0070:
	{
		int32_t L_26 = V_5;
		if ((((int32_t)L_26) <= ((int32_t)((int32_t)15))))
		{
			goto IL_0057;
		}
	}

IL_0079:
	{
		int32_t L_27 = V_5;
		V_6 = L_27;
		int32_t L_28 = V_7;
		int32_t L_29 = V_5;
		if ((((int32_t)L_28) >= ((int32_t)L_29)))
		{
			goto IL_008a;
		}
	}
	{
		int32_t L_30 = V_5;
		V_7 = L_30;
	}

IL_008a:
	{
		V_4 = ((int32_t)15);
		goto IL_00ac;
	}

IL_0093:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_31 = __this->___c;
		int32_t L_32 = V_4;
		NullCheck(L_31);
		int32_t L_33 = L_32;
		int32_t L_34 = (L_31)->GetAt(static_cast<il2cpp_array_size_t>(L_33));
		if (!L_34)
		{
			goto IL_00a6;
		}
	}
	{
		goto IL_00b3;
	}

IL_00a6:
	{
		int32_t L_35 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_35, 1));
	}

IL_00ac:
	{
		int32_t L_36 = V_4;
		if (L_36)
		{
			goto IL_0093;
		}
	}

IL_00b3:
	{
		int32_t L_37 = V_4;
		V_2 = L_37;
		int32_t L_38 = V_7;
		int32_t L_39 = V_4;
		if ((((int32_t)L_38) <= ((int32_t)L_39)))
		{
			goto IL_00c3;
		}
	}
	{
		int32_t L_40 = V_4;
		V_7 = L_40;
	}

IL_00c3:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_41 = ___7_m;
		int32_t L_42 = V_7;
		NullCheck(L_41);
		(L_41)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)L_42);
		int32_t L_43 = V_5;
		V_13 = ((int32_t)(1<<((int32_t)(L_43&((int32_t)31)))));
		goto IL_00fb;
	}

IL_00d7:
	{
		int32_t L_44 = V_13;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_45 = __this->___c;
		int32_t L_46 = V_5;
		NullCheck(L_45);
		int32_t L_47 = L_46;
		int32_t L_48 = (L_45)->GetAt(static_cast<il2cpp_array_size_t>(L_47));
		int32_t L_49 = ((int32_t)il2cpp_codegen_subtract(L_44, L_48));
		V_13 = L_49;
		if ((((int32_t)L_49) >= ((int32_t)0)))
		{
			goto IL_00ef;
		}
	}
	{
		return ((int32_t)-3);
	}

IL_00ef:
	{
		int32_t L_50 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_50, 1));
		int32_t L_51 = V_13;
		V_13 = ((int32_t)(L_51<<1));
	}

IL_00fb:
	{
		int32_t L_52 = V_5;
		int32_t L_53 = V_4;
		if ((((int32_t)L_52) < ((int32_t)L_53)))
		{
			goto IL_00d7;
		}
	}
	{
		int32_t L_54 = V_13;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_55 = __this->___c;
		int32_t L_56 = V_4;
		NullCheck(L_55);
		int32_t L_57 = L_56;
		int32_t L_58 = (L_55)->GetAt(static_cast<il2cpp_array_size_t>(L_57));
		int32_t L_59 = ((int32_t)il2cpp_codegen_subtract(L_54, L_58));
		V_13 = L_59;
		if ((((int32_t)L_59) >= ((int32_t)0)))
		{
			goto IL_011c;
		}
	}
	{
		return ((int32_t)-3);
	}

IL_011c:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_60 = __this->___c;
		int32_t L_61 = V_4;
		NullCheck(L_60);
		int32_t* L_62 = ((L_60)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_61)));
		int32_t L_63 = *((int32_t*)L_62);
		int32_t L_64 = V_13;
		*((int32_t*)L_62) = (int32_t)((int32_t)il2cpp_codegen_add(L_63, L_64));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_65 = __this->___x;
		int32_t L_66 = 0;
		V_5 = L_66;
		NullCheck(L_65);
		(L_65)->SetAt(static_cast<il2cpp_array_size_t>(1), (int32_t)L_66);
		V_9 = 1;
		V_12 = 2;
		goto IL_016a;
	}

IL_0146:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_67 = __this->___x;
		int32_t L_68 = V_12;
		int32_t L_69 = V_5;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_70 = __this->___c;
		int32_t L_71 = V_9;
		NullCheck(L_70);
		int32_t L_72 = L_71;
		int32_t L_73 = (L_70)->GetAt(static_cast<il2cpp_array_size_t>(L_72));
		int32_t L_74 = ((int32_t)il2cpp_codegen_add(L_69, L_73));
		V_5 = L_74;
		NullCheck(L_67);
		(L_67)->SetAt(static_cast<il2cpp_array_size_t>(L_68), (int32_t)L_74);
		int32_t L_75 = V_12;
		V_12 = ((int32_t)il2cpp_codegen_add(L_75, 1));
		int32_t L_76 = V_9;
		V_9 = ((int32_t)il2cpp_codegen_add(L_76, 1));
	}

IL_016a:
	{
		int32_t L_77 = V_4;
		int32_t L_78 = ((int32_t)il2cpp_codegen_subtract(L_77, 1));
		V_4 = L_78;
		if (L_78)
		{
			goto IL_0146;
		}
	}
	{
		V_4 = 0;
		V_9 = 0;
	}

IL_017c:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_79 = ___0_b;
		int32_t L_80 = ___1_bindex;
		int32_t L_81 = V_9;
		NullCheck(L_79);
		int32_t L_82 = ((int32_t)il2cpp_codegen_add(L_80, L_81));
		int32_t L_83 = (L_79)->GetAt(static_cast<il2cpp_array_size_t>(L_82));
		int32_t L_84 = L_83;
		V_5 = L_84;
		if (!L_84)
		{
			goto IL_01a6;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_85 = ___10_v;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_86 = __this->___x;
		int32_t L_87 = V_5;
		NullCheck(L_86);
		int32_t* L_88 = ((L_86)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_87)));
		int32_t L_89 = *((int32_t*)L_88);
		int32_t L_90 = L_89;
		V_15 = L_90;
		*((int32_t*)L_88) = (int32_t)((int32_t)il2cpp_codegen_add(L_90, 1));
		int32_t L_91 = V_15;
		int32_t L_92 = V_4;
		NullCheck(L_85);
		(L_85)->SetAt(static_cast<il2cpp_array_size_t>(L_91), (int32_t)L_92);
	}

IL_01a6:
	{
		int32_t L_93 = V_9;
		V_9 = ((int32_t)il2cpp_codegen_add(L_93, 1));
		int32_t L_94 = V_4;
		int32_t L_95 = ((int32_t)il2cpp_codegen_add(L_94, 1));
		V_4 = L_95;
		int32_t L_96 = ___2_n;
		if ((((int32_t)L_95) < ((int32_t)L_96)))
		{
			goto IL_017c;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_97 = __this->___x;
		int32_t L_98 = V_2;
		NullCheck(L_97);
		int32_t L_99 = L_98;
		int32_t L_100 = (L_97)->GetAt(static_cast<il2cpp_array_size_t>(L_99));
		___2_n = L_100;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_101 = __this->___x;
		int32_t L_102 = 0;
		V_4 = L_102;
		NullCheck(L_101);
		(L_101)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)L_102);
		V_9 = 0;
		V_3 = (-1);
		int32_t L_103 = V_7;
		V_11 = ((-L_103));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_104 = __this->___u;
		NullCheck(L_104);
		(L_104)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)0);
		V_10 = 0;
		V_14 = 0;
		goto IL_0498;
	}

IL_01ed:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_105 = __this->___c;
		int32_t L_106 = V_6;
		NullCheck(L_105);
		int32_t L_107 = L_106;
		int32_t L_108 = (L_105)->GetAt(static_cast<il2cpp_array_size_t>(L_107));
		V_0 = L_108;
		goto IL_0488;
	}

IL_01fc:
	{
		goto IL_033a;
	}

IL_0201:
	{
		int32_t L_109 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add(L_109, 1));
		int32_t L_110 = V_11;
		int32_t L_111 = V_7;
		V_11 = ((int32_t)il2cpp_codegen_add(L_110, L_111));
		int32_t L_112 = V_2;
		int32_t L_113 = V_11;
		V_14 = ((int32_t)il2cpp_codegen_subtract(L_112, L_113));
		int32_t L_114 = V_14;
		int32_t L_115 = V_7;
		if ((((int32_t)L_114) <= ((int32_t)L_115)))
		{
			goto IL_0222;
		}
	}
	{
		int32_t L_116 = V_7;
		G_B38_0 = L_116;
		goto IL_0224;
	}

IL_0222:
	{
		int32_t L_117 = V_14;
		G_B38_0 = L_117;
	}

IL_0224:
	{
		V_14 = G_B38_0;
		int32_t L_118 = V_6;
		int32_t L_119 = V_11;
		int32_t L_120 = ((int32_t)il2cpp_codegen_subtract(L_118, L_119));
		V_5 = L_120;
		int32_t L_121 = ((int32_t)(1<<((int32_t)(L_120&((int32_t)31)))));
		V_1 = L_121;
		int32_t L_122 = V_0;
		if ((((int32_t)L_121) <= ((int32_t)((int32_t)il2cpp_codegen_add(L_122, 1)))))
		{
			goto IL_028c;
		}
	}
	{
		int32_t L_123 = V_1;
		int32_t L_124 = V_0;
		V_1 = ((int32_t)il2cpp_codegen_subtract(L_123, ((int32_t)il2cpp_codegen_add(L_124, 1))));
		int32_t L_125 = V_6;
		V_12 = L_125;
		int32_t L_126 = V_5;
		int32_t L_127 = V_14;
		if ((((int32_t)L_126) >= ((int32_t)L_127)))
		{
			goto IL_028c;
		}
	}
	{
		goto IL_027e;
	}

IL_0255:
	{
		int32_t L_128 = V_1;
		int32_t L_129 = ((int32_t)(L_128<<1));
		V_1 = L_129;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_130 = __this->___c;
		int32_t L_131 = V_12;
		int32_t L_132 = ((int32_t)il2cpp_codegen_add(L_131, 1));
		V_12 = L_132;
		NullCheck(L_130);
		int32_t L_133 = L_132;
		int32_t L_134 = (L_130)->GetAt(static_cast<il2cpp_array_size_t>(L_133));
		if ((((int32_t)L_129) > ((int32_t)L_134)))
		{
			goto IL_0272;
		}
	}
	{
		goto IL_028c;
	}

IL_0272:
	{
		int32_t L_135 = V_1;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_136 = __this->___c;
		int32_t L_137 = V_12;
		NullCheck(L_136);
		int32_t L_138 = L_137;
		int32_t L_139 = (L_136)->GetAt(static_cast<il2cpp_array_size_t>(L_138));
		V_1 = ((int32_t)il2cpp_codegen_subtract(L_135, L_139));
	}

IL_027e:
	{
		int32_t L_140 = V_5;
		int32_t L_141 = ((int32_t)il2cpp_codegen_add(L_140, 1));
		V_5 = L_141;
		int32_t L_142 = V_14;
		if ((((int32_t)L_141) < ((int32_t)L_142)))
		{
			goto IL_0255;
		}
	}

IL_028c:
	{
		int32_t L_143 = V_5;
		V_14 = ((int32_t)(1<<((int32_t)(L_143&((int32_t)31)))));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_144 = ___9_hn;
		NullCheck(L_144);
		int32_t L_145 = 0;
		int32_t L_146 = (L_144)->GetAt(static_cast<il2cpp_array_size_t>(L_145));
		int32_t L_147 = V_14;
		if ((((int32_t)((int32_t)il2cpp_codegen_add(L_146, L_147))) <= ((int32_t)((int32_t)1440))))
		{
			goto IL_02a9;
		}
	}
	{
		return ((int32_t)-3);
	}

IL_02a9:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_148 = __this->___u;
		int32_t L_149 = V_3;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_150 = ___9_hn;
		NullCheck(L_150);
		int32_t L_151 = 0;
		int32_t L_152 = (L_150)->GetAt(static_cast<il2cpp_array_size_t>(L_151));
		int32_t L_153 = L_152;
		V_10 = L_153;
		NullCheck(L_148);
		(L_148)->SetAt(static_cast<il2cpp_array_size_t>(L_149), (int32_t)L_153);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_154 = ___9_hn;
		NullCheck(L_154);
		int32_t* L_155 = ((L_154)->GetAddressAt(static_cast<il2cpp_array_size_t>(0)));
		int32_t L_156 = *((int32_t*)L_155);
		int32_t L_157 = V_14;
		*((int32_t*)L_155) = (int32_t)((int32_t)il2cpp_codegen_add(L_156, L_157));
		int32_t L_158 = V_3;
		if (!L_158)
		{
			goto IL_0334;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_159 = __this->___x;
		int32_t L_160 = V_3;
		int32_t L_161 = V_4;
		NullCheck(L_159);
		(L_159)->SetAt(static_cast<il2cpp_array_size_t>(L_160), (int32_t)L_161);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_162 = __this->___r;
		int32_t L_163 = V_5;
		NullCheck(L_162);
		(L_162)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)((int32_t)(uint8_t)L_163));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_164 = __this->___r;
		int32_t L_165 = V_7;
		NullCheck(L_164);
		(L_164)->SetAt(static_cast<il2cpp_array_size_t>(1), (int32_t)((int32_t)(uint8_t)L_165));
		int32_t L_166 = V_4;
		int32_t L_167 = V_11;
		int32_t L_168 = V_7;
		V_5 = ((int32_t)(L_166>>((int32_t)(((int32_t)il2cpp_codegen_subtract(L_167, L_168))&((int32_t)31)))));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_169 = __this->___r;
		int32_t L_170 = V_10;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_171 = __this->___u;
		int32_t L_172 = V_3;
		NullCheck(L_171);
		int32_t L_173 = ((int32_t)il2cpp_codegen_subtract(L_172, 1));
		int32_t L_174 = (L_171)->GetAt(static_cast<il2cpp_array_size_t>(L_173));
		int32_t L_175 = V_5;
		NullCheck(L_169);
		(L_169)->SetAt(static_cast<il2cpp_array_size_t>(2), (int32_t)((int32_t)il2cpp_codegen_subtract(((int32_t)il2cpp_codegen_subtract(L_170, L_174)), L_175)));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_176 = __this->___r;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_177 = ___8_hp;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_178 = __this->___u;
		int32_t L_179 = V_3;
		NullCheck(L_178);
		int32_t L_180 = ((int32_t)il2cpp_codegen_subtract(L_179, 1));
		int32_t L_181 = (L_178)->GetAt(static_cast<il2cpp_array_size_t>(L_180));
		int32_t L_182 = V_5;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_176, 0, (RuntimeArray*)L_177, ((int32_t)il2cpp_codegen_multiply(((int32_t)il2cpp_codegen_add(L_181, L_182)), 3)), 3, NULL);
		goto IL_033a;
	}

IL_0334:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_183 = ___6_t;
		int32_t L_184 = V_10;
		NullCheck(L_183);
		(L_183)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)L_184);
	}

IL_033a:
	{
		int32_t L_185 = V_6;
		int32_t L_186 = V_11;
		int32_t L_187 = V_7;
		if ((((int32_t)L_185) > ((int32_t)((int32_t)il2cpp_codegen_add(L_186, L_187)))))
		{
			goto IL_0201;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_188 = __this->___r;
		int32_t L_189 = V_6;
		int32_t L_190 = V_11;
		NullCheck(L_188);
		(L_188)->SetAt(static_cast<il2cpp_array_size_t>(1), (int32_t)((int32_t)(uint8_t)((int32_t)il2cpp_codegen_subtract(L_189, L_190))));
		int32_t L_191 = V_9;
		int32_t L_192 = ___2_n;
		if ((((int32_t)L_191) < ((int32_t)L_192)))
		{
			goto IL_036e;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_193 = __this->___r;
		NullCheck(L_193);
		(L_193)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)((int32_t)192));
		goto IL_03e3;
	}

IL_036e:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_194 = ___10_v;
		int32_t L_195 = V_9;
		NullCheck(L_194);
		int32_t L_196 = L_195;
		int32_t L_197 = (L_194)->GetAt(static_cast<il2cpp_array_size_t>(L_196));
		int32_t L_198 = ___3_s;
		if ((((int32_t)L_197) >= ((int32_t)L_198)))
		{
			goto IL_03b1;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_199 = __this->___r;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_200 = ___10_v;
		int32_t L_201 = V_9;
		NullCheck(L_200);
		int32_t L_202 = L_201;
		int32_t L_203 = (L_200)->GetAt(static_cast<il2cpp_array_size_t>(L_202));
		if ((((int32_t)L_203) >= ((int32_t)((int32_t)256))))
		{
			G_B56_0 = 0;
			G_B56_1 = L_199;
			goto IL_0396;
		}
		G_B55_0 = 0;
		G_B55_1 = L_199;
	}
	{
		G_B57_0 = 0;
		G_B57_1 = G_B55_0;
		G_B57_2 = G_B55_1;
		goto IL_0398;
	}

IL_0396:
	{
		G_B57_0 = ((int32_t)96);
		G_B57_1 = G_B56_0;
		G_B57_2 = G_B56_1;
	}

IL_0398:
	{
		NullCheck(G_B57_2);
		(G_B57_2)->SetAt(static_cast<il2cpp_array_size_t>(G_B57_1), (int32_t)((int32_t)(uint8_t)G_B57_0));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_204 = __this->___r;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_205 = ___10_v;
		int32_t L_206 = V_9;
		int32_t L_207 = L_206;
		V_9 = ((int32_t)il2cpp_codegen_add(L_207, 1));
		NullCheck(L_205);
		int32_t L_208 = L_207;
		int32_t L_209 = (L_205)->GetAt(static_cast<il2cpp_array_size_t>(L_208));
		NullCheck(L_204);
		(L_204)->SetAt(static_cast<il2cpp_array_size_t>(2), (int32_t)L_209);
		goto IL_03e3;
	}

IL_03b1:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_210 = __this->___r;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_211 = ___5_e;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_212 = ___10_v;
		int32_t L_213 = V_9;
		NullCheck(L_212);
		int32_t L_214 = L_213;
		int32_t L_215 = (L_212)->GetAt(static_cast<il2cpp_array_size_t>(L_214));
		int32_t L_216 = ___3_s;
		NullCheck(L_211);
		int32_t L_217 = ((int32_t)il2cpp_codegen_subtract(L_215, L_216));
		int32_t L_218 = (L_211)->GetAt(static_cast<il2cpp_array_size_t>(L_217));
		NullCheck(L_210);
		(L_210)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)((int32_t)(uint8_t)((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_add(L_218, ((int32_t)16))), ((int32_t)64)))));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_219 = __this->___r;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_220 = ___4_d;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_221 = ___10_v;
		int32_t L_222 = V_9;
		int32_t L_223 = L_222;
		V_9 = ((int32_t)il2cpp_codegen_add(L_223, 1));
		NullCheck(L_221);
		int32_t L_224 = L_223;
		int32_t L_225 = (L_221)->GetAt(static_cast<il2cpp_array_size_t>(L_224));
		int32_t L_226 = ___3_s;
		NullCheck(L_220);
		int32_t L_227 = ((int32_t)il2cpp_codegen_subtract(L_225, L_226));
		int32_t L_228 = (L_220)->GetAt(static_cast<il2cpp_array_size_t>(L_227));
		NullCheck(L_219);
		(L_219)->SetAt(static_cast<il2cpp_array_size_t>(2), (int32_t)L_228);
	}

IL_03e3:
	{
		int32_t L_229 = V_6;
		int32_t L_230 = V_11;
		V_1 = ((int32_t)(1<<((int32_t)(((int32_t)il2cpp_codegen_subtract(L_229, L_230))&((int32_t)31)))));
		int32_t L_231 = V_4;
		int32_t L_232 = V_11;
		V_5 = ((int32_t)(L_231>>((int32_t)(L_232&((int32_t)31)))));
		goto IL_0419;
	}

IL_03fd:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_233 = __this->___r;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_234 = ___8_hp;
		int32_t L_235 = V_10;
		int32_t L_236 = V_5;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_233, 0, (RuntimeArray*)L_234, ((int32_t)il2cpp_codegen_multiply(((int32_t)il2cpp_codegen_add(L_235, L_236)), 3)), 3, NULL);
		int32_t L_237 = V_5;
		int32_t L_238 = V_1;
		V_5 = ((int32_t)il2cpp_codegen_add(L_237, L_238));
	}

IL_0419:
	{
		int32_t L_239 = V_5;
		int32_t L_240 = V_14;
		if ((((int32_t)L_239) < ((int32_t)L_240)))
		{
			goto IL_03fd;
		}
	}
	{
		int32_t L_241 = V_6;
		V_5 = ((int32_t)(1<<((int32_t)(((int32_t)il2cpp_codegen_subtract(L_241, 1))&((int32_t)31)))));
		goto IL_043f;
	}

IL_0432:
	{
		int32_t L_242 = V_4;
		int32_t L_243 = V_5;
		V_4 = ((int32_t)(L_242^L_243));
		int32_t L_244 = V_5;
		V_5 = ((int32_t)(L_244>>1));
	}

IL_043f:
	{
		int32_t L_245 = V_4;
		int32_t L_246 = V_5;
		if (((int32_t)(L_245&L_246)))
		{
			goto IL_0432;
		}
	}
	{
		int32_t L_247 = V_4;
		int32_t L_248 = V_5;
		V_4 = ((int32_t)(L_247^L_248));
		int32_t L_249 = V_11;
		V_8 = ((int32_t)il2cpp_codegen_subtract(((int32_t)(1<<((int32_t)(L_249&((int32_t)31))))), 1));
		goto IL_0476;
	}

IL_0460:
	{
		int32_t L_250 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_subtract(L_250, 1));
		int32_t L_251 = V_11;
		int32_t L_252 = V_7;
		V_11 = ((int32_t)il2cpp_codegen_subtract(L_251, L_252));
		int32_t L_253 = V_11;
		V_8 = ((int32_t)il2cpp_codegen_subtract(((int32_t)(1<<((int32_t)(L_253&((int32_t)31))))), 1));
	}

IL_0476:
	{
		int32_t L_254 = V_4;
		int32_t L_255 = V_8;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_256 = __this->___x;
		int32_t L_257 = V_3;
		NullCheck(L_256);
		int32_t L_258 = L_257;
		int32_t L_259 = (L_256)->GetAt(static_cast<il2cpp_array_size_t>(L_258));
		if ((!(((uint32_t)((int32_t)(L_254&L_255))) == ((uint32_t)L_259))))
		{
			goto IL_0460;
		}
	}

IL_0488:
	{
		int32_t L_260 = V_0;
		int32_t L_261 = L_260;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_261, 1));
		if (L_261)
		{
			goto IL_01fc;
		}
	}
	{
		int32_t L_262 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_add(L_262, 1));
	}

IL_0498:
	{
		int32_t L_263 = V_6;
		int32_t L_264 = V_2;
		if ((((int32_t)L_263) <= ((int32_t)L_264)))
		{
			goto IL_01ed;
		}
	}
	{
		int32_t L_265 = V_13;
		if (!L_265)
		{
			goto IL_04b5;
		}
	}
	{
		int32_t L_266 = V_2;
		if ((((int32_t)L_266) == ((int32_t)1)))
		{
			goto IL_04b5;
		}
	}
	{
		G_B75_0 = ((int32_t)-5);
		goto IL_04b6;
	}

IL_04b5:
	{
		G_B75_0 = 0;
	}

IL_04b6:
	{
		return G_B75_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfTree_inflate_trees_bits_m08FC677299F8FBF8D84B4E1AF802E5037586C15A (InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* __this, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_c, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___1_bb, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___2_tb, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___3_hp, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___4_z, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral13A5361A51002BE0AE3A86C6F54E7ADAC4F2CE94);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral90581047810EB87A7277461DDA1C1493B91DAAA4);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		InfTree_initWorkArea_m6920B04FCABD2ED354A307F6B18A49D4421546F6(__this, ((int32_t)19), NULL);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = __this->___hn;
		NullCheck(L_0);
		(L_0)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)0);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = ___0_c;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_2 = ___2_tb;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_3 = ___1_bb;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_4 = ___3_hp;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_5 = __this->___hn;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_6 = __this->___v;
		int32_t L_7;
		L_7 = InfTree_huft_build_m46F10893F99C616504F6CBB38F34995EBF61FCE7(__this, L_1, 0, ((int32_t)19), ((int32_t)19), (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL, (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)NULL, L_2, L_3, L_4, L_5, L_6, NULL);
		V_0 = L_7;
		int32_t L_8 = V_0;
		if ((!(((uint32_t)L_8) == ((uint32_t)((int32_t)-3)))))
		{
			goto IL_0049;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_9 = ___4_z;
		NullCheck(L_9);
		L_9->___msg = _stringLiteral90581047810EB87A7277461DDA1C1493B91DAAA4;
		Il2CppCodeGenWriteBarrier((void**)(&L_9->___msg), (void*)_stringLiteral90581047810EB87A7277461DDA1C1493B91DAAA4);
		goto IL_0068;
	}

IL_0049:
	{
		int32_t L_10 = V_0;
		if ((((int32_t)L_10) == ((int32_t)((int32_t)-5))))
		{
			goto IL_0059;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_11 = ___1_bb;
		NullCheck(L_11);
		int32_t L_12 = 0;
		int32_t L_13 = (L_11)->GetAt(static_cast<il2cpp_array_size_t>(L_12));
		if (L_13)
		{
			goto IL_0068;
		}
	}

IL_0059:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_14 = ___4_z;
		NullCheck(L_14);
		L_14->___msg = _stringLiteral13A5361A51002BE0AE3A86C6F54E7ADAC4F2CE94;
		Il2CppCodeGenWriteBarrier((void**)(&L_14->___msg), (void*)_stringLiteral13A5361A51002BE0AE3A86C6F54E7ADAC4F2CE94);
		V_0 = ((int32_t)-3);
	}

IL_0068:
	{
		int32_t L_15 = V_0;
		return L_15;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfTree_inflate_trees_dynamic_m3FC3C6E9606B5B5494CA4D6F13D4B23F292A177F (InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* __this, int32_t ___0_nl, int32_t ___1_nd, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___2_c, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___3_bl, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___4_bd, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___5_tl, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___6_td, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___7_hp, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___8_z, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral527C1A81C9577E20EFCD218DE9B39383A8F64CD0);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5A888468814C6717D8F1F53C27076E49BCF685AE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral61CF8C6E69A5020616A55D8196F59FE4DE0129D6);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCFBC3A862771D0485E915BD869029175AD24B07C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE960A05B0E3F3B1A832A46162FB0C2332497D8F4);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		InfTree_initWorkArea_m6920B04FCABD2ED354A307F6B18A49D4421546F6(__this, ((int32_t)288), NULL);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = __this->___hn;
		NullCheck(L_0);
		(L_0)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)0);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = ___2_c;
		int32_t L_2 = ___0_nl;
		il2cpp_codegen_runtime_class_init_inline(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_3 = ((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___cplens;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_4 = ((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___cplext;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_5 = ___5_tl;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_6 = ___3_bl;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_7 = ___7_hp;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_8 = __this->___hn;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_9 = __this->___v;
		int32_t L_10;
		L_10 = InfTree_huft_build_m46F10893F99C616504F6CBB38F34995EBF61FCE7(__this, L_1, 0, L_2, ((int32_t)257), L_3, L_4, L_5, L_6, L_7, L_8, L_9, NULL);
		V_0 = L_10;
		int32_t L_11 = V_0;
		if (L_11)
		{
			goto IL_004e;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_12 = ___3_bl;
		NullCheck(L_12);
		int32_t L_13 = 0;
		int32_t L_14 = (L_12)->GetAt(static_cast<il2cpp_array_size_t>(L_13));
		if (L_14)
		{
			goto IL_0080;
		}
	}

IL_004e:
	{
		int32_t L_15 = V_0;
		if ((!(((uint32_t)L_15) == ((uint32_t)((int32_t)-3)))))
		{
			goto IL_0067;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_16 = ___8_z;
		NullCheck(L_16);
		L_16->___msg = _stringLiteral527C1A81C9577E20EFCD218DE9B39383A8F64CD0;
		Il2CppCodeGenWriteBarrier((void**)(&L_16->___msg), (void*)_stringLiteral527C1A81C9577E20EFCD218DE9B39383A8F64CD0);
		goto IL_007e;
	}

IL_0067:
	{
		int32_t L_17 = V_0;
		if ((((int32_t)L_17) == ((int32_t)((int32_t)-4))))
		{
			goto IL_007e;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_18 = ___8_z;
		NullCheck(L_18);
		L_18->___msg = _stringLiteralE960A05B0E3F3B1A832A46162FB0C2332497D8F4;
		Il2CppCodeGenWriteBarrier((void**)(&L_18->___msg), (void*)_stringLiteralE960A05B0E3F3B1A832A46162FB0C2332497D8F4);
		V_0 = ((int32_t)-3);
	}

IL_007e:
	{
		int32_t L_19 = V_0;
		return L_19;
	}

IL_0080:
	{
		InfTree_initWorkArea_m6920B04FCABD2ED354A307F6B18A49D4421546F6(__this, ((int32_t)288), NULL);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_20 = ___2_c;
		int32_t L_21 = ___0_nl;
		int32_t L_22 = ___1_nd;
		il2cpp_codegen_runtime_class_init_inline(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_23 = ((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___cpdist;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_24 = ((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___cpdext;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_25 = ___6_td;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_26 = ___4_bd;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_27 = ___7_hp;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_28 = __this->___hn;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_29 = __this->___v;
		int32_t L_30;
		L_30 = InfTree_huft_build_m46F10893F99C616504F6CBB38F34995EBF61FCE7(__this, L_20, L_21, L_22, 0, L_23, L_24, L_25, L_26, L_27, L_28, L_29, NULL);
		V_0 = L_30;
		int32_t L_31 = V_0;
		if (L_31)
		{
			goto IL_00cc;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_32 = ___4_bd;
		NullCheck(L_32);
		int32_t L_33 = 0;
		int32_t L_34 = (L_32)->GetAt(static_cast<il2cpp_array_size_t>(L_33));
		if (L_34)
		{
			goto IL_011a;
		}
	}
	{
		int32_t L_35 = ___0_nl;
		if ((((int32_t)L_35) <= ((int32_t)((int32_t)257))))
		{
			goto IL_011a;
		}
	}

IL_00cc:
	{
		int32_t L_36 = V_0;
		if ((!(((uint32_t)L_36) == ((uint32_t)((int32_t)-3)))))
		{
			goto IL_00e5;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_37 = ___8_z;
		NullCheck(L_37);
		L_37->___msg = _stringLiteral5A888468814C6717D8F1F53C27076E49BCF685AE;
		Il2CppCodeGenWriteBarrier((void**)(&L_37->___msg), (void*)_stringLiteral5A888468814C6717D8F1F53C27076E49BCF685AE);
		goto IL_0118;
	}

IL_00e5:
	{
		int32_t L_38 = V_0;
		if ((!(((uint32_t)L_38) == ((uint32_t)((int32_t)-5)))))
		{
			goto IL_0101;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_39 = ___8_z;
		NullCheck(L_39);
		L_39->___msg = _stringLiteralCFBC3A862771D0485E915BD869029175AD24B07C;
		Il2CppCodeGenWriteBarrier((void**)(&L_39->___msg), (void*)_stringLiteralCFBC3A862771D0485E915BD869029175AD24B07C);
		V_0 = ((int32_t)-3);
		goto IL_0118;
	}

IL_0101:
	{
		int32_t L_40 = V_0;
		if ((((int32_t)L_40) == ((int32_t)((int32_t)-4))))
		{
			goto IL_0118;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_41 = ___8_z;
		NullCheck(L_41);
		L_41->___msg = _stringLiteral61CF8C6E69A5020616A55D8196F59FE4DE0129D6;
		Il2CppCodeGenWriteBarrier((void**)(&L_41->___msg), (void*)_stringLiteral61CF8C6E69A5020616A55D8196F59FE4DE0129D6);
		V_0 = ((int32_t)-3);
	}

IL_0118:
	{
		int32_t L_42 = V_0;
		return L_42;
	}

IL_011a:
	{
		return 0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InfTree_inflate_trees_fixed_m7D58777D20FA8CACF92F4D42A45E0BCC42934D28 (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___0_bl, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___1_bd, Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* ___2_tl, Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* ___3_td, ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* ___4_z, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = ___0_bl;
		NullCheck(L_0);
		(L_0)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)((int32_t)9));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = ___1_bd;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (int32_t)5);
		Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* L_2 = ___2_tl;
		il2cpp_codegen_runtime_class_init_inline(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_3 = ((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___fixed_tl;
		NullCheck(L_2);
		(L_2)->SetAt(static_cast<il2cpp_array_size_t>(0), (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)L_3);
		Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* L_4 = ___3_td;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_5 = ((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___fixed_td;
		NullCheck(L_4);
		(L_4)->SetAt(static_cast<il2cpp_array_size_t>(0), (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)L_5);
		return 0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfTree_initWorkArea_m6920B04FCABD2ED354A307F6B18A49D4421546F6 (InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A* __this, int32_t ___0_vsize, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = __this->___hn;
		if (L_0)
		{
			goto IL_0056;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)1);
		__this->___hn = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___hn), (void*)L_1);
		int32_t L_2 = ___0_vsize;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_3 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)L_2);
		__this->___v = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___v), (void*)L_3);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_4 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)16));
		__this->___c = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___c), (void*)L_4);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_5 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)3);
		__this->___r = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___r), (void*)L_5);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_6 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)15));
		__this->___u = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___u), (void*)L_6);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_7 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)16));
		__this->___x = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___x), (void*)L_7);
	}

IL_0056:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_8 = __this->___v;
		NullCheck(L_8);
		int32_t L_9 = ___0_vsize;
		if ((((int32_t)((int32_t)(((RuntimeArray*)L_8)->max_length))) >= ((int32_t)L_9)))
		{
			goto IL_0070;
		}
	}
	{
		int32_t L_10 = ___0_vsize;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_11 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)L_10);
		__this->___v = L_11;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___v), (void*)L_11);
	}

IL_0070:
	{
		V_0 = 0;
		goto IL_0084;
	}

IL_0077:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_12 = __this->___v;
		int32_t L_13 = V_0;
		NullCheck(L_12);
		(L_12)->SetAt(static_cast<il2cpp_array_size_t>(L_13), (int32_t)0);
		int32_t L_14 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_14, 1));
	}

IL_0084:
	{
		int32_t L_15 = V_0;
		int32_t L_16 = ___0_vsize;
		if ((((int32_t)L_15) < ((int32_t)L_16)))
		{
			goto IL_0077;
		}
	}
	{
		V_1 = 0;
		goto IL_009f;
	}

IL_0092:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_17 = __this->___c;
		int32_t L_18 = V_1;
		NullCheck(L_17);
		(L_17)->SetAt(static_cast<il2cpp_array_size_t>(L_18), (int32_t)0);
		int32_t L_19 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_19, 1));
	}

IL_009f:
	{
		int32_t L_20 = V_1;
		if ((((int32_t)L_20) < ((int32_t)((int32_t)16))))
		{
			goto IL_0092;
		}
	}
	{
		V_2 = 0;
		goto IL_00bb;
	}

IL_00ae:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_21 = __this->___r;
		int32_t L_22 = V_2;
		NullCheck(L_21);
		(L_21)->SetAt(static_cast<il2cpp_array_size_t>(L_22), (int32_t)0);
		int32_t L_23 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_23, 1));
	}

IL_00bb:
	{
		int32_t L_24 = V_2;
		if ((((int32_t)L_24) < ((int32_t)3)))
		{
			goto IL_00ae;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_25 = __this->___c;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_26 = __this->___u;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_25, 0, (RuntimeArray*)L_26, 0, ((int32_t)15), NULL);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_27 = __this->___c;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_28 = __this->___x;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_27, 0, (RuntimeArray*)L_28, 0, ((int32_t)16), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InfTree__cctor_m46368BB0EC015156935E82C555DC6A442AF52A49 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE3_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE4_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE5_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE6_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE7_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE8_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)1536));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE3_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___fixed_tl = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___fixed_tl), (void*)L_1);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_3 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)96));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE4_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___fixed_td = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___fixed_td), (void*)L_4);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_6 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)31));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_7 = L_6;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_8 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE5_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_7, L_8, NULL);
		((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___cplens = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___cplens), (void*)L_7);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_9 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)31));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_10 = L_9;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_11 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE6_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_10, L_11, NULL);
		((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___cplext = L_10;
		Il2CppCodeGenWriteBarrier((void**)(&((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___cplext), (void*)L_10);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_12 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)30));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_13 = L_12;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_14 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE7_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_13, L_14, NULL);
		((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___cpdist = L_13;
		Il2CppCodeGenWriteBarrier((void**)(&((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___cpdist), (void*)L_13);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_15 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)30));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_16 = L_15;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_17 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE8_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_16, L_17, NULL);
		((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___cpdext = L_16;
		Il2CppCodeGenWriteBarrier((void**)(&((InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_StaticFields*)il2cpp_codegen_static_fields_for(InfTree_t94163C60543CC78E6277597E7F675ACEFD2B865A_il2cpp_TypeInfo_var))->___cpdext), (void*)L_16);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StaticTree__ctor_mAE62D8BA87B612590731EDA50692FCC18CBF7B22 (StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* __this, Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___0_static_tree, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___1_extra_bits, int32_t ___2_extra_base, int32_t ___3_elems, int32_t ___4_max_length, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_0 = ___0_static_tree;
		__this->___static_tree = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___static_tree), (void*)L_0);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = ___1_extra_bits;
		__this->___extra_bits = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___extra_bits), (void*)L_1);
		int32_t L_2 = ___2_extra_base;
		__this->___extra_base = L_2;
		int32_t L_3 = ___3_elems;
		__this->___elems = L_3;
		int32_t L_4 = ___4_max_length;
		__this->___max_length = L_4;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StaticTree__cctor_m4BC3A6F4C65BDF222436862F8B642A1BF01408EF (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE9_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEA_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_0 = (Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB*)(Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB*)SZArrayNew(Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB_il2cpp_TypeInfo_var, (uint32_t)((int32_t)576));
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DE9_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		((StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var))->___static_ltree = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var))->___static_ltree), (void*)L_1);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_3 = (Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB*)(Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB*)SZArrayNew(Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB_il2cpp_TypeInfo_var, (uint32_t)((int32_t)60));
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEA_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		((StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var))->___static_dtree = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&((StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var))->___static_dtree), (void*)L_4);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_6 = ((StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var))->___static_ltree;
		il2cpp_codegen_runtime_class_init_inline(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_7 = ((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___extra_lbits;
		StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* L_8 = (StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC*)il2cpp_codegen_object_new(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var);
		StaticTree__ctor_mAE62D8BA87B612590731EDA50692FCC18CBF7B22(L_8, L_6, L_7, ((int32_t)257), ((int32_t)286), ((int32_t)15), NULL);
		((StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var))->___static_l_desc = L_8;
		Il2CppCodeGenWriteBarrier((void**)(&((StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var))->___static_l_desc), (void*)L_8);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_9 = ((StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var))->___static_dtree;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_10 = ((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___extra_dbits;
		StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* L_11 = (StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC*)il2cpp_codegen_object_new(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var);
		StaticTree__ctor_mAE62D8BA87B612590731EDA50692FCC18CBF7B22(L_11, L_9, L_10, 0, ((int32_t)30), ((int32_t)15), NULL);
		((StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var))->___static_d_desc = L_11;
		Il2CppCodeGenWriteBarrier((void**)(&((StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var))->___static_d_desc), (void*)L_11);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_12 = ((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___extra_blbits;
		StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* L_13 = (StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC*)il2cpp_codegen_object_new(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var);
		StaticTree__ctor_mAE62D8BA87B612590731EDA50692FCC18CBF7B22(L_13, (Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB*)NULL, L_12, 0, ((int32_t)19), 7, NULL);
		((StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var))->___static_bl_desc = L_13;
		Il2CppCodeGenWriteBarrier((void**)(&((StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC_il2cpp_TypeInfo_var))->___static_bl_desc), (void*)L_13);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Tree__ctor_mD431179F083BC2CB1F7D6B67F8931D539A0530E4 (Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Tree_d_code_m3C5368509D432323609DA61B311CBC695C443641 (int32_t ___0_dist, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t G_B3_0 = 0;
	{
		int32_t L_0 = ___0_dist;
		if ((((int32_t)L_0) >= ((int32_t)((int32_t)256))))
		{
			goto IL_0017;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = ((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->____dist_code;
		int32_t L_2 = ___0_dist;
		NullCheck(L_1);
		int32_t L_3 = L_2;
		uint8_t L_4 = (L_1)->GetAt(static_cast<il2cpp_array_size_t>(L_3));
		G_B3_0 = ((int32_t)(L_4));
		goto IL_0026;
	}

IL_0017:
	{
		il2cpp_codegen_runtime_class_init_inline(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_5 = ((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->____dist_code;
		int32_t L_6 = ___0_dist;
		NullCheck(L_5);
		int32_t L_7 = ((int32_t)il2cpp_codegen_add(((int32_t)256), ((int32_t)(L_6>>7))));
		uint8_t L_8 = (L_5)->GetAt(static_cast<il2cpp_array_size_t>(L_7));
		G_B3_0 = ((int32_t)(L_8));
	}

IL_0026:
	{
		return G_B3_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Tree_gen_bitlen_mC805176D33CF7261132A5A8A1687E50A68A97675 (Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD* __this, Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* ___0_s, const RuntimeMethod* method) 
{
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* V_0 = NULL;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* V_1 = NULL;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* V_2 = NULL;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	int32_t V_7 = 0;
	int32_t V_8 = 0;
	int32_t V_9 = 0;
	int16_t V_10 = 0;
	int32_t V_11 = 0;
	{
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_0 = __this->___dyn_tree;
		V_0 = L_0;
		StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* L_1 = __this->___stat_desc;
		NullCheck(L_1);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_2 = L_1->___static_tree;
		V_1 = L_2;
		StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* L_3 = __this->___stat_desc;
		NullCheck(L_3);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_4 = L_3->___extra_bits;
		V_2 = L_4;
		StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* L_5 = __this->___stat_desc;
		NullCheck(L_5);
		int32_t L_6 = L_5->___extra_base;
		V_3 = L_6;
		StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* L_7 = __this->___stat_desc;
		NullCheck(L_7);
		int32_t L_8 = L_7->___max_length;
		V_4 = L_8;
		V_11 = 0;
		V_8 = 0;
		goto IL_0053;
	}

IL_0043:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_9 = ___0_s;
		NullCheck(L_9);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_10 = L_9->___bl_count;
		int32_t L_11 = V_8;
		NullCheck(L_10);
		(L_10)->SetAt(static_cast<il2cpp_array_size_t>(L_11), (int16_t)0);
		int32_t L_12 = V_8;
		V_8 = ((int32_t)il2cpp_codegen_add(L_12, 1));
	}

IL_0053:
	{
		int32_t L_13 = V_8;
		if ((((int32_t)L_13) <= ((int32_t)((int32_t)15))))
		{
			goto IL_0043;
		}
	}
	{
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_14 = V_0;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_15 = ___0_s;
		NullCheck(L_15);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_16 = L_15->___heap;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_17 = ___0_s;
		NullCheck(L_17);
		int32_t L_18 = L_17->___heap_max;
		NullCheck(L_16);
		int32_t L_19 = L_18;
		int32_t L_20 = (L_16)->GetAt(static_cast<il2cpp_array_size_t>(L_19));
		NullCheck(L_14);
		(L_14)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(L_20, 2)), 1))), (int16_t)0);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_21 = ___0_s;
		NullCheck(L_21);
		int32_t L_22 = L_21->___heap_max;
		V_5 = ((int32_t)il2cpp_codegen_add(L_22, 1));
		goto IL_0136;
	}

IL_007f:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_23 = ___0_s;
		NullCheck(L_23);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_24 = L_23->___heap;
		int32_t L_25 = V_5;
		NullCheck(L_24);
		int32_t L_26 = L_25;
		int32_t L_27 = (L_24)->GetAt(static_cast<il2cpp_array_size_t>(L_26));
		V_6 = L_27;
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_28 = V_0;
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_29 = V_0;
		int32_t L_30 = V_6;
		NullCheck(L_29);
		int32_t L_31 = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(L_30, 2)), 1));
		int16_t L_32 = (L_29)->GetAt(static_cast<il2cpp_array_size_t>(L_31));
		NullCheck(L_28);
		int32_t L_33 = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply((int32_t)L_32, 2)), 1));
		int16_t L_34 = (L_28)->GetAt(static_cast<il2cpp_array_size_t>(L_33));
		V_8 = ((int32_t)il2cpp_codegen_add((int32_t)L_34, 1));
		int32_t L_35 = V_8;
		int32_t L_36 = V_4;
		if ((((int32_t)L_35) <= ((int32_t)L_36)))
		{
			goto IL_00af;
		}
	}
	{
		int32_t L_37 = V_4;
		V_8 = L_37;
		int32_t L_38 = V_11;
		V_11 = ((int32_t)il2cpp_codegen_add(L_38, 1));
	}

IL_00af:
	{
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_39 = V_0;
		int32_t L_40 = V_6;
		int32_t L_41 = V_8;
		NullCheck(L_39);
		(L_39)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(L_40, 2)), 1))), (int16_t)((int16_t)L_41));
		int32_t L_42 = V_6;
		int32_t L_43 = __this->___max_code;
		if ((((int32_t)L_42) <= ((int32_t)L_43)))
		{
			goto IL_00cc;
		}
	}
	{
		goto IL_0130;
	}

IL_00cc:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_44 = ___0_s;
		NullCheck(L_44);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_45 = L_44->___bl_count;
		int32_t L_46 = V_8;
		NullCheck(L_45);
		int16_t* L_47 = ((L_45)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_46)));
		int32_t L_48 = *((int16_t*)L_47);
		*((int16_t*)L_47) = (int16_t)((int16_t)((int32_t)il2cpp_codegen_add(L_48, 1)));
		V_9 = 0;
		int32_t L_49 = V_6;
		int32_t L_50 = V_3;
		if ((((int32_t)L_49) < ((int32_t)L_50)))
		{
			goto IL_00f2;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_51 = V_2;
		int32_t L_52 = V_6;
		int32_t L_53 = V_3;
		NullCheck(L_51);
		int32_t L_54 = ((int32_t)il2cpp_codegen_subtract(L_52, L_53));
		int32_t L_55 = (L_51)->GetAt(static_cast<il2cpp_array_size_t>(L_54));
		V_9 = L_55;
	}

IL_00f2:
	{
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_56 = V_0;
		int32_t L_57 = V_6;
		NullCheck(L_56);
		int32_t L_58 = ((int32_t)il2cpp_codegen_multiply(L_57, 2));
		int16_t L_59 = (L_56)->GetAt(static_cast<il2cpp_array_size_t>(L_58));
		V_10 = L_59;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_60 = ___0_s;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_61 = L_60;
		NullCheck(L_61);
		int32_t L_62 = L_61->___opt_len;
		int16_t L_63 = V_10;
		int32_t L_64 = V_8;
		int32_t L_65 = V_9;
		NullCheck(L_61);
		L_61->___opt_len = ((int32_t)il2cpp_codegen_add(L_62, ((int32_t)il2cpp_codegen_multiply((int32_t)L_63, ((int32_t)il2cpp_codegen_add(L_64, L_65))))));
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_66 = V_1;
		if (!L_66)
		{
			goto IL_0130;
		}
	}
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_67 = ___0_s;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_68 = L_67;
		NullCheck(L_68);
		int32_t L_69 = L_68->___static_len;
		int16_t L_70 = V_10;
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_71 = V_1;
		int32_t L_72 = V_6;
		NullCheck(L_71);
		int32_t L_73 = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(L_72, 2)), 1));
		int16_t L_74 = (L_71)->GetAt(static_cast<il2cpp_array_size_t>(L_73));
		int32_t L_75 = V_9;
		NullCheck(L_68);
		L_68->___static_len = ((int32_t)il2cpp_codegen_add(L_69, ((int32_t)il2cpp_codegen_multiply((int32_t)L_70, ((int32_t)il2cpp_codegen_add((int32_t)L_74, L_75))))));
	}

IL_0130:
	{
		int32_t L_76 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_76, 1));
	}

IL_0136:
	{
		int32_t L_77 = V_5;
		if ((((int32_t)L_77) < ((int32_t)((int32_t)573))))
		{
			goto IL_007f;
		}
	}
	{
		int32_t L_78 = V_11;
		if (L_78)
		{
			goto IL_014a;
		}
	}
	{
		return;
	}

IL_014a:
	{
		int32_t L_79 = V_4;
		V_8 = ((int32_t)il2cpp_codegen_subtract(L_79, 1));
		goto IL_015b;
	}

IL_0155:
	{
		int32_t L_80 = V_8;
		V_8 = ((int32_t)il2cpp_codegen_subtract(L_80, 1));
	}

IL_015b:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_81 = ___0_s;
		NullCheck(L_81);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_82 = L_81->___bl_count;
		int32_t L_83 = V_8;
		NullCheck(L_82);
		int32_t L_84 = L_83;
		int16_t L_85 = (L_82)->GetAt(static_cast<il2cpp_array_size_t>(L_84));
		if (!L_85)
		{
			goto IL_0155;
		}
	}
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_86 = ___0_s;
		NullCheck(L_86);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_87 = L_86->___bl_count;
		int32_t L_88 = V_8;
		NullCheck(L_87);
		int16_t* L_89 = ((L_87)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_88)));
		int32_t L_90 = *((int16_t*)L_89);
		*((int16_t*)L_89) = (int16_t)((int16_t)((int32_t)il2cpp_codegen_subtract(L_90, 1)));
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_91 = ___0_s;
		NullCheck(L_91);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_92 = L_91->___bl_count;
		int32_t L_93 = V_8;
		NullCheck(L_92);
		int16_t* L_94 = ((L_92)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_add(L_93, 1)))));
		int32_t L_95 = *((int16_t*)L_94);
		*((int16_t*)L_94) = (int16_t)((int16_t)((int32_t)il2cpp_codegen_add(L_95, 2)));
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_96 = ___0_s;
		NullCheck(L_96);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_97 = L_96->___bl_count;
		int32_t L_98 = V_4;
		NullCheck(L_97);
		int16_t* L_99 = ((L_97)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_98)));
		int32_t L_100 = *((int16_t*)L_99);
		*((int16_t*)L_99) = (int16_t)((int16_t)((int32_t)il2cpp_codegen_subtract(L_100, 1)));
		int32_t L_101 = V_11;
		V_11 = ((int32_t)il2cpp_codegen_subtract(L_101, 2));
		int32_t L_102 = V_11;
		if ((((int32_t)L_102) > ((int32_t)0)))
		{
			goto IL_014a;
		}
	}
	{
		int32_t L_103 = V_4;
		V_8 = L_103;
		goto IL_023d;
	}

IL_01bb:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_104 = ___0_s;
		NullCheck(L_104);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_105 = L_104->___bl_count;
		int32_t L_106 = V_8;
		NullCheck(L_105);
		int32_t L_107 = L_106;
		int16_t L_108 = (L_105)->GetAt(static_cast<il2cpp_array_size_t>(L_107));
		V_6 = L_108;
		goto IL_0230;
	}

IL_01cb:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_109 = ___0_s;
		NullCheck(L_109);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_110 = L_109->___heap;
		int32_t L_111 = V_5;
		int32_t L_112 = ((int32_t)il2cpp_codegen_subtract(L_111, 1));
		V_5 = L_112;
		NullCheck(L_110);
		int32_t L_113 = L_112;
		int32_t L_114 = (L_110)->GetAt(static_cast<il2cpp_array_size_t>(L_113));
		V_7 = L_114;
		int32_t L_115 = V_7;
		int32_t L_116 = __this->___max_code;
		if ((((int32_t)L_115) <= ((int32_t)L_116)))
		{
			goto IL_01ed;
		}
	}
	{
		goto IL_0230;
	}

IL_01ed:
	{
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_117 = V_0;
		int32_t L_118 = V_7;
		NullCheck(L_117);
		int32_t L_119 = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(L_118, 2)), 1));
		int16_t L_120 = (L_117)->GetAt(static_cast<il2cpp_array_size_t>(L_119));
		int32_t L_121 = V_8;
		if ((((int32_t)L_120) == ((int32_t)L_121)))
		{
			goto IL_022a;
		}
	}
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_122 = ___0_s;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_123 = L_122;
		NullCheck(L_123);
		int32_t L_124 = L_123->___opt_len;
		int32_t L_125 = V_8;
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_126 = V_0;
		int32_t L_127 = V_7;
		NullCheck(L_126);
		int32_t L_128 = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(L_127, 2)), 1));
		int16_t L_129 = (L_126)->GetAt(static_cast<il2cpp_array_size_t>(L_128));
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_130 = V_0;
		int32_t L_131 = V_7;
		NullCheck(L_130);
		int32_t L_132 = ((int32_t)il2cpp_codegen_multiply(L_131, 2));
		int16_t L_133 = (L_130)->GetAt(static_cast<il2cpp_array_size_t>(L_132));
		NullCheck(L_123);
		L_123->___opt_len = ((int32_t)il2cpp_codegen_add(L_124, ((int32_t)((int64_t)il2cpp_codegen_multiply(((int64_t)il2cpp_codegen_subtract(((int64_t)L_125), ((int64_t)L_129))), ((int64_t)L_133))))));
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_134 = V_0;
		int32_t L_135 = V_7;
		int32_t L_136 = V_8;
		NullCheck(L_134);
		(L_134)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(L_135, 2)), 1))), (int16_t)((int16_t)L_136));
	}

IL_022a:
	{
		int32_t L_137 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_subtract(L_137, 1));
	}

IL_0230:
	{
		int32_t L_138 = V_6;
		if (L_138)
		{
			goto IL_01cb;
		}
	}
	{
		int32_t L_139 = V_8;
		V_8 = ((int32_t)il2cpp_codegen_subtract(L_139, 1));
	}

IL_023d:
	{
		int32_t L_140 = V_8;
		if (L_140)
		{
			goto IL_01bb;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Tree_build_tree_m0389DD7B6D0F483FBAEF5B9CBD40A8AFE88A9C81 (Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD* __this, Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* ___0_s, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* V_0 = NULL;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* V_1 = NULL;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	int32_t V_7 = 0;
	int16_t V_8 = 0;
	int32_t G_B9_0 = 0;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* G_B9_1 = NULL;
	int32_t G_B8_0 = 0;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* G_B8_1 = NULL;
	int32_t G_B10_0 = 0;
	int32_t G_B10_1 = 0;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* G_B10_2 = NULL;
	{
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_0 = __this->___dyn_tree;
		V_0 = L_0;
		StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* L_1 = __this->___stat_desc;
		NullCheck(L_1);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_2 = L_1->___static_tree;
		V_1 = L_2;
		StaticTree_t4F667D59D01FF3F17C23BAAEF7323A1367DCFCDC* L_3 = __this->___stat_desc;
		NullCheck(L_3);
		int32_t L_4 = L_3->___elems;
		V_2 = L_4;
		V_5 = (-1);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_5 = ___0_s;
		NullCheck(L_5);
		L_5->___heap_len = 0;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_6 = ___0_s;
		NullCheck(L_6);
		L_6->___heap_max = ((int32_t)573);
		V_3 = 0;
		goto IL_007d;
	}

IL_003b:
	{
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_7 = V_0;
		int32_t L_8 = V_3;
		NullCheck(L_7);
		int32_t L_9 = ((int32_t)il2cpp_codegen_multiply(L_8, 2));
		int16_t L_10 = (L_7)->GetAt(static_cast<il2cpp_array_size_t>(L_9));
		if (!L_10)
		{
			goto IL_0071;
		}
	}
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_11 = ___0_s;
		NullCheck(L_11);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_12 = L_11->___heap;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_13 = ___0_s;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_14 = L_13;
		NullCheck(L_14);
		int32_t L_15 = L_14->___heap_len;
		int32_t L_16 = ((int32_t)il2cpp_codegen_add(L_15, 1));
		V_7 = L_16;
		NullCheck(L_14);
		L_14->___heap_len = L_16;
		int32_t L_17 = V_7;
		int32_t L_18 = V_3;
		int32_t L_19 = L_18;
		V_5 = L_19;
		NullCheck(L_12);
		(L_12)->SetAt(static_cast<il2cpp_array_size_t>(L_17), (int32_t)L_19);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_20 = ___0_s;
		NullCheck(L_20);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_21 = L_20->___depth;
		int32_t L_22 = V_3;
		NullCheck(L_21);
		(L_21)->SetAt(static_cast<il2cpp_array_size_t>(L_22), (uint8_t)0);
		goto IL_0079;
	}

IL_0071:
	{
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_23 = V_0;
		int32_t L_24 = V_3;
		NullCheck(L_23);
		(L_23)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(L_24, 2)), 1))), (int16_t)0);
	}

IL_0079:
	{
		int32_t L_25 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add(L_25, 1));
	}

IL_007d:
	{
		int32_t L_26 = V_3;
		int32_t L_27 = V_2;
		if ((((int32_t)L_26) < ((int32_t)L_27)))
		{
			goto IL_003b;
		}
	}
	{
		goto IL_00f9;
	}

IL_0089:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_28 = ___0_s;
		NullCheck(L_28);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_29 = L_28->___heap;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_30 = ___0_s;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_31 = L_30;
		NullCheck(L_31);
		int32_t L_32 = L_31->___heap_len;
		int32_t L_33 = ((int32_t)il2cpp_codegen_add(L_32, 1));
		V_7 = L_33;
		NullCheck(L_31);
		L_31->___heap_len = L_33;
		int32_t L_34 = V_7;
		int32_t L_35 = V_5;
		if ((((int32_t)L_35) >= ((int32_t)2)))
		{
			G_B9_0 = L_34;
			G_B9_1 = L_29;
			goto IL_00b6;
		}
		G_B8_0 = L_34;
		G_B8_1 = L_29;
	}
	{
		int32_t L_36 = V_5;
		int32_t L_37 = ((int32_t)il2cpp_codegen_add(L_36, 1));
		V_5 = L_37;
		G_B10_0 = L_37;
		G_B10_1 = G_B8_0;
		G_B10_2 = G_B8_1;
		goto IL_00b7;
	}

IL_00b6:
	{
		G_B10_0 = 0;
		G_B10_1 = G_B9_0;
		G_B10_2 = G_B9_1;
	}

IL_00b7:
	{
		int32_t L_38 = G_B10_0;
		V_7 = L_38;
		NullCheck(G_B10_2);
		(G_B10_2)->SetAt(static_cast<il2cpp_array_size_t>(G_B10_1), (int32_t)L_38);
		int32_t L_39 = V_7;
		V_6 = L_39;
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_40 = V_0;
		int32_t L_41 = V_6;
		NullCheck(L_40);
		(L_40)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_multiply(L_41, 2))), (int16_t)1);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_42 = ___0_s;
		NullCheck(L_42);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_43 = L_42->___depth;
		int32_t L_44 = V_6;
		NullCheck(L_43);
		(L_43)->SetAt(static_cast<il2cpp_array_size_t>(L_44), (uint8_t)0);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_45 = ___0_s;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_46 = L_45;
		NullCheck(L_46);
		int32_t L_47 = L_46->___opt_len;
		NullCheck(L_46);
		L_46->___opt_len = ((int32_t)il2cpp_codegen_subtract(L_47, 1));
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_48 = V_1;
		if (!L_48)
		{
			goto IL_00f9;
		}
	}
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_49 = ___0_s;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_50 = L_49;
		NullCheck(L_50);
		int32_t L_51 = L_50->___static_len;
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_52 = V_1;
		int32_t L_53 = V_6;
		NullCheck(L_52);
		int32_t L_54 = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(L_53, 2)), 1));
		int16_t L_55 = (L_52)->GetAt(static_cast<il2cpp_array_size_t>(L_54));
		NullCheck(L_50);
		L_50->___static_len = ((int32_t)il2cpp_codegen_subtract(L_51, (int32_t)L_55));
	}

IL_00f9:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_56 = ___0_s;
		NullCheck(L_56);
		int32_t L_57 = L_56->___heap_len;
		if ((((int32_t)L_57) < ((int32_t)2)))
		{
			goto IL_0089;
		}
	}
	{
		int32_t L_58 = V_5;
		__this->___max_code = L_58;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_59 = ___0_s;
		NullCheck(L_59);
		int32_t L_60 = L_59->___heap_len;
		V_3 = ((int32_t)(L_60/2));
		goto IL_0127;
	}

IL_011b:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_61 = ___0_s;
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_62 = V_0;
		int32_t L_63 = V_3;
		NullCheck(L_61);
		Deflate_pqdownheap_mB4A295AA13CEC78D449DEFD1E2CF668E83E7AE4E(L_61, L_62, L_63, NULL);
		int32_t L_64 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_subtract(L_64, 1));
	}

IL_0127:
	{
		int32_t L_65 = V_3;
		if ((((int32_t)L_65) >= ((int32_t)1)))
		{
			goto IL_011b;
		}
	}
	{
		int32_t L_66 = V_2;
		V_6 = L_66;
	}

IL_0131:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_67 = ___0_s;
		NullCheck(L_67);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_68 = L_67->___heap;
		NullCheck(L_68);
		int32_t L_69 = 1;
		int32_t L_70 = (L_68)->GetAt(static_cast<il2cpp_array_size_t>(L_69));
		V_3 = L_70;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_71 = ___0_s;
		NullCheck(L_71);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_72 = L_71->___heap;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_73 = ___0_s;
		NullCheck(L_73);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_74 = L_73->___heap;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_75 = ___0_s;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_76 = L_75;
		NullCheck(L_76);
		int32_t L_77 = L_76->___heap_len;
		int32_t L_78 = L_77;
		V_7 = L_78;
		NullCheck(L_76);
		L_76->___heap_len = ((int32_t)il2cpp_codegen_subtract(L_78, 1));
		int32_t L_79 = V_7;
		NullCheck(L_74);
		int32_t L_80 = L_79;
		int32_t L_81 = (L_74)->GetAt(static_cast<il2cpp_array_size_t>(L_80));
		NullCheck(L_72);
		(L_72)->SetAt(static_cast<il2cpp_array_size_t>(1), (int32_t)L_81);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_82 = ___0_s;
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_83 = V_0;
		NullCheck(L_82);
		Deflate_pqdownheap_mB4A295AA13CEC78D449DEFD1E2CF668E83E7AE4E(L_82, L_83, 1, NULL);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_84 = ___0_s;
		NullCheck(L_84);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_85 = L_84->___heap;
		NullCheck(L_85);
		int32_t L_86 = 1;
		int32_t L_87 = (L_85)->GetAt(static_cast<il2cpp_array_size_t>(L_86));
		V_4 = L_87;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_88 = ___0_s;
		NullCheck(L_88);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_89 = L_88->___heap;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_90 = ___0_s;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_91 = L_90;
		NullCheck(L_91);
		int32_t L_92 = L_91->___heap_max;
		int32_t L_93 = ((int32_t)il2cpp_codegen_subtract(L_92, 1));
		V_7 = L_93;
		NullCheck(L_91);
		L_91->___heap_max = L_93;
		int32_t L_94 = V_7;
		int32_t L_95 = V_3;
		NullCheck(L_89);
		(L_89)->SetAt(static_cast<il2cpp_array_size_t>(L_94), (int32_t)L_95);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_96 = ___0_s;
		NullCheck(L_96);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_97 = L_96->___heap;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_98 = ___0_s;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_99 = L_98;
		NullCheck(L_99);
		int32_t L_100 = L_99->___heap_max;
		int32_t L_101 = ((int32_t)il2cpp_codegen_subtract(L_100, 1));
		V_7 = L_101;
		NullCheck(L_99);
		L_99->___heap_max = L_101;
		int32_t L_102 = V_7;
		int32_t L_103 = V_4;
		NullCheck(L_97);
		(L_97)->SetAt(static_cast<il2cpp_array_size_t>(L_102), (int32_t)L_103);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_104 = V_0;
		int32_t L_105 = V_6;
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_106 = V_0;
		int32_t L_107 = V_3;
		NullCheck(L_106);
		int32_t L_108 = ((int32_t)il2cpp_codegen_multiply(L_107, 2));
		int16_t L_109 = (L_106)->GetAt(static_cast<il2cpp_array_size_t>(L_108));
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_110 = V_0;
		int32_t L_111 = V_4;
		NullCheck(L_110);
		int32_t L_112 = ((int32_t)il2cpp_codegen_multiply(L_111, 2));
		int16_t L_113 = (L_110)->GetAt(static_cast<il2cpp_array_size_t>(L_112));
		NullCheck(L_104);
		(L_104)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_multiply(L_105, 2))), (int16_t)((int16_t)((int32_t)il2cpp_codegen_add((int32_t)L_109, (int32_t)L_113))));
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_114 = ___0_s;
		NullCheck(L_114);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_115 = L_114->___depth;
		int32_t L_116 = V_6;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_117 = ___0_s;
		NullCheck(L_117);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_118 = L_117->___depth;
		int32_t L_119 = V_3;
		NullCheck(L_118);
		int32_t L_120 = L_119;
		uint8_t L_121 = (L_118)->GetAt(static_cast<il2cpp_array_size_t>(L_120));
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_122 = ___0_s;
		NullCheck(L_122);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_123 = L_122->___depth;
		int32_t L_124 = V_4;
		NullCheck(L_123);
		int32_t L_125 = L_124;
		uint8_t L_126 = (L_123)->GetAt(static_cast<il2cpp_array_size_t>(L_125));
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		uint8_t L_127;
		L_127 = Math_Max_m12FB4E1302123ADB441E3A7BDF52E8404DDE53A2(L_121, L_126, NULL);
		NullCheck(L_115);
		(L_115)->SetAt(static_cast<il2cpp_array_size_t>(L_116), (uint8_t)((int32_t)(uint8_t)((int32_t)il2cpp_codegen_add((int32_t)L_127, 1))));
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_128 = V_0;
		int32_t L_129 = V_3;
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_130 = V_0;
		int32_t L_131 = V_4;
		int32_t L_132 = V_6;
		int16_t L_133 = ((int16_t)L_132);
		V_8 = L_133;
		NullCheck(L_130);
		(L_130)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(L_131, 2)), 1))), (int16_t)L_133);
		int16_t L_134 = V_8;
		NullCheck(L_128);
		(L_128)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(L_129, 2)), 1))), (int16_t)L_134);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_135 = ___0_s;
		NullCheck(L_135);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_136 = L_135->___heap;
		int32_t L_137 = V_6;
		int32_t L_138 = L_137;
		V_6 = ((int32_t)il2cpp_codegen_add(L_138, 1));
		NullCheck(L_136);
		(L_136)->SetAt(static_cast<il2cpp_array_size_t>(1), (int32_t)L_138);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_139 = ___0_s;
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_140 = V_0;
		NullCheck(L_139);
		Deflate_pqdownheap_mB4A295AA13CEC78D449DEFD1E2CF668E83E7AE4E(L_139, L_140, 1, NULL);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_141 = ___0_s;
		NullCheck(L_141);
		int32_t L_142 = L_141->___heap_len;
		if ((((int32_t)L_142) >= ((int32_t)2)))
		{
			goto IL_0131;
		}
	}
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_143 = ___0_s;
		NullCheck(L_143);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_144 = L_143->___heap;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_145 = ___0_s;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_146 = L_145;
		NullCheck(L_146);
		int32_t L_147 = L_146->___heap_max;
		int32_t L_148 = ((int32_t)il2cpp_codegen_subtract(L_147, 1));
		V_7 = L_148;
		NullCheck(L_146);
		L_146->___heap_max = L_148;
		int32_t L_149 = V_7;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_150 = ___0_s;
		NullCheck(L_150);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_151 = L_150->___heap;
		NullCheck(L_151);
		int32_t L_152 = 1;
		int32_t L_153 = (L_151)->GetAt(static_cast<il2cpp_array_size_t>(L_152));
		NullCheck(L_144);
		(L_144)->SetAt(static_cast<il2cpp_array_size_t>(L_149), (int32_t)L_153);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_154 = ___0_s;
		Tree_gen_bitlen_mC805176D33CF7261132A5A8A1687E50A68A97675(__this, L_154, NULL);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_155 = V_0;
		int32_t L_156 = V_5;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_157 = ___0_s;
		NullCheck(L_157);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_158 = L_157->___bl_count;
		il2cpp_codegen_runtime_class_init_inline(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var);
		Tree_gen_codes_m2D3268BCC85C606634411576B8C2F4FCF08A523F(L_155, L_156, L_158, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Tree_gen_codes_m2D3268BCC85C606634411576B8C2F4FCF08A523F (Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___0_tree, int32_t ___1_max_code, Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___2_bl_count, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* V_0 = NULL;
	int16_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	int16_t V_5 = 0;
	{
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_0 = (Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB*)(Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB*)SZArrayNew(Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB_il2cpp_TypeInfo_var, (uint32_t)((int32_t)16));
		V_0 = L_0;
		V_1 = (int16_t)0;
		V_2 = 1;
		goto IL_0024;
	}

IL_0011:
	{
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_1 = V_0;
		int32_t L_2 = V_2;
		int16_t L_3 = V_1;
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_4 = ___2_bl_count;
		int32_t L_5 = V_2;
		NullCheck(L_4);
		int32_t L_6 = ((int32_t)il2cpp_codegen_subtract(L_5, 1));
		int16_t L_7 = (L_4)->GetAt(static_cast<il2cpp_array_size_t>(L_6));
		int16_t L_8 = ((int16_t)((int32_t)(((int32_t)il2cpp_codegen_add((int32_t)L_3, (int32_t)L_7))<<1)));
		V_1 = L_8;
		NullCheck(L_1);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(L_2), (int16_t)L_8);
		int32_t L_9 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_9, 1));
	}

IL_0024:
	{
		int32_t L_10 = V_2;
		if ((((int32_t)L_10) <= ((int32_t)((int32_t)15))))
		{
			goto IL_0011;
		}
	}
	{
		V_3 = 0;
		goto IL_006c;
	}

IL_0033:
	{
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_11 = ___0_tree;
		int32_t L_12 = V_3;
		NullCheck(L_11);
		int32_t L_13 = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_multiply(L_12, 2)), 1));
		int16_t L_14 = (L_11)->GetAt(static_cast<il2cpp_array_size_t>(L_13));
		V_4 = L_14;
		int32_t L_15 = V_4;
		if (L_15)
		{
			goto IL_0048;
		}
	}
	{
		goto IL_0068;
	}

IL_0048:
	{
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_16 = ___0_tree;
		int32_t L_17 = V_3;
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_18 = V_0;
		int32_t L_19 = V_4;
		NullCheck(L_18);
		int16_t* L_20 = ((L_18)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_19)));
		int32_t L_21 = *((int16_t*)L_20);
		int32_t L_22 = L_21;
		V_5 = (int16_t)L_22;
		*((int16_t*)L_20) = (int16_t)((int16_t)((int32_t)il2cpp_codegen_add(L_22, 1)));
		int16_t L_23 = V_5;
		int32_t L_24 = V_4;
		il2cpp_codegen_runtime_class_init_inline(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var);
		int32_t L_25;
		L_25 = Tree_bi_reverse_m21930311CEEE30D0F936BDF649B5B7BC93DC2FFC(L_23, L_24, NULL);
		NullCheck(L_16);
		(L_16)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_multiply(L_17, 2))), (int16_t)((int16_t)L_25));
	}

IL_0068:
	{
		int32_t L_26 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add(L_26, 1));
	}

IL_006c:
	{
		int32_t L_27 = V_3;
		int32_t L_28 = ___1_max_code;
		if ((((int32_t)L_27) <= ((int32_t)L_28)))
		{
			goto IL_0033;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Tree_bi_reverse_m21930311CEEE30D0F936BDF649B5B7BC93DC2FFC (int32_t ___0_code, int32_t ___1_len, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		V_0 = 0;
	}

IL_0002:
	{
		int32_t L_0 = V_0;
		int32_t L_1 = ___0_code;
		V_0 = ((int32_t)(L_0|((int32_t)(L_1&1))));
		int32_t L_2 = ___0_code;
		___0_code = ((int32_t)(L_2>>1));
		int32_t L_3 = V_0;
		V_0 = ((int32_t)(L_3<<1));
		int32_t L_4 = ___1_len;
		int32_t L_5 = ((int32_t)il2cpp_codegen_subtract(L_4, 1));
		___1_len = L_5;
		if ((((int32_t)L_5) > ((int32_t)0)))
		{
			goto IL_0002;
		}
	}
	{
		int32_t L_6 = V_0;
		return ((int32_t)(L_6>>1));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Tree__cctor_m54DA162F8CFECD03998CE1DFAC8E186FA3EA99B5 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEB_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEC_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DED_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEE_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEF_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DF0_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DF1_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DF2_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)29));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEB_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___extra_lbits = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___extra_lbits), (void*)L_1);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_3 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)30));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEC_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___extra_dbits = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___extra_dbits), (void*)L_4);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_6 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)19));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_7 = L_6;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_8 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DED_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_7, L_8, NULL);
		((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___extra_blbits = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___extra_blbits), (void*)L_7);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_9 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)19));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_10 = L_9;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_11 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEE_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_10, L_11, NULL);
		((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___bl_order = L_10;
		Il2CppCodeGenWriteBarrier((void**)(&((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___bl_order), (void*)L_10);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_12 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)512));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_13 = L_12;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_14 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DEF_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_13, L_14, NULL);
		((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->____dist_code = L_13;
		Il2CppCodeGenWriteBarrier((void**)(&((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->____dist_code), (void*)L_13);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_15 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)256));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_16 = L_15;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_17 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DF0_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_16, L_17, NULL);
		((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->____length_code = L_16;
		Il2CppCodeGenWriteBarrier((void**)(&((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->____length_code), (void*)L_16);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_18 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)29));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_19 = L_18;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_20 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DF1_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_19, L_20, NULL);
		((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___base_length = L_19;
		Il2CppCodeGenWriteBarrier((void**)(&((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___base_length), (void*)L_19);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_21 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)30));
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_22 = L_21;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_23 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7Bf1ec6559U2Da1a6U2D449fU2D8081U2D69e52363ea60U7D_t939936A0DC4CA2B0C86C0D69E494E6E2F7B680FE____U24fieldU2DF2_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_22, L_23, NULL);
		((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___base_dist = L_22;
		Il2CppCodeGenWriteBarrier((void**)(&((Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t9BC8BC5E2E2AB3DAE65BFF67E0BCC9825A0983CD_il2cpp_TypeInfo_var))->___base_dist), (void*)L_22);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZOutputStream__ctor_m5144E4068CFE22C2AD1E34DEA45808CF1CD2C502 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* ___0_output, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_0 = (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159*)il2cpp_codegen_object_new(ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159_il2cpp_TypeInfo_var);
		ZStream__ctor_m3F4505E8C1CCB3315F19DD3D4345AE22856E1F75(L_0, NULL);
		__this->___z = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___z), (void*)L_0);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)512));
		__this->___buf = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___buf), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_2 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)1);
		__this->___buf1 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___buf1), (void*)L_2);
		il2cpp_codegen_runtime_class_init_inline(Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE_il2cpp_TypeInfo_var);
		Stream__ctor_mE8B074A0EBEB026FFF14062AB4B8A78E17EFFBF0(__this, NULL);
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_3 = ___0_output;
		__this->___output = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___output), (void*)L_3);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_4 = __this->___z;
		NullCheck(L_4);
		int32_t L_5;
		L_5 = ZStream_inflateInit_m143D4D0243CA113657D2588ECB76683FDB337CA4(L_4, NULL);
		__this->___compress = (bool)0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ZOutputStream_get_CanRead_m96FA1646F291C93A56601D1D74611CCFE73EFA32 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, const RuntimeMethod* method) 
{
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ZOutputStream_get_CanSeek_m0F54DE8D582A0519194A9229097E1E7A448DE639 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, const RuntimeMethod* method) 
{
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ZOutputStream_get_CanWrite_mE84AD244AD7FE8E05A1837A3F900E060EC5E7E77 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___closed;
		return (bool)((((int32_t)L_0) == ((int32_t)0))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZOutputStream_set_FlushMode_m9D051BB3B38955679ECE06D50C4E53179C200345 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___flushLevel = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t ZOutputStream_get_Length_mB0EE6639C42CFE697F7AC0F459D6DE666C2167F6 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZOutputStream_get_Length_mB0EE6639C42CFE697F7AC0F459D6DE666C2167F6_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t ZOutputStream_get_Position_m058592C46002ADA486B37A63B86751B57503A47A (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZOutputStream_get_Position_m058592C46002ADA486B37A63B86751B57503A47A_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZOutputStream_set_Position_mF3AB2E5F71D00FB0891C44BF4A731BE074F2C7BF (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, int64_t ___0_value, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZOutputStream_set_Position_mF3AB2E5F71D00FB0891C44BF4A731BE074F2C7BF_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZOutputStream_Close_mEECDE0E40E7C5E5B4341E3A7225408486009D156 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, const RuntimeMethod* method) 
{
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	{
		bool L_0 = __this->___closed;
		if (!L_0)
		{
			goto IL_000c;
		}
	}
	{
		return;
	}

IL_000c:
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_0022:
			{
				__this->___closed = (bool)1;
				VirtualActionInvoker0::Invoke(38, __this);
				Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_1 = __this->___output;
				NullCheck(L_1);
				VirtualActionInvoker0::Invoke(18, L_1);
				__this->___output = (Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE*)NULL;
				Il2CppCodeGenWriteBarrier((void**)(&__this->___output), (void*)(Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE*)NULL);
				return;
			}
		});
		try
		{

IL_000c_1:
			try
			{

IL_000c_2:
				VirtualActionInvoker0::Invoke(39, __this);
				goto IL_001d_1;
			}
			catch(Il2CppExceptionWrapper& e)
			{
				if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
				{
					IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
					goto CATCH_0017_1;
				}
				throw e;
			}

CATCH_0017_1:
			{
				IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910* L_2 = ((IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910*)IL2CPP_GET_ACTIVE_EXCEPTION(IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910*));;
				IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
				goto IL_001d_1;
			}

IL_001d_1:
			{
				goto IL_0042;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0042:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZOutputStream_End_m2429F4D8297C566BFDA4AE22340D2D807E564AA6 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, const RuntimeMethod* method) 
{
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_0 = __this->___z;
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		return;
	}

IL_000c:
	{
		bool L_1 = __this->___compress;
		if (!L_1)
		{
			goto IL_0028;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_2 = __this->___z;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = ZStream_deflateEnd_mF650C79EFE4A71E58A815EE840EDEFCB125B4C6E(L_2, NULL);
		goto IL_0034;
	}

IL_0028:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_4 = __this->___z;
		NullCheck(L_4);
		int32_t L_5;
		L_5 = ZStream_inflateEnd_m5F7D9F5A98C6E0A114C74524939232063F1807ED(L_4, NULL);
	}

IL_0034:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_6 = __this->___z;
		NullCheck(L_6);
		ZStream_free_mC316D8E6ECB2DA4AC952270C4D2A50456A214AE7(L_6, NULL);
		__this->___z = (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___z), (void*)(ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159*)NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZOutputStream_Finish_mB1EE1A4B30EA2CF845108C50B0D2655FA62E1EB6 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t G_B3_0 = 0;
	String_t* G_B8_0 = NULL;

IL_0000:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_0 = __this->___z;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = __this->___buf;
		NullCheck(L_0);
		L_0->___next_out = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&L_0->___next_out), (void*)L_1);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_2 = __this->___z;
		NullCheck(L_2);
		L_2->___next_out_index = 0;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_3 = __this->___z;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = __this->___buf;
		NullCheck(L_4);
		NullCheck(L_3);
		L_3->___avail_out = ((int32_t)(((RuntimeArray*)L_4)->max_length));
		bool L_5 = __this->___compress;
		if (!L_5)
		{
			goto IL_004c;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_6 = __this->___z;
		NullCheck(L_6);
		int32_t L_7;
		L_7 = ZStream_deflate_mC8B68294A59CED32C2FCEA8AA012D94932517CCB(L_6, 4, NULL);
		G_B3_0 = L_7;
		goto IL_0058;
	}

IL_004c:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_8 = __this->___z;
		NullCheck(L_8);
		int32_t L_9;
		L_9 = ZStream_inflate_mB6AE79419A82CEBC6DDBB92BE75155EE6CE56DA7(L_8, 4, NULL);
		G_B3_0 = L_9;
	}

IL_0058:
	{
		V_0 = G_B3_0;
		int32_t L_10 = V_0;
		if ((((int32_t)L_10) == ((int32_t)1)))
		{
			goto IL_009b;
		}
	}
	{
		int32_t L_11 = V_0;
		if (!L_11)
		{
			goto IL_009b;
		}
	}
	{
		bool L_12 = __this->___compress;
		if (!L_12)
		{
			goto IL_007b;
		}
	}
	{
		G_B8_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralDC4A06A0DE599F745DBDD44A6FDE6212859D3A5F));
		goto IL_0080;
	}

IL_007b:
	{
		G_B8_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralC1E0482ABDB4530F47C01C2A81FB06ED6E98A110));
	}

IL_0080:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_13 = __this->___z;
		NullCheck(L_13);
		String_t* L_14 = L_13->___msg;
		String_t* L_15;
		L_15 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(G_B8_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral81B54C81CE5770A2FB716FE3138FA18CE998793D)), L_14, NULL);
		IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910* L_16 = (IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910_il2cpp_TypeInfo_var)));
		IOException__ctor_mE0612A16064F93C7EBB468D6874777BD70CB50CA(L_16, L_15, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_16, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZOutputStream_Finish_mB1EE1A4B30EA2CF845108C50B0D2655FA62E1EB6_RuntimeMethod_var)));
	}

IL_009b:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_17 = __this->___buf;
		NullCheck(L_17);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_18 = __this->___z;
		NullCheck(L_18);
		int32_t L_19 = L_18->___avail_out;
		V_1 = ((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_17)->max_length)), L_19));
		int32_t L_20 = V_1;
		if ((((int32_t)L_20) <= ((int32_t)0)))
		{
			goto IL_00ca;
		}
	}
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_21 = __this->___output;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_22 = __this->___buf;
		int32_t L_23 = V_1;
		NullCheck(L_21);
		VirtualActionInvoker3< ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(34, L_21, L_22, 0, L_23);
	}

IL_00ca:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_24 = __this->___z;
		NullCheck(L_24);
		int32_t L_25 = L_24->___avail_in;
		if ((((int32_t)L_25) > ((int32_t)0)))
		{
			goto IL_0000;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_26 = __this->___z;
		NullCheck(L_26);
		int32_t L_27 = L_26->___avail_out;
		if (!L_27)
		{
			goto IL_0000;
		}
	}
	{
		VirtualActionInvoker0::Invoke(20, __this);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZOutputStream_Flush_m754A7B395E1C8BF71AD4754FA043161EBB63AE54 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, const RuntimeMethod* method) 
{
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_0 = __this->___output;
		NullCheck(L_0);
		VirtualActionInvoker0::Invoke(20, L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZOutputStream_Read_m5CC1684F4D0FDD3D45F8A00A4832FFFC7C288BD2 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, int32_t ___1_offset, int32_t ___2_count, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZOutputStream_Read_m5CC1684F4D0FDD3D45F8A00A4832FFFC7C288BD2_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t ZOutputStream_Seek_m84B5ED0E1557F2D0F865AE92E9E488B1E2584810 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, int64_t ___0_offset, int32_t ___1_origin, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZOutputStream_Seek_m84B5ED0E1557F2D0F865AE92E9E488B1E2584810_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZOutputStream_SetLength_m339EBB86CB8188B04EBC9576B7371BCC6BFBE8E9 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, int64_t ___0_value, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZOutputStream_SetLength_m339EBB86CB8188B04EBC9576B7371BCC6BFBE8E9_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZOutputStream_Write_m9E72C9FBE8DC2FF250209F4A20410E70C5B6CEB0 (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_b, int32_t ___1_off, int32_t ___2_len, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t G_B6_0 = 0;
	String_t* G_B10_0 = NULL;
	{
		int32_t L_0 = ___2_len;
		if (L_0)
		{
			goto IL_0007;
		}
	}
	{
		return;
	}

IL_0007:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_1 = __this->___z;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_2 = ___0_b;
		NullCheck(L_1);
		L_1->___next_in = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___next_in), (void*)L_2);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_3 = __this->___z;
		int32_t L_4 = ___1_off;
		NullCheck(L_3);
		L_3->___next_in_index = L_4;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_5 = __this->___z;
		int32_t L_6 = ___2_len;
		NullCheck(L_5);
		L_5->___avail_in = L_6;
	}

IL_002b:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_7 = __this->___z;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_8 = __this->___buf;
		NullCheck(L_7);
		L_7->___next_out = L_8;
		Il2CppCodeGenWriteBarrier((void**)(&L_7->___next_out), (void*)L_8);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_9 = __this->___z;
		NullCheck(L_9);
		L_9->___next_out_index = 0;
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_10 = __this->___z;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_11 = __this->___buf;
		NullCheck(L_11);
		NullCheck(L_10);
		L_10->___avail_out = ((int32_t)(((RuntimeArray*)L_11)->max_length));
		bool L_12 = __this->___compress;
		if (!L_12)
		{
			goto IL_007c;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_13 = __this->___z;
		int32_t L_14 = __this->___flushLevel;
		NullCheck(L_13);
		int32_t L_15;
		L_15 = ZStream_deflate_mC8B68294A59CED32C2FCEA8AA012D94932517CCB(L_13, L_14, NULL);
		G_B6_0 = L_15;
		goto IL_008d;
	}

IL_007c:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_16 = __this->___z;
		int32_t L_17 = __this->___flushLevel;
		NullCheck(L_16);
		int32_t L_18;
		L_18 = ZStream_inflate_mB6AE79419A82CEBC6DDBB92BE75155EE6CE56DA7(L_16, L_17, NULL);
		G_B6_0 = L_18;
	}

IL_008d:
	{
		V_0 = G_B6_0;
		int32_t L_19 = V_0;
		if (!L_19)
		{
			goto IL_00c9;
		}
	}
	{
		bool L_20 = __this->___compress;
		if (!L_20)
		{
			goto IL_00a9;
		}
	}
	{
		G_B10_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralDC4A06A0DE599F745DBDD44A6FDE6212859D3A5F));
		goto IL_00ae;
	}

IL_00a9:
	{
		G_B10_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralC1E0482ABDB4530F47C01C2A81FB06ED6E98A110));
	}

IL_00ae:
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_21 = __this->___z;
		NullCheck(L_21);
		String_t* L_22 = L_21->___msg;
		String_t* L_23;
		L_23 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(G_B10_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral81B54C81CE5770A2FB716FE3138FA18CE998793D)), L_22, NULL);
		IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910* L_24 = (IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910_il2cpp_TypeInfo_var)));
		IOException__ctor_mE0612A16064F93C7EBB468D6874777BD70CB50CA(L_24, L_23, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_24, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZOutputStream_Write_m9E72C9FBE8DC2FF250209F4A20410E70C5B6CEB0_RuntimeMethod_var)));
	}

IL_00c9:
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_25 = __this->___output;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_26 = __this->___buf;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_27 = __this->___buf;
		NullCheck(L_27);
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_28 = __this->___z;
		NullCheck(L_28);
		int32_t L_29 = L_28->___avail_out;
		NullCheck(L_25);
		VirtualActionInvoker3< ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(34, L_25, L_26, 0, ((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_27)->max_length)), L_29)));
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_30 = __this->___z;
		NullCheck(L_30);
		int32_t L_31 = L_30->___avail_in;
		if ((((int32_t)L_31) > ((int32_t)0)))
		{
			goto IL_002b;
		}
	}
	{
		ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* L_32 = __this->___z;
		NullCheck(L_32);
		int32_t L_33 = L_32->___avail_out;
		if (!L_33)
		{
			goto IL_002b;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZOutputStream_WriteByte_mF12D7113382D38EC2A1817BFCF30FA0EF1E1B1DC (ZOutputStream_tD3083C716E31A9C827D6F6F8FD45CA5F204A90C2* __this, uint8_t ___0_b, const RuntimeMethod* method) 
{
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = __this->___buf1;
		uint8_t L_1 = ___0_b;
		NullCheck(L_0);
		(L_0)->SetAt(static_cast<il2cpp_array_size_t>(0), (uint8_t)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_2 = __this->___buf1;
		VirtualActionInvoker3< ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(34, __this, L_2, 0, 1);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZStream__ctor_m3F4505E8C1CCB3315F19DD3D4345AE22856E1F75 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D* L_0 = (Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D*)il2cpp_codegen_object_new(Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D_il2cpp_TypeInfo_var);
		Adler32__ctor_m64048ACD59254C7F1277C4A8DA58FF141166A236(L_0, NULL);
		__this->____adler = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____adler), (void*)L_0);
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_inflateInit_m143D4D0243CA113657D2588ECB76683FDB337CA4 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0;
		L_0 = ZStream_inflateInit_m6BAD260A712D100475C473BD11C65084E61AC9E5(__this, ((int32_t)15), NULL);
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_inflateInit_m6BAD260A712D100475C473BD11C65084E61AC9E5 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, int32_t ___0_w, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_w;
		int32_t L_1;
		L_1 = ZStream_inflateInit_mFEA1826439939858F3A3F6D9B23BA7FFC22A0F83(__this, L_0, (bool)0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_inflateInit_mFEA1826439939858F3A3F6D9B23BA7FFC22A0F83 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, int32_t ___0_w, bool ___1_nowrap, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* G_B2_0 = NULL;
	Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* G_B2_1 = NULL;
	ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* G_B1_0 = NULL;
	Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* G_B1_1 = NULL;
	int32_t G_B3_0 = 0;
	ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* G_B3_1 = NULL;
	Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* G_B3_2 = NULL;
	{
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_0 = (Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50*)il2cpp_codegen_object_new(Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50_il2cpp_TypeInfo_var);
		Inflate__ctor_mC3FDB4D5246A093EE60FEF5698867D51DFB27104(L_0, NULL);
		__this->___istate = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___istate), (void*)L_0);
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_1 = __this->___istate;
		bool L_2 = ___1_nowrap;
		if (!L_2)
		{
			G_B2_0 = __this;
			G_B2_1 = L_1;
			goto IL_001f;
		}
		G_B1_0 = __this;
		G_B1_1 = L_1;
	}
	{
		int32_t L_3 = ___0_w;
		G_B3_0 = ((-L_3));
		G_B3_1 = G_B1_0;
		G_B3_2 = G_B1_1;
		goto IL_0020;
	}

IL_001f:
	{
		int32_t L_4 = ___0_w;
		G_B3_0 = L_4;
		G_B3_1 = G_B2_0;
		G_B3_2 = G_B2_1;
	}

IL_0020:
	{
		NullCheck(G_B3_2);
		int32_t L_5;
		L_5 = Inflate_inflateInit_mE54CCCBA0F6A9571D5E2AA707A3240C286815C86(G_B3_2, G_B3_1, G_B3_0, NULL);
		return L_5;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_inflate_mB6AE79419A82CEBC6DDBB92BE75155EE6CE56DA7 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, int32_t ___0_f, const RuntimeMethod* method) 
{
	{
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_0 = __this->___istate;
		if (L_0)
		{
			goto IL_000e;
		}
	}
	{
		return ((int32_t)-2);
	}

IL_000e:
	{
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_1 = __this->___istate;
		int32_t L_2 = ___0_f;
		NullCheck(L_1);
		int32_t L_3;
		L_3 = Inflate_inflate_m51AE19089B0B082180C4E6C97A88561386E3821E(L_1, __this, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_inflateEnd_m5F7D9F5A98C6E0A114C74524939232063F1807ED (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_0 = __this->___istate;
		if (L_0)
		{
			goto IL_000e;
		}
	}
	{
		return ((int32_t)-2);
	}

IL_000e:
	{
		Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50* L_1 = __this->___istate;
		NullCheck(L_1);
		int32_t L_2;
		L_2 = Inflate_inflateEnd_mF5A412434A2CE53A5D62205EFAF86FD8485CA85E(L_1, __this, NULL);
		V_0 = L_2;
		__this->___istate = (Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___istate), (void*)(Inflate_t9B7CDBD91EFE78979F272508DB17C199F1B5FE50*)NULL);
		int32_t L_3 = V_0;
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_deflateInit_m75EAE6A53CA885B5BDC27B67F9CDA473C9B7E043 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, int32_t ___0_level, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_level;
		int32_t L_1;
		L_1 = ZStream_deflateInit_m20294775020642696F4FB1210A3BAA40D0486B71(__this, L_0, ((int32_t)15), NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_deflateInit_m20294775020642696F4FB1210A3BAA40D0486B71 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, int32_t ___0_level, int32_t ___1_bits, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_level;
		int32_t L_1 = ___1_bits;
		int32_t L_2;
		L_2 = ZStream_deflateInit_m9175B61C308978E5262D36D2A0FDF726BCC83D5E(__this, L_0, L_1, (bool)0, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_deflateInit_m9175B61C308978E5262D36D2A0FDF726BCC83D5E (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, int32_t ___0_level, int32_t ___1_bits, bool ___2_nowrap, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t G_B2_0 = 0;
	ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* G_B2_1 = NULL;
	Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* G_B2_2 = NULL;
	int32_t G_B1_0 = 0;
	ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* G_B1_1 = NULL;
	Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* G_B1_2 = NULL;
	int32_t G_B3_0 = 0;
	int32_t G_B3_1 = 0;
	ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* G_B3_2 = NULL;
	Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* G_B3_3 = NULL;
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_0 = (Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B*)il2cpp_codegen_object_new(Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B_il2cpp_TypeInfo_var);
		Deflate__ctor_mE92BACC46F4569B41FA175D382097918B79362EB(L_0, NULL);
		__this->___dstate = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___dstate), (void*)L_0);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_1 = __this->___dstate;
		int32_t L_2 = ___0_level;
		bool L_3 = ___2_nowrap;
		if (!L_3)
		{
			G_B2_0 = L_2;
			G_B2_1 = __this;
			G_B2_2 = L_1;
			goto IL_0020;
		}
		G_B1_0 = L_2;
		G_B1_1 = __this;
		G_B1_2 = L_1;
	}
	{
		int32_t L_4 = ___1_bits;
		G_B3_0 = ((-L_4));
		G_B3_1 = G_B1_0;
		G_B3_2 = G_B1_1;
		G_B3_3 = G_B1_2;
		goto IL_0021;
	}

IL_0020:
	{
		int32_t L_5 = ___1_bits;
		G_B3_0 = L_5;
		G_B3_1 = G_B2_0;
		G_B3_2 = G_B2_1;
		G_B3_3 = G_B2_2;
	}

IL_0021:
	{
		NullCheck(G_B3_3);
		int32_t L_6;
		L_6 = Deflate_deflateInit_m9E749DAAA1A022941B744D9280C7610FAA4BEC1B(G_B3_3, G_B3_2, G_B3_1, G_B3_0, NULL);
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_deflate_mC8B68294A59CED32C2FCEA8AA012D94932517CCB (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, int32_t ___0_flush, const RuntimeMethod* method) 
{
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_0 = __this->___dstate;
		if (L_0)
		{
			goto IL_000e;
		}
	}
	{
		return ((int32_t)-2);
	}

IL_000e:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_1 = __this->___dstate;
		int32_t L_2 = ___0_flush;
		NullCheck(L_1);
		int32_t L_3;
		L_3 = Deflate_deflate_m497186F3E3DA81CEFC3A249ACA43B7420FF84E2E(L_1, __this, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_deflateEnd_mF650C79EFE4A71E58A815EE840EDEFCB125B4C6E (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_0 = __this->___dstate;
		if (L_0)
		{
			goto IL_000e;
		}
	}
	{
		return ((int32_t)-2);
	}

IL_000e:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_1 = __this->___dstate;
		NullCheck(L_1);
		int32_t L_2;
		L_2 = Deflate_deflateEnd_m9795852C677FC77B33A8008BEE56C8DE6CBA498B(L_1, NULL);
		V_0 = L_2;
		__this->___dstate = (Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___dstate), (void*)(Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B*)NULL);
		int32_t L_3 = V_0;
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZStream_flush_pending_mA344B69E0AD90E8D464AABCB4385E1DB47317B5B (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_0 = __this->___dstate;
		NullCheck(L_0);
		int32_t L_1 = L_0->___pending;
		V_0 = L_1;
		int32_t L_2 = V_0;
		int32_t L_3 = __this->___avail_out;
		if ((((int32_t)L_2) <= ((int32_t)L_3)))
		{
			goto IL_001f;
		}
	}
	{
		int32_t L_4 = __this->___avail_out;
		V_0 = L_4;
	}

IL_001f:
	{
		int32_t L_5 = V_0;
		if (L_5)
		{
			goto IL_0026;
		}
	}
	{
		return;
	}

IL_0026:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_6 = __this->___dstate;
		NullCheck(L_6);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_7 = L_6->___pending_buf;
		NullCheck(L_7);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_8 = __this->___dstate;
		NullCheck(L_8);
		int32_t L_9 = L_8->___pending_out;
		if ((((int32_t)((int32_t)(((RuntimeArray*)L_7)->max_length))) <= ((int32_t)L_9)))
		{
			goto IL_008a;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_10 = __this->___next_out;
		NullCheck(L_10);
		int32_t L_11 = __this->___next_out_index;
		if ((((int32_t)((int32_t)(((RuntimeArray*)L_10)->max_length))) <= ((int32_t)L_11)))
		{
			goto IL_008a;
		}
	}
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_12 = __this->___dstate;
		NullCheck(L_12);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_13 = L_12->___pending_buf;
		NullCheck(L_13);
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_14 = __this->___dstate;
		NullCheck(L_14);
		int32_t L_15 = L_14->___pending_out;
		int32_t L_16 = V_0;
		if ((((int32_t)((int32_t)(((RuntimeArray*)L_13)->max_length))) < ((int32_t)((int32_t)il2cpp_codegen_add(L_15, L_16)))))
		{
			goto IL_008a;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_17 = __this->___next_out;
		NullCheck(L_17);
		int32_t L_18 = __this->___next_out_index;
		int32_t L_19 = V_0;
		if ((((int32_t)((int32_t)(((RuntimeArray*)L_17)->max_length))) >= ((int32_t)((int32_t)il2cpp_codegen_add(L_18, L_19)))))
		{
			goto IL_008a;
		}
	}

IL_008a:
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_20 = __this->___dstate;
		NullCheck(L_20);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_21 = L_20->___pending_buf;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_22 = __this->___dstate;
		NullCheck(L_22);
		int32_t L_23 = L_22->___pending_out;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_24 = __this->___next_out;
		int32_t L_25 = __this->___next_out_index;
		int32_t L_26 = V_0;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_21, L_23, (RuntimeArray*)L_24, L_25, L_26, NULL);
		int32_t L_27 = __this->___next_out_index;
		int32_t L_28 = V_0;
		__this->___next_out_index = ((int32_t)il2cpp_codegen_add(L_27, L_28));
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_29 = __this->___dstate;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_30 = L_29;
		NullCheck(L_30);
		int32_t L_31 = L_30->___pending_out;
		int32_t L_32 = V_0;
		NullCheck(L_30);
		L_30->___pending_out = ((int32_t)il2cpp_codegen_add(L_31, L_32));
		int64_t L_33 = __this->___total_out;
		int32_t L_34 = V_0;
		__this->___total_out = ((int64_t)il2cpp_codegen_add(L_33, ((int64_t)L_34)));
		int32_t L_35 = __this->___avail_out;
		int32_t L_36 = V_0;
		__this->___avail_out = ((int32_t)il2cpp_codegen_subtract(L_35, L_36));
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_37 = __this->___dstate;
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_38 = L_37;
		NullCheck(L_38);
		int32_t L_39 = L_38->___pending;
		int32_t L_40 = V_0;
		NullCheck(L_38);
		L_38->___pending = ((int32_t)il2cpp_codegen_subtract(L_39, L_40));
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_41 = __this->___dstate;
		NullCheck(L_41);
		int32_t L_42 = L_41->___pending;
		if (L_42)
		{
			goto IL_011f;
		}
	}
	{
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_43 = __this->___dstate;
		NullCheck(L_43);
		L_43->___pending_out = 0;
	}

IL_011f:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZStream_read_buf_m66D7075FAB672468591A04A13E2730792600BA78 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buf, int32_t ___1_start, int32_t ___2_size, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0 = __this->___avail_in;
		V_0 = L_0;
		int32_t L_1 = V_0;
		int32_t L_2 = ___2_size;
		if ((((int32_t)L_1) <= ((int32_t)L_2)))
		{
			goto IL_0010;
		}
	}
	{
		int32_t L_3 = ___2_size;
		V_0 = L_3;
	}

IL_0010:
	{
		int32_t L_4 = V_0;
		if (L_4)
		{
			goto IL_0018;
		}
	}
	{
		return 0;
	}

IL_0018:
	{
		int32_t L_5 = __this->___avail_in;
		int32_t L_6 = V_0;
		__this->___avail_in = ((int32_t)il2cpp_codegen_subtract(L_5, L_6));
		Deflate_tCEE89D77A9FC96FE6CE6AA8B46CD6BDAC785818B* L_7 = __this->___dstate;
		NullCheck(L_7);
		int32_t L_8 = L_7->___noheader;
		if (L_8)
		{
			goto IL_005a;
		}
	}
	{
		Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D* L_9 = __this->____adler;
		int64_t L_10 = __this->___adler;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_11 = __this->___next_in;
		int32_t L_12 = __this->___next_in_index;
		int32_t L_13 = V_0;
		NullCheck(L_9);
		int64_t L_14;
		L_14 = Adler32_adler32_m47B915EF9E1AD64F5504CC03340A1E09ED08A416(L_9, L_10, L_11, L_12, L_13, NULL);
		__this->___adler = L_14;
	}

IL_005a:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_15 = __this->___next_in;
		int32_t L_16 = __this->___next_in_index;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_17 = ___0_buf;
		int32_t L_18 = ___1_start;
		int32_t L_19 = V_0;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_15, L_16, (RuntimeArray*)L_17, L_18, L_19, NULL);
		int32_t L_20 = __this->___next_in_index;
		int32_t L_21 = V_0;
		__this->___next_in_index = ((int32_t)il2cpp_codegen_add(L_20, L_21));
		int64_t L_22 = __this->___total_in;
		int32_t L_23 = V_0;
		__this->___total_in = ((int64_t)il2cpp_codegen_add(L_22, ((int64_t)L_23)));
		int32_t L_24 = V_0;
		return L_24;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZStream_free_mC316D8E6ECB2DA4AC952270C4D2A50456A214AE7 (ZStream_t25DA4BB03EB5A6CA0B9D8CE0E180446E11C78159* __this, const RuntimeMethod* method) 
{
	{
		__this->___next_in = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___next_in), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)NULL);
		__this->___next_out = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___next_out), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)NULL);
		__this->___msg = (String_t*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___msg), (void*)(String_t*)NULL);
		__this->____adler = (Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____adler), (void*)(Adler32_t587A697DD5A98C5043F13B7EE429D016D047399D*)NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
