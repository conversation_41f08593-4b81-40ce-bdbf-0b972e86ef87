﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Environments_get_Current_m672842AC5C842317A6EE2D4EBE3AEDAE201D141C (void);
extern void Environments_set_Current_mF2F9146800431DE9BBE5F95E94919DF01477BB84 (void);
extern void Environments__ctor_m907828E3A910416469C90CF39C6D863F6C0B5A6B (void);
static Il2CppMethodPointer s_methodPointers[3] = 
{
	Environments_get_Current_m672842AC5C842317A6EE2D4EBE3AEDAE201D141C,
	Environments_set_Current_mF2F9146800431DE9BBE5F95E94919DF01477BB84,
	Environments__ctor_m907828E3A910416469C90CF39C6D863F6C0B5A6B,
};
static const int32_t s_InvokerIndices[3] = 
{
	6398,
	5349,
	6521,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Services_Core_Environments_Internal_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Services_Core_Environments_Internal_CodeGenModule = 
{
	"Unity.Services.Core.Environments.Internal.dll",
	3,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
