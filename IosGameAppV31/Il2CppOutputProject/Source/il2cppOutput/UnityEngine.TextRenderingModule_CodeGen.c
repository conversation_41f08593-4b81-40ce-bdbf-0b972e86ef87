﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void TextGenerationSettings_CompareColors_m29E454405FB3871CA77CC7F94DAB1B4320BD5E53 (void);
extern void TextGenerationSettings_CompareVector2_mF3EE374922B2ECE13BA7B01B26199494A30C2E2E (void);
extern void TextGenerationSettings_Equals_m04DE9655555622E3A4EECC2EAB3BC36B38E20840 (void);
extern void TextGenerator__ctor_m2BFF25B11D7FD0FA4F8E1B04D107219A30E8A187 (void);
extern void TextGenerator__ctor_m05E7CFDC682F880A590F4AA840F81647844A0A32 (void);
extern void TextGenerator_Finalize_mD3C40A16FD4661E4712C85586430041C72E914F7 (void);
extern void TextGenerator_System_IDisposable_Dispose_m5B2B52DA9C73CE736E908030738002D6D860227F (void);
extern void TextGenerator_get_characterCountVisible_m407C344C5A13A26471315449899B927FF3D3CDFC (void);
extern void TextGenerator_ValidatedSettings_mAE5128305E953EE41E5E655675323896EF1154AE (void);
extern void TextGenerator_Invalidate_m999CE96E2F02E326092DFEB53EE8BC08CAAF8260 (void);
extern void TextGenerator_GetCharacters_mB7956402E0C66D9F9D51A3ADFCA84BE83BB54D90 (void);
extern void TextGenerator_GetLines_mFA8508172C251E1E4BBB1FBB734C82C81EF9330F (void);
extern void TextGenerator_GetVertices_m97C7D4CB9B122A10BA5921BC38F83A245EB1B97E (void);
extern void TextGenerator_GetPreferredWidth_mDDE43A5C8BDD7BBD235AE740C4C2A293A92BD28E (void);
extern void TextGenerator_GetPreferredHeight_m0EAF7CE0F503C97F4D35D0EE4A3AD923B472F89E (void);
extern void TextGenerator_PopulateWithErrors_m0E56C12E9D7714F4B8772422AAB2CAC9EA10F834 (void);
extern void TextGenerator_Populate_m5620AF6DBC180FC3FBE3E5F5930CF7CB65DD3CE2 (void);
extern void TextGenerator_PopulateWithError_m0F7C1CE45F372D80603C28F1E01F6FF796B95E02 (void);
extern void TextGenerator_PopulateAlways_mA99FFC0276B51F8DED3EABD1FF660130BE803EDA (void);
extern void TextGenerator_get_verts_mA197E8944ABE4831B93275662BB66EC53DE349D8 (void);
extern void TextGenerator_get_characters_mDA07F7108C6EF6F5DF61C478FDF3EB18DCE6B34A (void);
extern void TextGenerator_get_lines_m52DAB15921DC96120CA6C21A3FEB2406D4A76064 (void);
extern void TextGenerator_get_rectExtents_m55100375428EFAA89F3AC1B528E8716E615F9F2C (void);
extern void TextGenerator_get_characterCount_mD4FD13BBBEDA2AB495C045160693754E4C106FFF (void);
extern void TextGenerator_get_lineCount_mC568C375C85695478DD054B296CBEAB11D3C4108 (void);
extern void TextGenerator_Internal_Create_m66701C08DAE160892766930F831D2AB1EB4E998C (void);
extern void TextGenerator_Internal_Destroy_m0AE3865A6886503BAE5798E6D9899EF59A0285A7 (void);
extern void TextGenerator_Populate_Internal_m74A216D58183B4069BFBD6CDF624CB97A9EC4CAA (void);
extern void TextGenerator_Populate_Internal_m4DF908756A1AC26B04AC8AB12A004203366B294B (void);
extern void TextGenerator_GetVerticesInternal_m8BB7AB760D1071709A584FD9371D7549DA2A1B21 (void);
extern void TextGenerator_GetCharactersInternal_mB5157BBD4E1C42ACEDCC696D718E656BDD5E2398 (void);
extern void TextGenerator_GetLinesInternal_mDAC83E88FD1FD79CCCE294E49E02ED748CFC8E12 (void);
extern void TextGenerator_get_rectExtents_Injected_m2AB029C462FB27B9643C3EA2EB345E0EB011B2B8 (void);
extern void TextGenerator_Populate_Internal_Injected_m694EA7CA7449D60B16643460DA13ABC6BE0F2947 (void);
extern void TextMesh_set_text_mDF79D39638ED82797D0B0B3BB9E6B10712F8EA9E (void);
extern void TextMesh_set_font_m7E407CAEDBB382B95B70069D8FAB8A9E74EAAA74 (void);
extern void CharacterInfo_get_advance_m0AAC68ABA1BD5B184B53E01F270BA4FD1D2C2B9A (void);
extern void CharacterInfo_get_glyphWidth_m874E46221A743197C48F80D6F51B1F06F7E06D5D (void);
extern void CharacterInfo_get_minY_m9BC2A892418F3E986B0EAFE56F28FABD8CAA4A3A (void);
extern void CharacterInfo_get_maxY_m2439B7BB55176E566ECE4CAB87B3AA252DF46927 (void);
extern void CharacterInfo_get_minX_m69B3FB781E712AE29CDEC596F9932862FB553328 (void);
extern void CharacterInfo_get_maxX_m55625E1CD75FA327A33D2DA9C4A15C846CBB0A20 (void);
extern void CharacterInfo_get_uvBottomLeftUnFlipped_mCBA8417A773038E225D431DA5A5D67445AD13244 (void);
extern void CharacterInfo_get_uvBottomRightUnFlipped_m8C9B5C60873D8DA8232F37B87B03D6B235A0997E (void);
extern void CharacterInfo_get_uvTopRightUnFlipped_m13A645BC543D634A53EE7C0282C64F68535EAC29 (void);
extern void CharacterInfo_get_uvTopLeftUnFlipped_m5DE3E983B48D8E5611FF7C8F78827A1841DCD2CB (void);
extern void CharacterInfo_get_uvBottomLeft_mDBE99341E4D50DB991F249E438891D118AB2DF9F (void);
extern void CharacterInfo_get_uvBottomRight_m2A6F8FA7B8C05E65653AE2986E0889CCEAE3EF17 (void);
extern void CharacterInfo_get_uvTopRight_m706389A181F4E4EAAB886C08D6E1A9C3D8FD2C16 (void);
extern void CharacterInfo_get_uvTopLeft_m8366979523BC656BF87BA43327604E6870BC6A33 (void);
extern void UIVertex__cctor_mF98CC8F21D6CD8703B47E168E976BBE15E0F6C62 (void);
extern void Font_add_textureRebuilt_m0BBB44846C17A580B078599DA5AE231DA9D6DAD7 (void);
extern void Font_remove_textureRebuilt_mB7EF9EEE803E9C70AF4217190B49C83FE287904A (void);
extern void Font_get_material_m61ABDEC14C6D659DDC5A4F080023699116C17364 (void);
extern void Font_get_dynamic_m5231258BFEAABA3B3EA6AB5D8C2FDCBC2AD1371C (void);
extern void Font_get_fontSize_m02C2F5C7A7C89A878F04C2087248DD46BBF9E26B (void);
extern void Font__ctor_m9106C7F312AE77F6721001A5A3143951201AC841 (void);
extern void Font__ctor_m9F4256214EE9A4A3F9C8287C21ABCAA0BCCBA461 (void);
extern void Font_CreateDynamicFontFromOSFont_m3DF4B7A70F4AD6918FC44195252D9A645B3C5D62 (void);
extern void Font_CreateDynamicFontFromOSFont_m3A62128B393F57EE1C15A100A96F591FBFFCAFE6 (void);
extern void Font_InvokeTextureRebuilt_Internal_m874D1025267C908E5FCD437B41929E4DE248B01B (void);
extern void Font_HasCharacter_m71A84FE036055880E1543D79A38FEFA495AD200B (void);
extern void Font_HasCharacter_mAB838A26F002CB5E4B4DB297F7D6836A28625B18 (void);
extern void Font_Internal_CreateFont_m97CB036BAA033DDAD87E14F9D3493A3A2D9C72B1 (void);
extern void Font_Internal_CreateDynamicFont_m5252E98E214E0B9B2D42C70A67A9A446341E050E (void);
extern void Font_GetCharacterInfo_m66CF18A6ECA8A877CA793DCC733DCF9659F398E8 (void);
extern void Font_RequestCharactersInTexture_m87509ABBEDF61305BA10B2DC65B565E57FF6DDD4 (void);
extern void FontTextureRebuildCallback__ctor_m1AF27FC83F3136E493F47015F99CE7A4E6BCA0BC (void);
extern void FontTextureRebuildCallback_Invoke_m8B52C3F4823ADBB80062209E6BA2B33202AE958D (void);
static Il2CppMethodPointer s_methodPointers[69] = 
{
	TextGenerationSettings_CompareColors_m29E454405FB3871CA77CC7F94DAB1B4320BD5E53,
	TextGenerationSettings_CompareVector2_mF3EE374922B2ECE13BA7B01B26199494A30C2E2E,
	TextGenerationSettings_Equals_m04DE9655555622E3A4EECC2EAB3BC36B38E20840,
	TextGenerator__ctor_m2BFF25B11D7FD0FA4F8E1B04D107219A30E8A187,
	TextGenerator__ctor_m05E7CFDC682F880A590F4AA840F81647844A0A32,
	TextGenerator_Finalize_mD3C40A16FD4661E4712C85586430041C72E914F7,
	TextGenerator_System_IDisposable_Dispose_m5B2B52DA9C73CE736E908030738002D6D860227F,
	TextGenerator_get_characterCountVisible_m407C344C5A13A26471315449899B927FF3D3CDFC,
	TextGenerator_ValidatedSettings_mAE5128305E953EE41E5E655675323896EF1154AE,
	TextGenerator_Invalidate_m999CE96E2F02E326092DFEB53EE8BC08CAAF8260,
	TextGenerator_GetCharacters_mB7956402E0C66D9F9D51A3ADFCA84BE83BB54D90,
	TextGenerator_GetLines_mFA8508172C251E1E4BBB1FBB734C82C81EF9330F,
	TextGenerator_GetVertices_m97C7D4CB9B122A10BA5921BC38F83A245EB1B97E,
	TextGenerator_GetPreferredWidth_mDDE43A5C8BDD7BBD235AE740C4C2A293A92BD28E,
	TextGenerator_GetPreferredHeight_m0EAF7CE0F503C97F4D35D0EE4A3AD923B472F89E,
	TextGenerator_PopulateWithErrors_m0E56C12E9D7714F4B8772422AAB2CAC9EA10F834,
	TextGenerator_Populate_m5620AF6DBC180FC3FBE3E5F5930CF7CB65DD3CE2,
	TextGenerator_PopulateWithError_m0F7C1CE45F372D80603C28F1E01F6FF796B95E02,
	TextGenerator_PopulateAlways_mA99FFC0276B51F8DED3EABD1FF660130BE803EDA,
	TextGenerator_get_verts_mA197E8944ABE4831B93275662BB66EC53DE349D8,
	TextGenerator_get_characters_mDA07F7108C6EF6F5DF61C478FDF3EB18DCE6B34A,
	TextGenerator_get_lines_m52DAB15921DC96120CA6C21A3FEB2406D4A76064,
	TextGenerator_get_rectExtents_m55100375428EFAA89F3AC1B528E8716E615F9F2C,
	TextGenerator_get_characterCount_mD4FD13BBBEDA2AB495C045160693754E4C106FFF,
	TextGenerator_get_lineCount_mC568C375C85695478DD054B296CBEAB11D3C4108,
	TextGenerator_Internal_Create_m66701C08DAE160892766930F831D2AB1EB4E998C,
	TextGenerator_Internal_Destroy_m0AE3865A6886503BAE5798E6D9899EF59A0285A7,
	TextGenerator_Populate_Internal_m74A216D58183B4069BFBD6CDF624CB97A9EC4CAA,
	TextGenerator_Populate_Internal_m4DF908756A1AC26B04AC8AB12A004203366B294B,
	TextGenerator_GetVerticesInternal_m8BB7AB760D1071709A584FD9371D7549DA2A1B21,
	TextGenerator_GetCharactersInternal_mB5157BBD4E1C42ACEDCC696D718E656BDD5E2398,
	TextGenerator_GetLinesInternal_mDAC83E88FD1FD79CCCE294E49E02ED748CFC8E12,
	TextGenerator_get_rectExtents_Injected_m2AB029C462FB27B9643C3EA2EB345E0EB011B2B8,
	TextGenerator_Populate_Internal_Injected_m694EA7CA7449D60B16643460DA13ABC6BE0F2947,
	TextMesh_set_text_mDF79D39638ED82797D0B0B3BB9E6B10712F8EA9E,
	TextMesh_set_font_m7E407CAEDBB382B95B70069D8FAB8A9E74EAAA74,
	CharacterInfo_get_advance_m0AAC68ABA1BD5B184B53E01F270BA4FD1D2C2B9A,
	CharacterInfo_get_glyphWidth_m874E46221A743197C48F80D6F51B1F06F7E06D5D,
	CharacterInfo_get_minY_m9BC2A892418F3E986B0EAFE56F28FABD8CAA4A3A,
	CharacterInfo_get_maxY_m2439B7BB55176E566ECE4CAB87B3AA252DF46927,
	CharacterInfo_get_minX_m69B3FB781E712AE29CDEC596F9932862FB553328,
	CharacterInfo_get_maxX_m55625E1CD75FA327A33D2DA9C4A15C846CBB0A20,
	CharacterInfo_get_uvBottomLeftUnFlipped_mCBA8417A773038E225D431DA5A5D67445AD13244,
	CharacterInfo_get_uvBottomRightUnFlipped_m8C9B5C60873D8DA8232F37B87B03D6B235A0997E,
	CharacterInfo_get_uvTopRightUnFlipped_m13A645BC543D634A53EE7C0282C64F68535EAC29,
	CharacterInfo_get_uvTopLeftUnFlipped_m5DE3E983B48D8E5611FF7C8F78827A1841DCD2CB,
	CharacterInfo_get_uvBottomLeft_mDBE99341E4D50DB991F249E438891D118AB2DF9F,
	CharacterInfo_get_uvBottomRight_m2A6F8FA7B8C05E65653AE2986E0889CCEAE3EF17,
	CharacterInfo_get_uvTopRight_m706389A181F4E4EAAB886C08D6E1A9C3D8FD2C16,
	CharacterInfo_get_uvTopLeft_m8366979523BC656BF87BA43327604E6870BC6A33,
	UIVertex__cctor_mF98CC8F21D6CD8703B47E168E976BBE15E0F6C62,
	Font_add_textureRebuilt_m0BBB44846C17A580B078599DA5AE231DA9D6DAD7,
	Font_remove_textureRebuilt_mB7EF9EEE803E9C70AF4217190B49C83FE287904A,
	Font_get_material_m61ABDEC14C6D659DDC5A4F080023699116C17364,
	Font_get_dynamic_m5231258BFEAABA3B3EA6AB5D8C2FDCBC2AD1371C,
	Font_get_fontSize_m02C2F5C7A7C89A878F04C2087248DD46BBF9E26B,
	Font__ctor_m9106C7F312AE77F6721001A5A3143951201AC841,
	Font__ctor_m9F4256214EE9A4A3F9C8287C21ABCAA0BCCBA461,
	Font_CreateDynamicFontFromOSFont_m3DF4B7A70F4AD6918FC44195252D9A645B3C5D62,
	Font_CreateDynamicFontFromOSFont_m3A62128B393F57EE1C15A100A96F591FBFFCAFE6,
	Font_InvokeTextureRebuilt_Internal_m874D1025267C908E5FCD437B41929E4DE248B01B,
	Font_HasCharacter_m71A84FE036055880E1543D79A38FEFA495AD200B,
	Font_HasCharacter_mAB838A26F002CB5E4B4DB297F7D6836A28625B18,
	Font_Internal_CreateFont_m97CB036BAA033DDAD87E14F9D3493A3A2D9C72B1,
	Font_Internal_CreateDynamicFont_m5252E98E214E0B9B2D42C70A67A9A446341E050E,
	Font_GetCharacterInfo_m66CF18A6ECA8A877CA793DCC733DCF9659F398E8,
	Font_RequestCharactersInTexture_m87509ABBEDF61305BA10B2DC65B565E57FF6DDD4,
	FontTextureRebuildCallback__ctor_m1AF27FC83F3136E493F47015F99CE7A4E6BCA0BC,
	FontTextureRebuildCallback_Invoke_m8B52C3F4823ADBB80062209E6BA2B33202AE958D,
};
extern void TextGenerationSettings_CompareColors_m29E454405FB3871CA77CC7F94DAB1B4320BD5E53_AdjustorThunk (void);
extern void TextGenerationSettings_CompareVector2_mF3EE374922B2ECE13BA7B01B26199494A30C2E2E_AdjustorThunk (void);
extern void TextGenerationSettings_Equals_m04DE9655555622E3A4EECC2EAB3BC36B38E20840_AdjustorThunk (void);
extern void CharacterInfo_get_advance_m0AAC68ABA1BD5B184B53E01F270BA4FD1D2C2B9A_AdjustorThunk (void);
extern void CharacterInfo_get_glyphWidth_m874E46221A743197C48F80D6F51B1F06F7E06D5D_AdjustorThunk (void);
extern void CharacterInfo_get_minY_m9BC2A892418F3E986B0EAFE56F28FABD8CAA4A3A_AdjustorThunk (void);
extern void CharacterInfo_get_maxY_m2439B7BB55176E566ECE4CAB87B3AA252DF46927_AdjustorThunk (void);
extern void CharacterInfo_get_minX_m69B3FB781E712AE29CDEC596F9932862FB553328_AdjustorThunk (void);
extern void CharacterInfo_get_maxX_m55625E1CD75FA327A33D2DA9C4A15C846CBB0A20_AdjustorThunk (void);
extern void CharacterInfo_get_uvBottomLeftUnFlipped_mCBA8417A773038E225D431DA5A5D67445AD13244_AdjustorThunk (void);
extern void CharacterInfo_get_uvBottomRightUnFlipped_m8C9B5C60873D8DA8232F37B87B03D6B235A0997E_AdjustorThunk (void);
extern void CharacterInfo_get_uvTopRightUnFlipped_m13A645BC543D634A53EE7C0282C64F68535EAC29_AdjustorThunk (void);
extern void CharacterInfo_get_uvTopLeftUnFlipped_m5DE3E983B48D8E5611FF7C8F78827A1841DCD2CB_AdjustorThunk (void);
extern void CharacterInfo_get_uvBottomLeft_mDBE99341E4D50DB991F249E438891D118AB2DF9F_AdjustorThunk (void);
extern void CharacterInfo_get_uvBottomRight_m2A6F8FA7B8C05E65653AE2986E0889CCEAE3EF17_AdjustorThunk (void);
extern void CharacterInfo_get_uvTopRight_m706389A181F4E4EAAB886C08D6E1A9C3D8FD2C16_AdjustorThunk (void);
extern void CharacterInfo_get_uvTopLeft_m8366979523BC656BF87BA43327604E6870BC6A33_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[17] = 
{
	{ 0x06000001, TextGenerationSettings_CompareColors_m29E454405FB3871CA77CC7F94DAB1B4320BD5E53_AdjustorThunk },
	{ 0x06000002, TextGenerationSettings_CompareVector2_mF3EE374922B2ECE13BA7B01B26199494A30C2E2E_AdjustorThunk },
	{ 0x06000003, TextGenerationSettings_Equals_m04DE9655555622E3A4EECC2EAB3BC36B38E20840_AdjustorThunk },
	{ 0x06000025, CharacterInfo_get_advance_m0AAC68ABA1BD5B184B53E01F270BA4FD1D2C2B9A_AdjustorThunk },
	{ 0x06000026, CharacterInfo_get_glyphWidth_m874E46221A743197C48F80D6F51B1F06F7E06D5D_AdjustorThunk },
	{ 0x06000027, CharacterInfo_get_minY_m9BC2A892418F3E986B0EAFE56F28FABD8CAA4A3A_AdjustorThunk },
	{ 0x06000028, CharacterInfo_get_maxY_m2439B7BB55176E566ECE4CAB87B3AA252DF46927_AdjustorThunk },
	{ 0x06000029, CharacterInfo_get_minX_m69B3FB781E712AE29CDEC596F9932862FB553328_AdjustorThunk },
	{ 0x0600002A, CharacterInfo_get_maxX_m55625E1CD75FA327A33D2DA9C4A15C846CBB0A20_AdjustorThunk },
	{ 0x0600002B, CharacterInfo_get_uvBottomLeftUnFlipped_mCBA8417A773038E225D431DA5A5D67445AD13244_AdjustorThunk },
	{ 0x0600002C, CharacterInfo_get_uvBottomRightUnFlipped_m8C9B5C60873D8DA8232F37B87B03D6B235A0997E_AdjustorThunk },
	{ 0x0600002D, CharacterInfo_get_uvTopRightUnFlipped_m13A645BC543D634A53EE7C0282C64F68535EAC29_AdjustorThunk },
	{ 0x0600002E, CharacterInfo_get_uvTopLeftUnFlipped_m5DE3E983B48D8E5611FF7C8F78827A1841DCD2CB_AdjustorThunk },
	{ 0x0600002F, CharacterInfo_get_uvBottomLeft_mDBE99341E4D50DB991F249E438891D118AB2DF9F_AdjustorThunk },
	{ 0x06000030, CharacterInfo_get_uvBottomRight_m2A6F8FA7B8C05E65653AE2986E0889CCEAE3EF17_AdjustorThunk },
	{ 0x06000031, CharacterInfo_get_uvTopRight_m706389A181F4E4EAAB886C08D6E1A9C3D8FD2C16_AdjustorThunk },
	{ 0x06000032, CharacterInfo_get_uvTopLeft_m8366979523BC656BF87BA43327604E6870BC6A33_AdjustorThunk },
};
static const int32_t s_InvokerIndices[69] = 
{
	1983,
	2121,
	4018,
	6521,
	5320,
	6521,
	6521,
	6366,
	4908,
	6521,
	5349,
	5349,
	5349,
	2559,
	2559,
	1378,
	2058,
	2309,
	2309,
	6398,
	6398,
	6398,
	6421,
	6366,
	6366,
	9739,
	9621,
	1,
	2,
	5349,
	5349,
	5349,
	5240,
	0,
	5349,
	5349,
	6366,
	6366,
	6366,
	6366,
	6366,
	6366,
	6510,
	6510,
	6510,
	6510,
	6510,
	6510,
	6510,
	6510,
	9780,
	9624,
	9624,
	6398,
	6301,
	6366,
	6521,
	3134,
	8732,
	8732,
	9624,
	4037,
	3896,
	8989,
	8326,
	735,
	1785,
	3136,
	6521,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TextRenderingModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TextRenderingModule_CodeGenModule = 
{
	"UnityEngine.TextRenderingModule.dll",
	69,
	s_methodPointers,
	17,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
