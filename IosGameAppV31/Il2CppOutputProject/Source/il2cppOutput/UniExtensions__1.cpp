﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


struct VirtualActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename T1>
struct VirtualActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename T1, typename T2, typename T3>
struct VirtualActionInvoker3
{
	typedef void (*Action)(void*, T1, T2, T3, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1, T2 p2, T3 p3)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, p2, p3, invokeData.method);
	}
};
template <typename R>
struct VirtualFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename R, typename T1>
struct VirtualFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename R, typename T1, typename T2, typename T3>
struct VirtualFuncInvoker3
{
	typedef R (*Func)(void*, T1, T2, T3, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1, T2 p2, T3 p3)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, p1, p2, p3, invokeData.method);
	}
};

struct Dictionary_2_t87EDE08B2E48F793A22DE50D6B3CC2E7EBB2DB54;
struct List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct SByteU5BU5D_t88116DA68378C3333DB73E7D36C1A06AFAA91913;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA;
struct ConfigU5BU5D_tB693C4846B975F9CA572D40C3BF1B4CED8AF8C36;
struct CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0;
struct CodePageDataItem_t52460FA30AE37F4F26ACB81055E58002262F19F2;
struct DecoderFallback_t7324102215E4ED41EC065C02EB501CB0BC23CD90;
struct DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065;
struct EncoderFallback_tD2C40CE114AA9D8E1F7196608B2D088548015293;
struct Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095;
struct Exception_t;
struct GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct InflateBlocks_tBC2A4D79CAC3D37F4AC09C341C3B0262089A534A;
struct InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7;
struct NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct SemaphoreSlim_t0D5CB5685D9BFA5BF95CEC6E7395490F933E8DB2;
struct StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF;
struct Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE;
struct String_t;
struct Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B;
struct ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE;
struct ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0;
struct ReadWriteTask_t0821BF49EE38596C7734E86E1A6A39D769BE2C05;

IL2CPP_EXTERN_C RuntimeClass* Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Exception_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Path_t8A38A801D0219E8209C1B1D90D82D4D755D998BC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7B30b02ba4U2Dbf91U2D4f1fU2D8354U2D17168d1ebd16U7D_t0C3D2B42FE991186D3F5142D6805B75483177AB6____U24fieldU2D11_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3EU7B30b02ba4U2Dbf91U2D4f1fU2D8354U2D17168d1ebd16U7D_t0C3D2B42FE991186D3F5142D6805B75483177AB6____U24fieldU2D12_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral09B11B6CC411D8B9FFB75EAAE9A35B2AF248CE40;
IL2CPP_EXTERN_C String_t* _stringLiteral09BE1A5FDF1304B1AF66C7AA9E11F5D68F1A7A53;
IL2CPP_EXTERN_C String_t* _stringLiteral0FE5434C6E938CF30A74BC53A565C36727AA6E9E;
IL2CPP_EXTERN_C String_t* _stringLiteral10D38DC34936AF6C5FB05636D856173414F0E6F5;
IL2CPP_EXTERN_C String_t* _stringLiteral14D85C5F7C475603476AF01830C5A5FDF517ACB9;
IL2CPP_EXTERN_C String_t* _stringLiteral24B5EB22D8E2EC2684FA8E9A50101FC76FC80368;
IL2CPP_EXTERN_C String_t* _stringLiteral3233DCBABA7FC4545C644475BBE0516447D29DBD;
IL2CPP_EXTERN_C String_t* _stringLiteral3D7AFE151D76C21DBC230D2F7D219FAB99ADCF3A;
IL2CPP_EXTERN_C String_t* _stringLiteral6E343721F0E25487FE2756047043B53F090F4259;
IL2CPP_EXTERN_C String_t* _stringLiteral81B54C81CE5770A2FB716FE3138FA18CE998793D;
IL2CPP_EXTERN_C String_t* _stringLiteral8454D7468D90A7E6BF4F4218E867EA1DE489B984;
IL2CPP_EXTERN_C String_t* _stringLiteral86BBAACC00198DBB3046818AD3FC2AA10AE48DE1;
IL2CPP_EXTERN_C String_t* _stringLiteral8D4D82166AFA3DFA90B42F9624DAC59E5DE0E310;
IL2CPP_EXTERN_C String_t* _stringLiteral967386972D9F477783B0C3205A3CE33D2DB77D2B;
IL2CPP_EXTERN_C String_t* _stringLiteral983DAC088DBADBDF2CEE0A7F894D264FCD8D805D;
IL2CPP_EXTERN_C String_t* _stringLiteral9AE36592E95F2354A6676CC52CF848B11A46DBD9;
IL2CPP_EXTERN_C String_t* _stringLiteral9FC4F45EF48BB08BC4D6C5BC2A71B64139E0FEE4;
IL2CPP_EXTERN_C String_t* _stringLiteralA7908ABB3E478F6388EB5FB76212E4B3E81900AD;
IL2CPP_EXTERN_C String_t* _stringLiteralB274B371D443401B2ED8CB4D62663E14A8DAFE8C;
IL2CPP_EXTERN_C String_t* _stringLiteralB2B8359BD91EB1DF5DC1B2DE300F3D1DC2B414E8;
IL2CPP_EXTERN_C String_t* _stringLiteralC1E0482ABDB4530F47C01C2A81FB06ED6E98A110;
IL2CPP_EXTERN_C String_t* _stringLiteralC62B0C3C63415051741BF2BBE989F54545097E70;
IL2CPP_EXTERN_C String_t* _stringLiteralD82DD7A67123B96565FAC38717BA5FA359AB739E;
IL2CPP_EXTERN_C String_t* _stringLiteralDC4A06A0DE599F745DBDD44A6FDE6212859D3A5F;
IL2CPP_EXTERN_C String_t* _stringLiteralF0FA167A2B553EA3A482772133E47AA3C132FFA2;
IL2CPP_EXTERN_C String_t* _stringLiteralFBC07F6BD47DF829ABDBABCE58B748504460B7BB;
IL2CPP_EXTERN_C const RuntimeMethod* GZipStream_Seek_m8503951F8A4D64BF1CFE2026D09E07CF26BDE6FE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GZipStream_SetLength_mA8E5EFF8E8A24E0C32205F97B26D0C48B817A75A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GZipStream_SlurpHeader_m5D83302AAF7EB805EA37524883FA3FD9CDB98A8E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GZipStream_get_Length_m5FC04810DDDEE5E6DDA2CAC1DAD9D5F5D8362F32_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GZipStream_get_Position_m54F39FEB928AD39788C63C43527322859163841A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GZipStream_set_FileName_m1BBFAD7EB67A55FE4F47A1AF9600C1D9F03805E3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GZipStream_set_Position_mAC32F5E26F25BEE5CFDA744C0D14A8618492FB3F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_mCF9C36A7DE08AFB2D2EEC76139677E086A6CAD73_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_ToArray_m23F8BB7CABADC11EBCAB5AB701B75F56C865BE24_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_mC411581553467A7DB76C06E5E5FCA34E4EC621B9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1__ctor_mB17304720EA19F5469A4883827F53A75FEB492CF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1_get_HasValue_m092C73DCE052BFB5C60A39EF9F4E3401AA95221C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1_get_Value_m5A868F663848BC21C18F056731D3AC404CE59991_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibBaseStream_ReadZeroTerminatedString_m1129CEBC534AC892ACE3F2B6B66FE8103CF44A4E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibBaseStream_Read_m9181D0A7D2941CDEA659A803791B98B5700047BF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibBaseStream_Seek_mD37F4E628A8B871EE6D9CD7DBFE74DAADAE9622E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibBaseStream_Write_m17DC0152DFDCE0EB5B55E3FC562B8313490EB376_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibBaseStream__ReadAndValidateGzipHeader_m65C3C51F4DFAECA2DCCA593299CBD453EF142CE6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibBaseStream_finish_m943ABACCE83B407587BABF9AA0FAEE8059E5B35D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibBaseStream_get_Position_mF9C08461A36C0A5E1FF1A88BBA7060DBA1E4E035_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibBaseStream_set_Position_m01FE6FC87A956DABEFB425C0550C1931D758E76F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibCodec_Deflate_m68368C43EA37CDB2D268D1EAD2F2F4620FA46330_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibCodec_EndDeflate_mFF40B57BD0A08EB4543C2EA55C8DCB069899E43D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibCodec_EndInflate_mE7E95E904F7098F6899D8BD79823CFCDE877C4B9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibCodec_Inflate_mEFD195C98EAC837238E73A3E763AB324379A4550_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibCodec_InitializeDeflate_m5553968574555F56D0E692871C12333B6138EDDA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibCodec_InitializeInflate_mCBD1E3DA91FF1CA7064486432E262E24F5E1877E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ZlibCodec_flush_pending_mD40B5B6D11072492E3AAD5FF14964E75B56DAE56_RuntimeMethod_var;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B  : public RuntimeObject
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct U3CPrivateImplementationDetailsU3EU7B30b02ba4U2Dbf91U2D4f1fU2D8354U2D17168d1ebd16U7D_t0C3D2B42FE991186D3F5142D6805B75483177AB6  : public RuntimeObject
{
};
struct Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698  : public RuntimeObject
{
};
struct CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0  : public RuntimeObject
{
	int64_t ____TotalBytesRead;
	uint32_t ____RunningCrc32Result;
};
struct Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095  : public RuntimeObject
{
	int32_t ___m_codePage;
	CodePageDataItem_t52460FA30AE37F4F26ACB81055E58002262F19F2* ___dataItem;
	bool ___m_deserializedFromEverett;
	bool ___m_isReadOnly;
	EncoderFallback_tD2C40CE114AA9D8E1F7196608B2D088548015293* ___encoderFallback;
	DecoderFallback_t7324102215E4ED41EC065C02EB501CB0BC23CD90* ___decoderFallback;
};
struct InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7  : public RuntimeObject
{
	int32_t ___mode;
	int32_t ___method;
	Int64U5BU5D_tAEDFCBDB5414E2A140A6F34C0538BF97FCF67A1D* ___was;
	int64_t ___need;
	int32_t ___marker;
	bool ____handleRfc1950HeaderBytes;
	int32_t ___wbits;
	InflateBlocks_tBC2A4D79CAC3D37F4AC09C341C3B0262089A534A* ___blocks;
};
struct MarshalByRefObject_t8C2F4C5854177FD60439EB1FCCFC1B3CFAFE8DCE  : public RuntimeObject
{
	RuntimeObject* ____identity;
};
struct MarshalByRefObject_t8C2F4C5854177FD60439EB1FCCFC1B3CFAFE8DCE_marshaled_pinvoke
{
	Il2CppIUnknown* ____identity;
};
struct MarshalByRefObject_t8C2F4C5854177FD60439EB1FCCFC1B3CFAFE8DCE_marshaled_com
{
	Il2CppIUnknown* ____identity;
};
struct SharedUtils_t73048D0995A2767247321FFF5D73A3F3FA9CB4E6  : public RuntimeObject
{
};
struct StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF  : public RuntimeObject
{
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___static_tree;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___extra_bits;
	int32_t ___extra_base;
	int32_t ___elems;
	int32_t ___max_length;
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E  : public RuntimeObject
{
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___dyn_tree;
	int32_t ___max_code;
	StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF* ___stat_desc;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE  : public RuntimeObject
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___InputBuffer;
	int32_t ___NextIn;
	int32_t ___AvailableBytesIn;
	int64_t ___TotalBytesIn;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___OutputBuffer;
	int32_t ___NextOut;
	int32_t ___AvailableBytesOut;
	int64_t ___TotalBytesOut;
	String_t* ___Message;
	DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* ___dstate;
	InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7* ___istate;
	int64_t ____Adler32;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D 
{
	uint64_t ____dateData;
};
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	double ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175 
{
	int16_t ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3 
{
	int64_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE  : public MarshalByRefObject_t8C2F4C5854177FD60439EB1FCCFC1B3CFAFE8DCE
{
	ReadWriteTask_t0821BF49EE38596C7734E86E1A6A39D769BE2C05* ____activeReadWriteTask;
	SemaphoreSlim_t0D5CB5685D9BFA5BF95CEC6E7395490F933E8DB2* ____asyncActiveSemaphore;
};
struct TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A 
{
	int64_t ____ticks;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D1152_t2A5BFA69364BF931FE6ECFCA9CF655CD95B123D1 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D1152_t2A5BFA69364BF931FE6ECFCA9CF655CD95B123D1__padding[1152];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D116_t56804F9DEC461A86E466A18276F390D65389C8CE 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D116_t56804F9DEC461A86E466A18276F390D65389C8CE__padding[116];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D120_t939B458F019F1FBADD3671293A1D014EBF00F24E 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D120_t939B458F019F1FBADD3671293A1D014EBF00F24E__padding[120];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D124_tDFBC14C8397BD8F8BDFA2FD728BBA44CBAE0B000 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D124_tDFBC14C8397BD8F8BDFA2FD728BBA44CBAE0B000__padding[124];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D20_tAD7C2FCDC33CAC143D74C0B261EAB37D41046D15 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D20_tAD7C2FCDC33CAC143D74C0B261EAB37D41046D15__padding[20];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D256_tAA5FD8AEDC2F39B0C9E6852EE936E0F4744AAEB5 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D256_tAA5FD8AEDC2F39B0C9E6852EE936E0F4744AAEB5__padding[256];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D384_t906C077F3A383B3D96860F97E6116737BD0B2175 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D384_t906C077F3A383B3D96860F97E6116737BD0B2175__padding[384];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D512_tC23CB5AF37830583FE0213AABD769822AC937422 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D512_tC23CB5AF37830583FE0213AABD769822AC937422__padding[512];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D6144_t51AFB7BA32BB61707ED7919E98740A93355C6A73 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D6144_t51AFB7BA32BB61707ED7919E98740A93355C6A73__padding[6144];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D68_t3A4979EDB17FF3822A6C71EC11AD1174F9597AA4 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D68_t3A4979EDB17FF3822A6C71EC11AD1174F9597AA4__padding[68];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct U24ArrayTypeU3D76_t6DE5326D9206C4193B600BD71453E9A9724B548E 
{
	union
	{
		struct
		{
		};
		uint8_t U24ArrayTypeU3D76_t6DE5326D9206C4193B600BD71453E9A9724B548E__padding[76];
	};
};
#pragma pack(pop, tp)
struct Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC 
{
	bool ___hasValue;
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___value;
};
struct CompressionLevel_tEE7A0C568426F5CAD4EC9F642428277970437DDC 
{
	int32_t ___value__;
};
struct CompressionMode_t69841D5F5CD67B7DA7CF16C487935AEB4795516F 
{
	int32_t ___value__;
};
struct CompressionStrategy_t6205BBD8836B8D0ED10C4024F04498D358DE2437 
{
	int32_t ___value__;
};
struct DateTimeKind_t3AD6DA06BEF8955A740777163FFB481C19089BBC 
{
	int32_t ___value__;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct SeekOrigin_t7EB9AD0EDF26368A40F48FA2098F02160B1E8000 
{
	int32_t ___value__;
};
struct ZlibStreamFlavor_tCA107E1895531014073E250C353A227764A4E77E 
{
	int32_t ___value__;
};
struct StreamMode_t7B67618707C100C749FCCB087F8C1926356A6B78 
{
	int32_t ___value__;
};
struct DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065  : public RuntimeObject
{
	ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* ___strm;
	int32_t ___status;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___pending;
	int32_t ___nextPending;
	int32_t ___pendingCount;
	int8_t ___data_type;
	int8_t ___method;
	int32_t ___last_flush;
	int32_t ___w_size;
	int32_t ___w_bits;
	int32_t ___w_mask;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___window;
	int32_t ___window_size;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___prev;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___head;
	int32_t ___ins_h;
	int32_t ___hash_size;
	int32_t ___hash_bits;
	int32_t ___hash_mask;
	int32_t ___hash_shift;
	int32_t ___block_start;
	int32_t ___match_length;
	int32_t ___prev_match;
	int32_t ___match_available;
	int32_t ___strstart;
	int32_t ___match_start;
	int32_t ___lookahead;
	int32_t ___prev_length;
	int32_t ___max_chain_length;
	int32_t ___max_lazy_match;
	int32_t ___compressionLevel;
	int32_t ___compressionStrategy;
	int32_t ___good_match;
	int32_t ___nice_match;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___dyn_ltree;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___dyn_dtree;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___bl_tree;
	Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E* ___l_desc;
	Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E* ___d_desc;
	Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E* ___bl_desc;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___bl_count;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___heap;
	int32_t ___heap_len;
	int32_t ___heap_max;
	SByteU5BU5D_t88116DA68378C3333DB73E7D36C1A06AFAA91913* ___depth;
	int32_t ___l_buf;
	int32_t ___lit_bufsize;
	int32_t ___last_lit;
	int32_t ___d_buf;
	int32_t ___opt_len;
	int32_t ___static_len;
	int32_t ___matches;
	int32_t ___last_eob_len;
	int16_t ___bi_buf;
	int32_t ___bi_valid;
	bool ___Rfc1950BytesEmitted;
	bool ____WantRfc1950HeaderBytes;
};
struct GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187  : public Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE
{
	String_t* ___Comment;
	Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC ___LastModified;
	ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* ____baseStream;
	bool ____firstReadDone;
	String_t* ____FileName;
	int32_t ____Crc32;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B  : public Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE
{
	ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* ____z;
	int32_t ___WORKING_BUFFER_SIZE_DEFAULT;
	int32_t ___WORKING_BUFFER_SIZE_MIN;
	int32_t ____streamMode;
	int32_t ____flushMode;
	int32_t ____flavor;
	bool ____leaveOpen;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ____workingBuffer;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ____buf1;
	bool ____wantCompress;
	Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* ____stream;
	CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* ___crc;
	String_t* ____GzipFileName;
	String_t* ____GzipComment;
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ____GzipMtime;
	bool ___nomoreinput;
};
struct ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0  : public Exception_t
{
};
struct IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B_StaticFields
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___s_emptyArray;
};
struct U3CPrivateImplementationDetailsU3EU7B30b02ba4U2Dbf91U2D4f1fU2D8354U2D17168d1ebd16U7D_t0C3D2B42FE991186D3F5142D6805B75483177AB6_StaticFields
{
	U24ArrayTypeU3D6144_t51AFB7BA32BB61707ED7919E98740A93355C6A73 ___U24fieldU2D0;
	U24ArrayTypeU3D384_t906C077F3A383B3D96860F97E6116737BD0B2175 ___U24fieldU2D1;
	U24ArrayTypeU3D124_tDFBC14C8397BD8F8BDFA2FD728BBA44CBAE0B000 ___U24fieldU2D2;
	U24ArrayTypeU3D124_tDFBC14C8397BD8F8BDFA2FD728BBA44CBAE0B000 ___U24fieldU2D3;
	U24ArrayTypeU3D120_t939B458F019F1FBADD3671293A1D014EBF00F24E ___U24fieldU2D4;
	U24ArrayTypeU3D120_t939B458F019F1FBADD3671293A1D014EBF00F24E ___U24fieldU2D5;
	U24ArrayTypeU3D68_t3A4979EDB17FF3822A6C71EC11AD1174F9597AA4 ___U24fieldU2D6;
	U24ArrayTypeU3D76_t6DE5326D9206C4193B600BD71453E9A9724B548E ___U24fieldU2D7;
	U24ArrayTypeU3D68_t3A4979EDB17FF3822A6C71EC11AD1174F9597AA4 ___U24fieldU2D8;
	U24ArrayTypeU3D116_t56804F9DEC461A86E466A18276F390D65389C8CE ___U24fieldU2D9;
	U24ArrayTypeU3D120_t939B458F019F1FBADD3671293A1D014EBF00F24E ___U24fieldU2DA;
	U24ArrayTypeU3D76_t6DE5326D9206C4193B600BD71453E9A9724B548E ___U24fieldU2DB;
	U24ArrayTypeU3D20_tAD7C2FCDC33CAC143D74C0B261EAB37D41046D15 ___U24fieldU2DC;
	U24ArrayTypeU3D512_tC23CB5AF37830583FE0213AABD769822AC937422 ___U24fieldU2DD;
	U24ArrayTypeU3D256_tAA5FD8AEDC2F39B0C9E6852EE936E0F4744AAEB5 ___U24fieldU2DE;
	U24ArrayTypeU3D116_t56804F9DEC461A86E466A18276F390D65389C8CE ___U24fieldU2DF;
	U24ArrayTypeU3D120_t939B458F019F1FBADD3671293A1D014EBF00F24E ___U24fieldU2D10;
	U24ArrayTypeU3D1152_t2A5BFA69364BF931FE6ECFCA9CF655CD95B123D1 ___U24fieldU2D11;
	U24ArrayTypeU3D120_t939B458F019F1FBADD3671293A1D014EBF00F24E ___U24fieldU2D12;
};
struct Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_StaticFields
{
	int32_t ___BASE;
	int32_t ___NMAX;
};
struct CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0_StaticFields
{
	UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA* ___crc32Table;
};
struct Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095_StaticFields
{
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___defaultEncoding;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___unicodeEncoding;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___bigEndianUnicode;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___utf7Encoding;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___utf8Encoding;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___utf32Encoding;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___asciiEncoding;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___latin1Encoding;
	Dictionary_2_t87EDE08B2E48F793A22DE50D6B3CC2E7EBB2DB54* ___encodings;
	RuntimeObject* ___s_InternalSyncObject;
};
struct InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7_StaticFields
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___mark;
};
struct StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields
{
	int32_t ___L_CODES;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___static_ltree;
	Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___static_dtree;
	StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF* ___static_l_desc;
	StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF* ___static_d_desc;
	StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF* ___static_bl_desc;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E_StaticFields
{
	int32_t ___L_CODES;
	int32_t ___HEAP_SIZE;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___extra_lbits;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___extra_dbits;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___extra_blbits;
	SByteU5BU5D_t88116DA68378C3333DB73E7D36C1A06AFAA91913* ___bl_order;
	SByteU5BU5D_t88116DA68378C3333DB73E7D36C1A06AFAA91913* ____dist_code;
	SByteU5BU5D_t88116DA68378C3333DB73E7D36C1A06AFAA91913* ____length_code;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___base_length;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___base_dist;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_StaticFields
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___s_daysToMonth365;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___s_daysToMonth366;
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___MinValue;
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___MaxValue;
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___UnixEpoch;
};
struct Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE_StaticFields
{
	Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* ___Null;
};
struct TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A_StaticFields
{
	TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___Zero;
	TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___MaxValue;
	TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A ___MinValue;
};
struct Exception_t_StaticFields
{
	RuntimeObject* ___s_EDILock;
};
struct DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065_StaticFields
{
	ConfigU5BU5D_tB693C4846B975F9CA572D40C3BF1B4CED8AF8C36* ___configTable;
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ___z_errmsg;
	int32_t ___MIN_LOOKAHEAD;
	int32_t ___L_CODES;
	int32_t ___HEAP_SIZE;
};
struct GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_StaticFields
{
	DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ____unixEpoch;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___iso8859dash1;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB  : public RuntimeArray
{
	ALIGN_FIELD (8) int16_t m_Items[1];

	inline int16_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline int16_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, int16_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline int16_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline int16_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, int16_t value)
	{
		m_Items[index] = value;
	}
};
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C  : public RuntimeArray
{
	ALIGN_FIELD (8) int32_t m_Items[1];

	inline int32_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline int32_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, int32_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline int32_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline int32_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, int32_t value)
	{
		m_Items[index] = value;
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_mC411581553467A7DB76C06E5E5FCA34E4EC621B9_gshared (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mCF9C36A7DE08AFB2D2EEC76139677E086A6CAD73_gshared_inline (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* __this, uint8_t ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* List_1_ToArray_m23F8BB7CABADC11EBCAB5AB701B75F56C865BE24_gshared (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Nullable_1_get_HasValue_m092C73DCE052BFB5C60A39EF9F4E3401AA95221C_gshared_inline (Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Nullable_1__ctor_mB17304720EA19F5469A4883827F53A75FEB492CF_gshared (Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC* __this, DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D Nullable_1_get_Value_m5A868F663848BC21C18F056731D3AC404CE59991_gshared (Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void List_1_AddWithResize_m735EBD7363A06760DE25BACA0FC47F76CA0DEEAB_gshared (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* __this, uint8_t ___0_item, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F (Exception_t* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StaticTree__ctor_m9820874FFC3EF3292D13A338E8988E9E9CF084BA (StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF* __this, Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___0_static_tree, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___1_extra_bits, int32_t ___2_extra_base, int32_t ___3_elems, int32_t ___4_max_length, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_InitializeInflate_mCBD1E3DA91FF1CA7064486432E262E24F5E1877E (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, int32_t ___0_windowBits, bool ___1_expectRfc1950Header, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* __this, String_t* ___0_s, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InflateManager__ctor_mEF35131A0B33EEC084DE5678CDFB10CCF95227C4 (InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7* __this, bool ___0_expectRfc1950HeaderBytes, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InflateManager_Initialize_m61634F91BC501376E92B59399B7A89DCABA6586B (InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7* __this, ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* ___0_z, int32_t ___1_w, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InflateManager_Inflate_m50B8CF154C3B2375CE085841C36F9750C2FAAF39 (InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7* __this, ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* ___0_z, int32_t ___1_f, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t InflateManager_End_mB7EF05FA9F1FFE1086DE00C05CA9CA4E31E455E1 (InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7* __this, ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* ___0_z, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_InitializeDeflate_m5553968574555F56D0E692871C12333B6138EDDA (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, int32_t ___0_level, int32_t ___1_bits, bool ___2_wantRfc1950Header, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DeflateManager__ctor_mA7B904F9B3FCC870B5A53FAE3B0AE4C725B84328 (DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void DeflateManager_set_WantRfc1950HeaderBytes_m799CF5C55738EC42D599049066DDD7F5D5392338_inline (DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DeflateManager_Initialize_m8323A058D2E8FF0EF623FE6353016E5D96378F7B (DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* __this, ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* ___0_strm, int32_t ___1_level, int32_t ___2_bits, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DeflateManager_Deflate_m40DAAB1A938EAFD48A53AD38B4865575F9987FAC (DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* __this, ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* ___0_strm, int32_t ___1_flush, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DeflateManager_End_m4CD9A64B75E607003F5EF5E5C459BFF4EA6A3319 (DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987 (String_t* ___0_format, RuntimeObject* ___1_arg0, RuntimeObject* ___2_arg1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41 (RuntimeArray* ___0_sourceArray, int32_t ___1_sourceIndex, RuntimeArray* ___2_destinationArray, int32_t ___3_destinationIndex, int32_t ___4_length, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool DeflateManager_get_WantRfc1950HeaderBytes_m5661399ADA54DFF965A373E2BECC13189F25FFEE_inline (DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t Adler_Adler32_m8A3761EF98770BFCEE3242665825CB496E4C8313 (int64_t ___0_adler, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___1_buf, int32_t ___2_index, int32_t ___3_len, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GZipStream__ctor_m72A5DAF83F1394062C95CA12E59E17EA848E4741 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* ___0_stream, int32_t ___1_mode, int32_t ___2_level, bool ___3_leaveOpen, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Stream__ctor_mE8B074A0EBEB026FFF14062AB4B8A78E17EFFBF0 (Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream__ctor_mCA4A9F54161B0AEDAD281EE0CC09247BBB41F6E7 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* ___0_stream, int32_t ___1_compressionMode, int32_t ___2_level, int32_t ___3_flavor, bool ___4_leaveOpen, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t String_IndexOf_m69E9BDAFD93767C85A7FF861B453415D3B4A200F (String_t* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Replace_mABDB7003A1D0AEDCAE9FF85E3DFFFBA752D2A166 (String_t* __this, String_t* ___0_oldValue, String_t* ___1_newValue, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_EndsWith_mCD3754F5401E19CE7821CD398986E4EAA6AD87DC (String_t* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Path_GetFileName_mB1A8CE314EE250B06E3D33142315E2BD3A75D1D6 (String_t* ___0_path, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibBaseStream_get_Crc32_m240AACB0116DFA174E4752C5E958948348E2CCC1 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotImplementedException__ctor_mDAB47BC6BD0E342E8F2171E5CABE3E67EA049F1C (NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GZipStream_set_FileName_m1BBFAD7EB67A55FE4F47A1AF9600C1D9F03805E3 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GZipStream_EmitHeader_m3D76DF2020913E4C80760EF5E24FB0E33874C527 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t GZipStream_SlurpHeader_m5D83302AAF7EB805EA37524883FA3FD9CDB98A8E (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, int32_t ___1_offset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* GZipStream_SlurpZeroTerminatedString_m24CFAC49769B74F0C3C800A8621D09FA73A61564 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, int32_t ___1_offset, int32_t* ___2_count, const RuntimeMethod* method) ;
inline void List_1__ctor_mC411581553467A7DB76C06E5E5FCA34E4EC621B9 (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B*, const RuntimeMethod*))List_1__ctor_mC411581553467A7DB76C06E5E5FCA34E4EC621B9_gshared)(__this, method);
}
inline void List_1_Add_mCF9C36A7DE08AFB2D2EEC76139677E086A6CAD73_inline (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* __this, uint8_t ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B*, uint8_t, const RuntimeMethod*))List_1_Add_mCF9C36A7DE08AFB2D2EEC76139677E086A6CAD73_gshared_inline)(__this, ___0_item, method);
}
inline ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* List_1_ToArray_m23F8BB7CABADC11EBCAB5AB701B75F56C865BE24 (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* __this, const RuntimeMethod* method)
{
	return ((  ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* (*) (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B*, const RuntimeMethod*))List_1_ToArray_m23F8BB7CABADC11EBCAB5AB701B75F56C865BE24_gshared)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* GZipStream_get_FileName_m0D6FA7D5C31AA7D4849C22BEA1ACCFD8CA798E83_inline (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, const RuntimeMethod* method) ;
inline bool Nullable_1_get_HasValue_m092C73DCE052BFB5C60A39EF9F4E3401AA95221C_inline (Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC*, const RuntimeMethod*))Nullable_1_get_HasValue_m092C73DCE052BFB5C60A39EF9F4E3401AA95221C_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D DateTime_get_Now_m636CB9651A9099D20BA1CF813A0C69637317325C (const RuntimeMethod* method) ;
inline void Nullable_1__ctor_mB17304720EA19F5469A4883827F53A75FEB492CF (Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC* __this, DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___0_value, const RuntimeMethod* method)
{
	((  void (*) (Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC*, DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D, const RuntimeMethod*))Nullable_1__ctor_mB17304720EA19F5469A4883827F53A75FEB492CF_gshared)(__this, ___0_value, method);
}
inline DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D Nullable_1_get_Value_m5A868F663848BC21C18F056731D3AC404CE59991 (Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC* __this, const RuntimeMethod* method)
{
	return ((  DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D (*) (Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC*, const RuntimeMethod*))Nullable_1_get_Value_m5A868F663848BC21C18F056731D3AC404CE59991_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A DateTime_op_Subtraction_m64D26F5ABFAE6E166A7E567093D025F6C69F0123 (DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___0_d1, DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D ___1_d2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double TimeSpan_get_TotalSeconds_mED686E7CECE6A76A7DC38518698B9199DB8CDEA8 (TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* BitConverter_GetBytes_mCD74C79673617CEBF85F8A653520C860A9F014F9 (int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DateTime__ctor_mD89390EF215242275A4E8F78C2C3E8BC3EF6F3C3 (DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D* __this, int32_t ___0_year, int32_t ___1_month, int32_t ___2_day, int32_t ___3_hour, int32_t ___4_minute, int32_t ___5_second, int32_t ___6_kind, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* Encoding_GetEncoding_m979B224460094E241BD5C283BE279886664C9187 (String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibCodec__ctor_m40C747342EAC3B3891ABCB80C5E34229FFF68D0F (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_InitializeInflate_m326D69D2B9CD25CAD8E83064A6891F178AB1B116 (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, bool ___0_expectRfc1950Header, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_InitializeDeflate_m542BFAC119F9EA2F3F15B7C87E2714F82188A9EB (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, int32_t ___0_level, bool ___1_wantRfc1950Header, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CRC32__ctor_m01585BBA4CC7A8DA520F4FC404FB4C1033258FA4 (CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t CRC32_get_Crc32Result_mD49F2B1164980FB8CAE0B15C817088A2C85E3DB6 (CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CRC32_SlurpBlock_m24007D3AB683FF87DB6CCF4EC1ADE999E963B642 (CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_block, int32_t ___1_offset, int32_t ___2_count, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_Deflate_m68368C43EA37CDB2D268D1EAD2F2F4620FA46330 (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, int32_t ___0_flush, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_Inflate_mEFD195C98EAC837238E73A3E763AB324379A4550 (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, int32_t ___0_f, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int64_t CRC32_get_TotalBytesRead_mDFA2814BC50D7BDBC3F62D1B412CAEB8F50ABDE0_inline (CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_mA8DBB4C2516B9723C5A41E6CB1E2FAF4BBE96DD8 (String_t* ___0_format, RuntimeObject* ___1_arg0, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t BitConverter_ToInt32_m259B4E62995575B80C4086347C867EB3C8D7DAB3 (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_value, int32_t ___1_startIndex, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_EndDeflate_mFF40B57BD0A08EB4543C2EA55C8DCB069899E43D (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_EndInflate_mE7E95E904F7098F6899D8BD79823CFCDE877C4B9 (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream_end_m8B560782B294CA50D2ADDB31DDA09AD8356FE55A (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream_finish_m943ABACCE83B407587BABF9AA0FAEE8059E5B35D (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D DateTime_AddSeconds_mC5FE3FB22A1295CA747746ECE48B9D4A4B6B2E81 (DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D* __this, double ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ZlibBaseStream_ReadZeroTerminatedString_m1129CEBC534AC892ACE3F2B6B66FE8103CF44A4E (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream__ReadAndValidateGzipHeader_m65C3C51F4DFAECA2DCCA593299CBD453EF142CE6 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SharedUtils_ReadInput_m04DEA02BA9AABEB317EF671B6B2559A554C2BB5F (Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* ___0_sourceStream, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___1_target, int32_t ___2_start, int32_t ___3_count, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9E3155FB84015C823606188F53B47CB44C444991 (String_t* ___0_str0, String_t* ___1_str1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_mA0534D6E2AE4D67A6BD8D45B3321323930EB930C (String_t* ___0_format, RuntimeObject* ___1_arg0, RuntimeObject* ___2_arg1, RuntimeObject* ___3_arg2, const RuntimeMethod* method) ;
inline void List_1_AddWithResize_m735EBD7363A06760DE25BACA0FC47F76CA0DEEAB (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* __this, uint8_t ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B*, uint8_t, const RuntimeMethod*))List_1_AddWithResize_m735EBD7363A06760DE25BACA0FC47F76CA0DEEAB_gshared)(__this, ___0_item, method);
}
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* __this, String_t* ___0_s, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ___0_s;
		il2cpp_codegen_runtime_class_init_inline(Exception_t_il2cpp_TypeInfo_var);
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(__this, L_0, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SharedUtils_URShift_mBCFB1746911A5964EA1DB6DDBBBA7C3E208371B7 (int32_t ___0_number, int32_t ___1_bits, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_number;
		if ((((int32_t)L_0) < ((int32_t)0)))
		{
			goto IL_000e;
		}
	}
	{
		int32_t L_1 = ___0_number;
		int32_t L_2 = ___1_bits;
		return ((int32_t)(L_1>>((int32_t)(L_2&((int32_t)31)))));
	}

IL_000e:
	{
		int32_t L_3 = ___0_number;
		int32_t L_4 = ___1_bits;
		int32_t L_5 = ___1_bits;
		return ((int32_t)il2cpp_codegen_add(((int32_t)(L_3>>((int32_t)(L_4&((int32_t)31))))), ((int32_t)(2<<((int32_t)(((~L_5))&((int32_t)31)))))));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t SharedUtils_URShift_mD9F0E37D8F8C7F9BB4798A1817949B02862DA1EF (int64_t ___0_number, int32_t ___1_bits, const RuntimeMethod* method) 
{
	{
		int64_t L_0 = ___0_number;
		if ((((int64_t)L_0) < ((int64_t)((int64_t)0))))
		{
			goto IL_000f;
		}
	}
	{
		int64_t L_1 = ___0_number;
		int32_t L_2 = ___1_bits;
		return ((int64_t)(L_1>>((int32_t)(L_2&((int32_t)63)))));
	}

IL_000f:
	{
		int64_t L_3 = ___0_number;
		int32_t L_4 = ___1_bits;
		int32_t L_5 = ___1_bits;
		return ((int64_t)il2cpp_codegen_add(((int64_t)(L_3>>((int32_t)(L_4&((int32_t)63))))), ((int64_t)(((int64_t)2)<<((int32_t)(((~L_5))&((int32_t)63)))))));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SharedUtils_ReadInput_m04DEA02BA9AABEB317EF671B6B2559A554C2BB5F (Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* ___0_sourceStream, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___1_target, int32_t ___2_start, int32_t ___3_count, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = ___1_target;
		NullCheck(L_0);
		if (((int32_t)(((RuntimeArray*)L_0)->max_length)))
		{
			goto IL_000a;
		}
	}
	{
		return 0;
	}

IL_000a:
	{
		int32_t L_1 = ___3_count;
		if (L_1)
		{
			goto IL_0012;
		}
	}
	{
		return 0;
	}

IL_0012:
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_2 = ___0_sourceStream;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = ___1_target;
		int32_t L_4 = ___2_start;
		int32_t L_5 = ___3_count;
		NullCheck(L_2);
		int32_t L_6;
		L_6 = VirtualFuncInvoker3< int32_t, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(31, L_2, L_3, L_4, L_5);
		V_0 = L_6;
		int32_t L_7 = V_0;
		return L_7;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StaticTree__ctor_m9820874FFC3EF3292D13A338E8988E9E9CF084BA (StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF* __this, Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* ___0_static_tree, Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___1_extra_bits, int32_t ___2_extra_base, int32_t ___3_elems, int32_t ___4_max_length, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_0 = ___0_static_tree;
		__this->___static_tree = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___static_tree), (void*)L_0);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = ___1_extra_bits;
		__this->___extra_bits = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___extra_bits), (void*)L_1);
		int32_t L_2 = ___2_extra_base;
		__this->___extra_base = L_2;
		int32_t L_3 = ___3_elems;
		__this->___elems = L_3;
		int32_t L_4 = ___4_max_length;
		__this->___max_length = L_4;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StaticTree__cctor_mF4E3B0AB27CEE7E11487D5238E60784DA9CECAFA (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7B30b02ba4U2Dbf91U2D4f1fU2D8354U2D17168d1ebd16U7D_t0C3D2B42FE991186D3F5142D6805B75483177AB6____U24fieldU2D11_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3EU7B30b02ba4U2Dbf91U2D4f1fU2D8354U2D17168d1ebd16U7D_t0C3D2B42FE991186D3F5142D6805B75483177AB6____U24fieldU2D12_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___L_CODES = ((int32_t)286);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_0 = (Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB*)(Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB*)SZArrayNew(Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB_il2cpp_TypeInfo_var, (uint32_t)((int32_t)576));
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7B30b02ba4U2Dbf91U2D4f1fU2D8354U2D17168d1ebd16U7D_t0C3D2B42FE991186D3F5142D6805B75483177AB6____U24fieldU2D11_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___static_ltree = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___static_ltree), (void*)L_1);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_3 = (Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB*)(Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB*)SZArrayNew(Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB_il2cpp_TypeInfo_var, (uint32_t)((int32_t)60));
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3EU7B30b02ba4U2Dbf91U2D4f1fU2D8354U2D17168d1ebd16U7D_t0C3D2B42FE991186D3F5142D6805B75483177AB6____U24fieldU2D12_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___static_dtree = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___static_dtree), (void*)L_4);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_6 = ((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___static_ltree;
		il2cpp_codegen_runtime_class_init_inline(Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E_il2cpp_TypeInfo_var);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_7 = ((Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E_il2cpp_TypeInfo_var))->___extra_lbits;
		int32_t L_8 = ((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___L_CODES;
		StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF* L_9 = (StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF*)il2cpp_codegen_object_new(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var);
		StaticTree__ctor_m9820874FFC3EF3292D13A338E8988E9E9CF084BA(L_9, L_6, L_7, ((int32_t)257), L_8, ((int32_t)15), NULL);
		((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___static_l_desc = L_9;
		Il2CppCodeGenWriteBarrier((void**)(&((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___static_l_desc), (void*)L_9);
		Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB* L_10 = ((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___static_dtree;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_11 = ((Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E_il2cpp_TypeInfo_var))->___extra_dbits;
		StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF* L_12 = (StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF*)il2cpp_codegen_object_new(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var);
		StaticTree__ctor_m9820874FFC3EF3292D13A338E8988E9E9CF084BA(L_12, L_10, L_11, 0, ((int32_t)30), ((int32_t)15), NULL);
		((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___static_d_desc = L_12;
		Il2CppCodeGenWriteBarrier((void**)(&((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___static_d_desc), (void*)L_12);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_13 = ((Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E_StaticFields*)il2cpp_codegen_static_fields_for(Tree_t86FD20FD23D4BCB527174999C93350E088D7BB0E_il2cpp_TypeInfo_var))->___extra_blbits;
		StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF* L_14 = (StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF*)il2cpp_codegen_object_new(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var);
		StaticTree__ctor_m9820874FFC3EF3292D13A338E8988E9E9CF084BA(L_14, (Int16U5BU5D_t8175CE8DD9C9F9FB0CF4F58E45BC570575B43CFB*)NULL, L_13, 0, ((int32_t)19), 7, NULL);
		((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___static_bl_desc = L_14;
		Il2CppCodeGenWriteBarrier((void**)(&((StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_StaticFields*)il2cpp_codegen_static_fields_for(StaticTree_t3B02B0A900C934500FEB369C21D1B96A5E75C6BF_il2cpp_TypeInfo_var))->___static_bl_desc), (void*)L_14);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t Adler_Adler32_m8A3761EF98770BFCEE3242665825CB496E4C8313 (int64_t ___0_adler, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___1_buf, int32_t ___2_index, int32_t ___3_len, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int64_t V_0 = 0;
	int64_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t G_B6_0 = 0;
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = ___1_buf;
		if (L_0)
		{
			goto IL_0009;
		}
	}
	{
		return ((int64_t)1);
	}

IL_0009:
	{
		int64_t L_1 = ___0_adler;
		V_0 = ((int64_t)(L_1&((int64_t)((int32_t)65535))));
		int64_t L_2 = ___0_adler;
		V_1 = ((int64_t)(((int64_t)(L_2>>((int32_t)16)))&((int64_t)((int32_t)65535))));
		goto IL_01e9;
	}

IL_0023:
	{
		int32_t L_3 = ___3_len;
		il2cpp_codegen_runtime_class_init_inline(Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var);
		int32_t L_4 = ((Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_StaticFields*)il2cpp_codegen_static_fields_for(Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var))->___NMAX;
		if ((((int32_t)L_3) >= ((int32_t)L_4)))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = ___3_len;
		G_B6_0 = L_5;
		goto IL_0039;
	}

IL_0034:
	{
		il2cpp_codegen_runtime_class_init_inline(Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var);
		int32_t L_6 = ((Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_StaticFields*)il2cpp_codegen_static_fields_for(Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var))->___NMAX;
		G_B6_0 = L_6;
	}

IL_0039:
	{
		V_2 = G_B6_0;
		int32_t L_7 = ___3_len;
		int32_t L_8 = V_2;
		___3_len = ((int32_t)il2cpp_codegen_subtract(L_7, L_8));
		goto IL_01a9;
	}

IL_0044:
	{
		int64_t L_9 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_10 = ___1_buf;
		int32_t L_11 = ___2_index;
		int32_t L_12 = L_11;
		___2_index = ((int32_t)il2cpp_codegen_add(L_12, 1));
		NullCheck(L_10);
		int32_t L_13 = L_12;
		uint8_t L_14 = (L_10)->GetAt(static_cast<il2cpp_array_size_t>(L_13));
		V_0 = ((int64_t)il2cpp_codegen_add(L_9, ((int64_t)((int32_t)((int32_t)L_14&((int32_t)255))))));
		int64_t L_15 = V_1;
		int64_t L_16 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_15, L_16));
		int64_t L_17 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_18 = ___1_buf;
		int32_t L_19 = ___2_index;
		int32_t L_20 = L_19;
		___2_index = ((int32_t)il2cpp_codegen_add(L_20, 1));
		NullCheck(L_18);
		int32_t L_21 = L_20;
		uint8_t L_22 = (L_18)->GetAt(static_cast<il2cpp_array_size_t>(L_21));
		V_0 = ((int64_t)il2cpp_codegen_add(L_17, ((int64_t)((int32_t)((int32_t)L_22&((int32_t)255))))));
		int64_t L_23 = V_1;
		int64_t L_24 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_23, L_24));
		int64_t L_25 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_26 = ___1_buf;
		int32_t L_27 = ___2_index;
		int32_t L_28 = L_27;
		___2_index = ((int32_t)il2cpp_codegen_add(L_28, 1));
		NullCheck(L_26);
		int32_t L_29 = L_28;
		uint8_t L_30 = (L_26)->GetAt(static_cast<il2cpp_array_size_t>(L_29));
		V_0 = ((int64_t)il2cpp_codegen_add(L_25, ((int64_t)((int32_t)((int32_t)L_30&((int32_t)255))))));
		int64_t L_31 = V_1;
		int64_t L_32 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_31, L_32));
		int64_t L_33 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_34 = ___1_buf;
		int32_t L_35 = ___2_index;
		int32_t L_36 = L_35;
		___2_index = ((int32_t)il2cpp_codegen_add(L_36, 1));
		NullCheck(L_34);
		int32_t L_37 = L_36;
		uint8_t L_38 = (L_34)->GetAt(static_cast<il2cpp_array_size_t>(L_37));
		V_0 = ((int64_t)il2cpp_codegen_add(L_33, ((int64_t)((int32_t)((int32_t)L_38&((int32_t)255))))));
		int64_t L_39 = V_1;
		int64_t L_40 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_39, L_40));
		int64_t L_41 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_42 = ___1_buf;
		int32_t L_43 = ___2_index;
		int32_t L_44 = L_43;
		___2_index = ((int32_t)il2cpp_codegen_add(L_44, 1));
		NullCheck(L_42);
		int32_t L_45 = L_44;
		uint8_t L_46 = (L_42)->GetAt(static_cast<il2cpp_array_size_t>(L_45));
		V_0 = ((int64_t)il2cpp_codegen_add(L_41, ((int64_t)((int32_t)((int32_t)L_46&((int32_t)255))))));
		int64_t L_47 = V_1;
		int64_t L_48 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_47, L_48));
		int64_t L_49 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_50 = ___1_buf;
		int32_t L_51 = ___2_index;
		int32_t L_52 = L_51;
		___2_index = ((int32_t)il2cpp_codegen_add(L_52, 1));
		NullCheck(L_50);
		int32_t L_53 = L_52;
		uint8_t L_54 = (L_50)->GetAt(static_cast<il2cpp_array_size_t>(L_53));
		V_0 = ((int64_t)il2cpp_codegen_add(L_49, ((int64_t)((int32_t)((int32_t)L_54&((int32_t)255))))));
		int64_t L_55 = V_1;
		int64_t L_56 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_55, L_56));
		int64_t L_57 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_58 = ___1_buf;
		int32_t L_59 = ___2_index;
		int32_t L_60 = L_59;
		___2_index = ((int32_t)il2cpp_codegen_add(L_60, 1));
		NullCheck(L_58);
		int32_t L_61 = L_60;
		uint8_t L_62 = (L_58)->GetAt(static_cast<il2cpp_array_size_t>(L_61));
		V_0 = ((int64_t)il2cpp_codegen_add(L_57, ((int64_t)((int32_t)((int32_t)L_62&((int32_t)255))))));
		int64_t L_63 = V_1;
		int64_t L_64 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_63, L_64));
		int64_t L_65 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_66 = ___1_buf;
		int32_t L_67 = ___2_index;
		int32_t L_68 = L_67;
		___2_index = ((int32_t)il2cpp_codegen_add(L_68, 1));
		NullCheck(L_66);
		int32_t L_69 = L_68;
		uint8_t L_70 = (L_66)->GetAt(static_cast<il2cpp_array_size_t>(L_69));
		V_0 = ((int64_t)il2cpp_codegen_add(L_65, ((int64_t)((int32_t)((int32_t)L_70&((int32_t)255))))));
		int64_t L_71 = V_1;
		int64_t L_72 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_71, L_72));
		int64_t L_73 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_74 = ___1_buf;
		int32_t L_75 = ___2_index;
		int32_t L_76 = L_75;
		___2_index = ((int32_t)il2cpp_codegen_add(L_76, 1));
		NullCheck(L_74);
		int32_t L_77 = L_76;
		uint8_t L_78 = (L_74)->GetAt(static_cast<il2cpp_array_size_t>(L_77));
		V_0 = ((int64_t)il2cpp_codegen_add(L_73, ((int64_t)((int32_t)((int32_t)L_78&((int32_t)255))))));
		int64_t L_79 = V_1;
		int64_t L_80 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_79, L_80));
		int64_t L_81 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_82 = ___1_buf;
		int32_t L_83 = ___2_index;
		int32_t L_84 = L_83;
		___2_index = ((int32_t)il2cpp_codegen_add(L_84, 1));
		NullCheck(L_82);
		int32_t L_85 = L_84;
		uint8_t L_86 = (L_82)->GetAt(static_cast<il2cpp_array_size_t>(L_85));
		V_0 = ((int64_t)il2cpp_codegen_add(L_81, ((int64_t)((int32_t)((int32_t)L_86&((int32_t)255))))));
		int64_t L_87 = V_1;
		int64_t L_88 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_87, L_88));
		int64_t L_89 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_90 = ___1_buf;
		int32_t L_91 = ___2_index;
		int32_t L_92 = L_91;
		___2_index = ((int32_t)il2cpp_codegen_add(L_92, 1));
		NullCheck(L_90);
		int32_t L_93 = L_92;
		uint8_t L_94 = (L_90)->GetAt(static_cast<il2cpp_array_size_t>(L_93));
		V_0 = ((int64_t)il2cpp_codegen_add(L_89, ((int64_t)((int32_t)((int32_t)L_94&((int32_t)255))))));
		int64_t L_95 = V_1;
		int64_t L_96 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_95, L_96));
		int64_t L_97 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_98 = ___1_buf;
		int32_t L_99 = ___2_index;
		int32_t L_100 = L_99;
		___2_index = ((int32_t)il2cpp_codegen_add(L_100, 1));
		NullCheck(L_98);
		int32_t L_101 = L_100;
		uint8_t L_102 = (L_98)->GetAt(static_cast<il2cpp_array_size_t>(L_101));
		V_0 = ((int64_t)il2cpp_codegen_add(L_97, ((int64_t)((int32_t)((int32_t)L_102&((int32_t)255))))));
		int64_t L_103 = V_1;
		int64_t L_104 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_103, L_104));
		int64_t L_105 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_106 = ___1_buf;
		int32_t L_107 = ___2_index;
		int32_t L_108 = L_107;
		___2_index = ((int32_t)il2cpp_codegen_add(L_108, 1));
		NullCheck(L_106);
		int32_t L_109 = L_108;
		uint8_t L_110 = (L_106)->GetAt(static_cast<il2cpp_array_size_t>(L_109));
		V_0 = ((int64_t)il2cpp_codegen_add(L_105, ((int64_t)((int32_t)((int32_t)L_110&((int32_t)255))))));
		int64_t L_111 = V_1;
		int64_t L_112 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_111, L_112));
		int64_t L_113 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_114 = ___1_buf;
		int32_t L_115 = ___2_index;
		int32_t L_116 = L_115;
		___2_index = ((int32_t)il2cpp_codegen_add(L_116, 1));
		NullCheck(L_114);
		int32_t L_117 = L_116;
		uint8_t L_118 = (L_114)->GetAt(static_cast<il2cpp_array_size_t>(L_117));
		V_0 = ((int64_t)il2cpp_codegen_add(L_113, ((int64_t)((int32_t)((int32_t)L_118&((int32_t)255))))));
		int64_t L_119 = V_1;
		int64_t L_120 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_119, L_120));
		int64_t L_121 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_122 = ___1_buf;
		int32_t L_123 = ___2_index;
		int32_t L_124 = L_123;
		___2_index = ((int32_t)il2cpp_codegen_add(L_124, 1));
		NullCheck(L_122);
		int32_t L_125 = L_124;
		uint8_t L_126 = (L_122)->GetAt(static_cast<il2cpp_array_size_t>(L_125));
		V_0 = ((int64_t)il2cpp_codegen_add(L_121, ((int64_t)((int32_t)((int32_t)L_126&((int32_t)255))))));
		int64_t L_127 = V_1;
		int64_t L_128 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_127, L_128));
		int64_t L_129 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_130 = ___1_buf;
		int32_t L_131 = ___2_index;
		int32_t L_132 = L_131;
		___2_index = ((int32_t)il2cpp_codegen_add(L_132, 1));
		NullCheck(L_130);
		int32_t L_133 = L_132;
		uint8_t L_134 = (L_130)->GetAt(static_cast<il2cpp_array_size_t>(L_133));
		V_0 = ((int64_t)il2cpp_codegen_add(L_129, ((int64_t)((int32_t)((int32_t)L_134&((int32_t)255))))));
		int64_t L_135 = V_1;
		int64_t L_136 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_135, L_136));
		int32_t L_137 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_subtract(L_137, ((int32_t)16)));
	}

IL_01a9:
	{
		int32_t L_138 = V_2;
		if ((((int32_t)L_138) >= ((int32_t)((int32_t)16))))
		{
			goto IL_0044;
		}
	}
	{
		int32_t L_139 = V_2;
		if (!L_139)
		{
			goto IL_01d7;
		}
	}

IL_01b7:
	{
		int64_t L_140 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_141 = ___1_buf;
		int32_t L_142 = ___2_index;
		int32_t L_143 = L_142;
		___2_index = ((int32_t)il2cpp_codegen_add(L_143, 1));
		NullCheck(L_141);
		int32_t L_144 = L_143;
		uint8_t L_145 = (L_141)->GetAt(static_cast<il2cpp_array_size_t>(L_144));
		V_0 = ((int64_t)il2cpp_codegen_add(L_140, ((int64_t)((int32_t)((int32_t)L_145&((int32_t)255))))));
		int64_t L_146 = V_1;
		int64_t L_147 = V_0;
		V_1 = ((int64_t)il2cpp_codegen_add(L_146, L_147));
		int32_t L_148 = V_2;
		int32_t L_149 = ((int32_t)il2cpp_codegen_subtract(L_148, 1));
		V_2 = L_149;
		if (L_149)
		{
			goto IL_01b7;
		}
	}

IL_01d7:
	{
		int64_t L_150 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var);
		int32_t L_151 = ((Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_StaticFields*)il2cpp_codegen_static_fields_for(Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var))->___BASE;
		V_0 = ((int64_t)(L_150%((int64_t)L_151)));
		int64_t L_152 = V_1;
		int32_t L_153 = ((Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_StaticFields*)il2cpp_codegen_static_fields_for(Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var))->___BASE;
		V_1 = ((int64_t)(L_152%((int64_t)L_153)));
	}

IL_01e9:
	{
		int32_t L_154 = ___3_len;
		if ((((int32_t)L_154) > ((int32_t)0)))
		{
			goto IL_0023;
		}
	}
	{
		int64_t L_155 = V_1;
		int64_t L_156 = V_0;
		return ((int64_t)(((int64_t)(L_155<<((int32_t)16)))|L_156));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Adler__cctor_m3CEFE6FA47D503A1D6EEFEBE1C38585A1EE10220 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		((Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_StaticFields*)il2cpp_codegen_static_fields_for(Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var))->___BASE = ((int32_t)65521);
		((Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_StaticFields*)il2cpp_codegen_static_fields_for(Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var))->___NMAX = ((int32_t)5552);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibCodec__ctor_m40C747342EAC3B3891ABCB80C5E34229FFF68D0F (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_InitializeInflate_m326D69D2B9CD25CAD8E83064A6891F178AB1B116 (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, bool ___0_expectRfc1950Header, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_expectRfc1950Header;
		int32_t L_1;
		L_1 = ZlibCodec_InitializeInflate_mCBD1E3DA91FF1CA7064486432E262E24F5E1877E(__this, ((int32_t)15), L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_InitializeInflate_mCBD1E3DA91FF1CA7064486432E262E24F5E1877E (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, int32_t ___0_windowBits, bool ___1_expectRfc1950Header, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_0 = __this->___dstate;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_1 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_1, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral0FE5434C6E938CF30A74BC53A565C36727AA6E9E)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_1, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibCodec_InitializeInflate_mCBD1E3DA91FF1CA7064486432E262E24F5E1877E_RuntimeMethod_var)));
	}

IL_0016:
	{
		bool L_2 = ___1_expectRfc1950Header;
		InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7* L_3 = (InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7*)il2cpp_codegen_object_new(InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7_il2cpp_TypeInfo_var);
		InflateManager__ctor_mEF35131A0B33EEC084DE5678CDFB10CCF95227C4(L_3, L_2, NULL);
		__this->___istate = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___istate), (void*)L_3);
		InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7* L_4 = __this->___istate;
		int32_t L_5 = ___0_windowBits;
		NullCheck(L_4);
		int32_t L_6;
		L_6 = InflateManager_Initialize_m61634F91BC501376E92B59399B7A89DCABA6586B(L_4, __this, L_5, NULL);
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_Inflate_mEFD195C98EAC837238E73A3E763AB324379A4550 (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, int32_t ___0_f, const RuntimeMethod* method) 
{
	{
		InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7* L_0 = __this->___istate;
		if (L_0)
		{
			goto IL_0016;
		}
	}
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_1 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_1, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF0FA167A2B553EA3A482772133E47AA3C132FFA2)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_1, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibCodec_Inflate_mEFD195C98EAC837238E73A3E763AB324379A4550_RuntimeMethod_var)));
	}

IL_0016:
	{
		InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7* L_2 = __this->___istate;
		int32_t L_3 = ___0_f;
		NullCheck(L_2);
		int32_t L_4;
		L_4 = InflateManager_Inflate_m50B8CF154C3B2375CE085841C36F9750C2FAAF39(L_2, __this, L_3, NULL);
		return L_4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_EndInflate_mE7E95E904F7098F6899D8BD79823CFCDE877C4B9 (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7* L_0 = __this->___istate;
		if (L_0)
		{
			goto IL_0016;
		}
	}
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_1 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_1, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF0FA167A2B553EA3A482772133E47AA3C132FFA2)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_1, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibCodec_EndInflate_mE7E95E904F7098F6899D8BD79823CFCDE877C4B9_RuntimeMethod_var)));
	}

IL_0016:
	{
		InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7* L_2 = __this->___istate;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = InflateManager_End_mB7EF05FA9F1FFE1086DE00C05CA9CA4E31E455E1(L_2, __this, NULL);
		V_0 = L_3;
		__this->___istate = (InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___istate), (void*)(InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7*)NULL);
		int32_t L_4 = V_0;
		return L_4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_InitializeDeflate_m542BFAC119F9EA2F3F15B7C87E2714F82188A9EB (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, int32_t ___0_level, bool ___1_wantRfc1950Header, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_level;
		bool L_1 = ___1_wantRfc1950Header;
		int32_t L_2;
		L_2 = ZlibCodec_InitializeDeflate_m5553968574555F56D0E692871C12333B6138EDDA(__this, L_0, ((int32_t)15), L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_InitializeDeflate_m5553968574555F56D0E692871C12333B6138EDDA (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, int32_t ___0_level, int32_t ___1_bits, bool ___2_wantRfc1950Header, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		InflateManager_t5F82DC3176F43CDDEBBDB4043D863BFCBE770FC7* L_0 = __this->___istate;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_1 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_1, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral9AE36592E95F2354A6676CC52CF848B11A46DBD9)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_1, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibCodec_InitializeDeflate_m5553968574555F56D0E692871C12333B6138EDDA_RuntimeMethod_var)));
	}

IL_0016:
	{
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_2 = (DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065*)il2cpp_codegen_object_new(DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065_il2cpp_TypeInfo_var);
		DeflateManager__ctor_mA7B904F9B3FCC870B5A53FAE3B0AE4C725B84328(L_2, NULL);
		__this->___dstate = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___dstate), (void*)L_2);
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_3 = __this->___dstate;
		bool L_4 = ___2_wantRfc1950Header;
		NullCheck(L_3);
		DeflateManager_set_WantRfc1950HeaderBytes_m799CF5C55738EC42D599049066DDD7F5D5392338_inline(L_3, L_4, NULL);
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_5 = __this->___dstate;
		int32_t L_6 = ___0_level;
		int32_t L_7 = ___1_bits;
		NullCheck(L_5);
		int32_t L_8;
		L_8 = DeflateManager_Initialize_m8323A058D2E8FF0EF623FE6353016E5D96378F7B(L_5, __this, L_6, L_7, NULL);
		return L_8;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_Deflate_m68368C43EA37CDB2D268D1EAD2F2F4620FA46330 (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, int32_t ___0_flush, const RuntimeMethod* method) 
{
	{
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_0 = __this->___dstate;
		if (L_0)
		{
			goto IL_0016;
		}
	}
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_1 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_1, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral983DAC088DBADBDF2CEE0A7F894D264FCD8D805D)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_1, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibCodec_Deflate_m68368C43EA37CDB2D268D1EAD2F2F4620FA46330_RuntimeMethod_var)));
	}

IL_0016:
	{
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_2 = __this->___dstate;
		int32_t L_3 = ___0_flush;
		NullCheck(L_2);
		int32_t L_4;
		L_4 = DeflateManager_Deflate_m40DAAB1A938EAFD48A53AD38B4865575F9987FAC(L_2, __this, L_3, NULL);
		return L_4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_EndDeflate_mFF40B57BD0A08EB4543C2EA55C8DCB069899E43D (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_0 = __this->___dstate;
		if (L_0)
		{
			goto IL_0016;
		}
	}
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_1 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_1, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral983DAC088DBADBDF2CEE0A7F894D264FCD8D805D)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_1, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibCodec_EndDeflate_mFF40B57BD0A08EB4543C2EA55C8DCB069899E43D_RuntimeMethod_var)));
	}

IL_0016:
	{
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_2 = __this->___dstate;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = DeflateManager_End_m4CD9A64B75E607003F5EF5E5C459BFF4EA6A3319(L_2, NULL);
		V_0 = L_3;
		__this->___dstate = (DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___dstate), (void*)(DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065*)NULL);
		int32_t L_4 = V_0;
		return L_4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibCodec_flush_pending_mD40B5B6D11072492E3AAD5FF14964E75B56DAE56 (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_0 = __this->___dstate;
		NullCheck(L_0);
		int32_t L_1 = L_0->___pendingCount;
		V_0 = L_1;
		int32_t L_2 = V_0;
		int32_t L_3 = __this->___AvailableBytesOut;
		if ((((int32_t)L_2) <= ((int32_t)L_3)))
		{
			goto IL_001f;
		}
	}
	{
		int32_t L_4 = __this->___AvailableBytesOut;
		V_0 = L_4;
	}

IL_001f:
	{
		int32_t L_5 = V_0;
		if (L_5)
		{
			goto IL_0026;
		}
	}
	{
		return;
	}

IL_0026:
	{
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_6 = __this->___dstate;
		NullCheck(L_6);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_7 = L_6->___pending;
		NullCheck(L_7);
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_8 = __this->___dstate;
		NullCheck(L_8);
		int32_t L_9 = L_8->___nextPending;
		if ((((int32_t)((int32_t)(((RuntimeArray*)L_7)->max_length))) <= ((int32_t)L_9)))
		{
			goto IL_008a;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_10 = __this->___OutputBuffer;
		NullCheck(L_10);
		int32_t L_11 = __this->___NextOut;
		if ((((int32_t)((int32_t)(((RuntimeArray*)L_10)->max_length))) <= ((int32_t)L_11)))
		{
			goto IL_008a;
		}
	}
	{
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_12 = __this->___dstate;
		NullCheck(L_12);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_13 = L_12->___pending;
		NullCheck(L_13);
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_14 = __this->___dstate;
		NullCheck(L_14);
		int32_t L_15 = L_14->___nextPending;
		int32_t L_16 = V_0;
		if ((((int32_t)((int32_t)(((RuntimeArray*)L_13)->max_length))) < ((int32_t)((int32_t)il2cpp_codegen_add(L_15, L_16)))))
		{
			goto IL_008a;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_17 = __this->___OutputBuffer;
		NullCheck(L_17);
		int32_t L_18 = __this->___NextOut;
		int32_t L_19 = V_0;
		if ((((int32_t)((int32_t)(((RuntimeArray*)L_17)->max_length))) >= ((int32_t)((int32_t)il2cpp_codegen_add(L_18, L_19)))))
		{
			goto IL_00bc;
		}
	}

IL_008a:
	{
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_20 = __this->___dstate;
		NullCheck(L_20);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_21 = L_20->___pending;
		NullCheck(L_21);
		int32_t L_22 = ((int32_t)(((RuntimeArray*)L_21)->max_length));
		RuntimeObject* L_23 = Box(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var)), &L_22);
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_24 = __this->___dstate;
		NullCheck(L_24);
		int32_t L_25 = L_24->___pendingCount;
		int32_t L_26 = L_25;
		RuntimeObject* L_27 = Box(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var)), &L_26);
		String_t* L_28;
		L_28 = String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral09BE1A5FDF1304B1AF66C7AA9E11F5D68F1A7A53)), L_23, L_27, NULL);
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_29 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_29, L_28, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_29, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibCodec_flush_pending_mD40B5B6D11072492E3AAD5FF14964E75B56DAE56_RuntimeMethod_var)));
	}

IL_00bc:
	{
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_30 = __this->___dstate;
		NullCheck(L_30);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_31 = L_30->___pending;
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_32 = __this->___dstate;
		NullCheck(L_32);
		int32_t L_33 = L_32->___nextPending;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_34 = __this->___OutputBuffer;
		int32_t L_35 = __this->___NextOut;
		int32_t L_36 = V_0;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_31, L_33, (RuntimeArray*)L_34, L_35, L_36, NULL);
		int32_t L_37 = __this->___NextOut;
		int32_t L_38 = V_0;
		__this->___NextOut = ((int32_t)il2cpp_codegen_add(L_37, L_38));
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_39 = __this->___dstate;
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_40 = L_39;
		NullCheck(L_40);
		int32_t L_41 = L_40->___nextPending;
		int32_t L_42 = V_0;
		NullCheck(L_40);
		L_40->___nextPending = ((int32_t)il2cpp_codegen_add(L_41, L_42));
		int64_t L_43 = __this->___TotalBytesOut;
		int32_t L_44 = V_0;
		__this->___TotalBytesOut = ((int64_t)il2cpp_codegen_add(L_43, ((int64_t)L_44)));
		int32_t L_45 = __this->___AvailableBytesOut;
		int32_t L_46 = V_0;
		__this->___AvailableBytesOut = ((int32_t)il2cpp_codegen_subtract(L_45, L_46));
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_47 = __this->___dstate;
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_48 = L_47;
		NullCheck(L_48);
		int32_t L_49 = L_48->___pendingCount;
		int32_t L_50 = V_0;
		NullCheck(L_48);
		L_48->___pendingCount = ((int32_t)il2cpp_codegen_subtract(L_49, L_50));
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_51 = __this->___dstate;
		NullCheck(L_51);
		int32_t L_52 = L_51->___pendingCount;
		if (L_52)
		{
			goto IL_0151;
		}
	}
	{
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_53 = __this->___dstate;
		NullCheck(L_53);
		L_53->___nextPending = 0;
	}

IL_0151:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibCodec_read_buf_mED8E85BF915B67D19C5659B5CE4BEC43EFD6B84E (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buf, int32_t ___1_start, int32_t ___2_size, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		int32_t L_0 = __this->___AvailableBytesIn;
		V_0 = L_0;
		int32_t L_1 = V_0;
		int32_t L_2 = ___2_size;
		if ((((int32_t)L_1) <= ((int32_t)L_2)))
		{
			goto IL_0010;
		}
	}
	{
		int32_t L_3 = ___2_size;
		V_0 = L_3;
	}

IL_0010:
	{
		int32_t L_4 = V_0;
		if (L_4)
		{
			goto IL_0018;
		}
	}
	{
		return 0;
	}

IL_0018:
	{
		int32_t L_5 = __this->___AvailableBytesIn;
		int32_t L_6 = V_0;
		__this->___AvailableBytesIn = ((int32_t)il2cpp_codegen_subtract(L_5, L_6));
		DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* L_7 = __this->___dstate;
		NullCheck(L_7);
		bool L_8;
		L_8 = DeflateManager_get_WantRfc1950HeaderBytes_m5661399ADA54DFF965A373E2BECC13189F25FFEE_inline(L_7, NULL);
		if (!L_8)
		{
			goto IL_0054;
		}
	}
	{
		int64_t L_9 = __this->____Adler32;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_10 = __this->___InputBuffer;
		int32_t L_11 = __this->___NextIn;
		int32_t L_12 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Adler_t254C6E1194444DD56B44F909FDCF53C6E8561698_il2cpp_TypeInfo_var);
		int64_t L_13;
		L_13 = Adler_Adler32_m8A3761EF98770BFCEE3242665825CB496E4C8313(L_9, L_10, L_11, L_12, NULL);
		__this->____Adler32 = L_13;
	}

IL_0054:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_14 = __this->___InputBuffer;
		int32_t L_15 = __this->___NextIn;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_16 = ___0_buf;
		int32_t L_17 = ___1_start;
		int32_t L_18 = V_0;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_14, L_15, (RuntimeArray*)L_16, L_17, L_18, NULL);
		int32_t L_19 = __this->___NextIn;
		int32_t L_20 = V_0;
		__this->___NextIn = ((int32_t)il2cpp_codegen_add(L_19, L_20));
		int64_t L_21 = __this->___TotalBytesIn;
		int32_t L_22 = V_0;
		__this->___TotalBytesIn = ((int64_t)il2cpp_codegen_add(L_21, ((int64_t)L_22)));
		int32_t L_23 = V_0;
		return L_23;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GZipStream__ctor_mBDDB00F6B2BE4A007CD4441E2B8E6701EA599D0B (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* ___0_stream, int32_t ___1_mode, const RuntimeMethod* method) 
{
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_0 = ___0_stream;
		int32_t L_1 = ___1_mode;
		GZipStream__ctor_m72A5DAF83F1394062C95CA12E59E17EA848E4741(__this, L_0, L_1, 6, (bool)0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GZipStream__ctor_m72A5DAF83F1394062C95CA12E59E17EA848E4741 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* ___0_stream, int32_t ___1_mode, int32_t ___2_level, bool ___3_leaveOpen, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE_il2cpp_TypeInfo_var);
		Stream__ctor_mE8B074A0EBEB026FFF14062AB4B8A78E17EFFBF0(__this, NULL);
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_0 = ___0_stream;
		int32_t L_1 = ___1_mode;
		int32_t L_2 = ___2_level;
		bool L_3 = ___3_leaveOpen;
		ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* L_4 = (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B*)il2cpp_codegen_object_new(ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B_il2cpp_TypeInfo_var);
		ZlibBaseStream__ctor_mCA4A9F54161B0AEDAD281EE0CC09247BBB41F6E7(L_4, L_0, L_1, L_2, ((int32_t)1952), L_3, NULL);
		__this->____baseStream = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____baseStream), (void*)L_4);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* GZipStream_get_FileName_m0D6FA7D5C31AA7D4849C22BEA1ACCFD8CA798E83 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->____FileName;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GZipStream_set_FileName_m1BBFAD7EB67A55FE4F47A1AF9600C1D9F03805E3 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, String_t* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Path_t8A38A801D0219E8209C1B1D90D82D4D755D998BC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral09B11B6CC411D8B9FFB75EAAE9A35B2AF248CE40);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral86BBAACC00198DBB3046818AD3FC2AA10AE48DE1);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ___0_value;
		__this->____FileName = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____FileName), (void*)L_0);
		String_t* L_1 = __this->____FileName;
		if (L_1)
		{
			goto IL_0013;
		}
	}
	{
		return;
	}

IL_0013:
	{
		String_t* L_2 = __this->____FileName;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = String_IndexOf_m69E9BDAFD93767C85A7FF861B453415D3B4A200F(L_2, _stringLiteral86BBAACC00198DBB3046818AD3FC2AA10AE48DE1, NULL);
		if ((((int32_t)L_3) == ((int32_t)(-1))))
		{
			goto IL_0044;
		}
	}
	{
		String_t* L_4 = __this->____FileName;
		NullCheck(L_4);
		String_t* L_5;
		L_5 = String_Replace_mABDB7003A1D0AEDCAE9FF85E3DFFFBA752D2A166(L_4, _stringLiteral86BBAACC00198DBB3046818AD3FC2AA10AE48DE1, _stringLiteral09B11B6CC411D8B9FFB75EAAE9A35B2AF248CE40, NULL);
		__this->____FileName = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____FileName), (void*)L_5);
	}

IL_0044:
	{
		String_t* L_6 = __this->____FileName;
		NullCheck(L_6);
		bool L_7;
		L_7 = String_EndsWith_mCD3754F5401E19CE7821CD398986E4EAA6AD87DC(L_6, _stringLiteral09B11B6CC411D8B9FFB75EAAE9A35B2AF248CE40, NULL);
		if (!L_7)
		{
			goto IL_0064;
		}
	}
	{
		Exception_t* L_8 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_8, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral9FC4F45EF48BB08BC4D6C5BC2A71B64139E0FEE4)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_8, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&GZipStream_set_FileName_m1BBFAD7EB67A55FE4F47A1AF9600C1D9F03805E3_RuntimeMethod_var)));
	}

IL_0064:
	{
		String_t* L_9 = __this->____FileName;
		NullCheck(L_9);
		int32_t L_10;
		L_10 = String_IndexOf_m69E9BDAFD93767C85A7FF861B453415D3B4A200F(L_9, _stringLiteral09B11B6CC411D8B9FFB75EAAE9A35B2AF248CE40, NULL);
		if ((((int32_t)L_10) == ((int32_t)(-1))))
		{
			goto IL_008b;
		}
	}
	{
		String_t* L_11 = __this->____FileName;
		il2cpp_codegen_runtime_class_init_inline(Path_t8A38A801D0219E8209C1B1D90D82D4D755D998BC_il2cpp_TypeInfo_var);
		String_t* L_12;
		L_12 = Path_GetFileName_mB1A8CE314EE250B06E3D33142315E2BD3A75D1D6(L_11, NULL);
		__this->____FileName = L_12;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____FileName), (void*)L_12);
	}

IL_008b:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GZipStream_Close_mCEC51FA814FFBE146D6479832FCEF04CBAFFAAD7 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, const RuntimeMethod* method) 
{
	{
		ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* L_0 = __this->____baseStream;
		NullCheck(L_0);
		VirtualActionInvoker0::Invoke(18, L_0);
		ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* L_1 = __this->____baseStream;
		NullCheck(L_1);
		int32_t L_2;
		L_2 = ZlibBaseStream_get_Crc32_m240AACB0116DFA174E4752C5E958948348E2CCC1(L_1, NULL);
		__this->____Crc32 = L_2;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool GZipStream_get_CanRead_m3957B0FBD4C180B037BE1C3D142F13D7163FE9DF (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, const RuntimeMethod* method) 
{
	{
		ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* L_0 = __this->____baseStream;
		NullCheck(L_0);
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_1 = L_0->____stream;
		NullCheck(L_1);
		bool L_2;
		L_2 = VirtualFuncInvoker0< bool >::Invoke(7, L_1);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool GZipStream_get_CanSeek_mEA092245DA6306EFD5D314D33C0DFC8578AC94F6 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, const RuntimeMethod* method) 
{
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool GZipStream_get_CanWrite_m41F144C4C39711004F90C6C6C8559018CB4468CB (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, const RuntimeMethod* method) 
{
	{
		ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* L_0 = __this->____baseStream;
		NullCheck(L_0);
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_1 = L_0->____stream;
		NullCheck(L_1);
		bool L_2;
		L_2 = VirtualFuncInvoker0< bool >::Invoke(10, L_1);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GZipStream_Flush_mECF86502A2DD3C91AB2DC198F959841C1FF5C13B (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, const RuntimeMethod* method) 
{
	{
		ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* L_0 = __this->____baseStream;
		NullCheck(L_0);
		VirtualActionInvoker0::Invoke(20, L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t GZipStream_get_Length_m5FC04810DDDEE5E6DDA2CAC1DAD9D5F5D8362F32 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, const RuntimeMethod* method) 
{
	{
		NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8* L_0 = (NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8_il2cpp_TypeInfo_var)));
		NotImplementedException__ctor_mDAB47BC6BD0E342E8F2171E5CABE3E67EA049F1C(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&GZipStream_get_Length_m5FC04810DDDEE5E6DDA2CAC1DAD9D5F5D8362F32_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t GZipStream_get_Position_m54F39FEB928AD39788C63C43527322859163841A (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, const RuntimeMethod* method) 
{
	{
		NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8* L_0 = (NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8_il2cpp_TypeInfo_var)));
		NotImplementedException__ctor_mDAB47BC6BD0E342E8F2171E5CABE3E67EA049F1C(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&GZipStream_get_Position_m54F39FEB928AD39788C63C43527322859163841A_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GZipStream_set_Position_mAC32F5E26F25BEE5CFDA744C0D14A8618492FB3F (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, int64_t ___0_value, const RuntimeMethod* method) 
{
	{
		NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8* L_0 = (NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8_il2cpp_TypeInfo_var)));
		NotImplementedException__ctor_mDAB47BC6BD0E342E8F2171E5CABE3E67EA049F1C(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&GZipStream_set_Position_mAC32F5E26F25BEE5CFDA744C0D14A8618492FB3F_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t GZipStream_Read_m34A44666EC5B822EB020DAF8916EA2481BB18942 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, int32_t ___1_offset, int32_t ___2_count, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* L_0 = __this->____baseStream;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = ___0_buffer;
		int32_t L_2 = ___1_offset;
		int32_t L_3 = ___2_count;
		NullCheck(L_0);
		int32_t L_4;
		L_4 = VirtualFuncInvoker3< int32_t, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(31, L_0, L_1, L_2, L_3);
		V_0 = L_4;
		bool L_5 = __this->____firstReadDone;
		if (L_5)
		{
			goto IL_0043;
		}
	}
	{
		__this->____firstReadDone = (bool)1;
		ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* L_6 = __this->____baseStream;
		NullCheck(L_6);
		String_t* L_7 = L_6->____GzipFileName;
		GZipStream_set_FileName_m1BBFAD7EB67A55FE4F47A1AF9600C1D9F03805E3(__this, L_7, NULL);
		ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* L_8 = __this->____baseStream;
		NullCheck(L_8);
		String_t* L_9 = L_8->____GzipComment;
		__this->___Comment = L_9;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___Comment), (void*)L_9);
	}

IL_0043:
	{
		int32_t L_10 = V_0;
		return L_10;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t GZipStream_Seek_m8503951F8A4D64BF1CFE2026D09E07CF26BDE6FE (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, int64_t ___0_offset, int32_t ___1_origin, const RuntimeMethod* method) 
{
	{
		NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8* L_0 = (NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8_il2cpp_TypeInfo_var)));
		NotImplementedException__ctor_mDAB47BC6BD0E342E8F2171E5CABE3E67EA049F1C(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&GZipStream_Seek_m8503951F8A4D64BF1CFE2026D09E07CF26BDE6FE_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GZipStream_SetLength_mA8E5EFF8E8A24E0C32205F97B26D0C48B817A75A (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, int64_t ___0_value, const RuntimeMethod* method) 
{
	{
		NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8* L_0 = (NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8_il2cpp_TypeInfo_var)));
		NotImplementedException__ctor_mDAB47BC6BD0E342E8F2171E5CABE3E67EA049F1C(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&GZipStream_SetLength_mA8E5EFF8E8A24E0C32205F97B26D0C48B817A75A_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GZipStream_Write_mC524891790868544FBECFBE26A21C864F0CAC335 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, int32_t ___1_offset, int32_t ___2_count, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* L_0 = __this->____baseStream;
		NullCheck(L_0);
		int32_t L_1 = L_0->____streamMode;
		if ((!(((uint32_t)L_1) == ((uint32_t)2))))
		{
			goto IL_003f;
		}
	}
	{
		ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* L_2 = __this->____baseStream;
		NullCheck(L_2);
		bool L_3 = L_2->____wantCompress;
		if (!L_3)
		{
			goto IL_002c;
		}
	}
	{
		GZipStream_EmitHeader_m3D76DF2020913E4C80760EF5E24FB0E33874C527(__this, NULL);
		goto IL_003f;
	}

IL_002c:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = ___0_buffer;
		int32_t L_5 = ___1_offset;
		int32_t L_6;
		L_6 = GZipStream_SlurpHeader_m5D83302AAF7EB805EA37524883FA3FD9CDB98A8E(__this, L_4, L_5, NULL);
		V_0 = L_6;
		int32_t L_7 = ___1_offset;
		int32_t L_8 = V_0;
		___1_offset = ((int32_t)il2cpp_codegen_add(L_7, L_8));
		int32_t L_9 = ___2_count;
		int32_t L_10 = V_0;
		___2_count = ((int32_t)il2cpp_codegen_subtract(L_9, L_10));
	}

IL_003f:
	{
		ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* L_11 = __this->____baseStream;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_12 = ___0_buffer;
		int32_t L_13 = ___1_offset;
		int32_t L_14 = ___2_count;
		NullCheck(L_11);
		VirtualActionInvoker3< ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(34, L_11, L_12, L_13, L_14);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t GZipStream_SlurpHeader_m5D83302AAF7EB805EA37524883FA3FD9CDB98A8E (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, int32_t ___1_offset, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* V_1 = NULL;
	int16_t V_2 = 0;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* V_3 = NULL;
	int32_t V_4 = 0;
	{
		V_0 = 0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)10));
		V_1 = L_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = ___0_buffer;
		int32_t L_2 = ___1_offset;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = V_1;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = V_1;
		NullCheck(L_4);
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_1, L_2, (RuntimeArray*)L_3, 0, ((int32_t)(((RuntimeArray*)L_4)->max_length)), NULL);
		int32_t L_5 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_6 = V_1;
		NullCheck(L_6);
		V_0 = ((int32_t)il2cpp_codegen_add(L_5, ((int32_t)(((RuntimeArray*)L_6)->max_length))));
		int32_t L_7 = ___1_offset;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_8 = V_1;
		NullCheck(L_8);
		___1_offset = ((int32_t)il2cpp_codegen_add(L_7, ((int32_t)(((RuntimeArray*)L_8)->max_length))));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_9 = V_1;
		NullCheck(L_9);
		int32_t L_10 = 0;
		uint8_t L_11 = (L_9)->GetAt(static_cast<il2cpp_array_size_t>(L_10));
		if ((!(((uint32_t)L_11) == ((uint32_t)((int32_t)31)))))
		{
			goto IL_0043;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_12 = V_1;
		NullCheck(L_12);
		int32_t L_13 = 1;
		uint8_t L_14 = (L_12)->GetAt(static_cast<il2cpp_array_size_t>(L_13));
		if ((!(((uint32_t)L_14) == ((uint32_t)((int32_t)139)))))
		{
			goto IL_0043;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_15 = V_1;
		NullCheck(L_15);
		int32_t L_16 = 2;
		uint8_t L_17 = (L_15)->GetAt(static_cast<il2cpp_array_size_t>(L_16));
		if ((((int32_t)L_17) == ((int32_t)8)))
		{
			goto IL_004e;
		}
	}

IL_0043:
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_18 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_18, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralB2B8359BD91EB1DF5DC1B2DE300F3D1DC2B414E8)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_18, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&GZipStream_SlurpHeader_m5D83302AAF7EB805EA37524883FA3FD9CDB98A8E_RuntimeMethod_var)));
	}

IL_004e:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_19 = V_1;
		NullCheck(L_19);
		int32_t L_20 = 3;
		uint8_t L_21 = (L_19)->GetAt(static_cast<il2cpp_array_size_t>(L_20));
		if ((!(((uint32_t)((int32_t)((int32_t)L_21&4))) == ((uint32_t)4))))
		{
			goto IL_008f;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_22 = ___0_buffer;
		NullCheck(L_22);
		int32_t L_23 = ((int32_t)10);
		uint8_t L_24 = (L_22)->GetAt(static_cast<il2cpp_array_size_t>(L_23));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_25 = ___0_buffer;
		NullCheck(L_25);
		int32_t L_26 = ((int32_t)11);
		uint8_t L_27 = (L_25)->GetAt(static_cast<il2cpp_array_size_t>(L_26));
		V_2 = ((int16_t)((int32_t)il2cpp_codegen_add((int32_t)L_24, ((int32_t)il2cpp_codegen_multiply((int32_t)L_27, ((int32_t)256))))));
		int32_t L_28 = ___1_offset;
		___1_offset = ((int32_t)il2cpp_codegen_add(L_28, 2));
		int16_t L_29 = V_2;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_30 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)L_29);
		V_3 = L_30;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_31 = ___0_buffer;
		int32_t L_32 = ___1_offset;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_33 = V_3;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_34 = V_3;
		NullCheck(L_34);
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_31, L_32, (RuntimeArray*)L_33, 0, ((int32_t)(((RuntimeArray*)L_34)->max_length)), NULL);
		int32_t L_35 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_36 = V_3;
		NullCheck(L_36);
		V_0 = ((int32_t)il2cpp_codegen_add(L_35, ((int32_t)(((RuntimeArray*)L_36)->max_length))));
		int32_t L_37 = ___1_offset;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_38 = V_3;
		NullCheck(L_38);
		___1_offset = ((int32_t)il2cpp_codegen_add(L_37, ((int32_t)(((RuntimeArray*)L_38)->max_length))));
	}

IL_008f:
	{
		V_4 = 0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_39 = V_1;
		NullCheck(L_39);
		int32_t L_40 = 3;
		uint8_t L_41 = (L_39)->GetAt(static_cast<il2cpp_array_size_t>(L_40));
		if ((!(((uint32_t)((int32_t)((int32_t)L_41&8))) == ((uint32_t)8))))
		{
			goto IL_00b8;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_42 = ___0_buffer;
		int32_t L_43 = ___1_offset;
		String_t* L_44;
		L_44 = GZipStream_SlurpZeroTerminatedString_m24CFAC49769B74F0C3C800A8621D09FA73A61564(__this, L_42, L_43, (&V_4), NULL);
		GZipStream_set_FileName_m1BBFAD7EB67A55FE4F47A1AF9600C1D9F03805E3(__this, L_44, NULL);
		int32_t L_45 = V_0;
		int32_t L_46 = V_4;
		V_0 = ((int32_t)il2cpp_codegen_add(L_45, L_46));
		int32_t L_47 = ___1_offset;
		int32_t L_48 = V_4;
		___1_offset = ((int32_t)il2cpp_codegen_add(L_47, L_48));
	}

IL_00b8:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_49 = V_1;
		NullCheck(L_49);
		int32_t L_50 = 3;
		uint8_t L_51 = (L_49)->GetAt(static_cast<il2cpp_array_size_t>(L_50));
		if ((!(((uint32_t)((int32_t)((int32_t)L_51&((int32_t)16)))) == ((uint32_t)((int32_t)16)))))
		{
			goto IL_00e0;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_52 = ___0_buffer;
		int32_t L_53 = ___1_offset;
		String_t* L_54;
		L_54 = GZipStream_SlurpZeroTerminatedString_m24CFAC49769B74F0C3C800A8621D09FA73A61564(__this, L_52, L_53, (&V_4), NULL);
		__this->___Comment = L_54;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___Comment), (void*)L_54);
		int32_t L_55 = V_0;
		int32_t L_56 = V_4;
		V_0 = ((int32_t)il2cpp_codegen_add(L_55, L_56));
		int32_t L_57 = ___1_offset;
		int32_t L_58 = V_4;
		___1_offset = ((int32_t)il2cpp_codegen_add(L_57, L_58));
	}

IL_00e0:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_59 = V_1;
		NullCheck(L_59);
		int32_t L_60 = 3;
		uint8_t L_61 = (L_59)->GetAt(static_cast<il2cpp_array_size_t>(L_60));
		if ((!(((uint32_t)((int32_t)((int32_t)L_61&2))) == ((uint32_t)2))))
		{
			goto IL_00f4;
		}
	}
	{
		int32_t L_62 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_62, 1));
		int32_t L_63 = ___1_offset;
		___1_offset = ((int32_t)il2cpp_codegen_add(L_63, 1));
	}

IL_00f4:
	{
		int32_t L_64 = V_0;
		return L_64;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* GZipStream_SlurpZeroTerminatedString_m24CFAC49769B74F0C3C800A8621D09FA73A61564 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, int32_t ___1_offset, int32_t* ___2_count, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_mCF9C36A7DE08AFB2D2EEC76139677E086A6CAD73_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_ToArray_m23F8BB7CABADC11EBCAB5AB701B75F56C865BE24_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mC411581553467A7DB76C06E5E5FCA34E4EC621B9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* V_0 = NULL;
	bool V_1 = false;
	int32_t V_2 = 0;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* V_3 = NULL;
	{
		List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* L_0 = (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B*)il2cpp_codegen_object_new(List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B_il2cpp_TypeInfo_var);
		List_1__ctor_mC411581553467A7DB76C06E5E5FCA34E4EC621B9(L_0, List_1__ctor_mC411581553467A7DB76C06E5E5FCA34E4EC621B9_RuntimeMethod_var);
		V_0 = L_0;
		V_1 = (bool)0;
		int32_t* L_1 = ___2_count;
		*((int32_t*)L_1) = (int32_t)0;
	}

IL_000b:
	{
		int32_t L_2 = ___1_offset;
		int32_t* L_3 = ___2_count;
		int32_t L_4 = *((int32_t*)L_3);
		V_2 = ((int32_t)il2cpp_codegen_add(L_2, L_4));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_5 = ___0_buffer;
		int32_t L_6 = V_2;
		NullCheck(L_5);
		int32_t L_7 = L_6;
		uint8_t L_8 = (L_5)->GetAt(static_cast<il2cpp_array_size_t>(L_7));
		if (L_8)
		{
			goto IL_001f;
		}
	}
	{
		V_1 = (bool)1;
		goto IL_0028;
	}

IL_001f:
	{
		List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* L_9 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_10 = ___0_buffer;
		int32_t L_11 = V_2;
		NullCheck(L_10);
		int32_t L_12 = L_11;
		uint8_t L_13 = (L_10)->GetAt(static_cast<il2cpp_array_size_t>(L_12));
		NullCheck(L_9);
		List_1_Add_mCF9C36A7DE08AFB2D2EEC76139677E086A6CAD73_inline(L_9, L_13, List_1_Add_mCF9C36A7DE08AFB2D2EEC76139677E086A6CAD73_RuntimeMethod_var);
	}

IL_0028:
	{
		int32_t* L_14 = ___2_count;
		int32_t* L_15 = ___2_count;
		int32_t L_16 = *((int32_t*)L_15);
		*((int32_t*)L_14) = (int32_t)((int32_t)il2cpp_codegen_add(L_16, 1));
		bool L_17 = V_1;
		if (!L_17)
		{
			goto IL_000b;
		}
	}
	{
		List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* L_18 = V_0;
		NullCheck(L_18);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_19;
		L_19 = List_1_ToArray_m23F8BB7CABADC11EBCAB5AB701B75F56C865BE24(L_18, List_1_ToArray_m23F8BB7CABADC11EBCAB5AB701B75F56C865BE24_RuntimeMethod_var);
		V_3 = L_19;
		il2cpp_codegen_runtime_class_init_inline(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var);
		Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* L_20 = ((GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_StaticFields*)il2cpp_codegen_static_fields_for(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var))->___iso8859dash1;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_21 = V_3;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_22 = V_3;
		NullCheck(L_22);
		NullCheck(L_20);
		String_t* L_23;
		L_23 = VirtualFuncInvoker3< String_t*, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(36, L_20, L_21, 0, ((int32_t)(((RuntimeArray*)L_22)->max_length)));
		return L_23;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GZipStream_EmitHeader_m3D76DF2020913E4C80760EF5E24FB0E33874C527 (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1__ctor_mB17304720EA19F5469A4883827F53A75FEB492CF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_get_HasValue_m092C73DCE052BFB5C60A39EF9F4E3401AA95221C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_get_Value_m5A868F663848BC21C18F056731D3AC404CE59991_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* V_0 = NULL;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* V_1 = NULL;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* V_5 = NULL;
	int32_t V_6 = 0;
	uint8_t V_7 = 0x0;
	TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A V_8;
	memset((&V_8), 0, sizeof(V_8));
	int32_t V_9 = 0;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* G_B3_0 = NULL;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* G_B6_0 = NULL;
	int32_t G_B9_0 = 0;
	int32_t G_B12_0 = 0;
	{
		String_t* L_0 = __this->___Comment;
		if (L_0)
		{
			goto IL_0011;
		}
	}
	{
		G_B3_0 = ((ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(NULL));
		goto IL_0021;
	}

IL_0011:
	{
		il2cpp_codegen_runtime_class_init_inline(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var);
		Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* L_1 = ((GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_StaticFields*)il2cpp_codegen_static_fields_for(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var))->___iso8859dash1;
		String_t* L_2 = __this->___Comment;
		NullCheck(L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3;
		L_3 = VirtualFuncInvoker1< ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, String_t* >::Invoke(18, L_1, L_2);
		G_B3_0 = L_3;
	}

IL_0021:
	{
		V_0 = G_B3_0;
		String_t* L_4;
		L_4 = GZipStream_get_FileName_m0D6FA7D5C31AA7D4849C22BEA1ACCFD8CA798E83_inline(__this, NULL);
		if (L_4)
		{
			goto IL_0033;
		}
	}
	{
		G_B6_0 = ((ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(NULL));
		goto IL_0043;
	}

IL_0033:
	{
		il2cpp_codegen_runtime_class_init_inline(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var);
		Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* L_5 = ((GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_StaticFields*)il2cpp_codegen_static_fields_for(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var))->___iso8859dash1;
		String_t* L_6;
		L_6 = GZipStream_get_FileName_m0D6FA7D5C31AA7D4849C22BEA1ACCFD8CA798E83_inline(__this, NULL);
		NullCheck(L_5);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_7;
		L_7 = VirtualFuncInvoker1< ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, String_t* >::Invoke(18, L_5, L_6);
		G_B6_0 = L_7;
	}

IL_0043:
	{
		V_1 = G_B6_0;
		String_t* L_8 = __this->___Comment;
		if (L_8)
		{
			goto IL_0055;
		}
	}
	{
		G_B9_0 = 0;
		goto IL_005a;
	}

IL_0055:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_9 = V_0;
		NullCheck(L_9);
		G_B9_0 = ((int32_t)il2cpp_codegen_add(((int32_t)(((RuntimeArray*)L_9)->max_length)), 1));
	}

IL_005a:
	{
		V_2 = G_B9_0;
		String_t* L_10;
		L_10 = GZipStream_get_FileName_m0D6FA7D5C31AA7D4849C22BEA1ACCFD8CA798E83_inline(__this, NULL);
		if (L_10)
		{
			goto IL_006c;
		}
	}
	{
		G_B12_0 = 0;
		goto IL_0071;
	}

IL_006c:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_11 = V_1;
		NullCheck(L_11);
		G_B12_0 = ((int32_t)il2cpp_codegen_add(((int32_t)(((RuntimeArray*)L_11)->max_length)), 1));
	}

IL_0071:
	{
		V_3 = G_B12_0;
		int32_t L_12 = V_2;
		int32_t L_13 = V_3;
		V_4 = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_add(((int32_t)10), L_12)), L_13));
		int32_t L_14 = V_4;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_15 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)L_14);
		V_5 = L_15;
		V_6 = 0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_16 = V_5;
		int32_t L_17 = V_6;
		int32_t L_18 = L_17;
		V_6 = ((int32_t)il2cpp_codegen_add(L_18, 1));
		NullCheck(L_16);
		(L_16)->SetAt(static_cast<il2cpp_array_size_t>(L_18), (uint8_t)((int32_t)31));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_19 = V_5;
		int32_t L_20 = V_6;
		int32_t L_21 = L_20;
		V_6 = ((int32_t)il2cpp_codegen_add(L_21, 1));
		NullCheck(L_19);
		(L_19)->SetAt(static_cast<il2cpp_array_size_t>(L_21), (uint8_t)((int32_t)139));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_22 = V_5;
		int32_t L_23 = V_6;
		int32_t L_24 = L_23;
		V_6 = ((int32_t)il2cpp_codegen_add(L_24, 1));
		NullCheck(L_22);
		(L_22)->SetAt(static_cast<il2cpp_array_size_t>(L_24), (uint8_t)8);
		V_7 = (uint8_t)0;
		String_t* L_25 = __this->___Comment;
		if (!L_25)
		{
			goto IL_00c2;
		}
	}
	{
		uint8_t L_26 = V_7;
		V_7 = (uint8_t)((int32_t)(uint8_t)((int32_t)((int32_t)L_26^((int32_t)16))));
	}

IL_00c2:
	{
		String_t* L_27;
		L_27 = GZipStream_get_FileName_m0D6FA7D5C31AA7D4849C22BEA1ACCFD8CA798E83_inline(__this, NULL);
		if (!L_27)
		{
			goto IL_00d4;
		}
	}
	{
		uint8_t L_28 = V_7;
		V_7 = (uint8_t)((int32_t)(uint8_t)((int32_t)((int32_t)L_28^8)));
	}

IL_00d4:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_29 = V_5;
		int32_t L_30 = V_6;
		int32_t L_31 = L_30;
		V_6 = ((int32_t)il2cpp_codegen_add(L_31, 1));
		uint8_t L_32 = V_7;
		NullCheck(L_29);
		(L_29)->SetAt(static_cast<il2cpp_array_size_t>(L_31), (uint8_t)L_32);
		Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC* L_33 = (Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC*)(&__this->___LastModified);
		bool L_34;
		L_34 = Nullable_1_get_HasValue_m092C73DCE052BFB5C60A39EF9F4E3401AA95221C_inline(L_33, Nullable_1_get_HasValue_m092C73DCE052BFB5C60A39EF9F4E3401AA95221C_RuntimeMethod_var);
		if (L_34)
		{
			goto IL_0100;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_il2cpp_TypeInfo_var);
		DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D L_35;
		L_35 = DateTime_get_Now_m636CB9651A9099D20BA1CF813A0C69637317325C(NULL);
		Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC L_36;
		memset((&L_36), 0, sizeof(L_36));
		Nullable_1__ctor_mB17304720EA19F5469A4883827F53A75FEB492CF((&L_36), L_35, Nullable_1__ctor_mB17304720EA19F5469A4883827F53A75FEB492CF_RuntimeMethod_var);
		__this->___LastModified = L_36;
	}

IL_0100:
	{
		Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC* L_37 = (Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC*)(&__this->___LastModified);
		DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D L_38;
		L_38 = Nullable_1_get_Value_m5A868F663848BC21C18F056731D3AC404CE59991(L_37, Nullable_1_get_Value_m5A868F663848BC21C18F056731D3AC404CE59991_RuntimeMethod_var);
		il2cpp_codegen_runtime_class_init_inline(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var);
		DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D L_39 = ((GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_StaticFields*)il2cpp_codegen_static_fields_for(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var))->____unixEpoch;
		il2cpp_codegen_runtime_class_init_inline(DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_il2cpp_TypeInfo_var);
		TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A L_40;
		L_40 = DateTime_op_Subtraction_m64D26F5ABFAE6E166A7E567093D025F6C69F0123(L_38, L_39, NULL);
		V_8 = L_40;
		il2cpp_codegen_runtime_class_init_inline(TimeSpan_t8195C5B013A2C532FEBDF0B64B6911982E750F5A_il2cpp_TypeInfo_var);
		double L_41;
		L_41 = TimeSpan_get_TotalSeconds_mED686E7CECE6A76A7DC38518698B9199DB8CDEA8((&V_8), NULL);
		V_9 = il2cpp_codegen_cast_double_to_int<int32_t>(L_41);
		int32_t L_42 = V_9;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_43;
		L_43 = BitConverter_GetBytes_mCD74C79673617CEBF85F8A653520C860A9F014F9(L_42, NULL);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_44 = V_5;
		int32_t L_45 = V_6;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_43, 0, (RuntimeArray*)L_44, L_45, 4, NULL);
		int32_t L_46 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_add(L_46, 4));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_47 = V_5;
		int32_t L_48 = V_6;
		int32_t L_49 = L_48;
		V_6 = ((int32_t)il2cpp_codegen_add(L_49, 1));
		NullCheck(L_47);
		(L_47)->SetAt(static_cast<il2cpp_array_size_t>(L_49), (uint8_t)0);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_50 = V_5;
		int32_t L_51 = V_6;
		int32_t L_52 = L_51;
		V_6 = ((int32_t)il2cpp_codegen_add(L_52, 1));
		NullCheck(L_50);
		(L_50)->SetAt(static_cast<il2cpp_array_size_t>(L_52), (uint8_t)((int32_t)255));
		int32_t L_53 = V_3;
		if (!L_53)
		{
			goto IL_0176;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_54 = V_1;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_55 = V_5;
		int32_t L_56 = V_6;
		int32_t L_57 = V_3;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_54, 0, (RuntimeArray*)L_55, L_56, L_57, NULL);
		int32_t L_58 = V_6;
		int32_t L_59 = V_3;
		V_6 = ((int32_t)il2cpp_codegen_add(L_58, L_59));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_60 = V_5;
		int32_t L_61 = V_6;
		int32_t L_62 = L_61;
		V_6 = ((int32_t)il2cpp_codegen_add(L_62, 1));
		NullCheck(L_60);
		(L_60)->SetAt(static_cast<il2cpp_array_size_t>(L_62), (uint8_t)0);
	}

IL_0176:
	{
		int32_t L_63 = V_2;
		if (!L_63)
		{
			goto IL_0199;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_64 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_65 = V_5;
		int32_t L_66 = V_6;
		int32_t L_67 = V_2;
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_64, 0, (RuntimeArray*)L_65, L_66, L_67, NULL);
		int32_t L_68 = V_6;
		int32_t L_69 = V_2;
		V_6 = ((int32_t)il2cpp_codegen_add(L_68, L_69));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_70 = V_5;
		int32_t L_71 = V_6;
		int32_t L_72 = L_71;
		V_6 = ((int32_t)il2cpp_codegen_add(L_72, 1));
		NullCheck(L_70);
		(L_70)->SetAt(static_cast<il2cpp_array_size_t>(L_72), (uint8_t)0);
	}

IL_0199:
	{
		ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* L_73 = __this->____baseStream;
		NullCheck(L_73);
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_74 = L_73->____stream;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_75 = V_5;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_76 = V_5;
		NullCheck(L_76);
		NullCheck(L_74);
		VirtualActionInvoker3< ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(34, L_74, L_75, 0, ((int32_t)(((RuntimeArray*)L_76)->max_length)));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GZipStream__cctor_mF5DF96C583F4C3A722D71B4AA3A8D2A9D19071FE (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFBC07F6BD47DF829ABDBABCE58B748504460B7BB);
		s_Il2CppMethodInitialized = true;
	}
	{
		DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D L_0;
		memset((&L_0), 0, sizeof(L_0));
		DateTime__ctor_mD89390EF215242275A4E8F78C2C3E8BC3EF6F3C3((&L_0), ((int32_t)1970), 1, 1, 0, 0, 0, 1, NULL);
		((GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_StaticFields*)il2cpp_codegen_static_fields_for(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var))->____unixEpoch = L_0;
		Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* L_1;
		L_1 = Encoding_GetEncoding_m979B224460094E241BD5C283BE279886664C9187(_stringLiteralFBC07F6BD47DF829ABDBABCE58B748504460B7BB, NULL);
		((GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_StaticFields*)il2cpp_codegen_static_fields_for(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var))->___iso8859dash1 = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_StaticFields*)il2cpp_codegen_static_fields_for(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var))->___iso8859dash1), (void*)L_1);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream__ctor_mCA4A9F54161B0AEDAD281EE0CC09247BBB41F6E7 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* ___0_stream, int32_t ___1_compressionMode, int32_t ___2_level, int32_t ___3_flavor, bool ___4_leaveOpen, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_0 = (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE*)il2cpp_codegen_object_new(ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE_il2cpp_TypeInfo_var);
		ZlibCodec__ctor_m40C747342EAC3B3891ABCB80C5E34229FFF68D0F(L_0, NULL);
		__this->____z = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____z), (void*)L_0);
		__this->___WORKING_BUFFER_SIZE_DEFAULT = ((int32_t)16384);
		__this->___WORKING_BUFFER_SIZE_MIN = ((int32_t)128);
		__this->____streamMode = 2;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)1);
		__this->____buf1 = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____buf1), (void*)L_1);
		__this->___nomoreinput = (bool)0;
		il2cpp_codegen_runtime_class_init_inline(Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE_il2cpp_TypeInfo_var);
		Stream__ctor_mE8B074A0EBEB026FFF14062AB4B8A78E17EFFBF0(__this, NULL);
		__this->____flushMode = 0;
		int32_t L_2 = __this->___WORKING_BUFFER_SIZE_DEFAULT;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)L_2);
		__this->____workingBuffer = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____workingBuffer), (void*)L_3);
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_4 = ___0_stream;
		__this->____stream = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____stream), (void*)L_4);
		bool L_5 = ___4_leaveOpen;
		__this->____leaveOpen = L_5;
		int32_t L_6 = ___3_flavor;
		__this->____flavor = L_6;
		int32_t L_7 = ___3_flavor;
		V_0 = (bool)((((int32_t)L_7) == ((int32_t)((int32_t)1950)))? 1 : 0);
		int32_t L_8 = ___1_compressionMode;
		if ((!(((uint32_t)L_8) == ((uint32_t)1))))
		{
			goto IL_009a;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_9 = __this->____z;
		bool L_10 = V_0;
		NullCheck(L_9);
		int32_t L_11;
		L_11 = ZlibCodec_InitializeInflate_m326D69D2B9CD25CAD8E83064A6891F178AB1B116(L_9, L_10, NULL);
		__this->____wantCompress = (bool)0;
		goto IL_00af;
	}

IL_009a:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_12 = __this->____z;
		int32_t L_13 = ___2_level;
		bool L_14 = V_0;
		NullCheck(L_12);
		int32_t L_15;
		L_15 = ZlibCodec_InitializeDeflate_m542BFAC119F9EA2F3F15B7C87E2714F82188A9EB(L_12, L_13, L_14, NULL);
		__this->____wantCompress = (bool)1;
	}

IL_00af:
	{
		int32_t L_16 = ___3_flavor;
		if ((!(((uint32_t)L_16) == ((uint32_t)((int32_t)1952)))))
		{
			goto IL_00c6;
		}
	}
	{
		CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* L_17 = (CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0*)il2cpp_codegen_object_new(CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0_il2cpp_TypeInfo_var);
		CRC32__ctor_m01585BBA4CC7A8DA520F4FC404FB4C1033258FA4(L_17, NULL);
		__this->___crc = L_17;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___crc), (void*)L_17);
	}

IL_00c6:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibBaseStream_get_Crc32_m240AACB0116DFA174E4752C5E958948348E2CCC1 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) 
{
	{
		CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* L_0 = __this->___crc;
		if (L_0)
		{
			goto IL_000d;
		}
	}
	{
		return 0;
	}

IL_000d:
	{
		CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* L_1 = __this->___crc;
		NullCheck(L_1);
		int32_t L_2;
		L_2 = CRC32_get_Crc32Result_mD49F2B1164980FB8CAE0B15C817088A2C85E3DB6(L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream_WriteByte_m1BA7F8D94D242810521AA1246B916997F5156A06 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, uint8_t ___0_b, const RuntimeMethod* method) 
{
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = __this->____buf1;
		uint8_t L_1 = ___0_b;
		NullCheck(L_0);
		(L_0)->SetAt(static_cast<il2cpp_array_size_t>(0), (uint8_t)L_1);
		CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* L_2 = __this->___crc;
		if (!L_2)
		{
			goto IL_0027;
		}
	}
	{
		CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* L_3 = __this->___crc;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = __this->____buf1;
		NullCheck(L_3);
		CRC32_SlurpBlock_m24007D3AB683FF87DB6CCF4EC1ADE999E963B642(L_3, L_4, 0, 1, NULL);
	}

IL_0027:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_5 = __this->____buf1;
		VirtualActionInvoker3< ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(34, __this, L_5, 0, 1);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream_Write_m17DC0152DFDCE0EB5B55E3FC562B8313490EB376 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, int32_t ___1_offset, int32_t ___2_count, const RuntimeMethod* method) 
{
	bool V_0 = false;
	int32_t V_1 = 0;
	int32_t G_B12_0 = 0;
	String_t* G_B17_0 = NULL;
	int32_t G_B21_0 = 0;
	int32_t G_B26_0 = 0;
	{
		CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* L_0 = __this->___crc;
		if (!L_0)
		{
			goto IL_0019;
		}
	}
	{
		CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* L_1 = __this->___crc;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_2 = ___0_buffer;
		int32_t L_3 = ___1_offset;
		int32_t L_4 = ___2_count;
		NullCheck(L_1);
		CRC32_SlurpBlock_m24007D3AB683FF87DB6CCF4EC1ADE999E963B642(L_1, L_2, L_3, L_4, NULL);
	}

IL_0019:
	{
		int32_t L_5 = __this->____streamMode;
		if ((!(((uint32_t)L_5) == ((uint32_t)2))))
		{
			goto IL_002c;
		}
	}
	{
		__this->____streamMode = 0;
	}

IL_002c:
	{
		int32_t L_6 = __this->____streamMode;
		if (!L_6)
		{
			goto IL_0042;
		}
	}
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_7 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_7, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral24B5EB22D8E2EC2684FA8E9A50101FC76FC80368)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_7, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_Write_m17DC0152DFDCE0EB5B55E3FC562B8313490EB376_RuntimeMethod_var)));
	}

IL_0042:
	{
		int32_t L_8 = ___2_count;
		if (L_8)
		{
			goto IL_0049;
		}
	}
	{
		return;
	}

IL_0049:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_9 = __this->____z;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_10 = ___0_buffer;
		NullCheck(L_9);
		L_9->___InputBuffer = L_10;
		Il2CppCodeGenWriteBarrier((void**)(&L_9->___InputBuffer), (void*)L_10);
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_11 = __this->____z;
		int32_t L_12 = ___1_offset;
		NullCheck(L_11);
		L_11->___NextIn = L_12;
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_13 = __this->____z;
		int32_t L_14 = ___2_count;
		NullCheck(L_13);
		L_13->___AvailableBytesIn = L_14;
		V_0 = (bool)0;
	}

IL_006f:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_15 = __this->____z;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_16 = __this->____workingBuffer;
		NullCheck(L_15);
		L_15->___OutputBuffer = L_16;
		Il2CppCodeGenWriteBarrier((void**)(&L_15->___OutputBuffer), (void*)L_16);
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_17 = __this->____z;
		NullCheck(L_17);
		L_17->___NextOut = 0;
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_18 = __this->____z;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_19 = __this->____workingBuffer;
		NullCheck(L_19);
		NullCheck(L_18);
		L_18->___AvailableBytesOut = ((int32_t)(((RuntimeArray*)L_19)->max_length));
		bool L_20 = __this->____wantCompress;
		if (!L_20)
		{
			goto IL_00c0;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_21 = __this->____z;
		int32_t L_22 = __this->____flushMode;
		NullCheck(L_21);
		int32_t L_23;
		L_23 = ZlibCodec_Deflate_m68368C43EA37CDB2D268D1EAD2F2F4620FA46330(L_21, L_22, NULL);
		G_B12_0 = L_23;
		goto IL_00d1;
	}

IL_00c0:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_24 = __this->____z;
		int32_t L_25 = __this->____flushMode;
		NullCheck(L_24);
		int32_t L_26;
		L_26 = ZlibCodec_Inflate_mEFD195C98EAC837238E73A3E763AB324379A4550(L_24, L_25, NULL);
		G_B12_0 = L_26;
	}

IL_00d1:
	{
		V_1 = G_B12_0;
		int32_t L_27 = V_1;
		if (!L_27)
		{
			goto IL_0114;
		}
	}
	{
		int32_t L_28 = V_1;
		if ((((int32_t)L_28) == ((int32_t)1)))
		{
			goto IL_0114;
		}
	}
	{
		bool L_29 = __this->____wantCompress;
		if (!L_29)
		{
			goto IL_00f4;
		}
	}
	{
		G_B17_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralDC4A06A0DE599F745DBDD44A6FDE6212859D3A5F));
		goto IL_00f9;
	}

IL_00f4:
	{
		G_B17_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralC1E0482ABDB4530F47C01C2A81FB06ED6E98A110));
	}

IL_00f9:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_30 = __this->____z;
		NullCheck(L_30);
		String_t* L_31 = L_30->___Message;
		String_t* L_32;
		L_32 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(G_B17_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral81B54C81CE5770A2FB716FE3138FA18CE998793D)), L_31, NULL);
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_33 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_33, L_32, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_33, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_Write_m17DC0152DFDCE0EB5B55E3FC562B8313490EB376_RuntimeMethod_var)));
	}

IL_0114:
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_34 = __this->____stream;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_35 = __this->____workingBuffer;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_36 = __this->____workingBuffer;
		NullCheck(L_36);
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_37 = __this->____z;
		NullCheck(L_37);
		int32_t L_38 = L_37->___AvailableBytesOut;
		NullCheck(L_34);
		VirtualActionInvoker3< ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(34, L_34, L_35, 0, ((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_36)->max_length)), L_38)));
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_39 = __this->____z;
		NullCheck(L_39);
		int32_t L_40 = L_39->___AvailableBytesIn;
		if (L_40)
		{
			goto IL_015d;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_41 = __this->____z;
		NullCheck(L_41);
		int32_t L_42 = L_41->___AvailableBytesOut;
		G_B21_0 = ((((int32_t)((((int32_t)L_42) == ((int32_t)0))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		goto IL_015e;
	}

IL_015d:
	{
		G_B21_0 = 0;
	}

IL_015e:
	{
		V_0 = (bool)G_B21_0;
		int32_t L_43 = __this->____flavor;
		if ((!(((uint32_t)L_43) == ((uint32_t)((int32_t)1952)))))
		{
			goto IL_01a0;
		}
	}
	{
		bool L_44 = __this->____wantCompress;
		if (L_44)
		{
			goto IL_01a0;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_45 = __this->____z;
		NullCheck(L_45);
		int32_t L_46 = L_45->___AvailableBytesIn;
		if ((!(((uint32_t)L_46) == ((uint32_t)8))))
		{
			goto IL_019e;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_47 = __this->____z;
		NullCheck(L_47);
		int32_t L_48 = L_47->___AvailableBytesOut;
		G_B26_0 = ((((int32_t)((((int32_t)L_48) == ((int32_t)0))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		goto IL_019f;
	}

IL_019e:
	{
		G_B26_0 = 0;
	}

IL_019f:
	{
		V_0 = (bool)G_B26_0;
	}

IL_01a0:
	{
		bool L_49 = V_0;
		if (!L_49)
		{
			goto IL_006f;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream_finish_m943ABACCE83B407587BABF9AA0FAEE8059E5B35D (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* V_4 = NULL;
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	int32_t V_7 = 0;
	int32_t V_8 = 0;
	int32_t G_B7_0 = 0;
	String_t* G_B12_0 = NULL;
	int32_t G_B18_0 = 0;
	int32_t G_B23_0 = 0;
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_0 = __this->____z;
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		return;
	}

IL_000c:
	{
		int32_t L_1 = __this->____streamMode;
		if (L_1)
		{
			goto IL_01d8;
		}
	}
	{
		V_0 = (bool)0;
	}

IL_0019:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_2 = __this->____z;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = __this->____workingBuffer;
		NullCheck(L_2);
		L_2->___OutputBuffer = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&L_2->___OutputBuffer), (void*)L_3);
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_4 = __this->____z;
		NullCheck(L_4);
		L_4->___NextOut = 0;
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_5 = __this->____z;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_6 = __this->____workingBuffer;
		NullCheck(L_6);
		NullCheck(L_5);
		L_5->___AvailableBytesOut = ((int32_t)(((RuntimeArray*)L_6)->max_length));
		bool L_7 = __this->____wantCompress;
		if (!L_7)
		{
			goto IL_0065;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_8 = __this->____z;
		NullCheck(L_8);
		int32_t L_9;
		L_9 = ZlibCodec_Deflate_m68368C43EA37CDB2D268D1EAD2F2F4620FA46330(L_8, 4, NULL);
		G_B7_0 = L_9;
		goto IL_0071;
	}

IL_0065:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_10 = __this->____z;
		NullCheck(L_10);
		int32_t L_11;
		L_11 = ZlibCodec_Inflate_mEFD195C98EAC837238E73A3E763AB324379A4550(L_10, 4, NULL);
		G_B7_0 = L_11;
	}

IL_0071:
	{
		V_1 = G_B7_0;
		int32_t L_12 = V_1;
		if ((((int32_t)L_12) == ((int32_t)1)))
		{
			goto IL_00b4;
		}
	}
	{
		int32_t L_13 = V_1;
		if (!L_13)
		{
			goto IL_00b4;
		}
	}
	{
		bool L_14 = __this->____wantCompress;
		if (!L_14)
		{
			goto IL_0094;
		}
	}
	{
		G_B12_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralDC4A06A0DE599F745DBDD44A6FDE6212859D3A5F));
		goto IL_0099;
	}

IL_0094:
	{
		G_B12_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralC1E0482ABDB4530F47C01C2A81FB06ED6E98A110));
	}

IL_0099:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_15 = __this->____z;
		NullCheck(L_15);
		String_t* L_16 = L_15->___Message;
		String_t* L_17;
		L_17 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(G_B12_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral81B54C81CE5770A2FB716FE3138FA18CE998793D)), L_16, NULL);
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_18 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_18, L_17, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_18, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_finish_m943ABACCE83B407587BABF9AA0FAEE8059E5B35D_RuntimeMethod_var)));
	}

IL_00b4:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_19 = __this->____workingBuffer;
		NullCheck(L_19);
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_20 = __this->____z;
		NullCheck(L_20);
		int32_t L_21 = L_20->___AvailableBytesOut;
		if ((((int32_t)((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_19)->max_length)), L_21))) <= ((int32_t)0)))
		{
			goto IL_00f4;
		}
	}
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_22 = __this->____stream;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_23 = __this->____workingBuffer;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_24 = __this->____workingBuffer;
		NullCheck(L_24);
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_25 = __this->____z;
		NullCheck(L_25);
		int32_t L_26 = L_25->___AvailableBytesOut;
		NullCheck(L_22);
		VirtualActionInvoker3< ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(34, L_22, L_23, 0, ((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_24)->max_length)), L_26)));
	}

IL_00f4:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_27 = __this->____z;
		NullCheck(L_27);
		int32_t L_28 = L_27->___AvailableBytesIn;
		if (L_28)
		{
			goto IL_0117;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_29 = __this->____z;
		NullCheck(L_29);
		int32_t L_30 = L_29->___AvailableBytesOut;
		G_B18_0 = ((((int32_t)((((int32_t)L_30) == ((int32_t)0))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		goto IL_0118;
	}

IL_0117:
	{
		G_B18_0 = 0;
	}

IL_0118:
	{
		V_0 = (bool)G_B18_0;
		int32_t L_31 = __this->____flavor;
		if ((!(((uint32_t)L_31) == ((uint32_t)((int32_t)1952)))))
		{
			goto IL_015a;
		}
	}
	{
		bool L_32 = __this->____wantCompress;
		if (L_32)
		{
			goto IL_015a;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_33 = __this->____z;
		NullCheck(L_33);
		int32_t L_34 = L_33->___AvailableBytesIn;
		if ((!(((uint32_t)L_34) == ((uint32_t)8))))
		{
			goto IL_0158;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_35 = __this->____z;
		NullCheck(L_35);
		int32_t L_36 = L_35->___AvailableBytesOut;
		G_B23_0 = ((((int32_t)((((int32_t)L_36) == ((int32_t)0))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		goto IL_0159;
	}

IL_0158:
	{
		G_B23_0 = 0;
	}

IL_0159:
	{
		V_0 = (bool)G_B23_0;
	}

IL_015a:
	{
		bool L_37 = V_0;
		if (!L_37)
		{
			goto IL_0019;
		}
	}
	{
		VirtualActionInvoker0::Invoke(20, __this);
		int32_t L_38 = __this->____flavor;
		if ((!(((uint32_t)L_38) == ((uint32_t)((int32_t)1952)))))
		{
			goto IL_01d3;
		}
	}
	{
		bool L_39 = __this->____wantCompress;
		if (!L_39)
		{
			goto IL_01c8;
		}
	}
	{
		CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* L_40 = __this->___crc;
		NullCheck(L_40);
		int32_t L_41;
		L_41 = CRC32_get_Crc32Result_mD49F2B1164980FB8CAE0B15C817088A2C85E3DB6(L_40, NULL);
		V_2 = L_41;
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_42 = __this->____stream;
		int32_t L_43 = V_2;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_44;
		L_44 = BitConverter_GetBytes_mCD74C79673617CEBF85F8A653520C860A9F014F9(L_43, NULL);
		NullCheck(L_42);
		VirtualActionInvoker3< ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(34, L_42, L_44, 0, 4);
		CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* L_45 = __this->___crc;
		NullCheck(L_45);
		int64_t L_46;
		L_46 = CRC32_get_TotalBytesRead_mDFA2814BC50D7BDBC3F62D1B412CAEB8F50ABDE0_inline(L_45, NULL);
		V_3 = ((int32_t)((int64_t)(L_46&((int64_t)(uint64_t)((uint32_t)(-1))))));
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_47 = __this->____stream;
		int32_t L_48 = V_3;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_49;
		L_49 = BitConverter_GetBytes_mCD74C79673617CEBF85F8A653520C860A9F014F9(L_48, NULL);
		NullCheck(L_47);
		VirtualActionInvoker3< ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(34, L_47, L_49, 0, 4);
		goto IL_01d3;
	}

IL_01c8:
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_50 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_50, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral8D4D82166AFA3DFA90B42F9624DAC59E5DE0E310)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_50, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_finish_m943ABACCE83B407587BABF9AA0FAEE8059E5B35D_RuntimeMethod_var)));
	}

IL_01d3:
	{
		goto IL_02ea;
	}

IL_01d8:
	{
		int32_t L_51 = __this->____streamMode;
		if ((!(((uint32_t)L_51) == ((uint32_t)1))))
		{
			goto IL_02ea;
		}
	}
	{
		int32_t L_52 = __this->____flavor;
		if ((!(((uint32_t)L_52) == ((uint32_t)((int32_t)1952)))))
		{
			goto IL_02ea;
		}
	}
	{
		bool L_53 = __this->____wantCompress;
		if (L_53)
		{
			goto IL_02df;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_54 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)8);
		V_4 = L_54;
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_55 = __this->____z;
		NullCheck(L_55);
		int32_t L_56 = L_55->___AvailableBytesIn;
		if ((((int32_t)L_56) == ((int32_t)8)))
		{
			goto IL_0238;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_57 = __this->____z;
		NullCheck(L_57);
		int32_t L_58 = L_57->___AvailableBytesIn;
		int32_t L_59 = L_58;
		RuntimeObject* L_60 = Box(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var)), &L_59);
		String_t* L_61;
		L_61 = String_Format_mA8DBB4C2516B9723C5A41E6CB1E2FAF4BBE96DD8(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral967386972D9F477783B0C3205A3CE33D2DB77D2B)), L_60, NULL);
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_62 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_62, L_61, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_62, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_finish_m943ABACCE83B407587BABF9AA0FAEE8059E5B35D_RuntimeMethod_var)));
	}

IL_0238:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_63 = __this->____z;
		NullCheck(L_63);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_64 = L_63->___InputBuffer;
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_65 = __this->____z;
		NullCheck(L_65);
		int32_t L_66 = L_65->___NextIn;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_67 = V_4;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_68 = V_4;
		NullCheck(L_68);
		Array_Copy_mB4904E17BD92E320613A3251C0205E0786B3BF41((RuntimeArray*)L_64, L_66, (RuntimeArray*)L_67, 0, ((int32_t)(((RuntimeArray*)L_68)->max_length)), NULL);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_69 = V_4;
		int32_t L_70;
		L_70 = BitConverter_ToInt32_m259B4E62995575B80C4086347C867EB3C8D7DAB3(L_69, 0, NULL);
		V_5 = L_70;
		CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* L_71 = __this->___crc;
		NullCheck(L_71);
		int32_t L_72;
		L_72 = CRC32_get_Crc32Result_mD49F2B1164980FB8CAE0B15C817088A2C85E3DB6(L_71, NULL);
		V_6 = L_72;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_73 = V_4;
		int32_t L_74;
		L_74 = BitConverter_ToInt32_m259B4E62995575B80C4086347C867EB3C8D7DAB3(L_73, 4, NULL);
		V_7 = L_74;
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_75 = __this->____z;
		NullCheck(L_75);
		int64_t L_76 = L_75->___TotalBytesOut;
		V_8 = ((int32_t)((int64_t)(L_76&((int64_t)(uint64_t)((uint32_t)(-1))))));
		int32_t L_77 = V_6;
		int32_t L_78 = V_5;
		if ((((int32_t)L_77) == ((int32_t)L_78)))
		{
			goto IL_02b3;
		}
	}
	{
		int32_t L_79 = V_6;
		int32_t L_80 = L_79;
		RuntimeObject* L_81 = Box(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var)), &L_80);
		int32_t L_82 = V_5;
		int32_t L_83 = L_82;
		RuntimeObject* L_84 = Box(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var)), &L_83);
		String_t* L_85;
		L_85 = String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral6E343721F0E25487FE2756047043B53F090F4259)), L_81, L_84, NULL);
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_86 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_86, L_85, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_86, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_finish_m943ABACCE83B407587BABF9AA0FAEE8059E5B35D_RuntimeMethod_var)));
	}

IL_02b3:
	{
		int32_t L_87 = V_8;
		int32_t L_88 = V_7;
		if ((((int32_t)L_87) == ((int32_t)L_88)))
		{
			goto IL_02da;
		}
	}
	{
		int32_t L_89 = V_8;
		int32_t L_90 = L_89;
		RuntimeObject* L_91 = Box(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var)), &L_90);
		int32_t L_92 = V_7;
		int32_t L_93 = L_92;
		RuntimeObject* L_94 = Box(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var)), &L_93);
		String_t* L_95;
		L_95 = String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralB274B371D443401B2ED8CB4D62663E14A8DAFE8C)), L_91, L_94, NULL);
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_96 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_96, L_95, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_96, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_finish_m943ABACCE83B407587BABF9AA0FAEE8059E5B35D_RuntimeMethod_var)));
	}

IL_02da:
	{
		goto IL_02ea;
	}

IL_02df:
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_97 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_97, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral10D38DC34936AF6C5FB05636D856173414F0E6F5)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_97, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_finish_m943ABACCE83B407587BABF9AA0FAEE8059E5B35D_RuntimeMethod_var)));
	}

IL_02ea:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream_end_m8B560782B294CA50D2ADDB31DDA09AD8356FE55A (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) 
{
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_0 = __this->____z;
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		return;
	}

IL_000c:
	{
		bool L_1 = __this->____wantCompress;
		if (!L_1)
		{
			goto IL_0028;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_2 = __this->____z;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = ZlibCodec_EndDeflate_mFF40B57BD0A08EB4543C2EA55C8DCB069899E43D(L_2, NULL);
		goto IL_0034;
	}

IL_0028:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_4 = __this->____z;
		NullCheck(L_4);
		int32_t L_5;
		L_5 = ZlibCodec_EndInflate_mE7E95E904F7098F6899D8BD79823CFCDE877C4B9(L_4, NULL);
	}

IL_0034:
	{
		__this->____z = (ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____z), (void*)(ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE*)NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream_Close_m109A0572891B118EA9754E0BF99066D7F6F30430 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) 
{
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_0016:
			{
				{
					ZlibBaseStream_end_m8B560782B294CA50D2ADDB31DDA09AD8356FE55A(__this, NULL);
					bool L_0 = __this->____leaveOpen;
					if (L_0)
					{
						goto IL_0032;
					}
				}
				{
					Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_1 = __this->____stream;
					NullCheck(L_1);
					VirtualActionInvoker0::Invoke(18, L_1);
				}

IL_0032:
				{
					__this->____stream = (Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE*)NULL;
					Il2CppCodeGenWriteBarrier((void**)(&__this->____stream), (void*)(Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE*)NULL);
					return;
				}
			}
		});
		try
		{
			try
			{
				ZlibBaseStream_finish_m943ABACCE83B407587BABF9AA0FAEE8059E5B35D(__this, NULL);
				goto IL_0011_1;
			}
			catch(Il2CppExceptionWrapper& e)
			{
				if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
				{
					IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
					goto CATCH_000b_1;
				}
				throw e;
			}

CATCH_000b_1:
			{
				IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910* L_2 = ((IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910*)IL2CPP_GET_ACTIVE_EXCEPTION(IOException_t5D599190B003D41D45D4839A9B6B9AB53A755910*));;
				IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
				goto IL_0011_1;
			}

IL_0011_1:
			{
				goto IL_003a;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_003a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream_Flush_mDA0783AA3C9D0A6687AEDD9ABB828ACED548D585 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) 
{
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_0 = __this->____stream;
		NullCheck(L_0);
		VirtualActionInvoker0::Invoke(20, L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t ZlibBaseStream_Seek_mD37F4E628A8B871EE6D9CD7DBFE74DAADAE9622E (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, int64_t ___0_offset, int32_t ___1_origin, const RuntimeMethod* method) 
{
	{
		NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8* L_0 = (NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8_il2cpp_TypeInfo_var)));
		NotImplementedException__ctor_mDAB47BC6BD0E342E8F2171E5CABE3E67EA049F1C(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_Seek_mD37F4E628A8B871EE6D9CD7DBFE74DAADAE9622E_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream_SetLength_mFB65E079BF4C2E21D5EED5AC5911632C55E82CB3 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, int64_t ___0_value, const RuntimeMethod* method) 
{
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_0 = __this->____stream;
		int64_t L_1 = ___0_value;
		NullCheck(L_0);
		VirtualActionInvoker1< int64_t >::Invoke(30, L_0, L_1);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ZlibBaseStream_ReadZeroTerminatedString_m1129CEBC534AC892ACE3F2B6B66FE8103CF44A4E (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_mCF9C36A7DE08AFB2D2EEC76139677E086A6CAD73_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_ToArray_m23F8BB7CABADC11EBCAB5AB701B75F56C865BE24_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mC411581553467A7DB76C06E5E5FCA34E4EC621B9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* V_0 = NULL;
	bool V_1 = false;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* V_2 = NULL;
	{
		List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* L_0 = (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B*)il2cpp_codegen_object_new(List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B_il2cpp_TypeInfo_var);
		List_1__ctor_mC411581553467A7DB76C06E5E5FCA34E4EC621B9(L_0, List_1__ctor_mC411581553467A7DB76C06E5E5FCA34E4EC621B9_RuntimeMethod_var);
		V_0 = L_0;
		V_1 = (bool)0;
	}

IL_0008:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = __this->____buf1;
		int32_t L_2;
		L_2 = VirtualFuncInvoker3< int32_t, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(31, __this, L_1, 0, 1);
		if (L_2)
		{
			goto IL_0026;
		}
	}
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_3 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_3, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral3233DCBABA7FC4545C644475BBE0516447D29DBD)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_3, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_ReadZeroTerminatedString_m1129CEBC534AC892ACE3F2B6B66FE8103CF44A4E_RuntimeMethod_var)));
	}

IL_0026:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = __this->____buf1;
		NullCheck(L_4);
		int32_t L_5 = 0;
		uint8_t L_6 = (L_4)->GetAt(static_cast<il2cpp_array_size_t>(L_5));
		if (L_6)
		{
			goto IL_003a;
		}
	}
	{
		V_1 = (bool)1;
		goto IL_0048;
	}

IL_003a:
	{
		List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* L_7 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_8 = __this->____buf1;
		NullCheck(L_8);
		int32_t L_9 = 0;
		uint8_t L_10 = (L_8)->GetAt(static_cast<il2cpp_array_size_t>(L_9));
		NullCheck(L_7);
		List_1_Add_mCF9C36A7DE08AFB2D2EEC76139677E086A6CAD73_inline(L_7, L_10, List_1_Add_mCF9C36A7DE08AFB2D2EEC76139677E086A6CAD73_RuntimeMethod_var);
	}

IL_0048:
	{
		bool L_11 = V_1;
		if (!L_11)
		{
			goto IL_0008;
		}
	}
	{
		List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* L_12 = V_0;
		NullCheck(L_12);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_13;
		L_13 = List_1_ToArray_m23F8BB7CABADC11EBCAB5AB701B75F56C865BE24(L_12, List_1_ToArray_m23F8BB7CABADC11EBCAB5AB701B75F56C865BE24_RuntimeMethod_var);
		V_2 = L_13;
		il2cpp_codegen_runtime_class_init_inline(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var);
		Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* L_14 = ((GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_StaticFields*)il2cpp_codegen_static_fields_for(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var))->___iso8859dash1;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_15 = V_2;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_16 = V_2;
		NullCheck(L_16);
		NullCheck(L_14);
		String_t* L_17;
		L_17 = VirtualFuncInvoker3< String_t*, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(36, L_14, L_15, 0, ((int32_t)(((RuntimeArray*)L_16)->max_length)));
		return L_17;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream__ReadAndValidateGzipHeader_m65C3C51F4DFAECA2DCCA593299CBD453EF142CE6 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* V_0 = NULL;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int16_t V_3 = 0;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* V_4 = NULL;
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)10));
		V_0 = L_0;
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_1 = __this->____stream;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_2 = V_0;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = V_0;
		NullCheck(L_3);
		NullCheck(L_1);
		int32_t L_4;
		L_4 = VirtualFuncInvoker3< int32_t, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(31, L_1, L_2, 0, ((int32_t)(((RuntimeArray*)L_3)->max_length)));
		V_1 = L_4;
		int32_t L_5 = V_1;
		if ((((int32_t)L_5) == ((int32_t)((int32_t)10))))
		{
			goto IL_002c;
		}
	}
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_6 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_6, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral14D85C5F7C475603476AF01830C5A5FDF517ACB9)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_6, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream__ReadAndValidateGzipHeader_m65C3C51F4DFAECA2DCCA593299CBD453EF142CE6_RuntimeMethod_var)));
	}

IL_002c:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_7 = V_0;
		NullCheck(L_7);
		int32_t L_8 = 0;
		uint8_t L_9 = (L_7)->GetAt(static_cast<il2cpp_array_size_t>(L_8));
		if ((!(((uint32_t)L_9) == ((uint32_t)((int32_t)31)))))
		{
			goto IL_004c;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_10 = V_0;
		NullCheck(L_10);
		int32_t L_11 = 1;
		uint8_t L_12 = (L_10)->GetAt(static_cast<il2cpp_array_size_t>(L_11));
		if ((!(((uint32_t)L_12) == ((uint32_t)((int32_t)139)))))
		{
			goto IL_004c;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_13 = V_0;
		NullCheck(L_13);
		int32_t L_14 = 2;
		uint8_t L_15 = (L_13)->GetAt(static_cast<il2cpp_array_size_t>(L_14));
		if ((((int32_t)L_15) == ((int32_t)8)))
		{
			goto IL_0057;
		}
	}

IL_004c:
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_16 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_16, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralB2B8359BD91EB1DF5DC1B2DE300F3D1DC2B414E8)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_16, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream__ReadAndValidateGzipHeader_m65C3C51F4DFAECA2DCCA593299CBD453EF142CE6_RuntimeMethod_var)));
	}

IL_0057:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_17 = V_0;
		int32_t L_18;
		L_18 = BitConverter_ToInt32_m259B4E62995575B80C4086347C867EB3C8D7DAB3(L_17, 4, NULL);
		V_2 = L_18;
		il2cpp_codegen_runtime_class_init_inline(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var);
		int32_t L_19 = V_2;
		il2cpp_codegen_runtime_class_init_inline(DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D_il2cpp_TypeInfo_var);
		DateTime_t66193957C73913903DDAD89FEDC46139BCA5802D L_20;
		L_20 = DateTime_AddSeconds_mC5FE3FB22A1295CA747746ECE48B9D4A4B6B2E81((&((GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_StaticFields*)il2cpp_codegen_static_fields_for(GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187_il2cpp_TypeInfo_var))->____unixEpoch), ((double)L_19), NULL);
		__this->____GzipMtime = L_20;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_21 = V_0;
		NullCheck(L_21);
		int32_t L_22 = 3;
		uint8_t L_23 = (L_21)->GetAt(static_cast<il2cpp_array_size_t>(L_22));
		if ((!(((uint32_t)((int32_t)((int32_t)L_23&4))) == ((uint32_t)4))))
		{
			goto IL_00c7;
		}
	}
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_24 = __this->____stream;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_25 = V_0;
		NullCheck(L_24);
		int32_t L_26;
		L_26 = VirtualFuncInvoker3< int32_t, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(31, L_24, L_25, 0, 2);
		V_1 = L_26;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_27 = V_0;
		NullCheck(L_27);
		int32_t L_28 = 0;
		uint8_t L_29 = (L_27)->GetAt(static_cast<il2cpp_array_size_t>(L_28));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_30 = V_0;
		NullCheck(L_30);
		int32_t L_31 = 1;
		uint8_t L_32 = (L_30)->GetAt(static_cast<il2cpp_array_size_t>(L_31));
		V_3 = ((int16_t)((int32_t)il2cpp_codegen_add((int32_t)L_29, ((int32_t)il2cpp_codegen_multiply((int32_t)L_32, ((int32_t)256))))));
		int16_t L_33 = V_3;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_34 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)L_33);
		V_4 = L_34;
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_35 = __this->____stream;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_36 = V_4;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_37 = V_4;
		NullCheck(L_37);
		NullCheck(L_35);
		int32_t L_38;
		L_38 = VirtualFuncInvoker3< int32_t, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(31, L_35, L_36, 0, ((int32_t)(((RuntimeArray*)L_37)->max_length)));
		V_1 = L_38;
		int32_t L_39 = V_1;
		int16_t L_40 = V_3;
		if ((((int32_t)L_39) == ((int32_t)L_40)))
		{
			goto IL_00c7;
		}
	}
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_41 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_41, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral3D7AFE151D76C21DBC230D2F7D219FAB99ADCF3A)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_41, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream__ReadAndValidateGzipHeader_m65C3C51F4DFAECA2DCCA593299CBD453EF142CE6_RuntimeMethod_var)));
	}

IL_00c7:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_42 = V_0;
		NullCheck(L_42);
		int32_t L_43 = 3;
		uint8_t L_44 = (L_42)->GetAt(static_cast<il2cpp_array_size_t>(L_43));
		if ((!(((uint32_t)((int32_t)((int32_t)L_44&8))) == ((uint32_t)8))))
		{
			goto IL_00de;
		}
	}
	{
		String_t* L_45;
		L_45 = ZlibBaseStream_ReadZeroTerminatedString_m1129CEBC534AC892ACE3F2B6B66FE8103CF44A4E(__this, NULL);
		__this->____GzipFileName = L_45;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____GzipFileName), (void*)L_45);
	}

IL_00de:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_46 = V_0;
		NullCheck(L_46);
		int32_t L_47 = 3;
		uint8_t L_48 = (L_46)->GetAt(static_cast<il2cpp_array_size_t>(L_47));
		if ((!(((uint32_t)((int32_t)((int32_t)L_48&((int32_t)16)))) == ((uint32_t)((int32_t)16)))))
		{
			goto IL_00f7;
		}
	}
	{
		String_t* L_49;
		L_49 = ZlibBaseStream_ReadZeroTerminatedString_m1129CEBC534AC892ACE3F2B6B66FE8103CF44A4E(__this, NULL);
		__this->____GzipComment = L_49;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____GzipComment), (void*)L_49);
	}

IL_00f7:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_50 = V_0;
		NullCheck(L_50);
		int32_t L_51 = 3;
		uint8_t L_52 = (L_50)->GetAt(static_cast<il2cpp_array_size_t>(L_51));
		if ((!(((uint32_t)((int32_t)((int32_t)L_52&2))) == ((uint32_t)2))))
		{
			goto IL_0111;
		}
	}
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_53 = __this->____buf1;
		int32_t L_54;
		L_54 = VirtualFuncInvoker3< int32_t, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t >::Invoke(31, __this, L_53, 0, 1);
	}

IL_0111:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ZlibBaseStream_Read_m9181D0A7D2941CDEA659A803791B98B5700047BF (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, int32_t ___1_offset, int32_t ___2_count, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t G_B17_0 = 0;
	String_t* G_B22_0 = NULL;
	String_t* G_B27_0 = NULL;
	String_t* G_B26_0 = NULL;
	String_t* G_B28_0 = NULL;
	String_t* G_B28_1 = NULL;
	{
		int32_t L_0 = __this->____streamMode;
		if ((!(((uint32_t)L_0) == ((uint32_t)2))))
		{
			goto IL_0035;
		}
	}
	{
		__this->____streamMode = 1;
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_1 = __this->____z;
		NullCheck(L_1);
		L_1->___AvailableBytesIn = 0;
		int32_t L_2 = __this->____flavor;
		if ((!(((uint32_t)L_2) == ((uint32_t)((int32_t)1952)))))
		{
			goto IL_0035;
		}
	}
	{
		ZlibBaseStream__ReadAndValidateGzipHeader_m65C3C51F4DFAECA2DCCA593299CBD453EF142CE6(__this, NULL);
	}

IL_0035:
	{
		int32_t L_3 = __this->____streamMode;
		if ((((int32_t)L_3) == ((int32_t)1)))
		{
			goto IL_004c;
		}
	}
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_4 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_4, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralA7908ABB3E478F6388EB5FB76212E4B3E81900AD)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_4, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_Read_m9181D0A7D2941CDEA659A803791B98B5700047BF_RuntimeMethod_var)));
	}

IL_004c:
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_5 = __this->____stream;
		NullCheck(L_5);
		bool L_6;
		L_6 = VirtualFuncInvoker0< bool >::Invoke(7, L_5);
		if (L_6)
		{
			goto IL_0067;
		}
	}
	{
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_7 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_7, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralC62B0C3C63415051741BF2BBE989F54545097E70)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_7, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_Read_m9181D0A7D2941CDEA659A803791B98B5700047BF_RuntimeMethod_var)));
	}

IL_0067:
	{
		int32_t L_8 = ___2_count;
		if (L_8)
		{
			goto IL_006f;
		}
	}
	{
		return 0;
	}

IL_006f:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_9 = __this->____z;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_10 = ___0_buffer;
		NullCheck(L_9);
		L_9->___OutputBuffer = L_10;
		Il2CppCodeGenWriteBarrier((void**)(&L_9->___OutputBuffer), (void*)L_10);
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_11 = __this->____z;
		int32_t L_12 = ___1_offset;
		NullCheck(L_11);
		L_11->___NextOut = L_12;
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_13 = __this->____z;
		int32_t L_14 = ___2_count;
		NullCheck(L_13);
		L_13->___AvailableBytesOut = L_14;
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_15 = __this->____z;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_16 = __this->____workingBuffer;
		NullCheck(L_15);
		L_15->___InputBuffer = L_16;
		Il2CppCodeGenWriteBarrier((void**)(&L_15->___InputBuffer), (void*)L_16);
	}

IL_00a4:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_17 = __this->____z;
		NullCheck(L_17);
		int32_t L_18 = L_17->___AvailableBytesIn;
		if (L_18)
		{
			goto IL_0114;
		}
	}
	{
		bool L_19 = __this->___nomoreinput;
		if (L_19)
		{
			goto IL_0114;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_20 = __this->____z;
		NullCheck(L_20);
		L_20->___NextIn = 0;
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_21 = __this->____z;
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_22 = __this->____stream;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_23 = __this->____workingBuffer;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_24 = __this->____workingBuffer;
		NullCheck(L_24);
		int32_t L_25;
		L_25 = SharedUtils_ReadInput_m04DEA02BA9AABEB317EF671B6B2559A554C2BB5F(L_22, L_23, 0, ((int32_t)(((RuntimeArray*)L_24)->max_length)), NULL);
		NullCheck(L_21);
		L_21->___AvailableBytesIn = L_25;
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_26 = __this->____z;
		NullCheck(L_26);
		int32_t L_27 = L_26->___AvailableBytesIn;
		if ((!(((uint32_t)L_27) == ((uint32_t)(-1)))))
		{
			goto IL_0114;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_28 = __this->____z;
		NullCheck(L_28);
		L_28->___AvailableBytesIn = 0;
		__this->___nomoreinput = (bool)1;
	}

IL_0114:
	{
		bool L_29 = __this->____wantCompress;
		if (!L_29)
		{
			goto IL_0135;
		}
	}
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_30 = __this->____z;
		int32_t L_31 = __this->____flushMode;
		NullCheck(L_30);
		int32_t L_32;
		L_32 = ZlibCodec_Deflate_m68368C43EA37CDB2D268D1EAD2F2F4620FA46330(L_30, L_31, NULL);
		G_B17_0 = L_32;
		goto IL_0146;
	}

IL_0135:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_33 = __this->____z;
		int32_t L_34 = __this->____flushMode;
		NullCheck(L_33);
		int32_t L_35;
		L_35 = ZlibCodec_Inflate_mEFD195C98EAC837238E73A3E763AB324379A4550(L_33, L_34, NULL);
		G_B17_0 = L_35;
	}

IL_0146:
	{
		V_0 = G_B17_0;
		bool L_36 = __this->___nomoreinput;
		if (!L_36)
		{
			goto IL_0184;
		}
	}
	{
		int32_t L_37 = V_0;
		if ((!(((uint32_t)L_37) == ((uint32_t)((int32_t)-5)))))
		{
			goto IL_0184;
		}
	}
	{
		bool L_38 = __this->____wantCompress;
		if (!L_38)
		{
			goto IL_016f;
		}
	}
	{
		G_B22_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralDC4A06A0DE599F745DBDD44A6FDE6212859D3A5F));
		goto IL_0174;
	}

IL_016f:
	{
		G_B22_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralC1E0482ABDB4530F47C01C2A81FB06ED6E98A110));
	}

IL_0174:
	{
		String_t* L_39;
		L_39 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(G_B22_0, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral8454D7468D90A7E6BF4F4218E867EA1DE489B984)), NULL);
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_40 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_40, L_39, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_40, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_Read_m9181D0A7D2941CDEA659A803791B98B5700047BF_RuntimeMethod_var)));
	}

IL_0184:
	{
		int32_t L_41 = V_0;
		if (!L_41)
		{
			goto IL_01cc;
		}
	}
	{
		int32_t L_42 = V_0;
		if ((((int32_t)L_42) == ((int32_t)1)))
		{
			goto IL_01cc;
		}
	}
	{
		bool L_43 = __this->____wantCompress;
		if (!L_43)
		{
			G_B27_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralD82DD7A67123B96565FAC38717BA5FA359AB739E));
			goto IL_01ab;
		}
		G_B26_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralD82DD7A67123B96565FAC38717BA5FA359AB739E));
	}
	{
		G_B28_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralDC4A06A0DE599F745DBDD44A6FDE6212859D3A5F));
		G_B28_1 = G_B26_0;
		goto IL_01b0;
	}

IL_01ab:
	{
		G_B28_0 = ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralC1E0482ABDB4530F47C01C2A81FB06ED6E98A110));
		G_B28_1 = G_B27_0;
	}

IL_01b0:
	{
		int32_t L_44 = V_0;
		int32_t L_45 = L_44;
		RuntimeObject* L_46 = Box(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var)), &L_45);
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_47 = __this->____z;
		NullCheck(L_47);
		String_t* L_48 = L_47->___Message;
		String_t* L_49;
		L_49 = String_Format_mA0534D6E2AE4D67A6BD8D45B3321323930EB930C(G_B28_1, G_B28_0, L_46, L_48, NULL);
		ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0* L_50 = (ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibException_t4F17B4DE7868B1E81810951CA87CBA3FD9AF37C0_il2cpp_TypeInfo_var)));
		ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B(L_50, L_49, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_50, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_Read_m9181D0A7D2941CDEA659A803791B98B5700047BF_RuntimeMethod_var)));
	}

IL_01cc:
	{
		bool L_51 = __this->___nomoreinput;
		if (L_51)
		{
			goto IL_01de;
		}
	}
	{
		int32_t L_52 = V_0;
		if ((!(((uint32_t)L_52) == ((uint32_t)1))))
		{
			goto IL_01f4;
		}
	}

IL_01de:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_53 = __this->____z;
		NullCheck(L_53);
		int32_t L_54 = L_53->___AvailableBytesOut;
		int32_t L_55 = ___2_count;
		if ((!(((uint32_t)L_54) == ((uint32_t)L_55))))
		{
			goto IL_01f4;
		}
	}
	{
		goto IL_020b;
	}

IL_01f4:
	{
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_56 = __this->____z;
		NullCheck(L_56);
		int32_t L_57 = L_56->___AvailableBytesOut;
		int32_t L_58 = ___2_count;
		if ((!(((uint32_t)L_57) == ((uint32_t)L_58))))
		{
			goto IL_020b;
		}
	}
	{
		int32_t L_59 = V_0;
		if (!L_59)
		{
			goto IL_00a4;
		}
	}

IL_020b:
	{
		int32_t L_60 = ___2_count;
		ZlibCodec_tECC76FB1FEF97921D3AB27C02FE790CF2FC107FE* L_61 = __this->____z;
		NullCheck(L_61);
		int32_t L_62 = L_61->___AvailableBytesOut;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_60, L_62));
		CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* L_63 = __this->___crc;
		if (!L_63)
		{
			goto IL_0232;
		}
	}
	{
		CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* L_64 = __this->___crc;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_65 = ___0_buffer;
		int32_t L_66 = ___1_offset;
		int32_t L_67 = V_0;
		NullCheck(L_64);
		CRC32_SlurpBlock_m24007D3AB683FF87DB6CCF4EC1ADE999E963B642(L_64, L_65, L_66, L_67, NULL);
	}

IL_0232:
	{
		int32_t L_68 = V_0;
		return L_68;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ZlibBaseStream_get_CanRead_mBD037D02FA093F4234FA9E995B786CBC8EA20B33 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) 
{
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_0 = __this->____stream;
		NullCheck(L_0);
		bool L_1;
		L_1 = VirtualFuncInvoker0< bool >::Invoke(7, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ZlibBaseStream_get_CanSeek_m26406C8AC4C378467CBBB65540BA59F86510E132 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) 
{
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_0 = __this->____stream;
		NullCheck(L_0);
		bool L_1;
		L_1 = VirtualFuncInvoker0< bool >::Invoke(8, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ZlibBaseStream_get_CanWrite_m4530CC3BA4504B1893231B082F3E3B186DDA546F (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) 
{
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_0 = __this->____stream;
		NullCheck(L_0);
		bool L_1;
		L_1 = VirtualFuncInvoker0< bool >::Invoke(10, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t ZlibBaseStream_get_Length_m6BBD892C2EA6A8F43C8DA2E475F48A798AEA3586 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) 
{
	{
		Stream_tF844051B786E8F7F4244DBD218D74E8617B9A2DE* L_0 = __this->____stream;
		NullCheck(L_0);
		int64_t L_1;
		L_1 = VirtualFuncInvoker0< int64_t >::Invoke(11, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t ZlibBaseStream_get_Position_mF9C08461A36C0A5E1FF1A88BBA7060DBA1E4E035 (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, const RuntimeMethod* method) 
{
	{
		NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8* L_0 = (NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8_il2cpp_TypeInfo_var)));
		NotImplementedException__ctor_mDAB47BC6BD0E342E8F2171E5CABE3E67EA049F1C(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_get_Position_mF9C08461A36C0A5E1FF1A88BBA7060DBA1E4E035_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ZlibBaseStream_set_Position_m01FE6FC87A956DABEFB425C0550C1931D758E76F (ZlibBaseStream_tDEE28F9A23D415A693911CADB5C99DEABC520B4B* __this, int64_t ___0_value, const RuntimeMethod* method) 
{
	{
		NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8* L_0 = (NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotImplementedException_t6366FE4DCF15094C51F4833B91A2AE68D4DA90E8_il2cpp_TypeInfo_var)));
		NotImplementedException__ctor_mDAB47BC6BD0E342E8F2171E5CABE3E67EA049F1C(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZlibBaseStream_set_Position_m01FE6FC87A956DABEFB425C0550C1931D758E76F_RuntimeMethod_var)));
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void DeflateManager_set_WantRfc1950HeaderBytes_m799CF5C55738EC42D599049066DDD7F5D5392338_inline (DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->____WantRfc1950HeaderBytes = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool DeflateManager_get_WantRfc1950HeaderBytes_m5661399ADA54DFF965A373E2BECC13189F25FFEE_inline (DeflateManager_tE51A08F3C91BCDDB443C191EC92D631C3FA41065* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->____WantRfc1950HeaderBytes;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* GZipStream_get_FileName_m0D6FA7D5C31AA7D4849C22BEA1ACCFD8CA798E83_inline (GZipStream_tE46F1624B9D57524A9133C69B1971B05D0C26187* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->____FileName;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int64_t CRC32_get_TotalBytesRead_mDFA2814BC50D7BDBC3F62D1B412CAEB8F50ABDE0_inline (CRC32_t4EC20D993A658F0A38D6FE69C2113CBC07A5AEA0* __this, const RuntimeMethod* method) 
{
	{
		int64_t L_0 = __this->____TotalBytesRead;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mCF9C36A7DE08AFB2D2EEC76139677E086A6CAD73_gshared_inline (List_1_t7D15BE3246500BBF1A24C24DBF963B89A876255B* __this, uint8_t ___0_item, const RuntimeMethod* method) 
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* V_0 = NULL;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = __this->____items;
		V_0 = L_1;
		int32_t L_2 = __this->____size;
		V_1 = L_2;
		int32_t L_3 = V_1;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = V_0;
		NullCheck(L_4);
		if ((!(((uint32_t)L_3) < ((uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length))))))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = V_1;
		__this->____size = ((int32_t)il2cpp_codegen_add(L_5, 1));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_6 = V_0;
		int32_t L_7 = V_1;
		uint8_t L_8 = ___0_item;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (uint8_t)L_8);
		return;
	}

IL_0034:
	{
		uint8_t L_9 = ___0_item;
		List_1_AddWithResize_m735EBD7363A06760DE25BACA0FC47F76CA0DEEAB(__this, L_9, il2cpp_rgctx_method(method->klass->rgctx_data, 14));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Nullable_1_get_HasValue_m092C73DCE052BFB5C60A39EF9F4E3401AA95221C_gshared_inline (Nullable_1_tEADC262F7F8B8BC4CC0A003DBDD3CA7C1B63F9AC* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___hasValue;
		return L_0;
	}
}
