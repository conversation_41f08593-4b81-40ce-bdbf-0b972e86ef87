﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Console__ctor_m2755C53D2BA72D0E268C7E6FCE8BC275F108E6A0 (void);
extern void Console_get_Instance_m1D7D6684876CA6D805BE42F9A31BC9CDAEEFAC29 (void);
extern void Console_Awake_mA984C2995E0F7172A298ACDE4A4CAFFFB354298B (void);
extern void Console_GetMessageList_m188A5B0BE835254C82E3FA55F07379DDA15385D8 (void);
extern void Console_Log_m4C8ECC29B534EC6977A9340B43546F6548288C4A (void);
extern void Console_Warn_m0D5930BB5C00F466207E55D331004A0E8E893D7C (void);
extern void Console_Error_mB6C8026130404F262B46317AD1E11412E6674E3A (void);
extern void Console_Exception_m89579AA37D347386CDD278ACEAB82E9A051FC0EE (void);
extern void Console_Add_mCCCA2151001A8D946DDD6CDD524308E514155CD7 (void);
extern void ConsoleMessage__ctor_mF7F8270EDB0C5D4C5E1DA9F2AC69102265A75647 (void);
extern void UniExtender__ctor_m46C0DBE3E52F1DC0D853F3C45ED8A48CC9BE95E3 (void);
extern void UniExtender_get_Instance_m8CE2F866FE48943F333D0936BC8B14C5EF5A3B27 (void);
extern void UniExtender_Step_mFE39FFABF4AD0203ADAA616DEC6232750F4A5011 (void);
extern void U3CStepU3Ec__Iterator0__ctor_m4BAFECFD17EF907C33146CF3084BA51B0EB8208B (void);
extern void U3CStepU3Ec__Iterator0_MoveNext_m06D8021D8E3048FBEB8810EE0B352ADD9F204C92 (void);
extern void U3CStepU3Ec__Iterator0_System_Collections_Generic_IEnumeratorU3CobjectU3E_get_Current_m780F379ECAD60C598F723E0E95186656E8C52904 (void);
extern void U3CStepU3Ec__Iterator0_System_Collections_IEnumerator_get_Current_m5E012E69AD1EC75819ABA72BF3F11AE7162583E7 (void);
extern void U3CStepU3Ec__Iterator0_Dispose_m30F7AD5D5080202063BCBB2647F0BB935B8C8C67 (void);
extern void U3CStepU3Ec__Iterator0_Reset_mC21DBB0D194FC703A008EF0E5693E281A8D54466 (void);
extern void BackgroundTask__ctor_m3EC11D46263C4D95CDB547F1AB602A959FC405F6 (void);
extern void ForegroundTask__ctor_mADBA30B1A6E682C52A50BAFC183DFB9029F3A2EC (void);
extern void MagicThread__ctor_m0DFB3C42FDB6A5022F7369FD64093ABED0DA823A (void);
extern void MagicThread_get_instance_m8E845D628C99070C2C9E4B61A6A0FF47DE804341 (void);
extern void MagicThread_Start_mF8AC6E57F39A6880BF08FBA99BF208D81B646B90 (void);
extern void MagicThread_Start_m57B89415402B002958AFBE57B71603B9ADDA6366 (void);
extern void MagicThread_HandleCoroutine_m1B3F148449C762794A08BCE1093B8FDEED246013 (void);
extern void MagicThread_HandleThread_mB4B9E56BF718DA5B7E070613541627C4773A4846 (void);
extern void U3CStartU3Ec__Iterator0__ctor_mFEF24D928372ED04A27E79EADDEF4ECD798B24A4 (void);
extern void U3CStartU3Ec__Iterator0_MoveNext_mB95A30819D857BEF9475F32A090EA5C79E272618 (void);
extern void U3CStartU3Ec__Iterator0_System_Collections_Generic_IEnumeratorU3CobjectU3E_get_Current_m0F4616D0607A5BEFA451DC1560F5324018D9F288 (void);
extern void U3CStartU3Ec__Iterator0_System_Collections_IEnumerator_get_Current_m457DE2903DBF85070088161AA34850FE67E9FC25 (void);
extern void U3CStartU3Ec__Iterator0_Dispose_mFC5C7BA6378DC2DD92844DE19A24C4672B1305C2 (void);
extern void U3CStartU3Ec__Iterator0_Reset_m8A18F038B15CB1BADE907D414A62235B60830F0F (void);
extern void U3CStartU3Ec__AnonStorey2__ctor_m0CBD1CFFE86E9FAB70478ED1A5D34BE6AD3B6207 (void);
extern void U3CStartU3Ec__AnonStorey2_U3CU3Em__0_mA8B2DEE4D228A43E89A3805AB8A25F79A45A75CB (void);
extern void U3CHandleCoroutineU3Ec__Iterator1__ctor_m6859AF3FD8A9DE7D8F18F5A8A53727642C72B0B4 (void);
extern void U3CHandleCoroutineU3Ec__Iterator1_MoveNext_m7F0FB4B2F8332D251F998FB377745036E2FB1003 (void);
extern void U3CHandleCoroutineU3Ec__Iterator1_System_Collections_Generic_IEnumeratorU3CobjectU3E_get_Current_mCCEED1817A0E88D6B697BA788DEC2FCD53E95A98 (void);
extern void U3CHandleCoroutineU3Ec__Iterator1_System_Collections_IEnumerator_get_Current_mC563D5698BF20372CD47E15C388D8AEE792EE2F7 (void);
extern void U3CHandleCoroutineU3Ec__Iterator1_Dispose_m84E8920A40644F4A0AA4D953C3C0C24CE85C49D7 (void);
extern void U3CHandleCoroutineU3Ec__Iterator1_Reset_m724942954324868D21459D67606624AE5ECEF4D5 (void);
extern void ThreadPool__ctor_mD3D85527EFCFC550AF223B30C23E5BC8F8AA3D97 (void);
extern void ThreadPool_Enqueue_mC20F6AAC56A8D731F70F08E1AF176CB5B6CEED70 (void);
extern void ThreadPool_Dispose_m6911891CF9B148E939BEDB73EC7B2836FB4BE773 (void);
extern void Worker__ctor_m9361A43DF974E0528A2ABC03D8D6BABC5413543F (void);
extern void Worker_ProcessTasks_m79A4F7BA0AAA18ACF0652E56A8CDC5FBB05CD1B7 (void);
extern void WorkScheduler__ctor_mF27EEE81FEFFB4131085D54CF71BC7C54FA28A25 (void);
extern void WorkScheduler_ScheduleWorkItem_m8585955683B613CBBF0F9700DFB0BD04A32C0CB1 (void);
extern void WorkScheduler_ProcessQueue_mD60251CAD814D263C074FA00FD28FA57D9968EC4 (void);
extern void U3CProcessQueueU3Ec__Iterator0__ctor_m9A15AA02A2EE422F3BADC25C57757A370150FB34 (void);
extern void U3CProcessQueueU3Ec__Iterator0_MoveNext_m1064ED344B47D60D450F60A5AD2236B4BC58BAA6 (void);
extern void U3CProcessQueueU3Ec__Iterator0_System_Collections_Generic_IEnumeratorU3CobjectU3E_get_Current_mE87BB3ECDA850FC4B21E05CD9DE885C0D398F278 (void);
extern void U3CProcessQueueU3Ec__Iterator0_System_Collections_IEnumerator_get_Current_mDB3082B713B1D36542DCFF7E62BAE1A5820D4280 (void);
extern void U3CProcessQueueU3Ec__Iterator0_Dispose_m2EF630521D656BA006A5BABD31C0938380C67FDE (void);
extern void U3CProcessQueueU3Ec__Iterator0_Reset_m7E5DA115233C9BBA712BEE91BAAF1ABFBC2CCD10 (void);
extern void PrefabPool_Recycle_mCBD6804295F6879BF044A7DB94046BFAD83E9A04 (void);
extern void Recycler__ctor_mB8C90F53DA63AB0893B8017FFB8D8B86F7DA6A51 (void);
extern void Recycler_Recycle_m85E286E277FB6C7401D0A0A33F5EAAD817828A40 (void);
extern void CRC32__cctor_mEFAA70C369D68E53495F77886EB8A588C584F2F4 (void);
extern void CRC32__ctor_m01585BBA4CC7A8DA520F4FC404FB4C1033258FA4 (void);
extern void CRC32_get_TotalBytesRead_mDFA2814BC50D7BDBC3F62D1B412CAEB8F50ABDE0 (void);
extern void CRC32_get_Crc32Result_mD49F2B1164980FB8CAE0B15C817088A2C85E3DB6 (void);
extern void CRC32_SlurpBlock_m24007D3AB683FF87DB6CCF4EC1ADE999E963B642 (void);
extern void DeflateManager__ctor_mA7B904F9B3FCC870B5A53FAE3B0AE4C725B84328 (void);
extern void DeflateManager__cctor_m20B8A07B399C8653606AAEAE5436066157C3C57B (void);
extern void DeflateManager__InitializeLazyMatch_mA8892E4CF74BCD2F38DF3EB02AE7D1BC8C02B5D8 (void);
extern void DeflateManager__InitializeTreeData_m62387FD649D47C9849E3529F7B227A28B661F0E1 (void);
extern void DeflateManager__InitializeBlocks_m1DFECA5454780954E14CD10E9C3D599B874CC673 (void);
extern void DeflateManager_pqdownheap_mE474D48F7E8BAD4ED36A385E3F976DEB8C3DB9B0 (void);
extern void DeflateManager__IsSmaller_m30490896E0195361CA6BAD3B49FB8D2D2706C74D (void);
extern void DeflateManager_scan_tree_m237C31EF73DF1DFB8641D308EEA6037486393927 (void);
extern void DeflateManager_build_bl_tree_m39C072F9F5BA23A3210C2F8A194BA4C93C240DF5 (void);
extern void DeflateManager_send_all_trees_m58572F9D8788CDDA497E646D908A9FAD410497EE (void);
extern void DeflateManager_send_tree_m9140F4CF7AC9EBDAA9588DFBAEE0660421E7E67A (void);
extern void DeflateManager_put_byte_m690904A59269F01F41FB8C468B287D7C6EAA5402 (void);
extern void DeflateManager_put_byte_m0AECA8F2C73002AF67919548E5649B743911DC8F (void);
extern void DeflateManager_put_short_m4BB109B74BCE2235F353F4BDF0483D636212F383 (void);
extern void DeflateManager_putShortMSB_m6D8288EFA713BF3F4485ED669068F64984E98323 (void);
extern void DeflateManager_send_code_mEDB5875BFBE4EB5CAB7FD0470CECC40F1EB83122 (void);
extern void DeflateManager_send_bits_m5B9A22F24588179E33AE4D82DD351C4878502C9A (void);
extern void DeflateManager__tr_align_m3C99159DC9C90512B005DAC1F1B9ACC9253C6576 (void);
extern void DeflateManager__tr_tally_mE39511E723BC9B71C5069F8F13527E7EE4DE4404 (void);
extern void DeflateManager_compress_block_m9035EFD628E4595D52DE8C8C228703188FBC6B5A (void);
extern void DeflateManager_set_data_type_mEC353693C3EADBC19B291197A7645F6E4905381D (void);
extern void DeflateManager_bi_flush_m10B2240BB94E7E3A63A1BD504F74602686CE695C (void);
extern void DeflateManager_bi_windup_mF980F488B8782413940276CEF824CFE19476117F (void);
extern void DeflateManager_copy_block_m36949FDA8B83C1A9BB41C5A34CED9EB1E2384347 (void);
extern void DeflateManager_flush_block_only_m8B019E63BC75AA426ABDC01624F8340C7B832C05 (void);
extern void DeflateManager_DeflateNone_mF16868E57A9B5BEDB3192CDC292A03C99F7F3130 (void);
extern void DeflateManager__tr_stored_block_mD807E25844CFA89224B36AA6B928F7A73D195B3B (void);
extern void DeflateManager__tr_flush_block_mC3F9D89FF9C94EE1161FFEA7EAC3AE854175D67A (void);
extern void DeflateManager__fillWindow_mCDF9165F6410824EDB310C25D43B4B5FA8C54793 (void);
extern void DeflateManager_DeflateFast_m0587FD5380ACDE9FAF762AA6F4BAAB4A16CA8267 (void);
extern void DeflateManager_DeflateSlow_mD1B75999DFF2295A9AB98CF70893F307B18B8568 (void);
extern void DeflateManager_longest_match_mAD7BD24246D226ECD27343CB4F778A04EBD03A4A (void);
extern void DeflateManager_get_WantRfc1950HeaderBytes_m5661399ADA54DFF965A373E2BECC13189F25FFEE (void);
extern void DeflateManager_set_WantRfc1950HeaderBytes_m799CF5C55738EC42D599049066DDD7F5D5392338 (void);
extern void DeflateManager_Initialize_m8323A058D2E8FF0EF623FE6353016E5D96378F7B (void);
extern void DeflateManager_Initialize_mF69DC7D6D2CEFE7AE0D837FF45FB3ECE77E8E35A (void);
extern void DeflateManager_Reset_mFABCA5369089732FE3AD1839EA4BF1CDAF950FEF (void);
extern void DeflateManager_End_m4CD9A64B75E607003F5EF5E5C459BFF4EA6A3319 (void);
extern void DeflateManager_Deflate_m40DAAB1A938EAFD48A53AD38B4865575F9987FAC (void);
extern void Config__ctor_mFF6ED1DBF6029782B6E4718AC9AE64F63EF79F3D (void);
extern void InfTree__ctor_mAA8C43547C79920F20315A68BB1581FA65A76DB1 (void);
extern void InfTree_huft_build_mB4AFD80D7191AE505DE60D943738E87BADFEDC49 (void);
extern void InfTree_inflate_trees_bits_m2D92054D5D35D56C64D12559D572A84389285465 (void);
extern void InfTree_inflate_trees_dynamic_m14FB6F0BEC5DD5CAE81D445DD9BFDFFEFF2E7B6F (void);
extern void InfTree_inflate_trees_fixed_m4D5A108E70C2FF7B49901B74C44462F8BC8E4742 (void);
extern void InfTree_initWorkArea_m2EB45CBA9DCD12C4A744CF527B25D1E803783907 (void);
extern void InfTree__cctor_m672165205E6B0D08654EBD53AC4ED7CF1C906468 (void);
extern void InflateBlocks__ctor_m45410C9F8C49E46902FF0267D6D5721E37C4BF43 (void);
extern void InflateBlocks_Reset_mEAA9883F239226FCB2BEC624925C21577A1055BA (void);
extern void InflateBlocks_Process_m7A54D069BBFF5001F7FA6CBDC78E3B17C2E61A0A (void);
extern void InflateBlocks_Free_mAD7B3706C051458090F37579EB32CA32C0BE595E (void);
extern void InflateBlocks_Flush_m6ACB8EA32412E2DE59214DE531076721D1F86B3F (void);
extern void InflateBlocks__cctor_mA53FD6119F252907216CF7AF7CBECA6F0445D6AC (void);
extern void InflateCodes__ctor_m5FDAA1F47A082870149AF77B7D5CB19D2ED2B921 (void);
extern void InflateCodes_Init_m7AFE3E6AE550E370553AD733B67948EC20326205 (void);
extern void InflateCodes_Process_m124EE62B63D5B7BB10A60A71CC4E0410C734E41A (void);
extern void InflateCodes_InflateFast_m1086A17D058F7153B814434A7EE96E8D31DF5CA1 (void);
extern void InflateCodes__cctor_mE590F6F121D8B48B84DD0E8D282FE6359F7C9313 (void);
extern void InflateManager__ctor_mEF35131A0B33EEC084DE5678CDFB10CCF95227C4 (void);
extern void InflateManager_get_HandleRfc1950HeaderBytes_mC9D351530A4A7C85F54EC324AD3ED886EDB95AA7 (void);
extern void InflateManager_Reset_m1A56B29904F5BEEAF84840E1B87C828FD1E32EEC (void);
extern void InflateManager_End_mB7EF05FA9F1FFE1086DE00C05CA9CA4E31E455E1 (void);
extern void InflateManager_Initialize_m61634F91BC501376E92B59399B7A89DCABA6586B (void);
extern void InflateManager_Inflate_m50B8CF154C3B2375CE085841C36F9750C2FAAF39 (void);
extern void InflateManager__cctor_mC46DE0329F420366D1B68D5AB24799471545692B (void);
extern void Tree__ctor_m1DFF55C0C323841EA5B57791EC7981878049C359 (void);
extern void Tree_d_code_m026BBBAC806BC856268E8000FD2F05002312A91D (void);
extern void Tree_gen_bitlen_m5EB4FC5C28E85EE82FADB2B70AA42132F4CE09AC (void);
extern void Tree_build_tree_m26242E4BC4AC8B736C7E2EC7FB6D760229F168B5 (void);
extern void Tree_gen_codes_m4CF7ECC8B1104EDFD99B0F6FDD77FEC200BC5952 (void);
extern void Tree_bi_reverse_mCC9A61E31386F5389F51E6293F7F1F6C0623A79E (void);
extern void Tree__cctor_mA93CE955F3DB1FF9467EF40F3DD06449AAA65567 (void);
extern void ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B (void);
extern void SharedUtils_URShift_mBCFB1746911A5964EA1DB6DDBBBA7C3E208371B7 (void);
extern void SharedUtils_URShift_mD9F0E37D8F8C7F9BB4798A1817949B02862DA1EF (void);
extern void SharedUtils_ReadInput_m04DEA02BA9AABEB317EF671B6B2559A554C2BB5F (void);
extern void StaticTree__ctor_m9820874FFC3EF3292D13A338E8988E9E9CF084BA (void);
extern void StaticTree__cctor_mF4E3B0AB27CEE7E11487D5238E60784DA9CECAFA (void);
extern void Adler_Adler32_m8A3761EF98770BFCEE3242665825CB496E4C8313 (void);
extern void Adler__cctor_m3CEFE6FA47D503A1D6EEFEBE1C38585A1EE10220 (void);
extern void ZlibCodec__ctor_m40C747342EAC3B3891ABCB80C5E34229FFF68D0F (void);
extern void ZlibCodec_InitializeInflate_m326D69D2B9CD25CAD8E83064A6891F178AB1B116 (void);
extern void ZlibCodec_InitializeInflate_mCBD1E3DA91FF1CA7064486432E262E24F5E1877E (void);
extern void ZlibCodec_Inflate_mEFD195C98EAC837238E73A3E763AB324379A4550 (void);
extern void ZlibCodec_EndInflate_mE7E95E904F7098F6899D8BD79823CFCDE877C4B9 (void);
extern void ZlibCodec_InitializeDeflate_m542BFAC119F9EA2F3F15B7C87E2714F82188A9EB (void);
extern void ZlibCodec_InitializeDeflate_m5553968574555F56D0E692871C12333B6138EDDA (void);
extern void ZlibCodec_Deflate_m68368C43EA37CDB2D268D1EAD2F2F4620FA46330 (void);
extern void ZlibCodec_EndDeflate_mFF40B57BD0A08EB4543C2EA55C8DCB069899E43D (void);
extern void ZlibCodec_flush_pending_mD40B5B6D11072492E3AAD5FF14964E75B56DAE56 (void);
extern void ZlibCodec_read_buf_mED8E85BF915B67D19C5659B5CE4BEC43EFD6B84E (void);
extern void GZipStream__ctor_mBDDB00F6B2BE4A007CD4441E2B8E6701EA599D0B (void);
extern void GZipStream__ctor_m72A5DAF83F1394062C95CA12E59E17EA848E4741 (void);
extern void GZipStream_get_FileName_m0D6FA7D5C31AA7D4849C22BEA1ACCFD8CA798E83 (void);
extern void GZipStream_set_FileName_m1BBFAD7EB67A55FE4F47A1AF9600C1D9F03805E3 (void);
extern void GZipStream_Close_mCEC51FA814FFBE146D6479832FCEF04CBAFFAAD7 (void);
extern void GZipStream_get_CanRead_m3957B0FBD4C180B037BE1C3D142F13D7163FE9DF (void);
extern void GZipStream_get_CanSeek_mEA092245DA6306EFD5D314D33C0DFC8578AC94F6 (void);
extern void GZipStream_get_CanWrite_m41F144C4C39711004F90C6C6C8559018CB4468CB (void);
extern void GZipStream_Flush_mECF86502A2DD3C91AB2DC198F959841C1FF5C13B (void);
extern void GZipStream_get_Length_m5FC04810DDDEE5E6DDA2CAC1DAD9D5F5D8362F32 (void);
extern void GZipStream_get_Position_m54F39FEB928AD39788C63C43527322859163841A (void);
extern void GZipStream_set_Position_mAC32F5E26F25BEE5CFDA744C0D14A8618492FB3F (void);
extern void GZipStream_Read_m34A44666EC5B822EB020DAF8916EA2481BB18942 (void);
extern void GZipStream_Seek_m8503951F8A4D64BF1CFE2026D09E07CF26BDE6FE (void);
extern void GZipStream_SetLength_mA8E5EFF8E8A24E0C32205F97B26D0C48B817A75A (void);
extern void GZipStream_Write_mC524891790868544FBECFBE26A21C864F0CAC335 (void);
extern void GZipStream_SlurpHeader_m5D83302AAF7EB805EA37524883FA3FD9CDB98A8E (void);
extern void GZipStream_SlurpZeroTerminatedString_m24CFAC49769B74F0C3C800A8621D09FA73A61564 (void);
extern void GZipStream_EmitHeader_m3D76DF2020913E4C80760EF5E24FB0E33874C527 (void);
extern void GZipStream__cctor_mF5DF96C583F4C3A722D71B4AA3A8D2A9D19071FE (void);
extern void ZlibBaseStream__ctor_mCA4A9F54161B0AEDAD281EE0CC09247BBB41F6E7 (void);
extern void ZlibBaseStream_get_Crc32_m240AACB0116DFA174E4752C5E958948348E2CCC1 (void);
extern void ZlibBaseStream_WriteByte_m1BA7F8D94D242810521AA1246B916997F5156A06 (void);
extern void ZlibBaseStream_Write_m17DC0152DFDCE0EB5B55E3FC562B8313490EB376 (void);
extern void ZlibBaseStream_finish_m943ABACCE83B407587BABF9AA0FAEE8059E5B35D (void);
extern void ZlibBaseStream_end_m8B560782B294CA50D2ADDB31DDA09AD8356FE55A (void);
extern void ZlibBaseStream_Close_m109A0572891B118EA9754E0BF99066D7F6F30430 (void);
extern void ZlibBaseStream_Flush_mDA0783AA3C9D0A6687AEDD9ABB828ACED548D585 (void);
extern void ZlibBaseStream_Seek_mD37F4E628A8B871EE6D9CD7DBFE74DAADAE9622E (void);
extern void ZlibBaseStream_SetLength_mFB65E079BF4C2E21D5EED5AC5911632C55E82CB3 (void);
extern void ZlibBaseStream_ReadZeroTerminatedString_m1129CEBC534AC892ACE3F2B6B66FE8103CF44A4E (void);
extern void ZlibBaseStream__ReadAndValidateGzipHeader_m65C3C51F4DFAECA2DCCA593299CBD453EF142CE6 (void);
extern void ZlibBaseStream_Read_m9181D0A7D2941CDEA659A803791B98B5700047BF (void);
extern void ZlibBaseStream_get_CanRead_mBD037D02FA093F4234FA9E995B786CBC8EA20B33 (void);
extern void ZlibBaseStream_get_CanSeek_m26406C8AC4C378467CBBB65540BA59F86510E132 (void);
extern void ZlibBaseStream_get_CanWrite_m4530CC3BA4504B1893231B082F3E3B186DDA546F (void);
extern void ZlibBaseStream_get_Length_m6BBD892C2EA6A8F43C8DA2E475F48A798AEA3586 (void);
extern void ZlibBaseStream_get_Position_mF9C08461A36C0A5E1FF1A88BBA7060DBA1E4E035 (void);
extern void ZlibBaseStream_set_Position_m01FE6FC87A956DABEFB425C0550C1931D758E76F (void);
static Il2CppMethodPointer s_methodPointers[194] = 
{
	Console__ctor_m2755C53D2BA72D0E268C7E6FCE8BC275F108E6A0,
	Console_get_Instance_m1D7D6684876CA6D805BE42F9A31BC9CDAEEFAC29,
	Console_Awake_mA984C2995E0F7172A298ACDE4A4CAFFFB354298B,
	Console_GetMessageList_m188A5B0BE835254C82E3FA55F07379DDA15385D8,
	Console_Log_m4C8ECC29B534EC6977A9340B43546F6548288C4A,
	Console_Warn_m0D5930BB5C00F466207E55D331004A0E8E893D7C,
	Console_Error_mB6C8026130404F262B46317AD1E11412E6674E3A,
	Console_Exception_m89579AA37D347386CDD278ACEAB82E9A051FC0EE,
	Console_Add_mCCCA2151001A8D946DDD6CDD524308E514155CD7,
	ConsoleMessage__ctor_mF7F8270EDB0C5D4C5E1DA9F2AC69102265A75647,
	UniExtender__ctor_m46C0DBE3E52F1DC0D853F3C45ED8A48CC9BE95E3,
	UniExtender_get_Instance_m8CE2F866FE48943F333D0936BC8B14C5EF5A3B27,
	UniExtender_Step_mFE39FFABF4AD0203ADAA616DEC6232750F4A5011,
	U3CStepU3Ec__Iterator0__ctor_m4BAFECFD17EF907C33146CF3084BA51B0EB8208B,
	U3CStepU3Ec__Iterator0_MoveNext_m06D8021D8E3048FBEB8810EE0B352ADD9F204C92,
	U3CStepU3Ec__Iterator0_System_Collections_Generic_IEnumeratorU3CobjectU3E_get_Current_m780F379ECAD60C598F723E0E95186656E8C52904,
	U3CStepU3Ec__Iterator0_System_Collections_IEnumerator_get_Current_m5E012E69AD1EC75819ABA72BF3F11AE7162583E7,
	U3CStepU3Ec__Iterator0_Dispose_m30F7AD5D5080202063BCBB2647F0BB935B8C8C67,
	U3CStepU3Ec__Iterator0_Reset_mC21DBB0D194FC703A008EF0E5693E281A8D54466,
	BackgroundTask__ctor_m3EC11D46263C4D95CDB547F1AB602A959FC405F6,
	ForegroundTask__ctor_mADBA30B1A6E682C52A50BAFC183DFB9029F3A2EC,
	MagicThread__ctor_m0DFB3C42FDB6A5022F7369FD64093ABED0DA823A,
	MagicThread_get_instance_m8E845D628C99070C2C9E4B61A6A0FF47DE804341,
	MagicThread_Start_mF8AC6E57F39A6880BF08FBA99BF208D81B646B90,
	MagicThread_Start_m57B89415402B002958AFBE57B71603B9ADDA6366,
	MagicThread_HandleCoroutine_m1B3F148449C762794A08BCE1093B8FDEED246013,
	MagicThread_HandleThread_mB4B9E56BF718DA5B7E070613541627C4773A4846,
	U3CStartU3Ec__Iterator0__ctor_mFEF24D928372ED04A27E79EADDEF4ECD798B24A4,
	U3CStartU3Ec__Iterator0_MoveNext_mB95A30819D857BEF9475F32A090EA5C79E272618,
	U3CStartU3Ec__Iterator0_System_Collections_Generic_IEnumeratorU3CobjectU3E_get_Current_m0F4616D0607A5BEFA451DC1560F5324018D9F288,
	U3CStartU3Ec__Iterator0_System_Collections_IEnumerator_get_Current_m457DE2903DBF85070088161AA34850FE67E9FC25,
	U3CStartU3Ec__Iterator0_Dispose_mFC5C7BA6378DC2DD92844DE19A24C4672B1305C2,
	U3CStartU3Ec__Iterator0_Reset_m8A18F038B15CB1BADE907D414A62235B60830F0F,
	U3CStartU3Ec__AnonStorey2__ctor_m0CBD1CFFE86E9FAB70478ED1A5D34BE6AD3B6207,
	U3CStartU3Ec__AnonStorey2_U3CU3Em__0_mA8B2DEE4D228A43E89A3805AB8A25F79A45A75CB,
	U3CHandleCoroutineU3Ec__Iterator1__ctor_m6859AF3FD8A9DE7D8F18F5A8A53727642C72B0B4,
	U3CHandleCoroutineU3Ec__Iterator1_MoveNext_m7F0FB4B2F8332D251F998FB377745036E2FB1003,
	U3CHandleCoroutineU3Ec__Iterator1_System_Collections_Generic_IEnumeratorU3CobjectU3E_get_Current_mCCEED1817A0E88D6B697BA788DEC2FCD53E95A98,
	U3CHandleCoroutineU3Ec__Iterator1_System_Collections_IEnumerator_get_Current_mC563D5698BF20372CD47E15C388D8AEE792EE2F7,
	U3CHandleCoroutineU3Ec__Iterator1_Dispose_m84E8920A40644F4A0AA4D953C3C0C24CE85C49D7,
	U3CHandleCoroutineU3Ec__Iterator1_Reset_m724942954324868D21459D67606624AE5ECEF4D5,
	ThreadPool__ctor_mD3D85527EFCFC550AF223B30C23E5BC8F8AA3D97,
	ThreadPool_Enqueue_mC20F6AAC56A8D731F70F08E1AF176CB5B6CEED70,
	ThreadPool_Dispose_m6911891CF9B148E939BEDB73EC7B2836FB4BE773,
	Worker__ctor_m9361A43DF974E0528A2ABC03D8D6BABC5413543F,
	Worker_ProcessTasks_m79A4F7BA0AAA18ACF0652E56A8CDC5FBB05CD1B7,
	WorkScheduler__ctor_mF27EEE81FEFFB4131085D54CF71BC7C54FA28A25,
	WorkScheduler_ScheduleWorkItem_m8585955683B613CBBF0F9700DFB0BD04A32C0CB1,
	WorkScheduler_ProcessQueue_mD60251CAD814D263C074FA00FD28FA57D9968EC4,
	U3CProcessQueueU3Ec__Iterator0__ctor_m9A15AA02A2EE422F3BADC25C57757A370150FB34,
	U3CProcessQueueU3Ec__Iterator0_MoveNext_m1064ED344B47D60D450F60A5AD2236B4BC58BAA6,
	U3CProcessQueueU3Ec__Iterator0_System_Collections_Generic_IEnumeratorU3CobjectU3E_get_Current_mE87BB3ECDA850FC4B21E05CD9DE885C0D398F278,
	U3CProcessQueueU3Ec__Iterator0_System_Collections_IEnumerator_get_Current_mDB3082B713B1D36542DCFF7E62BAE1A5820D4280,
	U3CProcessQueueU3Ec__Iterator0_Dispose_m2EF630521D656BA006A5BABD31C0938380C67FDE,
	U3CProcessQueueU3Ec__Iterator0_Reset_m7E5DA115233C9BBA712BEE91BAAF1ABFBC2CCD10,
	NULL,
	PrefabPool_Recycle_mCBD6804295F6879BF044A7DB94046BFAD83E9A04,
	Recycler__ctor_mB8C90F53DA63AB0893B8017FFB8D8B86F7DA6A51,
	Recycler_Recycle_m85E286E277FB6C7401D0A0A33F5EAAD817828A40,
	CRC32__cctor_mEFAA70C369D68E53495F77886EB8A588C584F2F4,
	CRC32__ctor_m01585BBA4CC7A8DA520F4FC404FB4C1033258FA4,
	CRC32_get_TotalBytesRead_mDFA2814BC50D7BDBC3F62D1B412CAEB8F50ABDE0,
	CRC32_get_Crc32Result_mD49F2B1164980FB8CAE0B15C817088A2C85E3DB6,
	CRC32_SlurpBlock_m24007D3AB683FF87DB6CCF4EC1ADE999E963B642,
	DeflateManager__ctor_mA7B904F9B3FCC870B5A53FAE3B0AE4C725B84328,
	DeflateManager__cctor_m20B8A07B399C8653606AAEAE5436066157C3C57B,
	DeflateManager__InitializeLazyMatch_mA8892E4CF74BCD2F38DF3EB02AE7D1BC8C02B5D8,
	DeflateManager__InitializeTreeData_m62387FD649D47C9849E3529F7B227A28B661F0E1,
	DeflateManager__InitializeBlocks_m1DFECA5454780954E14CD10E9C3D599B874CC673,
	DeflateManager_pqdownheap_mE474D48F7E8BAD4ED36A385E3F976DEB8C3DB9B0,
	DeflateManager__IsSmaller_m30490896E0195361CA6BAD3B49FB8D2D2706C74D,
	DeflateManager_scan_tree_m237C31EF73DF1DFB8641D308EEA6037486393927,
	DeflateManager_build_bl_tree_m39C072F9F5BA23A3210C2F8A194BA4C93C240DF5,
	DeflateManager_send_all_trees_m58572F9D8788CDDA497E646D908A9FAD410497EE,
	DeflateManager_send_tree_m9140F4CF7AC9EBDAA9588DFBAEE0660421E7E67A,
	DeflateManager_put_byte_m690904A59269F01F41FB8C468B287D7C6EAA5402,
	DeflateManager_put_byte_m0AECA8F2C73002AF67919548E5649B743911DC8F,
	DeflateManager_put_short_m4BB109B74BCE2235F353F4BDF0483D636212F383,
	DeflateManager_putShortMSB_m6D8288EFA713BF3F4485ED669068F64984E98323,
	DeflateManager_send_code_mEDB5875BFBE4EB5CAB7FD0470CECC40F1EB83122,
	DeflateManager_send_bits_m5B9A22F24588179E33AE4D82DD351C4878502C9A,
	DeflateManager__tr_align_m3C99159DC9C90512B005DAC1F1B9ACC9253C6576,
	DeflateManager__tr_tally_mE39511E723BC9B71C5069F8F13527E7EE4DE4404,
	DeflateManager_compress_block_m9035EFD628E4595D52DE8C8C228703188FBC6B5A,
	DeflateManager_set_data_type_mEC353693C3EADBC19B291197A7645F6E4905381D,
	DeflateManager_bi_flush_m10B2240BB94E7E3A63A1BD504F74602686CE695C,
	DeflateManager_bi_windup_mF980F488B8782413940276CEF824CFE19476117F,
	DeflateManager_copy_block_m36949FDA8B83C1A9BB41C5A34CED9EB1E2384347,
	DeflateManager_flush_block_only_m8B019E63BC75AA426ABDC01624F8340C7B832C05,
	DeflateManager_DeflateNone_mF16868E57A9B5BEDB3192CDC292A03C99F7F3130,
	DeflateManager__tr_stored_block_mD807E25844CFA89224B36AA6B928F7A73D195B3B,
	DeflateManager__tr_flush_block_mC3F9D89FF9C94EE1161FFEA7EAC3AE854175D67A,
	DeflateManager__fillWindow_mCDF9165F6410824EDB310C25D43B4B5FA8C54793,
	DeflateManager_DeflateFast_m0587FD5380ACDE9FAF762AA6F4BAAB4A16CA8267,
	DeflateManager_DeflateSlow_mD1B75999DFF2295A9AB98CF70893F307B18B8568,
	DeflateManager_longest_match_mAD7BD24246D226ECD27343CB4F778A04EBD03A4A,
	DeflateManager_get_WantRfc1950HeaderBytes_m5661399ADA54DFF965A373E2BECC13189F25FFEE,
	DeflateManager_set_WantRfc1950HeaderBytes_m799CF5C55738EC42D599049066DDD7F5D5392338,
	DeflateManager_Initialize_m8323A058D2E8FF0EF623FE6353016E5D96378F7B,
	DeflateManager_Initialize_mF69DC7D6D2CEFE7AE0D837FF45FB3ECE77E8E35A,
	DeflateManager_Reset_mFABCA5369089732FE3AD1839EA4BF1CDAF950FEF,
	DeflateManager_End_m4CD9A64B75E607003F5EF5E5C459BFF4EA6A3319,
	DeflateManager_Deflate_m40DAAB1A938EAFD48A53AD38B4865575F9987FAC,
	Config__ctor_mFF6ED1DBF6029782B6E4718AC9AE64F63EF79F3D,
	InfTree__ctor_mAA8C43547C79920F20315A68BB1581FA65A76DB1,
	InfTree_huft_build_mB4AFD80D7191AE505DE60D943738E87BADFEDC49,
	InfTree_inflate_trees_bits_m2D92054D5D35D56C64D12559D572A84389285465,
	InfTree_inflate_trees_dynamic_m14FB6F0BEC5DD5CAE81D445DD9BFDFFEFF2E7B6F,
	InfTree_inflate_trees_fixed_m4D5A108E70C2FF7B49901B74C44462F8BC8E4742,
	InfTree_initWorkArea_m2EB45CBA9DCD12C4A744CF527B25D1E803783907,
	InfTree__cctor_m672165205E6B0D08654EBD53AC4ED7CF1C906468,
	InflateBlocks__ctor_m45410C9F8C49E46902FF0267D6D5721E37C4BF43,
	InflateBlocks_Reset_mEAA9883F239226FCB2BEC624925C21577A1055BA,
	InflateBlocks_Process_m7A54D069BBFF5001F7FA6CBDC78E3B17C2E61A0A,
	InflateBlocks_Free_mAD7B3706C051458090F37579EB32CA32C0BE595E,
	InflateBlocks_Flush_m6ACB8EA32412E2DE59214DE531076721D1F86B3F,
	InflateBlocks__cctor_mA53FD6119F252907216CF7AF7CBECA6F0445D6AC,
	InflateCodes__ctor_m5FDAA1F47A082870149AF77B7D5CB19D2ED2B921,
	InflateCodes_Init_m7AFE3E6AE550E370553AD733B67948EC20326205,
	InflateCodes_Process_m124EE62B63D5B7BB10A60A71CC4E0410C734E41A,
	InflateCodes_InflateFast_m1086A17D058F7153B814434A7EE96E8D31DF5CA1,
	InflateCodes__cctor_mE590F6F121D8B48B84DD0E8D282FE6359F7C9313,
	InflateManager__ctor_mEF35131A0B33EEC084DE5678CDFB10CCF95227C4,
	InflateManager_get_HandleRfc1950HeaderBytes_mC9D351530A4A7C85F54EC324AD3ED886EDB95AA7,
	InflateManager_Reset_m1A56B29904F5BEEAF84840E1B87C828FD1E32EEC,
	InflateManager_End_mB7EF05FA9F1FFE1086DE00C05CA9CA4E31E455E1,
	InflateManager_Initialize_m61634F91BC501376E92B59399B7A89DCABA6586B,
	InflateManager_Inflate_m50B8CF154C3B2375CE085841C36F9750C2FAAF39,
	InflateManager__cctor_mC46DE0329F420366D1B68D5AB24799471545692B,
	Tree__ctor_m1DFF55C0C323841EA5B57791EC7981878049C359,
	Tree_d_code_m026BBBAC806BC856268E8000FD2F05002312A91D,
	Tree_gen_bitlen_m5EB4FC5C28E85EE82FADB2B70AA42132F4CE09AC,
	Tree_build_tree_m26242E4BC4AC8B736C7E2EC7FB6D760229F168B5,
	Tree_gen_codes_m4CF7ECC8B1104EDFD99B0F6FDD77FEC200BC5952,
	Tree_bi_reverse_mCC9A61E31386F5389F51E6293F7F1F6C0623A79E,
	Tree__cctor_mA93CE955F3DB1FF9467EF40F3DD06449AAA65567,
	ZlibException__ctor_mE8B0D2B27447A307E0FEEBD05B776D913611D50B,
	SharedUtils_URShift_mBCFB1746911A5964EA1DB6DDBBBA7C3E208371B7,
	SharedUtils_URShift_mD9F0E37D8F8C7F9BB4798A1817949B02862DA1EF,
	SharedUtils_ReadInput_m04DEA02BA9AABEB317EF671B6B2559A554C2BB5F,
	StaticTree__ctor_m9820874FFC3EF3292D13A338E8988E9E9CF084BA,
	StaticTree__cctor_mF4E3B0AB27CEE7E11487D5238E60784DA9CECAFA,
	Adler_Adler32_m8A3761EF98770BFCEE3242665825CB496E4C8313,
	Adler__cctor_m3CEFE6FA47D503A1D6EEFEBE1C38585A1EE10220,
	ZlibCodec__ctor_m40C747342EAC3B3891ABCB80C5E34229FFF68D0F,
	ZlibCodec_InitializeInflate_m326D69D2B9CD25CAD8E83064A6891F178AB1B116,
	ZlibCodec_InitializeInflate_mCBD1E3DA91FF1CA7064486432E262E24F5E1877E,
	ZlibCodec_Inflate_mEFD195C98EAC837238E73A3E763AB324379A4550,
	ZlibCodec_EndInflate_mE7E95E904F7098F6899D8BD79823CFCDE877C4B9,
	ZlibCodec_InitializeDeflate_m542BFAC119F9EA2F3F15B7C87E2714F82188A9EB,
	ZlibCodec_InitializeDeflate_m5553968574555F56D0E692871C12333B6138EDDA,
	ZlibCodec_Deflate_m68368C43EA37CDB2D268D1EAD2F2F4620FA46330,
	ZlibCodec_EndDeflate_mFF40B57BD0A08EB4543C2EA55C8DCB069899E43D,
	ZlibCodec_flush_pending_mD40B5B6D11072492E3AAD5FF14964E75B56DAE56,
	ZlibCodec_read_buf_mED8E85BF915B67D19C5659B5CE4BEC43EFD6B84E,
	GZipStream__ctor_mBDDB00F6B2BE4A007CD4441E2B8E6701EA599D0B,
	GZipStream__ctor_m72A5DAF83F1394062C95CA12E59E17EA848E4741,
	GZipStream_get_FileName_m0D6FA7D5C31AA7D4849C22BEA1ACCFD8CA798E83,
	GZipStream_set_FileName_m1BBFAD7EB67A55FE4F47A1AF9600C1D9F03805E3,
	GZipStream_Close_mCEC51FA814FFBE146D6479832FCEF04CBAFFAAD7,
	GZipStream_get_CanRead_m3957B0FBD4C180B037BE1C3D142F13D7163FE9DF,
	GZipStream_get_CanSeek_mEA092245DA6306EFD5D314D33C0DFC8578AC94F6,
	GZipStream_get_CanWrite_m41F144C4C39711004F90C6C6C8559018CB4468CB,
	GZipStream_Flush_mECF86502A2DD3C91AB2DC198F959841C1FF5C13B,
	GZipStream_get_Length_m5FC04810DDDEE5E6DDA2CAC1DAD9D5F5D8362F32,
	GZipStream_get_Position_m54F39FEB928AD39788C63C43527322859163841A,
	GZipStream_set_Position_mAC32F5E26F25BEE5CFDA744C0D14A8618492FB3F,
	GZipStream_Read_m34A44666EC5B822EB020DAF8916EA2481BB18942,
	GZipStream_Seek_m8503951F8A4D64BF1CFE2026D09E07CF26BDE6FE,
	GZipStream_SetLength_mA8E5EFF8E8A24E0C32205F97B26D0C48B817A75A,
	GZipStream_Write_mC524891790868544FBECFBE26A21C864F0CAC335,
	GZipStream_SlurpHeader_m5D83302AAF7EB805EA37524883FA3FD9CDB98A8E,
	GZipStream_SlurpZeroTerminatedString_m24CFAC49769B74F0C3C800A8621D09FA73A61564,
	GZipStream_EmitHeader_m3D76DF2020913E4C80760EF5E24FB0E33874C527,
	GZipStream__cctor_mF5DF96C583F4C3A722D71B4AA3A8D2A9D19071FE,
	ZlibBaseStream__ctor_mCA4A9F54161B0AEDAD281EE0CC09247BBB41F6E7,
	ZlibBaseStream_get_Crc32_m240AACB0116DFA174E4752C5E958948348E2CCC1,
	ZlibBaseStream_WriteByte_m1BA7F8D94D242810521AA1246B916997F5156A06,
	ZlibBaseStream_Write_m17DC0152DFDCE0EB5B55E3FC562B8313490EB376,
	ZlibBaseStream_finish_m943ABACCE83B407587BABF9AA0FAEE8059E5B35D,
	ZlibBaseStream_end_m8B560782B294CA50D2ADDB31DDA09AD8356FE55A,
	ZlibBaseStream_Close_m109A0572891B118EA9754E0BF99066D7F6F30430,
	ZlibBaseStream_Flush_mDA0783AA3C9D0A6687AEDD9ABB828ACED548D585,
	ZlibBaseStream_Seek_mD37F4E628A8B871EE6D9CD7DBFE74DAADAE9622E,
	ZlibBaseStream_SetLength_mFB65E079BF4C2E21D5EED5AC5911632C55E82CB3,
	ZlibBaseStream_ReadZeroTerminatedString_m1129CEBC534AC892ACE3F2B6B66FE8103CF44A4E,
	ZlibBaseStream__ReadAndValidateGzipHeader_m65C3C51F4DFAECA2DCCA593299CBD453EF142CE6,
	ZlibBaseStream_Read_m9181D0A7D2941CDEA659A803791B98B5700047BF,
	ZlibBaseStream_get_CanRead_mBD037D02FA093F4234FA9E995B786CBC8EA20B33,
	ZlibBaseStream_get_CanSeek_m26406C8AC4C378467CBBB65540BA59F86510E132,
	ZlibBaseStream_get_CanWrite_m4530CC3BA4504B1893231B082F3E3B186DDA546F,
	ZlibBaseStream_get_Length_m6BBD892C2EA6A8F43C8DA2E475F48A798AEA3586,
	ZlibBaseStream_get_Position_mF9C08461A36C0A5E1FF1A88BBA7060DBA1E4E035,
	ZlibBaseStream_set_Position_m01FE6FC87A956DABEFB425C0550C1931D758E76F,
};
static const int32_t s_InvokerIndices[194] = 
{
	6521,
	9745,
	6521,
	9745,
	9624,
	9624,
	9624,
	9624,
	8715,
	2932,
	6521,
	9745,
	8758,
	6521,
	6301,
	6398,
	6398,
	6521,
	6521,
	6521,
	6521,
	6521,
	9745,
	8975,
	6398,
	4779,
	5349,
	6521,
	6301,
	6398,
	6398,
	6521,
	6521,
	6521,
	5349,
	6521,
	6301,
	6398,
	6398,
	6521,
	6521,
	6521,
	5349,
	6521,
	5349,
	6521,
	6521,
	3138,
	4779,
	6521,
	6301,
	6398,
	6398,
	6521,
	6521,
	0,
	5349,
	6521,
	6521,
	9780,
	6521,
	6367,
	6366,
	1785,
	6521,
	9780,
	6521,
	6521,
	6521,
	3134,
	7348,
	3134,
	6366,
	1728,
	3134,
	1785,
	5253,
	5320,
	5320,
	2932,
	2906,
	6521,
	2011,
	3138,
	6521,
	6521,
	6521,
	1726,
	5253,
	4513,
	1726,
	1726,
	6521,
	4513,
	4513,
	4513,
	6301,
	5253,
	1443,
	288,
	4539,
	6366,
	2304,
	603,
	6521,
	20,
	548,
	66,
	7099,
	5320,
	9780,
	1805,
	3138,
	2304,
	5349,
	2304,
	9780,
	6521,
	207,
	1449,
	122,
	9780,
	5253,
	6301,
	4539,
	4539,
	2304,
	2304,
	9780,
	6521,
	9277,
	5349,
	5349,
	8317,
	8625,
	9780,
	5349,
	8625,
	8663,
	7457,
	654,
	9780,
	7546,
	9780,
	6521,
	4467,
	2275,
	4513,
	6366,
	2275,
	1424,
	4513,
	6366,
	6521,
	1443,
	3134,
	1214,
	6398,
	5349,
	6521,
	6301,
	6301,
	6301,
	6521,
	6367,
	6367,
	5321,
	1443,
	2449,
	5321,
	1785,
	2304,
	1512,
	6521,
	9780,
	632,
	6366,
	5253,
	1785,
	6521,
	6521,
	6521,
	6521,
	2449,
	5321,
	6398,
	6521,
	1443,
	6301,
	6301,
	6301,
	6367,
	6367,
	5321,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x06000038, { 0, 6 } },
};
extern const uint32_t g_rgctx_IList_1_tF4376582245C1D79EAA081ED987450404FF8800E;
extern const uint32_t g_rgctx_ICollection_1_tFA25F93A7E86FF4F9D2BBF3F171C2AFD27FBA3C7;
extern const uint32_t g_rgctx_ICollection_1_get_Count_mFD890FCAF54818A07B2E0C40B3A6BE6EDBD6506C;
extern const uint32_t g_rgctx_IList_1_get_Item_m210D45651C8383C4711149807F4AA474E622A128;
extern const uint32_t g_rgctx_T_tF74EB1429EF66BA1015072D81F2D5F86F85AED30;
extern const uint32_t g_rgctx_IList_1_RemoveAt_mA675C7E084F207E484BB17D277575CEC9005226A;
static const Il2CppRGCTXDefinition s_rgctxValues[6] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IList_1_tF4376582245C1D79EAA081ED987450404FF8800E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_tFA25F93A7E86FF4F9D2BBF3F171C2AFD27FBA3C7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_get_Count_mFD890FCAF54818A07B2E0C40B3A6BE6EDBD6506C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IList_1_get_Item_m210D45651C8383C4711149807F4AA474E622A128 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF74EB1429EF66BA1015072D81F2D5F86F85AED30 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IList_1_RemoveAt_mA675C7E084F207E484BB17D277575CEC9005226A },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UniExtensions_CodeGenModule;
const Il2CppCodeGenModule g_UniExtensions_CodeGenModule = 
{
	"UniExtensions.dll",
	194,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	6,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
