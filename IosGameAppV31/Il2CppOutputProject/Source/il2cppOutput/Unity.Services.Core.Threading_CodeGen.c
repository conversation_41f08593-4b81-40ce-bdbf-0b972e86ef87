﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnityThreadUtilsInternal__ctor_m9430BA50989685AA40935124D40A44F29156A3A2 (void);
static Il2CppMethodPointer s_methodPointers[1] = 
{
	UnityThreadUtilsInternal__ctor_m9430BA50989685AA40935124D40A44F29156A3A2,
};
static const int32_t s_InvokerIndices[1] = 
{
	6521,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Services_Core_Threading_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Services_Core_Threading_CodeGenModule = 
{
	"Unity.Services.Core.Threading.dll",
	1,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
