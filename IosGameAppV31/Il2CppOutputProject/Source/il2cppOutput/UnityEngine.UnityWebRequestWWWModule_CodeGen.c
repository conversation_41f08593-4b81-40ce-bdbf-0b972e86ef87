﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void WWW_UnEscapeURL_m6D51170631248E58948E0221C6A0E99CB46C7EFE (void);
extern void WWW_UnEscapeURL_mBE1A51E8AD62C22B843A9D97558B7542FD0A35E2 (void);
extern void WWW_LoadFromCacheOrDownload_mE75A94DFDCDD17412FDB89CB60498BF4405F64F1 (void);
extern void WWW_LoadFromCacheOrDownload_mB384AAC7E38EB3D152BAA71749DE422A7B4117A1 (void);
extern void WWW_LoadFromCacheOrDownload_mFD27E3686C2415421CF328AE75017D39FF7A792F (void);
extern void WWW__ctor_m5D29D83E9EE0925ED8252347CE24EC236401503D (void);
extern void WWW__ctor_mE54C20112FA803BB706A405F2892106B2BCB86FD (void);
extern void WWW_get_assetBundle_mB4DC5AFC3732922D2AE792A02699058DABF5B38E (void);
extern void WWW_get_bytes_m83F5C24FC5AC80C5F3B9AF1E290E08F8B96C0642 (void);
extern void WWW_get_error_m6B2E4F6DB8ECC8217A112EC62AAA1D5E71AA1C93 (void);
extern void WWW_get_isDone_m7E88B666AD0E3903757043813B2811BBFCCCA52E (void);
extern void WWW_get_progress_m8BE51921011B9C737C690F8776F93109E5481B47 (void);
extern void WWW_CreateTextureFromDownloadedData_m670DE15775E657BC8CE6CD19FE2C29EA3429F8F0 (void);
extern void WWW_get_texture_mB38F7FC4220AC09935423B84FD4EB852CF172AAE (void);
extern void WWW_get_url_m368B1D7D23DC22E412A3F802C6E3047760665519 (void);
extern void WWW_get_keepWaiting_m2D6B60FD9CB9C3E86D69E87EA32953AA68AE26B9 (void);
extern void WWW_Dispose_mE5FC4A2013C63A68C287F139B177D86F75C4A74F (void);
extern void WWW_GetAudioClipInternal_m08B69E33747BD466B99370200B183B6341798974 (void);
extern void WWW_GetAudioClip_mACAD8F04E72EF939034B1EA9E8BF2AE524C67714 (void);
extern void WWW_GetAudioClip_m38EFF82040D63C9424EC3A4EDA9E562FB0097116 (void);
extern void WWW_WaitUntilDoneIfPossible_mD975AFF6737F00BB5003C5AEDBD795751F129A84 (void);
extern void WebRequestWWW_InternalCreateAudioClipUsingDH_m2FC91348090ABC797376D75D1F69200DE4946EC8 (void);
static Il2CppMethodPointer s_methodPointers[22] = 
{
	WWW_UnEscapeURL_m6D51170631248E58948E0221C6A0E99CB46C7EFE,
	WWW_UnEscapeURL_mBE1A51E8AD62C22B843A9D97558B7542FD0A35E2,
	WWW_LoadFromCacheOrDownload_mE75A94DFDCDD17412FDB89CB60498BF4405F64F1,
	WWW_LoadFromCacheOrDownload_mB384AAC7E38EB3D152BAA71749DE422A7B4117A1,
	WWW_LoadFromCacheOrDownload_mFD27E3686C2415421CF328AE75017D39FF7A792F,
	WWW__ctor_m5D29D83E9EE0925ED8252347CE24EC236401503D,
	WWW__ctor_mE54C20112FA803BB706A405F2892106B2BCB86FD,
	WWW_get_assetBundle_mB4DC5AFC3732922D2AE792A02699058DABF5B38E,
	WWW_get_bytes_m83F5C24FC5AC80C5F3B9AF1E290E08F8B96C0642,
	WWW_get_error_m6B2E4F6DB8ECC8217A112EC62AAA1D5E71AA1C93,
	WWW_get_isDone_m7E88B666AD0E3903757043813B2811BBFCCCA52E,
	WWW_get_progress_m8BE51921011B9C737C690F8776F93109E5481B47,
	WWW_CreateTextureFromDownloadedData_m670DE15775E657BC8CE6CD19FE2C29EA3429F8F0,
	WWW_get_texture_mB38F7FC4220AC09935423B84FD4EB852CF172AAE,
	WWW_get_url_m368B1D7D23DC22E412A3F802C6E3047760665519,
	WWW_get_keepWaiting_m2D6B60FD9CB9C3E86D69E87EA32953AA68AE26B9,
	WWW_Dispose_mE5FC4A2013C63A68C287F139B177D86F75C4A74F,
	WWW_GetAudioClipInternal_m08B69E33747BD466B99370200B183B6341798974,
	WWW_GetAudioClip_mACAD8F04E72EF939034B1EA9E8BF2AE524C67714,
	WWW_GetAudioClip_m38EFF82040D63C9424EC3A4EDA9E562FB0097116,
	WWW_WaitUntilDoneIfPossible_mD975AFF6737F00BB5003C5AEDBD795751F129A84,
	WebRequestWWW_InternalCreateAudioClipUsingDH_m2FC91348090ABC797376D75D1F69200DE4946EC8,
};
static const int32_t s_InvokerIndices[22] = 
{
	9391,
	8737,
	8732,
	8094,
	8087,
	5349,
	1238,
	6398,
	6398,
	6398,
	6301,
	6444,
	4766,
	6398,
	6398,
	6301,
	6521,
	1036,
	2471,
	1470,
	6301,
	7138,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestWWWModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestWWWModule_CodeGenModule = 
{
	"UnityEngine.UnityWebRequestWWWModule.dll",
	22,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
