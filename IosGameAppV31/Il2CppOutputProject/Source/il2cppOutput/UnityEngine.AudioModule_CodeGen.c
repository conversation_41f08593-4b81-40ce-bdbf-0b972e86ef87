﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void AudioSettings_get_dspTime_m246053C21870BC68BF7BBC5C5AC1B8F25337AEEA (void);
extern void AudioSettings_InvokeOnAudioConfigurationChanged_m8273D3AEB24F4C3E374238B6F699BE6696808E85 (void);
extern void AudioSettings_InvokeOnAudioSystemShuttingDown_m1B9895D60B3267EBDEC69B9169730DBAD8325E90 (void);
extern void AudioSettings_InvokeOnAudioSystemStartedUp_m7FE042936237E5BDCB20299D8C4CF583B661468C (void);
extern void AudioSettings_StartAudioOutput_mB04D851DD0E6115DEEFB55779F880146263C67BE (void);
extern void AudioSettings_StopAudioOutput_m3FE7A8EADAB2FB570BB05F7C353E25E15885D1CB (void);
extern void AudioConfigurationChangeHandler__ctor_mA9827AB9472EC8EE0A0F0FC24EBC06B4740DD944 (void);
extern void AudioConfigurationChangeHandler_Invoke_m4DC27DD11512481B60071B20284E6886DAE54DE2 (void);
extern void Mobile_get_muteState_m64C1E8C61537317A7F153E1A72F7D39D85DA684D (void);
extern void Mobile_set_muteState_m7C9A464BCA3762330E18CCAD79AF6C47B863CA02 (void);
extern void Mobile_get_stopAudioOutputOnMute_m43EC82258D38C418353DFE19F32B51B64B18DCCA (void);
extern void Mobile_InvokeOnMuteStateChanged_mE5242862F948BA9FBB013A2B45F645B6A21E6198 (void);
extern void Mobile_InvokeIsStopAudioOutputOnMuteEnabled_m854CB455C7BE7ADC06BABCB9AA24F60309AE7ED1 (void);
extern void Mobile_StartAudioOutput_m731D1EEEE7A0D56BAADD571BA0FCAC13FB071223 (void);
extern void Mobile_StopAudioOutput_m10B8CEF668EE4967D0AD1D6741B6A37540C28A46 (void);
extern void AudioClip__ctor_m038DA97CB07076D1D9391E1E103F0F41D3622F89 (void);
extern void AudioClip_GetData_mBDEFD7D7C8E5DEA3CCEE2D7DB406DBB0C244924E (void);
extern void AudioClip_SetData_mB49A9BC4639C62B9C8B22319D33D46AAD176BC3B (void);
extern void AudioClip_Construct_Internal_m88BC07CE3F412DDB62820F9430D1D52DA42A26F6 (void);
extern void AudioClip_GetName_m561BBA037957E25D5BC5A962A1AA0C789895C9D1 (void);
extern void AudioClip_CreateUserSound_m34DA102DD6848D555D4A9D45AFAA9D3E5574BC45 (void);
extern void AudioClip_get_length_m6102CB29AF65988797452E4D6E43D4788303873D (void);
extern void AudioClip_get_samples_mDEA01CA75E7DEA0F8D480E4AF97FB96085BCF38E (void);
extern void AudioClip_get_channels_mFEECF5D6389D196BA5102EB79257298B9FDC9F84 (void);
extern void AudioClip_get_frequency_m6647E10F4B2B1335187B0066E82468CCCF19647B (void);
extern void AudioClip_GetData_m1F6480FFDA2E354A7D8C8DE40F61AAB5AF6B4A1D (void);
extern void AudioClip_SetData_m7B473C614C11953D746770F4F89B44600B5A6AF3 (void);
extern void AudioClip_Create_mF6B34084B76355CBC1991D8F4EAA878AA3A033A2 (void);
extern void AudioClip_Create_m845EAF7C4DB9F316162F8441D225587CD043B9BF (void);
extern void AudioClip_Create_mE8111F06981E42666B6A9A59D0A3EBE002D2CDFB (void);
extern void AudioClip_Create_m988FEB04BC74440E65C3CF07414E4867AAE737F8 (void);
extern void AudioClip_add_m_PCMReaderCallback_mA226EA143D90E04117A740FC9FA9F1111346CA83 (void);
extern void AudioClip_remove_m_PCMReaderCallback_m3258A455005F4A94570B4F8FE28A5EDA91A88412 (void);
extern void AudioClip_add_m_PCMSetPositionCallback_mB280AD93A847C65F536D846FECC7DCBE9266C37F (void);
extern void AudioClip_remove_m_PCMSetPositionCallback_m39598139640580138742F129E0510917DF2E233C (void);
extern void AudioClip_InvokePCMReaderCallback_Internal_m766E5705AB5AE16F5F142867CC3758ABE4BF462C (void);
extern void AudioClip_InvokePCMSetPositionCallback_Internal_m986EF703B7DDE42343730DE93A095D05B9F4DBB8 (void);
extern void PCMReaderCallback__ctor_mF621B6CC1A4BA6525190C5037401CF2FD5C0CF28 (void);
extern void PCMReaderCallback_Invoke_m76784C690C36B513E2AA5B0E4FD9831B2C7E5152 (void);
extern void PCMSetPositionCallback__ctor_mD16F77DDB552EB69BB3F5EF39420B2F09F95455B (void);
extern void PCMSetPositionCallback_Invoke_m434D4F02FA25F91DF6199EC5A799C551C7F93702 (void);
extern void AudioBehaviour__ctor_m6D88837496C42A746A51383F3D6F29CA72A9D309 (void);
extern void AudioListener_get_volume_m8EAB8FBA127A53E689C1D8C1857781070381974A (void);
extern void AudioListener__ctor_m428A6CC2CFA95A7D6065D33098191569A7412EE4 (void);
extern void AudioSource_GetPitch_m80F6D2BAF966F669253E9231AFCFFC303779913D (void);
extern void AudioSource_SetPitch_mE75DEDF8F37301BDA63E0F545A7A00850C24F53E (void);
extern void AudioSource_PlayHelper_m4DE8C48925C3548BED306DAB9F87939F24A46960 (void);
extern void AudioSource_Play_m10DB5ACD1CC32EE433DBC10416B1450A30DE5F16 (void);
extern void AudioSource_PlayOneShotHelper_mD110EAF42353687BD0B1190EEF30F0C65A4CF265 (void);
extern void AudioSource_Stop_m8A4872F0A2680798CD28894DD28609445C4783F5 (void);
extern void AudioSource_GetOutputDataHelper_m209E9A63B5FEDFAA87E99B95E6D4D287AADC0444 (void);
extern void AudioSource_GetSpectrumDataHelper_m64E105A054751BD5E7477C7E309992EC0BF274EB (void);
extern void AudioSource_get_volume_m9CCF33BC636562EA282FDE07463B547D70134EE3 (void);
extern void AudioSource_set_volume_mD902BBDBBDE0E3C148609BF3C05096148E90F2C0 (void);
extern void AudioSource_get_pitch_mB1B0B8A52400B5C798BF1E644FE1C2FFA20A9863 (void);
extern void AudioSource_set_pitch_mD14631FC99BF38AAFB356D9C45546BC16CF9E811 (void);
extern void AudioSource_get_time_m130D08644F36736115FE082DAA2ED5E2C9D97A93 (void);
extern void AudioSource_set_time_m6670372FD9C494978B7B3E01B7F4D220616F6204 (void);
extern void AudioSource_get_timeSamples_mF230FF8ABBD5A5250CBC487D0E0FCE286BA95B82 (void);
extern void AudioSource_set_timeSamples_mAC3793C13390C591E4995A88A2CE90E26BBDA6BE (void);
extern void AudioSource_get_clip_m4F5027066F9FC44B44192713142B0C277BB418FE (void);
extern void AudioSource_set_clip_mFF441895E274286C88D9C75ED5CA1B1B39528D70 (void);
extern void AudioSource_get_outputAudioMixerGroup_mE141F3A6337D84F9BD43196A28CC85D092695CAB (void);
extern void AudioSource_set_outputAudioMixerGroup_m10D0A0EAE270424CD2F3BB960CFAA158D9FC24CF (void);
extern void AudioSource_Play_m95DF07111C61D0E0F00257A00384D31531D590C3 (void);
extern void AudioSource_Play_mC9D19FA54347ED102AD9913E3E7528BE969199FB (void);
extern void AudioSource_PlayDelayed_m6A4992F1A010DC12906C6002B22F19082967770E (void);
extern void AudioSource_PlayScheduled_m9F3C7245A13A1D4BC64AFA9A08763357133727D9 (void);
extern void AudioSource_PlayOneShot_m098BCAE084AABB128BB19ED805D2D985E7B75112 (void);
extern void AudioSource_PlayOneShot_mF6FE95C58996B38EF6E7F7482F95F5E15E0AB30B (void);
extern void AudioSource_SetScheduledStartTime_m831CB1AC7E3C70BEFB84892B0A50BA161CE1EDDD (void);
extern void AudioSource_SetScheduledEndTime_mC9BF39919029A6C6CB8981B09A792D45A60A3730 (void);
extern void AudioSource_Stop_m318F17F17A147C77FF6E0A5A7A6BE057DB90F537 (void);
extern void AudioSource_Pause_m2C2A09359E8AA924FEADECC1AFEA519B3C915B26 (void);
extern void AudioSource_UnPause_mC4A6A1E71439A3ADB4664B62DABDF4D79D3B21B9 (void);
extern void AudioSource_get_isPlaying_mC203303F2F7146B2C056CB47B9391463FDF408FC (void);
extern void AudioSource_get_loop_m2D83BF58E1BD1BEE4CC80413C12E761D3310FC2C (void);
extern void AudioSource_set_loop_m834A590939D8456008C0F897FD80B0ECFFB7FE56 (void);
extern void AudioSource_get_ignoreListenerVolume_mC58B59373161017F770D42A36C536511805AE87C (void);
extern void AudioSource_set_ignoreListenerVolume_mAB973FFB2B666C4C6DE3BF34C930C28CC315731D (void);
extern void AudioSource_get_playOnAwake_mB07DE7C6BE0F5E6229FA160DA65BE8B8978BF9D1 (void);
extern void AudioSource_set_playOnAwake_m7EACC6ECEF12D7BA86A4E5A53603F1C8F9E11949 (void);
extern void AudioSource_get_ignoreListenerPause_m544337985D4025632846D4AB4EC1ADD0CF0B4B01 (void);
extern void AudioSource_set_ignoreListenerPause_m1BC14FA0984DEDF62E1CDBAB323950100A0BF2B4 (void);
extern void AudioSource_get_velocityUpdateMode_mEFF48403F8A591A14927408F806E0603391E153B (void);
extern void AudioSource_set_velocityUpdateMode_m379F5704F12211BFB9AF3E3DE6647A6B057C7426 (void);
extern void AudioSource_get_panStereo_mEB4CE5FF235A46C8B7CE62529A9DDA75A15C2505 (void);
extern void AudioSource_set_panStereo_mE3BA673B5F93F731114E8901355A63F07C8A54DF (void);
extern void AudioSource_get_spatialBlend_m06E7948B2813AA3EAE031BD4D1DE61A29416B1CE (void);
extern void AudioSource_set_spatialBlend_mCEE7A3E87A8C146E048B2CA3413FDC7BDB7BE001 (void);
extern void AudioSource_get_reverbZoneMix_mA1BE21696195BADD380311B236AA46314911B859 (void);
extern void AudioSource_set_reverbZoneMix_m0AD755F3841952B06F87767F97CA93E2C9545D1E (void);
extern void AudioSource_get_bypassEffects_m0172FACE00674F743A70870EB138B3223D42A35E (void);
extern void AudioSource_set_bypassEffects_m56E81C34448803D4B63105071D96AC644CFFEA9A (void);
extern void AudioSource_get_bypassListenerEffects_m47CE7EC60DB5D13E4D818CFA6D5E1B9D6134EF02 (void);
extern void AudioSource_set_bypassListenerEffects_m321403F18B6174D2E91D080DBF5090C29BC11899 (void);
extern void AudioSource_get_bypassReverbZones_mA640A5F9FF8E52777CF13950D966839729D1B3DF (void);
extern void AudioSource_set_bypassReverbZones_m900FD2BA30F36243B5A5B872B0D019CBAB6AC410 (void);
extern void AudioSource_get_dopplerLevel_m7BF6F31D1E8927E059BC87933AD9B81D63AF97BE (void);
extern void AudioSource_set_dopplerLevel_mB9AC5164E5AF16ACECA3B8E29F5C8573C37E40D6 (void);
extern void AudioSource_get_spread_mC21DF6C651AD67BEB5D721F0EA0B2F3B080F4C77 (void);
extern void AudioSource_set_spread_mDFBC1BF11837C26EF9763A8DEEFC56AF95F6E83F (void);
extern void AudioSource_get_priority_mD4B6D16F6BCB1D5ACA3F2CC096EDA8861DA66881 (void);
extern void AudioSource_set_priority_mD1AB7ED858D8A1233642F5DBA81AEFBE35DD4B40 (void);
extern void AudioSource_get_mute_mE23745FC15F1105556CB7590CA651628FC562DBD (void);
extern void AudioSource_set_mute_m6407E0AEE7F088AC69BD8C1D270C2B2049769B09 (void);
extern void AudioSource_get_minDistance_m459BE399BBBEA04CBBCF50CFB15A09CB3D7431F0 (void);
extern void AudioSource_set_minDistance_m6CBE3A60C03C0F179192FBDD62095B2E9D717690 (void);
extern void AudioSource_get_maxDistance_m8C31CB391B999C8D344EFF0AFB8E20488F7A5F7E (void);
extern void AudioSource_set_maxDistance_m4BF310D54761500A77A6C4841A0BBDBD09225813 (void);
extern void AudioSource_get_rolloffMode_m1D5F4CCF83174583ACF0C365051E58978ED02CFD (void);
extern void AudioSource_set_rolloffMode_m441D9552D8648D6040E66EE2C2650A79DC5E6FB4 (void);
extern void AudioSource_GetOutputData_m8AEF8365E3B162E379E1D5FA6C1607999DE458F3 (void);
extern void AudioSource_GetSpectrumData_m0F3872A4C6B41EFD5A23BA24322B08367BFF0CFE (void);
extern void AudioSource__ctor_mC67BD65374AC3CDFB702307F4A89932D803191C1 (void);
extern void AudioSampleProvider_InvokeSampleFramesAvailable_mEB16F7230AB65A3576BF053AC5719F8E134FBCD4 (void);
extern void AudioSampleProvider_InvokeSampleFramesOverflow_m66593173A527981F5EB2A5EF77B0C9119DAB5E15 (void);
extern void SampleFramesHandler__ctor_m7DDE0BAD439CD80791140C7D42D661B598A7663A (void);
extern void SampleFramesHandler_Invoke_m478D5645634B8C734E58B59CF7750797FC54F1BC (void);
extern void AudioClipPlayable_GetHandle_mEA1D664328FF9B08E4F7D5EBCD4B51A754D97C44 (void);
extern void AudioClipPlayable_Equals_m9C1C75ACBB74FE06AD02BE4643F6EB39413EFF83 (void);
extern void AudioMixer_SetFloat_m4789959013BE79E4F84F446405914908ADC3F335 (void);
extern void AudioMixer_GetFloat_mAED8D277AD30D0346292555CBF81D8961117AEC9 (void);
extern void AudioMixerGroup__ctor_m0D3A84EDAC9B01AEC0B07AFB1F5B1807F74B9CB8 (void);
extern void AudioMixerPlayable_GetHandle_m6C182D9794E901D123223BB57738A302BEAB41FD (void);
extern void AudioMixerPlayable_Equals_mDFB945EB48199A338BAD00D40FB8EEC34CF64D57 (void);
static Il2CppMethodPointer s_methodPointers[126] = 
{
	AudioSettings_get_dspTime_m246053C21870BC68BF7BBC5C5AC1B8F25337AEEA,
	AudioSettings_InvokeOnAudioConfigurationChanged_m8273D3AEB24F4C3E374238B6F699BE6696808E85,
	AudioSettings_InvokeOnAudioSystemShuttingDown_m1B9895D60B3267EBDEC69B9169730DBAD8325E90,
	AudioSettings_InvokeOnAudioSystemStartedUp_m7FE042936237E5BDCB20299D8C4CF583B661468C,
	AudioSettings_StartAudioOutput_mB04D851DD0E6115DEEFB55779F880146263C67BE,
	AudioSettings_StopAudioOutput_m3FE7A8EADAB2FB570BB05F7C353E25E15885D1CB,
	AudioConfigurationChangeHandler__ctor_mA9827AB9472EC8EE0A0F0FC24EBC06B4740DD944,
	AudioConfigurationChangeHandler_Invoke_m4DC27DD11512481B60071B20284E6886DAE54DE2,
	Mobile_get_muteState_m64C1E8C61537317A7F153E1A72F7D39D85DA684D,
	Mobile_set_muteState_m7C9A464BCA3762330E18CCAD79AF6C47B863CA02,
	Mobile_get_stopAudioOutputOnMute_m43EC82258D38C418353DFE19F32B51B64B18DCCA,
	Mobile_InvokeOnMuteStateChanged_mE5242862F948BA9FBB013A2B45F645B6A21E6198,
	Mobile_InvokeIsStopAudioOutputOnMuteEnabled_m854CB455C7BE7ADC06BABCB9AA24F60309AE7ED1,
	Mobile_StartAudioOutput_m731D1EEEE7A0D56BAADD571BA0FCAC13FB071223,
	Mobile_StopAudioOutput_m10B8CEF668EE4967D0AD1D6741B6A37540C28A46,
	AudioClip__ctor_m038DA97CB07076D1D9391E1E103F0F41D3622F89,
	AudioClip_GetData_mBDEFD7D7C8E5DEA3CCEE2D7DB406DBB0C244924E,
	AudioClip_SetData_mB49A9BC4639C62B9C8B22319D33D46AAD176BC3B,
	AudioClip_Construct_Internal_m88BC07CE3F412DDB62820F9430D1D52DA42A26F6,
	AudioClip_GetName_m561BBA037957E25D5BC5A962A1AA0C789895C9D1,
	AudioClip_CreateUserSound_m34DA102DD6848D555D4A9D45AFAA9D3E5574BC45,
	AudioClip_get_length_m6102CB29AF65988797452E4D6E43D4788303873D,
	AudioClip_get_samples_mDEA01CA75E7DEA0F8D480E4AF97FB96085BCF38E,
	AudioClip_get_channels_mFEECF5D6389D196BA5102EB79257298B9FDC9F84,
	AudioClip_get_frequency_m6647E10F4B2B1335187B0066E82468CCCF19647B,
	AudioClip_GetData_m1F6480FFDA2E354A7D8C8DE40F61AAB5AF6B4A1D,
	AudioClip_SetData_m7B473C614C11953D746770F4F89B44600B5A6AF3,
	AudioClip_Create_mF6B34084B76355CBC1991D8F4EAA878AA3A033A2,
	AudioClip_Create_m845EAF7C4DB9F316162F8441D225587CD043B9BF,
	AudioClip_Create_mE8111F06981E42666B6A9A59D0A3EBE002D2CDFB,
	AudioClip_Create_m988FEB04BC74440E65C3CF07414E4867AAE737F8,
	AudioClip_add_m_PCMReaderCallback_mA226EA143D90E04117A740FC9FA9F1111346CA83,
	AudioClip_remove_m_PCMReaderCallback_m3258A455005F4A94570B4F8FE28A5EDA91A88412,
	AudioClip_add_m_PCMSetPositionCallback_mB280AD93A847C65F536D846FECC7DCBE9266C37F,
	AudioClip_remove_m_PCMSetPositionCallback_m39598139640580138742F129E0510917DF2E233C,
	AudioClip_InvokePCMReaderCallback_Internal_m766E5705AB5AE16F5F142867CC3758ABE4BF462C,
	AudioClip_InvokePCMSetPositionCallback_Internal_m986EF703B7DDE42343730DE93A095D05B9F4DBB8,
	PCMReaderCallback__ctor_mF621B6CC1A4BA6525190C5037401CF2FD5C0CF28,
	PCMReaderCallback_Invoke_m76784C690C36B513E2AA5B0E4FD9831B2C7E5152,
	PCMSetPositionCallback__ctor_mD16F77DDB552EB69BB3F5EF39420B2F09F95455B,
	PCMSetPositionCallback_Invoke_m434D4F02FA25F91DF6199EC5A799C551C7F93702,
	AudioBehaviour__ctor_m6D88837496C42A746A51383F3D6F29CA72A9D309,
	AudioListener_get_volume_m8EAB8FBA127A53E689C1D8C1857781070381974A,
	AudioListener__ctor_m428A6CC2CFA95A7D6065D33098191569A7412EE4,
	AudioSource_GetPitch_m80F6D2BAF966F669253E9231AFCFFC303779913D,
	AudioSource_SetPitch_mE75DEDF8F37301BDA63E0F545A7A00850C24F53E,
	AudioSource_PlayHelper_m4DE8C48925C3548BED306DAB9F87939F24A46960,
	AudioSource_Play_m10DB5ACD1CC32EE433DBC10416B1450A30DE5F16,
	AudioSource_PlayOneShotHelper_mD110EAF42353687BD0B1190EEF30F0C65A4CF265,
	AudioSource_Stop_m8A4872F0A2680798CD28894DD28609445C4783F5,
	AudioSource_GetOutputDataHelper_m209E9A63B5FEDFAA87E99B95E6D4D287AADC0444,
	AudioSource_GetSpectrumDataHelper_m64E105A054751BD5E7477C7E309992EC0BF274EB,
	AudioSource_get_volume_m9CCF33BC636562EA282FDE07463B547D70134EE3,
	AudioSource_set_volume_mD902BBDBBDE0E3C148609BF3C05096148E90F2C0,
	AudioSource_get_pitch_mB1B0B8A52400B5C798BF1E644FE1C2FFA20A9863,
	AudioSource_set_pitch_mD14631FC99BF38AAFB356D9C45546BC16CF9E811,
	AudioSource_get_time_m130D08644F36736115FE082DAA2ED5E2C9D97A93,
	AudioSource_set_time_m6670372FD9C494978B7B3E01B7F4D220616F6204,
	AudioSource_get_timeSamples_mF230FF8ABBD5A5250CBC487D0E0FCE286BA95B82,
	AudioSource_set_timeSamples_mAC3793C13390C591E4995A88A2CE90E26BBDA6BE,
	AudioSource_get_clip_m4F5027066F9FC44B44192713142B0C277BB418FE,
	AudioSource_set_clip_mFF441895E274286C88D9C75ED5CA1B1B39528D70,
	AudioSource_get_outputAudioMixerGroup_mE141F3A6337D84F9BD43196A28CC85D092695CAB,
	AudioSource_set_outputAudioMixerGroup_m10D0A0EAE270424CD2F3BB960CFAA158D9FC24CF,
	AudioSource_Play_m95DF07111C61D0E0F00257A00384D31531D590C3,
	AudioSource_Play_mC9D19FA54347ED102AD9913E3E7528BE969199FB,
	AudioSource_PlayDelayed_m6A4992F1A010DC12906C6002B22F19082967770E,
	AudioSource_PlayScheduled_m9F3C7245A13A1D4BC64AFA9A08763357133727D9,
	AudioSource_PlayOneShot_m098BCAE084AABB128BB19ED805D2D985E7B75112,
	AudioSource_PlayOneShot_mF6FE95C58996B38EF6E7F7482F95F5E15E0AB30B,
	AudioSource_SetScheduledStartTime_m831CB1AC7E3C70BEFB84892B0A50BA161CE1EDDD,
	AudioSource_SetScheduledEndTime_mC9BF39919029A6C6CB8981B09A792D45A60A3730,
	AudioSource_Stop_m318F17F17A147C77FF6E0A5A7A6BE057DB90F537,
	AudioSource_Pause_m2C2A09359E8AA924FEADECC1AFEA519B3C915B26,
	AudioSource_UnPause_mC4A6A1E71439A3ADB4664B62DABDF4D79D3B21B9,
	AudioSource_get_isPlaying_mC203303F2F7146B2C056CB47B9391463FDF408FC,
	AudioSource_get_loop_m2D83BF58E1BD1BEE4CC80413C12E761D3310FC2C,
	AudioSource_set_loop_m834A590939D8456008C0F897FD80B0ECFFB7FE56,
	AudioSource_get_ignoreListenerVolume_mC58B59373161017F770D42A36C536511805AE87C,
	AudioSource_set_ignoreListenerVolume_mAB973FFB2B666C4C6DE3BF34C930C28CC315731D,
	AudioSource_get_playOnAwake_mB07DE7C6BE0F5E6229FA160DA65BE8B8978BF9D1,
	AudioSource_set_playOnAwake_m7EACC6ECEF12D7BA86A4E5A53603F1C8F9E11949,
	AudioSource_get_ignoreListenerPause_m544337985D4025632846D4AB4EC1ADD0CF0B4B01,
	AudioSource_set_ignoreListenerPause_m1BC14FA0984DEDF62E1CDBAB323950100A0BF2B4,
	AudioSource_get_velocityUpdateMode_mEFF48403F8A591A14927408F806E0603391E153B,
	AudioSource_set_velocityUpdateMode_m379F5704F12211BFB9AF3E3DE6647A6B057C7426,
	AudioSource_get_panStereo_mEB4CE5FF235A46C8B7CE62529A9DDA75A15C2505,
	AudioSource_set_panStereo_mE3BA673B5F93F731114E8901355A63F07C8A54DF,
	AudioSource_get_spatialBlend_m06E7948B2813AA3EAE031BD4D1DE61A29416B1CE,
	AudioSource_set_spatialBlend_mCEE7A3E87A8C146E048B2CA3413FDC7BDB7BE001,
	AudioSource_get_reverbZoneMix_mA1BE21696195BADD380311B236AA46314911B859,
	AudioSource_set_reverbZoneMix_m0AD755F3841952B06F87767F97CA93E2C9545D1E,
	AudioSource_get_bypassEffects_m0172FACE00674F743A70870EB138B3223D42A35E,
	AudioSource_set_bypassEffects_m56E81C34448803D4B63105071D96AC644CFFEA9A,
	AudioSource_get_bypassListenerEffects_m47CE7EC60DB5D13E4D818CFA6D5E1B9D6134EF02,
	AudioSource_set_bypassListenerEffects_m321403F18B6174D2E91D080DBF5090C29BC11899,
	AudioSource_get_bypassReverbZones_mA640A5F9FF8E52777CF13950D966839729D1B3DF,
	AudioSource_set_bypassReverbZones_m900FD2BA30F36243B5A5B872B0D019CBAB6AC410,
	AudioSource_get_dopplerLevel_m7BF6F31D1E8927E059BC87933AD9B81D63AF97BE,
	AudioSource_set_dopplerLevel_mB9AC5164E5AF16ACECA3B8E29F5C8573C37E40D6,
	AudioSource_get_spread_mC21DF6C651AD67BEB5D721F0EA0B2F3B080F4C77,
	AudioSource_set_spread_mDFBC1BF11837C26EF9763A8DEEFC56AF95F6E83F,
	AudioSource_get_priority_mD4B6D16F6BCB1D5ACA3F2CC096EDA8861DA66881,
	AudioSource_set_priority_mD1AB7ED858D8A1233642F5DBA81AEFBE35DD4B40,
	AudioSource_get_mute_mE23745FC15F1105556CB7590CA651628FC562DBD,
	AudioSource_set_mute_m6407E0AEE7F088AC69BD8C1D270C2B2049769B09,
	AudioSource_get_minDistance_m459BE399BBBEA04CBBCF50CFB15A09CB3D7431F0,
	AudioSource_set_minDistance_m6CBE3A60C03C0F179192FBDD62095B2E9D717690,
	AudioSource_get_maxDistance_m8C31CB391B999C8D344EFF0AFB8E20488F7A5F7E,
	AudioSource_set_maxDistance_m4BF310D54761500A77A6C4841A0BBDBD09225813,
	AudioSource_get_rolloffMode_m1D5F4CCF83174583ACF0C365051E58978ED02CFD,
	AudioSource_set_rolloffMode_m441D9552D8648D6040E66EE2C2650A79DC5E6FB4,
	AudioSource_GetOutputData_m8AEF8365E3B162E379E1D5FA6C1607999DE458F3,
	AudioSource_GetSpectrumData_m0F3872A4C6B41EFD5A23BA24322B08367BFF0CFE,
	AudioSource__ctor_mC67BD65374AC3CDFB702307F4A89932D803191C1,
	AudioSampleProvider_InvokeSampleFramesAvailable_mEB16F7230AB65A3576BF053AC5719F8E134FBCD4,
	AudioSampleProvider_InvokeSampleFramesOverflow_m66593173A527981F5EB2A5EF77B0C9119DAB5E15,
	SampleFramesHandler__ctor_m7DDE0BAD439CD80791140C7D42D661B598A7663A,
	SampleFramesHandler_Invoke_m478D5645634B8C734E58B59CF7750797FC54F1BC,
	AudioClipPlayable_GetHandle_mEA1D664328FF9B08E4F7D5EBCD4B51A754D97C44,
	AudioClipPlayable_Equals_m9C1C75ACBB74FE06AD02BE4643F6EB39413EFF83,
	AudioMixer_SetFloat_m4789959013BE79E4F84F446405914908ADC3F335,
	AudioMixer_GetFloat_mAED8D277AD30D0346292555CBF81D8961117AEC9,
	AudioMixerGroup__ctor_m0D3A84EDAC9B01AEC0B07AFB1F5B1807F74B9CB8,
	AudioMixerPlayable_GetHandle_m6C182D9794E901D123223BB57738A302BEAB41FD,
	AudioMixerPlayable_Equals_mDFB945EB48199A338BAD00D40FB8EEC34CF64D57,
};
extern void AudioClipPlayable_GetHandle_mEA1D664328FF9B08E4F7D5EBCD4B51A754D97C44_AdjustorThunk (void);
extern void AudioClipPlayable_Equals_m9C1C75ACBB74FE06AD02BE4643F6EB39413EFF83_AdjustorThunk (void);
extern void AudioMixerPlayable_GetHandle_m6C182D9794E901D123223BB57738A302BEAB41FD_AdjustorThunk (void);
extern void AudioMixerPlayable_Equals_mDFB945EB48199A338BAD00D40FB8EEC34CF64D57_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[4] = 
{
	{ 0x06000078, AudioClipPlayable_GetHandle_mEA1D664328FF9B08E4F7D5EBCD4B51A754D97C44_AdjustorThunk },
	{ 0x06000079, AudioClipPlayable_Equals_m9C1C75ACBB74FE06AD02BE4643F6EB39413EFF83_AdjustorThunk },
	{ 0x0600007D, AudioMixerPlayable_GetHandle_m6C182D9794E901D123223BB57738A302BEAB41FD_AdjustorThunk },
	{ 0x0600007E, AudioMixerPlayable_Equals_mDFB945EB48199A338BAD00D40FB8EEC34CF64D57_AdjustorThunk },
};
static const int32_t s_InvokerIndices[126] = 
{
	9730,
	9616,
	9780,
	9780,
	9722,
	9722,
	3136,
	5253,
	9722,
	9616,
	9722,
	9616,
	9722,
	9780,
	9780,
	6521,
	7354,
	7354,
	9745,
	6398,
	632,
	6444,
	6366,
	6366,
	6366,
	2052,
	2052,
	6850,
	6689,
	7129,
	6742,
	5349,
	5349,
	5349,
	5349,
	5349,
	5320,
	3136,
	5349,
	3136,
	5320,
	6521,
	9765,
	6521,
	9447,
	8994,
	8998,
	5283,
	8329,
	5253,
	8326,
	7780,
	6444,
	5389,
	6444,
	5389,
	6444,
	5389,
	6366,
	5320,
	6398,
	5349,
	6398,
	5349,
	6521,
	5449,
	5389,
	5283,
	5349,
	3145,
	5283,
	5283,
	6521,
	6521,
	6521,
	6301,
	6301,
	5253,
	6301,
	5253,
	6301,
	5253,
	6301,
	5253,
	6366,
	5320,
	6444,
	5389,
	6444,
	5389,
	6444,
	5389,
	6301,
	5253,
	6301,
	5253,
	6301,
	5253,
	6444,
	5389,
	6444,
	5389,
	6366,
	5320,
	6301,
	5253,
	6444,
	5389,
	6444,
	5389,
	6366,
	5320,
	3134,
	1785,
	6521,
	5320,
	5320,
	3136,
	3151,
	6406,
	3821,
	2056,
	2047,
	6521,
	6406,
	3822,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_AudioModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_AudioModule_CodeGenModule = 
{
	"UnityEngine.AudioModule.dll",
	126,
	s_methodPointers,
	4,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
