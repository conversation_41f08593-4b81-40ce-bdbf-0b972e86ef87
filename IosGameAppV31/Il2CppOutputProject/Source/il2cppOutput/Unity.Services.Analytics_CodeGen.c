﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Ua2CoreInitializeCallback_Register_mF40ACDE0AA72C4F92C34C67044F57796F0EAE2B1 (void);
extern void Ua2CoreInitializeCallback_Initialize_mE8CA1840C20490FD250B30B6789619403DDE5748 (void);
extern void Ua2CoreInitializeCallback__ctor_m096A57E67DE0D5E40E7AB84CF3935A5D7DD898CE (void);
extern void U3CInitializeU3Ed__1_MoveNext_m6D1294910570A31C8D7E15AB3BE8B001184D85D5 (void);
extern void U3CInitializeU3Ed__1_SetStateMachine_mA709A8AC5E4AB1755D3691BAF4474C4148808268 (void);
extern void AnalyticsService_Initialize_mA5100DE9B72B38ADC2AA315347817AE1976B271B (void);
extern void AnalyticsService_TearDown_mE0CB24E46191EA7C31F67C648C7F623EA2686F37 (void);
extern void AnalyticsServiceSystemCalls_get_UtcNow_m2E57E69EE63CCEA016E8AB2AE4691CE106C59D7C (void);
extern void AnalyticsServiceSystemCalls__ctor_m0CE98AA41596DAEC3FE38B2657205368207C45D0 (void);
extern void AnalyticsServiceInstance__ctor_mBED8F343B403DC08C0A3BEF792EA3DE19B7BB50C (void);
extern void AnalyticsServiceInstance_ResumeDataDeletionIfNecessary_mA71BC424BAB3BDCA709948E63934C16562EA5AE6 (void);
extern void AnalyticsServiceInstance_DeactivateWithDataDeletionRequest_m109EDCF2D98B8AE461BAB4B72859E30E380FC63B (void);
extern void AnalyticsServiceInstance_DataDeletionCompleted_m4C4D2F1B9C08AAAD4FCD5DE8F23CD9FFF9CB7C6A (void);
extern void AnalyticsServiceInstance_Deactivate_m780C639B6454A3D839452F787FBD904B6D055FEE (void);
extern void AnalyticsServiceInstance_RecordStartupEvents_mE0613A92C86B3210BCD6FF01E49F48AD8BC4D06F (void);
extern void AnalyticsServiceInstance_PlayerChanged_mF9B73C330390169E9C4557F1713A064092BB1796 (void);
extern void AnalyticsServiceInstance_ApplicationPaused_mB64F8B2FF25B824FA074668EF917FC684828893C (void);
extern void AnalyticsServiceInstance_get_AutoflushPeriodMultiplier_m306D10F0B29C2FBE941D7607D61C7AE5702AC3AE (void);
extern void AnalyticsServiceInstance_Flush_mB8F0ED919FB47026C5CB61DD3562FAA09009C21E (void);
extern void AnalyticsServiceInstance_ApplicationQuit_mC6358CE4734C9A39DA8A1AAE1F529EB5D5A17E03 (void);
extern void AnalyticsServiceInstance_RecordGameRunningIfNecessary_mDC174CE67E36436A206DDC321308853A727484C3 (void);
extern void TransactionCurrencyConverter__ctor_m5ACA84E75A13698EAF50E70DF0FA9FA1EC7DBFFD (void);
extern void AnalyticsContainer_get_AutoFlushPeriod_mD89D49225FE7E3A9BAA505AAE9642EE42A9FE49A (void);
extern void AnalyticsContainer_get_ContainerDebug_m4BFBFAECE592756A6C456B034D13D85A9FEC8A09 (void);
extern void AnalyticsContainer_get_TimeUntilNextHeartbeat_mEE5DCA6F227C8611F9D714E07A76619C0B279574 (void);
extern void AnalyticsContainer_CreateContainer_m5217CD009ABF07FE51AC88C20F4B61B5A5BFF201 (void);
extern void AnalyticsContainer_Initialize_m57C0D3C612C0DD90A98563A788EC6BE852555111 (void);
extern void AnalyticsContainer_Enable_m7B1F481F487CB453A944765C5CF96E8AFE416219 (void);
extern void AnalyticsContainer_Disable_m9373F843C8BB6828BF3D709D6366CD1151A4B243 (void);
extern void AnalyticsContainer_Update_m077BEBF391B2FFFBA1892069B942D1C31C3038CD (void);
extern void AnalyticsContainer_OnApplicationPause_m0D7562975A5F9A1B1DD292A6EC115CB0E345FA8B (void);
extern void AnalyticsContainer_CleanUp_m060E72988945D2D3AD719F4D16A2056A3CFC72F9 (void);
extern void AnalyticsContainer__ctor_m181248237969AF6A721DE089E172681F92EE9A04 (void);
extern void CoreStatsHelper_SetCoreStatsConsent_mF5D6EDE2380AF3DA289C217CD505940D76D9877E (void);
extern void CoreStatsHelper__ctor_mD543DD28C5F258FC9B9BDD30D0192F8A7ECF9DF4 (void);
extern void SdkVersion__cctor_mC098F58A265DB7E8ED89F1FCE4A5FD67906B0FD7 (void);
extern void DeviceDataWrapper_get_CpuType_mE43F075EA29C5DC78CFA256195D17E67F259BA8D (void);
extern void DeviceDataWrapper_get_GpuType_m32723F25A7E892E79DBB611F12C3245E2658EA62 (void);
extern void DeviceDataWrapper_get_CpuCores_m4917E0C3B322804A3E526C36B3BAC06BB3647C65 (void);
extern void DeviceDataWrapper_get_RamTotal_mC0426CEA851C9CF2E396E345A8474CA8774C17D5 (void);
extern void DeviceDataWrapper_get_ScreenWidth_m3F3FFFBC0D7DE523833A6CD2E328D0AC8A68661B (void);
extern void DeviceDataWrapper_get_ScreenHeight_m601A469F716562134B01BCC3C23C203D4F93BFD0 (void);
extern void DeviceDataWrapper_get_ScreenDpi_mF4CE184F736D8BFF5B7EFD195DABD96EFA5A08BB (void);
extern void DeviceDataWrapper_get_OperatingSystem_mF4674AFA3A9C6C66874D9D70DE2299DA8B28A4E0 (void);
extern void DeviceDataWrapper_get_DeviceModel_mFDCB43AE54C28DD75F57C9D49AEB685CAC1C2045 (void);
extern void DeviceDataWrapper_get_IsDebugDevice_m4BB8AC3BE94072BE56C8B52B90C30730B07A27FE (void);
extern void DeviceDataWrapper_get_IsTiny_mA43AC24044F179ACA90F866A9DDBCE12F34C8F95 (void);
extern void DeviceDataWrapper__ctor_m5C68E180913910F8888E3EAA94581A61C5794E98 (void);
extern void CommonDataWrapper_get_Version_m50964BA00C346AB94B07F7D2CF797B659260E756 (void);
extern void CommonDataWrapper_get_GameBundleId_mAB1B5AE6A07CDEE96F6A30C11F7B21414EB0AEFA (void);
extern void CommonDataWrapper_get_ProjectId_mF2901D4DA200D1D8C1880A88727381D4056A281E (void);
extern void CommonDataWrapper_get_Platform_m8CD4BAB14E0C92736833460BC416E37D52488CDD (void);
extern void CommonDataWrapper_get_BuildGUID_m57FD5CAC4592C77795F9ADCE135FCA3479BBA36C (void);
extern void CommonDataWrapper_get_Idfv_m8E164D8A168B2B94D2EC98F5934CD0F248F73BA4 (void);
extern void CommonDataWrapper_get_GameStoreId_m0477A87CFE1D1D66044722D964CB0DBA246237EF (void);
extern void CommonDataWrapper_get_HasVolume_m7F4DB6FAD82976A30A30492AFD52DC9C12C8A1D5 (void);
extern void CommonDataWrapper_get_Volume_mF9E0EA4D6E45DB06F666AEB0828AECF38495838D (void);
extern void CommonDataWrapper_get_BatteryLevel_m04984DE8D1C0115ACBCC870F4A2F3E230BE5A20B (void);
extern void CommonDataWrapper_get_AnalyticsRegionLanguageCode_m0DB7E313B4B16258515766CAEBE3907E137EC5A7 (void);
extern void CommonDataWrapper__ctor_m6D6EAC94DF0508F181A0B41C32186D828DCA0272 (void);
extern void CommonDataWrapper_GetPlatform_mE598C5A8D6F6D0D0F9479868123DA79D45F2CD7D (void);
extern void DataGenerator__ctor_mA4F895805C1ED670712EBA41726AD2CC7B0C590A (void);
extern void DataGenerator_SdkStartup_mF4A7453FA11A5B335893005A2EC59B2E5F32376F (void);
extern void DataGenerator_GameRunning_m41DD1BD22000DFEA3968261BCE98B2161CB8C23D (void);
extern void DataGenerator_NewPlayer_mC1F0DC5C2FFC8770E15364784A26FD1BF6FE89EE (void);
extern void DataGenerator_GameStarted_m99451326DF5663F2CD0B7E268D38AF9DEF57B751 (void);
extern void DataGenerator_GameEnded_m7B356DF303F2B60863E7A942E847C91F68595DE9 (void);
extern void DataGenerator_ClientDevice_m245A8C14DF629388644F93AF7BD0C17E42794EB0 (void);
extern void DataGenerator_PushCommonParams_mBCA2A9870721102F62A8CB5C31BB1E0F84D248B7 (void);
extern void DeviceVolumeProvider_get_VolumeAvailable_m5CE710060BD8C7D300DD19CC799F82043F624CC4 (void);
extern void DeviceVolumeProvider_unity_services_analytics_get_device_volume_m3424DF6DB6A7CDBF086E0C65BA066DC6F875B7ED (void);
extern void DeviceVolumeProvider_GetDeviceVolume_m795C795430B63C4D9F1084A837E784B2E5337785 (void);
extern void AnalyticsUserIdServiceComponent__ctor_mC4D9557B2E49D4883E699F5F8034FE65740D334E (void);
extern void IdentityManager_get_UserId_mFC6AEF665FCEAC521E5C0A81B8B2C72DC67A8BE9 (void);
extern void IdentityManager_set_UserId_m621B67FC21828CC54314A59211794D3C75A78648 (void);
extern void IdentityManager_get_InstallId_mE1E051AD42ECD4BFC3ED3CAC52F8A57D3426051C (void);
extern void IdentityManager_set_InstallId_m45872CF0FBFBDD61FCCCD692C08BEF495476825B (void);
extern void IdentityManager_get_PlayerId_mB0158C91286F4CA875CE8E4B354CB6F8116CA79B (void);
extern void IdentityManager_get_ExternalId_mB5262A8A86B5357ED0736599120AE1D4DB17BBA4 (void);
extern void IdentityManager_set_ExternalId_m3BCAD76B8AEBAEE9F11C1548AD2ABD8371D441BB (void);
extern void IdentityManager_get_IsNewPlayer_m3C510AA1401E87F5BBF4951FE0B3B7A99DC48E2F (void);
extern void IdentityManager_set_IsNewPlayer_m30EA47E2185F182813E96DB2351BA7C421A555F1 (void);
extern void IdentityManager_add_OnPlayerChanged_m40145236F4A83F999CB68B5E4234293F3C53D113 (void);
extern void IdentityManager_remove_OnPlayerChanged_mFF14F291BD2F0C18393E1351E630F6206E06B04A (void);
extern void IdentityManager__ctor_m14AF44105B7B9F823AFBAD375F6FF919F0FDC0C6 (void);
extern void IdentityManager_ExternalUserIdChanged_m81AF6E8346EA442C32E6024D78633488015127B2 (void);
extern void Locale_unity_services_current_language_code_mD2D021F537C8BA5DF7003057B8BE0A83F311875A (void);
extern void Locale_CurrentLanguageCode_m720A4A975DCDE3417D54E73B51EA379DFC5B181E (void);
extern void Locale_AnalyticsRegionLanguageCode_mBC2C7A14D9ED824729BF977BE5C1478B559BB45B (void);
extern void AnalyticsForgetter_get_DeletionInProgress_mA2E85FC9A479A7CC90486397F7FC4098BA01EA98 (void);
extern void AnalyticsForgetter__ctor_mB7EFEF4ADE978A9CAD976324BCC6BE797558DB0E (void);
extern void AnalyticsForgetter_SetForgettingStatus_m92EBCCB1DCCE80F80D6C0051B95C638A521B0111 (void);
extern void AnalyticsForgetter_AttemptToForget_mC21B8E3EEBBD5F0B16A087EE44DA79C1467CA687 (void);
extern void AnalyticsForgetter_UploadComplete_mA94023B0D4A918B179EBDFA97BFBD2F9221EE29D (void);
extern void BufferSystemCalls_GenerateGuid_m61F3E2FF777BFA46961A04C2ADED85F198272DED (void);
extern void BufferSystemCalls_Now_m94184933D6A380AEEB970C38843D2E232D761DB0 (void);
extern void BufferSystemCalls_GetTimeZoneUtcOffset_mB21231260DC5D4BD41277C0779F4476A0A52BF20 (void);
extern void BufferSystemCalls__ctor_m11A81CCB33E4BA6C679E191AB344CC3277C57B8C (void);
extern void BufferX_get_Length_mAC86149425B8A65EBF678D67A191EBCD975BCC6C (void);
extern void BufferX_add_EventRecorded_m3B09C9F1F1CE39981A3FDD45EC6D07605EE761E2 (void);
extern void BufferX_remove_EventRecorded_m345E7079CD2F26FE9CDAA3F4D5E01405FC902529 (void);
extern void BufferX_add_EventsClearing_mB239E52FC724E4ABABFFD9B278CC939AC00A7CFA (void);
extern void BufferX_remove_EventsClearing_mDF25BF92DBE8C0188C920F23F85D4505EF6BD9C5 (void);
extern void BufferX__ctor_mF1EB5A3CD82F9CAB1640CCDDAA93BC03E18C79B7 (void);
extern void BufferX_WriteString_m6D00B4EC23DF37876EA6155C243345D9ABD997A6 (void);
extern void BufferX_WriteLong_m240FC685730686F76FE73A5DD067ABAE48997AD8 (void);
extern void BufferX_WriteByte_m12C49B76B743F83835010F50B8BDD99680AD6B14 (void);
extern void BufferX_WriteBytes_mAB875F9E3ABB56B16A350123FA3983F967C3BA79 (void);
extern void BufferX_WriteName_m8833F39525C5ECD5DBBF33168F5FEFC630EA2A7D (void);
extern void BufferX_WriteDateTime_mA5F6AB9647B6BCCAC8D757678B4924C79C828F80 (void);
extern void BufferX_SerializeLong_m4BE2088BE0D483081E97E1587B509837DBF4225D (void);
extern void BufferX_PushStandardEventStart_mCDD3882CAAD89B53A7297161050D60E42002C48E (void);
extern void BufferX_PushCommonEventStart_mF4C139E81580C2618D5E9FEB412115B77CF47B45 (void);
extern void BufferX_StripTrailingCommaIfNecessary_m835DF3D14DFBEB8486A8044A72C15F64C9CEA937 (void);
extern void BufferX_PushEndEvent_m06F34FD5EB0FF8E4743902FBEDD52D2013A6DE23 (void);
extern void BufferX_PushDouble_mBD733AFC8B05CF1442C1B7D62830D24863E3DF00 (void);
extern void BufferX_PushString_m2786331DB2F4AA7F21C9FA1B6C6599E3233DE393 (void);
extern void BufferX_ProcessCharacterOntoWorkingBuffer_mA488EA7413B881E935B3EDD43644D3B9AC62E49D (void);
extern void BufferX_PushInt64_m9980DFAD12A1BB6456B8E5FC33E28646A3DD2EA2 (void);
extern void BufferX_PushBool_m929464C0BC79BA4A2977167EFDE6235031CB273F (void);
extern void BufferX_Serialize_m4CA698F0ECE4218D3227187027C1A754651B85D9 (void);
extern void BufferX_ClearBuffer_mA8B0BBF389EA44EFE0CA85B2EF94A3B51153FEE8 (void);
extern void BufferX_ClearBuffer_m33260625C009242974AAD0747DDF14D2EF1BB726 (void);
extern void BufferX_FlushToDisk_m55BF963B573FD854740103B13825AACBD73C78E9 (void);
extern void BufferX_ClearDiskCache_mD54CC30E7AD92D94FC57DF95A3E4A4A1BEAEB1C6 (void);
extern void BufferX_SerializeDateTime_m1B8DB0257F014D2E517B2EB94CC80B5A879DB654 (void);
extern void FileSystemCalls__ctor_mED48303E0E5ADE100ED297D2F82072B5162F3E2A (void);
extern void FileSystemCalls_CanAccessFileSystem_m664852625AD86DA0EAAADEF43EB28D979294738F (void);
extern void FileSystemCalls_FileExists_m73217509E8CEDB625FAFD2AA3E008153D7F84513 (void);
extern void FileSystemCalls_DeleteFile_mC18864152DCA840D1FDD2DBD6287F4DADE4A59CC (void);
extern void FileSystemCalls_OpenFileForWriting_m5D2064955D6E16BA0EA7FC046A30096A06799A0D (void);
extern void DiskCache__ctor_mBA8B9A9ECF83506913BC62A4782F490C2D300263 (void);
extern void DiskCache_Write_m402EA80E6B31916CD36DA3CF1C0760FC81309EE1 (void);
extern void DiskCache_Clear_m3C6A5E5104B8CF7998D637099B86428D08E6003D (void);
extern void Dispatcher_get_ConsecutiveFailedUploadCount_m58D2668BE9E8194349A99CA67BB8583F09DE7C07 (void);
extern void Dispatcher_set_ConsecutiveFailedUploadCount_m9EECD2DEB2695DBCF973D907662ED7D6140C9469 (void);
extern void Dispatcher_get_FlushInProgress_mCC5DAEE29D9E97D753FE7B2C876FC7B9E3F50E28 (void);
extern void Dispatcher_set_FlushInProgress_mE7F87C33DBBF7012A706047DC2336D3B6EF9391E (void);
extern void Dispatcher_add_FlushStarted_m6F5B525BBC812A1DADFB4716004BC00B90FEEA7F (void);
extern void Dispatcher_remove_FlushStarted_mE292CA4A7809F3872255D8D77D2174DB9FEF4D74 (void);
extern void Dispatcher_add_FlushFinished_m736538D18D44C4D33FDA7471DB050A62F84BB5BA (void);
extern void Dispatcher_remove_FlushFinished_mB71D4DB6539E2623B0F76B4146E193A1CCAAF94A (void);
extern void Dispatcher__ctor_mDBB513D6B226DB1B13C536AE8FF0A531340D7208 (void);
extern void Dispatcher_SetBuffer_m37334C4FDDB8C2DBA98A12432E896F1D3B45BE29 (void);
extern void Dispatcher_Flush_m6167D29547BACF3E42364A0E384EDA58FA8D5247 (void);
extern void Dispatcher_FlushBufferToService_m55AC50B02B30BD4ED1E554D794AA5DEE9FB77C39 (void);
extern void Dispatcher_UploadCompleted_m1A96D525DB4DE2A378D239C3D11DA427E36B2459 (void);
extern void PlayerPrefsPersistence_SaveValue_mCBFD5D1492C195081BD0D2EC4283AD260938C900 (void);
extern void PlayerPrefsPersistence_SaveValue_m51EB598C6CCE5E5FB68717F312AFBE99F83BC8DC (void);
extern void PlayerPrefsPersistence_LoadInt_m0D150E3F74F72D95C70405BB9BA9E83BC13A9EEF (void);
extern void PlayerPrefsPersistence__ctor_m3F310A297230AB1ED4FD0884D4AAC45082E3DD77 (void);
extern void AnalyticsWebRequest__ctor_m8CA36BAF33C32134D350FC88E7BF795AFFDFD77D (void);
extern void AnalyticsWebRequest_get_IsNetworkError_m36FEA587B02163F41F9FD2E20CBD2E2D71144BC2 (void);
extern void AnalyticsWebRequest_Unity_Services_Analytics_Internal_IWebRequest_SendWebRequest_m415458FF981E6B3BC598B67AD580DA8443D35502 (void);
extern void AnalyticsWebRequest_Unity_Services_Analytics_Internal_IWebRequest_SetRequestHeader_m150D2DC1E3957BC6F3C6052A03693C70F095B3BC (void);
extern void WebRequestHelper_CreateWebRequest_m06C697E4C704BADC84F815F3F84A4144708DDFFA (void);
extern void WebRequestHelper_SendWebRequest_mF1D98192E9EBDB286BC094900284220080FF9F5D (void);
extern void WebRequestHelper__ctor_m5F76E493A0C15311928DCCCCE293752DF3DA0CC9 (void);
extern void U3CU3Ec__DisplayClass2_0__ctor_m0F6A2DDF4EF21C3B648EA299191EB2D006DB68F9 (void);
extern void U3CU3Ec__DisplayClass2_0_U3CSendWebRequestU3Eb__0_mAE3230661FB4148BD8285721D5E48F49441B37D6 (void);
extern void SessionManager_get_SessionId_mB333BEB743A8CB848E471D3DAE8D94799F2B3C2D (void);
extern void SessionManager_set_SessionId_mB86AF25C3E32F173BA3D42EFC43B5EDEB88A5D66 (void);
extern void SessionManager__ctor_m21E89203802ECBDAB0DCD76F1352FF63A84C4ACC (void);
extern void SessionManager_StartNewSession_mE4556A817A8E5391B554A7E6953CFE3E12A467F3 (void);
extern void StandardEventServiceComponent__ctor_mED8BAC8D2E1D561A6DCB4A1F35D4E7A60553CB87 (void);
static Il2CppMethodPointer s_methodPointers[247] = 
{
	Ua2CoreInitializeCallback_Register_mF40ACDE0AA72C4F92C34C67044F57796F0EAE2B1,
	Ua2CoreInitializeCallback_Initialize_mE8CA1840C20490FD250B30B6789619403DDE5748,
	Ua2CoreInitializeCallback__ctor_m096A57E67DE0D5E40E7AB84CF3935A5D7DD898CE,
	U3CInitializeU3Ed__1_MoveNext_m6D1294910570A31C8D7E15AB3BE8B001184D85D5,
	U3CInitializeU3Ed__1_SetStateMachine_mA709A8AC5E4AB1755D3691BAF4474C4148808268,
	AnalyticsService_Initialize_mA5100DE9B72B38ADC2AA315347817AE1976B271B,
	AnalyticsService_TearDown_mE0CB24E46191EA7C31F67C648C7F623EA2686F37,
	NULL,
	AnalyticsServiceSystemCalls_get_UtcNow_m2E57E69EE63CCEA016E8AB2AE4691CE106C59D7C,
	AnalyticsServiceSystemCalls__ctor_m0CE98AA41596DAEC3FE38B2657205368207C45D0,
	AnalyticsServiceInstance__ctor_mBED8F343B403DC08C0A3BEF792EA3DE19B7BB50C,
	AnalyticsServiceInstance_ResumeDataDeletionIfNecessary_mA71BC424BAB3BDCA709948E63934C16562EA5AE6,
	AnalyticsServiceInstance_DeactivateWithDataDeletionRequest_m109EDCF2D98B8AE461BAB4B72859E30E380FC63B,
	AnalyticsServiceInstance_DataDeletionCompleted_m4C4D2F1B9C08AAAD4FCD5DE8F23CD9FFF9CB7C6A,
	AnalyticsServiceInstance_Deactivate_m780C639B6454A3D839452F787FBD904B6D055FEE,
	AnalyticsServiceInstance_RecordStartupEvents_mE0613A92C86B3210BCD6FF01E49F48AD8BC4D06F,
	AnalyticsServiceInstance_PlayerChanged_mF9B73C330390169E9C4557F1713A064092BB1796,
	AnalyticsServiceInstance_ApplicationPaused_mB64F8B2FF25B824FA074668EF917FC684828893C,
	AnalyticsServiceInstance_get_AutoflushPeriodMultiplier_m306D10F0B29C2FBE941D7607D61C7AE5702AC3AE,
	AnalyticsServiceInstance_Flush_mB8F0ED919FB47026C5CB61DD3562FAA09009C21E,
	AnalyticsServiceInstance_ApplicationQuit_mC6358CE4734C9A39DA8A1AAE1F529EB5D5A17E03,
	AnalyticsServiceInstance_RecordGameRunningIfNecessary_mDC174CE67E36436A206DDC321308853A727484C3,
	TransactionCurrencyConverter__ctor_m5ACA84E75A13698EAF50E70DF0FA9FA1EC7DBFFD,
	NULL,
	NULL,
	AnalyticsContainer_get_AutoFlushPeriod_mD89D49225FE7E3A9BAA505AAE9642EE42A9FE49A,
	AnalyticsContainer_get_ContainerDebug_m4BFBFAECE592756A6C456B034D13D85A9FEC8A09,
	AnalyticsContainer_get_TimeUntilNextHeartbeat_mEE5DCA6F227C8611F9D714E07A76619C0B279574,
	AnalyticsContainer_CreateContainer_m5217CD009ABF07FE51AC88C20F4B61B5A5BFF201,
	AnalyticsContainer_Initialize_m57C0D3C612C0DD90A98563A788EC6BE852555111,
	AnalyticsContainer_Enable_m7B1F481F487CB453A944765C5CF96E8AFE416219,
	AnalyticsContainer_Disable_m9373F843C8BB6828BF3D709D6366CD1151A4B243,
	AnalyticsContainer_Update_m077BEBF391B2FFFBA1892069B942D1C31C3038CD,
	AnalyticsContainer_OnApplicationPause_m0D7562975A5F9A1B1DD292A6EC115CB0E345FA8B,
	AnalyticsContainer_CleanUp_m060E72988945D2D3AD719F4D16A2056A3CFC72F9,
	AnalyticsContainer__ctor_m181248237969AF6A721DE089E172681F92EE9A04,
	NULL,
	CoreStatsHelper_SetCoreStatsConsent_mF5D6EDE2380AF3DA289C217CD505940D76D9877E,
	CoreStatsHelper__ctor_mD543DD28C5F258FC9B9BDD30D0192F8A7ECF9DF4,
	SdkVersion__cctor_mC098F58A265DB7E8ED89F1FCE4A5FD67906B0FD7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	DeviceDataWrapper_get_CpuType_mE43F075EA29C5DC78CFA256195D17E67F259BA8D,
	DeviceDataWrapper_get_GpuType_m32723F25A7E892E79DBB611F12C3245E2658EA62,
	DeviceDataWrapper_get_CpuCores_m4917E0C3B322804A3E526C36B3BAC06BB3647C65,
	DeviceDataWrapper_get_RamTotal_mC0426CEA851C9CF2E396E345A8474CA8774C17D5,
	DeviceDataWrapper_get_ScreenWidth_m3F3FFFBC0D7DE523833A6CD2E328D0AC8A68661B,
	DeviceDataWrapper_get_ScreenHeight_m601A469F716562134B01BCC3C23C203D4F93BFD0,
	DeviceDataWrapper_get_ScreenDpi_mF4CE184F736D8BFF5B7EFD195DABD96EFA5A08BB,
	DeviceDataWrapper_get_OperatingSystem_mF4674AFA3A9C6C66874D9D70DE2299DA8B28A4E0,
	DeviceDataWrapper_get_DeviceModel_mFDCB43AE54C28DD75F57C9D49AEB685CAC1C2045,
	DeviceDataWrapper_get_IsDebugDevice_m4BB8AC3BE94072BE56C8B52B90C30730B07A27FE,
	DeviceDataWrapper_get_IsTiny_mA43AC24044F179ACA90F866A9DDBCE12F34C8F95,
	DeviceDataWrapper__ctor_m5C68E180913910F8888E3EAA94581A61C5794E98,
	CommonDataWrapper_get_Version_m50964BA00C346AB94B07F7D2CF797B659260E756,
	CommonDataWrapper_get_GameBundleId_mAB1B5AE6A07CDEE96F6A30C11F7B21414EB0AEFA,
	CommonDataWrapper_get_ProjectId_mF2901D4DA200D1D8C1880A88727381D4056A281E,
	CommonDataWrapper_get_Platform_m8CD4BAB14E0C92736833460BC416E37D52488CDD,
	CommonDataWrapper_get_BuildGUID_m57FD5CAC4592C77795F9ADCE135FCA3479BBA36C,
	CommonDataWrapper_get_Idfv_m8E164D8A168B2B94D2EC98F5934CD0F248F73BA4,
	CommonDataWrapper_get_GameStoreId_m0477A87CFE1D1D66044722D964CB0DBA246237EF,
	CommonDataWrapper_get_HasVolume_m7F4DB6FAD82976A30A30492AFD52DC9C12C8A1D5,
	CommonDataWrapper_get_Volume_mF9E0EA4D6E45DB06F666AEB0828AECF38495838D,
	CommonDataWrapper_get_BatteryLevel_m04984DE8D1C0115ACBCC870F4A2F3E230BE5A20B,
	CommonDataWrapper_get_AnalyticsRegionLanguageCode_m0DB7E313B4B16258515766CAEBE3907E137EC5A7,
	CommonDataWrapper__ctor_m6D6EAC94DF0508F181A0B41C32186D828DCA0272,
	CommonDataWrapper_GetPlatform_mE598C5A8D6F6D0D0F9479868123DA79D45F2CD7D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	DataGenerator__ctor_mA4F895805C1ED670712EBA41726AD2CC7B0C590A,
	DataGenerator_SdkStartup_mF4A7453FA11A5B335893005A2EC59B2E5F32376F,
	DataGenerator_GameRunning_m41DD1BD22000DFEA3968261BCE98B2161CB8C23D,
	DataGenerator_NewPlayer_mC1F0DC5C2FFC8770E15364784A26FD1BF6FE89EE,
	DataGenerator_GameStarted_m99451326DF5663F2CD0B7E268D38AF9DEF57B751,
	DataGenerator_GameEnded_m7B356DF303F2B60863E7A942E847C91F68595DE9,
	DataGenerator_ClientDevice_m245A8C14DF629388644F93AF7BD0C17E42794EB0,
	DataGenerator_PushCommonParams_mBCA2A9870721102F62A8CB5C31BB1E0F84D248B7,
	DeviceVolumeProvider_get_VolumeAvailable_m5CE710060BD8C7D300DD19CC799F82043F624CC4,
	DeviceVolumeProvider_unity_services_analytics_get_device_volume_m3424DF6DB6A7CDBF086E0C65BA066DC6F875B7ED,
	DeviceVolumeProvider_GetDeviceVolume_m795C795430B63C4D9F1084A837E784B2E5337785,
	AnalyticsUserIdServiceComponent__ctor_mC4D9557B2E49D4883E699F5F8034FE65740D334E,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	IdentityManager_get_UserId_mFC6AEF665FCEAC521E5C0A81B8B2C72DC67A8BE9,
	IdentityManager_set_UserId_m621B67FC21828CC54314A59211794D3C75A78648,
	IdentityManager_get_InstallId_mE1E051AD42ECD4BFC3ED3CAC52F8A57D3426051C,
	IdentityManager_set_InstallId_m45872CF0FBFBDD61FCCCD692C08BEF495476825B,
	IdentityManager_get_PlayerId_mB0158C91286F4CA875CE8E4B354CB6F8116CA79B,
	IdentityManager_get_ExternalId_mB5262A8A86B5357ED0736599120AE1D4DB17BBA4,
	IdentityManager_set_ExternalId_m3BCAD76B8AEBAEE9F11C1548AD2ABD8371D441BB,
	IdentityManager_get_IsNewPlayer_m3C510AA1401E87F5BBF4951FE0B3B7A99DC48E2F,
	IdentityManager_set_IsNewPlayer_m30EA47E2185F182813E96DB2351BA7C421A555F1,
	IdentityManager_add_OnPlayerChanged_m40145236F4A83F999CB68B5E4234293F3C53D113,
	IdentityManager_remove_OnPlayerChanged_mFF14F291BD2F0C18393E1351E630F6206E06B04A,
	IdentityManager__ctor_m14AF44105B7B9F823AFBAD375F6FF919F0FDC0C6,
	IdentityManager_ExternalUserIdChanged_m81AF6E8346EA442C32E6024D78633488015127B2,
	Locale_unity_services_current_language_code_mD2D021F537C8BA5DF7003057B8BE0A83F311875A,
	Locale_CurrentLanguageCode_m720A4A975DCDE3417D54E73B51EA379DFC5B181E,
	Locale_AnalyticsRegionLanguageCode_mBC2C7A14D9ED824729BF977BE5C1478B559BB45B,
	NULL,
	NULL,
	AnalyticsForgetter_get_DeletionInProgress_mA2E85FC9A479A7CC90486397F7FC4098BA01EA98,
	AnalyticsForgetter__ctor_mB7EFEF4ADE978A9CAD976324BCC6BE797558DB0E,
	AnalyticsForgetter_SetForgettingStatus_m92EBCCB1DCCE80F80D6C0051B95C638A521B0111,
	AnalyticsForgetter_AttemptToForget_mC21B8E3EEBBD5F0B16A087EE44DA79C1467CA687,
	AnalyticsForgetter_UploadComplete_mA94023B0D4A918B179EBDFA97BFBD2F9221EE29D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	BufferSystemCalls_GenerateGuid_m61F3E2FF777BFA46961A04C2ADED85F198272DED,
	BufferSystemCalls_Now_m94184933D6A380AEEB970C38843D2E232D761DB0,
	BufferSystemCalls_GetTimeZoneUtcOffset_mB21231260DC5D4BD41277C0779F4476A0A52BF20,
	BufferSystemCalls__ctor_m11A81CCB33E4BA6C679E191AB344CC3277C57B8C,
	BufferX_get_Length_mAC86149425B8A65EBF678D67A191EBCD975BCC6C,
	BufferX_add_EventRecorded_m3B09C9F1F1CE39981A3FDD45EC6D07605EE761E2,
	BufferX_remove_EventRecorded_m345E7079CD2F26FE9CDAA3F4D5E01405FC902529,
	BufferX_add_EventsClearing_mB239E52FC724E4ABABFFD9B278CC939AC00A7CFA,
	BufferX_remove_EventsClearing_mDF25BF92DBE8C0188C920F23F85D4505EF6BD9C5,
	BufferX__ctor_mF1EB5A3CD82F9CAB1640CCDDAA93BC03E18C79B7,
	BufferX_WriteString_m6D00B4EC23DF37876EA6155C243345D9ABD997A6,
	BufferX_WriteLong_m240FC685730686F76FE73A5DD067ABAE48997AD8,
	BufferX_WriteByte_m12C49B76B743F83835010F50B8BDD99680AD6B14,
	BufferX_WriteBytes_mAB875F9E3ABB56B16A350123FA3983F967C3BA79,
	BufferX_WriteName_m8833F39525C5ECD5DBBF33168F5FEFC630EA2A7D,
	BufferX_WriteDateTime_mA5F6AB9647B6BCCAC8D757678B4924C79C828F80,
	BufferX_SerializeLong_m4BE2088BE0D483081E97E1587B509837DBF4225D,
	BufferX_PushStandardEventStart_mCDD3882CAAD89B53A7297161050D60E42002C48E,
	BufferX_PushCommonEventStart_mF4C139E81580C2618D5E9FEB412115B77CF47B45,
	BufferX_StripTrailingCommaIfNecessary_m835DF3D14DFBEB8486A8044A72C15F64C9CEA937,
	BufferX_PushEndEvent_m06F34FD5EB0FF8E4743902FBEDD52D2013A6DE23,
	BufferX_PushDouble_mBD733AFC8B05CF1442C1B7D62830D24863E3DF00,
	BufferX_PushString_m2786331DB2F4AA7F21C9FA1B6C6599E3233DE393,
	BufferX_ProcessCharacterOntoWorkingBuffer_mA488EA7413B881E935B3EDD43644D3B9AC62E49D,
	BufferX_PushInt64_m9980DFAD12A1BB6456B8E5FC33E28646A3DD2EA2,
	BufferX_PushBool_m929464C0BC79BA4A2977167EFDE6235031CB273F,
	BufferX_Serialize_m4CA698F0ECE4218D3227187027C1A754651B85D9,
	BufferX_ClearBuffer_mA8B0BBF389EA44EFE0CA85B2EF94A3B51153FEE8,
	BufferX_ClearBuffer_m33260625C009242974AAD0747DDF14D2EF1BB726,
	BufferX_FlushToDisk_m55BF963B573FD854740103B13825AACBD73C78E9,
	BufferX_ClearDiskCache_mD54CC30E7AD92D94FC57DF95A3E4A4A1BEAEB1C6,
	BufferX_SerializeDateTime_m1B8DB0257F014D2E517B2EB94CC80B5A879DB654,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	FileSystemCalls__ctor_mED48303E0E5ADE100ED297D2F82072B5162F3E2A,
	FileSystemCalls_CanAccessFileSystem_m664852625AD86DA0EAAADEF43EB28D979294738F,
	FileSystemCalls_FileExists_m73217509E8CEDB625FAFD2AA3E008153D7F84513,
	FileSystemCalls_DeleteFile_mC18864152DCA840D1FDD2DBD6287F4DADE4A59CC,
	FileSystemCalls_OpenFileForWriting_m5D2064955D6E16BA0EA7FC046A30096A06799A0D,
	DiskCache__ctor_mBA8B9A9ECF83506913BC62A4782F490C2D300263,
	DiskCache_Write_m402EA80E6B31916CD36DA3CF1C0760FC81309EE1,
	DiskCache_Clear_m3C6A5E5104B8CF7998D637099B86428D08E6003D,
	NULL,
	NULL,
	NULL,
	Dispatcher_get_ConsecutiveFailedUploadCount_m58D2668BE9E8194349A99CA67BB8583F09DE7C07,
	Dispatcher_set_ConsecutiveFailedUploadCount_m9EECD2DEB2695DBCF973D907662ED7D6140C9469,
	Dispatcher_get_FlushInProgress_mCC5DAEE29D9E97D753FE7B2C876FC7B9E3F50E28,
	Dispatcher_set_FlushInProgress_mE7F87C33DBBF7012A706047DC2336D3B6EF9391E,
	Dispatcher_add_FlushStarted_m6F5B525BBC812A1DADFB4716004BC00B90FEEA7F,
	Dispatcher_remove_FlushStarted_mE292CA4A7809F3872255D8D77D2174DB9FEF4D74,
	Dispatcher_add_FlushFinished_m736538D18D44C4D33FDA7471DB050A62F84BB5BA,
	Dispatcher_remove_FlushFinished_mB71D4DB6539E2623B0F76B4146E193A1CCAAF94A,
	Dispatcher__ctor_mDBB513D6B226DB1B13C536AE8FF0A531340D7208,
	Dispatcher_SetBuffer_m37334C4FDDB8C2DBA98A12432E896F1D3B45BE29,
	Dispatcher_Flush_m6167D29547BACF3E42364A0E384EDA58FA8D5247,
	Dispatcher_FlushBufferToService_m55AC50B02B30BD4ED1E554D794AA5DEE9FB77C39,
	Dispatcher_UploadCompleted_m1A96D525DB4DE2A378D239C3D11DA427E36B2459,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PlayerPrefsPersistence_SaveValue_mCBFD5D1492C195081BD0D2EC4283AD260938C900,
	PlayerPrefsPersistence_SaveValue_m51EB598C6CCE5E5FB68717F312AFBE99F83BC8DC,
	PlayerPrefsPersistence_LoadInt_m0D150E3F74F72D95C70405BB9BA9E83BC13A9EEF,
	PlayerPrefsPersistence__ctor_m3F310A297230AB1ED4FD0884D4AAC45082E3DD77,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AnalyticsWebRequest__ctor_m8CA36BAF33C32134D350FC88E7BF795AFFDFD77D,
	AnalyticsWebRequest_get_IsNetworkError_m36FEA587B02163F41F9FD2E20CBD2E2D71144BC2,
	AnalyticsWebRequest_Unity_Services_Analytics_Internal_IWebRequest_SendWebRequest_m415458FF981E6B3BC598B67AD580DA8443D35502,
	AnalyticsWebRequest_Unity_Services_Analytics_Internal_IWebRequest_SetRequestHeader_m150D2DC1E3957BC6F3C6052A03693C70F095B3BC,
	WebRequestHelper_CreateWebRequest_m06C697E4C704BADC84F815F3F84A4144708DDFFA,
	WebRequestHelper_SendWebRequest_mF1D98192E9EBDB286BC094900284220080FF9F5D,
	WebRequestHelper__ctor_m5F76E493A0C15311928DCCCCE293752DF3DA0CC9,
	U3CU3Ec__DisplayClass2_0__ctor_m0F6A2DDF4EF21C3B648EA299191EB2D006DB68F9,
	U3CU3Ec__DisplayClass2_0_U3CSendWebRequestU3Eb__0_mAE3230661FB4148BD8285721D5E48F49441B37D6,
	NULL,
	NULL,
	SessionManager_get_SessionId_mB333BEB743A8CB848E471D3DAE8D94799F2B3C2D,
	SessionManager_set_SessionId_mB86AF25C3E32F173BA3D42EFC43B5EDEB88A5D66,
	SessionManager__ctor_m21E89203802ECBDAB0DCD76F1352FF63A84C4ACC,
	SessionManager_StartNewSession_mE4556A817A8E5391B554A7E6953CFE3E12A467F3,
	StandardEventServiceComponent__ctor_mED8BAC8D2E1D561A6DCB4A1F35D4E7A60553CB87,
};
extern void U3CInitializeU3Ed__1_MoveNext_m6D1294910570A31C8D7E15AB3BE8B001184D85D5_AdjustorThunk (void);
extern void U3CInitializeU3Ed__1_SetStateMachine_mA709A8AC5E4AB1755D3691BAF4474C4148808268_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[2] = 
{
	{ 0x06000004, U3CInitializeU3Ed__1_MoveNext_m6D1294910570A31C8D7E15AB3BE8B001184D85D5_AdjustorThunk },
	{ 0x06000005, U3CInitializeU3Ed__1_SetStateMachine_mA709A8AC5E4AB1755D3691BAF4474C4148808268_AdjustorThunk },
};
static const int32_t s_InvokerIndices[247] = 
{
	9780,
	4779,
	6521,
	6521,
	5349,
	9624,
	9780,
	0,
	6322,
	6521,
	59,
	6521,
	6521,
	6521,
	6521,
	5349,
	6521,
	5253,
	6366,
	6521,
	6521,
	6521,
	6521,
	0,
	0,
	6444,
	9745,
	6444,
	9745,
	5349,
	6521,
	6521,
	6521,
	5253,
	6521,
	6521,
	0,
	5253,
	6521,
	9780,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6398,
	6398,
	6366,
	6366,
	6366,
	6366,
	6444,
	6398,
	6398,
	6301,
	6301,
	6521,
	6398,
	6398,
	6398,
	6398,
	6398,
	6398,
	6398,
	6301,
	6444,
	6327,
	6398,
	5349,
	9745,
	0,
	0,
	0,
	0,
	0,
	0,
	1807,
	5349,
	5349,
	5349,
	5349,
	3134,
	5349,
	5349,
	9722,
	9765,
	9765,
	5349,
	0,
	0,
	0,
	0,
	0,
	0,
	6398,
	5349,
	6398,
	5349,
	6398,
	6398,
	5349,
	6301,
	5253,
	5349,
	5349,
	1249,
	5349,
	9745,
	9745,
	9745,
	0,
	0,
	6301,
	1807,
	5320,
	381,
	5321,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6398,
	6322,
	4914,
	6521,
	6366,
	5349,
	5349,
	5349,
	5349,
	1249,
	5240,
	5240,
	5240,
	5240,
	5349,
	5278,
	747,
	3134,
	5349,
	6521,
	6521,
	3131,
	3138,
	2279,
	3135,
	3124,
	6398,
	6521,
	5321,
	6521,
	6521,
	9379,
	0,
	0,
	0,
	0,
	0,
	0,
	6521,
	6301,
	3925,
	5349,
	4779,
	5349,
	3138,
	6521,
	0,
	0,
	0,
	6366,
	5320,
	6301,
	5253,
	5349,
	5349,
	5349,
	5349,
	3138,
	5349,
	6521,
	6521,
	5321,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	3134,
	3138,
	4539,
	6521,
	0,
	0,
	0,
	0,
	0,
	3138,
	6301,
	6398,
	3138,
	1522,
	3138,
	6521,
	6521,
	5349,
	0,
	0,
	6398,
	5349,
	6521,
	6521,
	3138,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Services_Analytics_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Services_Analytics_CodeGenModule = 
{
	"Unity.Services.Analytics.dll",
	247,
	s_methodPointers,
	2,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
