﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void InstallationId__ctor_mD8E8D5B7A72C54F2517D33F9F2C89FA708CC6BF7 (void);
extern void InstallationId_GetOrCreateIdentifier_mB808E735A25CB3357E33337F9A4B737DDAB6B40F (void);
extern void InstallationId_CreateIdentifier_mC089486FCD96983C9186689C41D1D75E36EFB90C (void);
extern void InstallationId_ReadIdentifierFromFile_m126AF7B4F947DBB382AA8848D51FEA52C91C3E7C (void);
extern void InstallationId_WriteIdentifierToFile_m67273AD8773FDBEE468B0BB7F502B3383AB9A6C4 (void);
extern void InstallationId_GenerateGuid_m7220D8C5AE5B4DCA4F4C01A6F921FF66C23E855A (void);
extern void NSUserDefaults_GetString_mA77E63188B222653CB1C8AA0B0CB089F7EE0690E (void);
extern void NSUserDefaults_SetString_m0BFB3A66E345CE80C080620675028FDE686166F0 (void);
extern void NSUserDefaults_UserDefaultsGetString_m92C7603D4E38B141A88AC3E2B87B1DF1600A06A8 (void);
extern void NSUserDefaults_UserDefaultsSetString_m6D339E36904A6238646E398703ED1CB13D09BA83 (void);
extern void UnityAdsIdentifier_get_UserId_m4E41B6AEB6405B6BFA7D4E52FE3F66478A18D90A (void);
extern void UnityAdsIdentifier_set_UserId_mA2519CC19694D24C35D6E2E72B79E2CE34DACA4F (void);
extern void UnityAdsIdentifier_get_IdentifierForInstallIos_m510AD72EA3847591D1F1AE3214FE4E81A258092E (void);
extern void UnityAdsIdentifier_set_IdentifierForInstallIos_mD9D71B7910AA0380D1C5E9445A04B6E812C5F835 (void);
extern void UnityAdsIdentifier__ctor_mFD5085EB0AD902D689B820D13D697CE640E2E7B8 (void);
extern void UnityAnalyticsIdentifier_get_UserId_m15964ABDB88E2D752C471143ED9C5A32017989CE (void);
extern void UnityAnalyticsIdentifier_set_UserId_m2751FA3CB0A777DAC8AA56ECBE8945EED55CAC3C (void);
extern void UnityAnalyticsIdentifier__ctor_mE19B73CB1414B5AC0619E8EF1366C0164B06D14B (void);
static Il2CppMethodPointer s_methodPointers[20] = 
{
	InstallationId__ctor_mD8E8D5B7A72C54F2517D33F9F2C89FA708CC6BF7,
	InstallationId_GetOrCreateIdentifier_mB808E735A25CB3357E33337F9A4B737DDAB6B40F,
	InstallationId_CreateIdentifier_mC089486FCD96983C9186689C41D1D75E36EFB90C,
	InstallationId_ReadIdentifierFromFile_m126AF7B4F947DBB382AA8848D51FEA52C91C3E7C,
	InstallationId_WriteIdentifierToFile_m67273AD8773FDBEE468B0BB7F502B3383AB9A6C4,
	InstallationId_GenerateGuid_m7220D8C5AE5B4DCA4F4C01A6F921FF66C23E855A,
	NULL,
	NULL,
	NSUserDefaults_GetString_mA77E63188B222653CB1C8AA0B0CB089F7EE0690E,
	NSUserDefaults_SetString_m0BFB3A66E345CE80C080620675028FDE686166F0,
	NSUserDefaults_UserDefaultsGetString_m92C7603D4E38B141A88AC3E2B87B1DF1600A06A8,
	NSUserDefaults_UserDefaultsSetString_m6D339E36904A6238646E398703ED1CB13D09BA83,
	UnityAdsIdentifier_get_UserId_m4E41B6AEB6405B6BFA7D4E52FE3F66478A18D90A,
	UnityAdsIdentifier_set_UserId_mA2519CC19694D24C35D6E2E72B79E2CE34DACA4F,
	UnityAdsIdentifier_get_IdentifierForInstallIos_m510AD72EA3847591D1F1AE3214FE4E81A258092E,
	UnityAdsIdentifier_set_IdentifierForInstallIos_mD9D71B7910AA0380D1C5E9445A04B6E812C5F835,
	UnityAdsIdentifier__ctor_mFD5085EB0AD902D689B820D13D697CE640E2E7B8,
	UnityAnalyticsIdentifier_get_UserId_m15964ABDB88E2D752C471143ED9C5A32017989CE,
	UnityAnalyticsIdentifier_set_UserId_m2751FA3CB0A777DAC8AA56ECBE8945EED55CAC3C,
	UnityAnalyticsIdentifier__ctor_mE19B73CB1414B5AC0619E8EF1366C0164B06D14B,
};
static const int32_t s_InvokerIndices[20] = 
{
	6521,
	6398,
	6521,
	9745,
	9624,
	9745,
	0,
	0,
	9391,
	8989,
	9391,
	8989,
	6398,
	5349,
	9745,
	9624,
	6521,
	6398,
	5349,
	6521,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Services_Core_Device_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Services_Core_Device_CodeGenModule = 
{
	"Unity.Services.Core.Device.dll",
	20,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
