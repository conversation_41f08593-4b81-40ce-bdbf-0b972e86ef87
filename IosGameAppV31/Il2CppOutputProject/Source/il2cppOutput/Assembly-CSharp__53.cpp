﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct ABSTweenPlugin_3_tC26278B0CA053BAF4547345679D3E8D1F748AC12;
struct ABSTweenPlugin_3_tE7EF56C4695649256BA2077D8E447E739C869256;
struct ABSTweenPlugin_3_t4082710A2BB933E2D055E454B3EFAC4C0A319444;
struct ABSTweenPlugin_3_tF7CEB9BBF8FF28D2F3BBF9DB950849DCA238F80C;
struct ABSTweenPlugin_3_tE5A78BE46D046C07A6356B8AB596B2D00F9295E7;
struct Action_1_t4A688F1B0820577407F1F7CCBDB6322B1D298E38;
struct DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95;
struct DOGetter_1_t423CA9B377A96EA7C69ABA0C83C81B8D5B045E85;
struct DOGetter_1_t65A2B66606014CD7EFBA1D8DDD1273F57511D146;
struct DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66;
struct DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338;
struct DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89;
struct DOSetter_1_t6889207117879689DE539960755CE5F277F65C0F;
struct DOSetter_1_t999572008E4C4CFE5EF6FA70393F8B092CA8C438;
struct DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C;
struct DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358;
struct List_1_t0C6BF1E3B166E9D2A63FC3291C519D61B950BFDC;
struct List_1_t8F74D963C9C956F3AD8F4A97EF4764A9C2952481;
struct List_1_t1CA49F1FC40D700FB8B833B5E6837D9A76EB3E2F;
struct List_1_tDA2C18E15C40590123A37DABB6D0D9AEB77A3BBD;
struct TweenCallback_1_tF0ADCA0C226C9C243ACB55E67D852E4BB53AEB67;
struct TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3;
struct TweenerCore_3_t390910F1CE8B4737AA72FEED470399C8DBE1AF8B;
struct TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271;
struct TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B;
struct TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA;
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C;
struct Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD;
struct AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C;
struct BoxCollider_tFA5D239388334D6DE0B8FFDAD6825C5B03786E23;
struct BoxCollider2D_tF860C7737FFB062CEC06577E0CD8364EEC1D4EDA;
struct CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct EaseFunction_t0F945D9D726B0915C5FBF30862E987EC3AC12A04;
struct Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E;
struct IAsyncResult_t7B9B5A0ECB35DCEC31B8A8122C37D687369253B5;
struct Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4;
struct MeshCollider_tB525E4DDE383252364ED0BDD32CF2B53914EE455;
struct MeshFilter_t6D1CE2473A1E45AC73013400585A1163BF66B2F5;
struct MethodInfo_t;
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF;
struct Rigidbody_t268697F5A994213ED97393309870968BC1C7393C;
struct Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C;
struct SpiralPlugin_t0EC3955D810AAED4713FA63AAEACDA66063B604E;
struct String_t;
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1;
struct Tween_t8CB06EBC48A5B6F5065C490E4F4909C18CE7983C;
struct TweenCallback_t7C8B8A38E7B30905FF1B83C943256EF23617BB24;
struct Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140;
struct Type_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9;
struct tk2dFontData_tFF80FF31AC59267FD1EB5A6D039BAEEE62ABC7CD;
struct tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A;
struct tk2dSpriteCollectionData_tB5B540C5BF07C6F84AD5386ED5929BC6FBC722F9;
struct tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B;
struct tk2dTextMeshData_t1FE2BE2F5BE79CFF6490FFA6161B563727A134C0;
struct U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C;
struct U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B;
struct U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2;
struct U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58;
struct U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4;
struct U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D;
struct U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E;
struct U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A;
struct U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B;
struct U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771;
struct U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3;
struct U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D;
struct U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47;
struct U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F;
struct U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B;
struct U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4;
struct U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991;
struct U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45;

IL2CPP_EXTERN_C RuntimeClass* DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* DOGetter_1_t65A2B66606014CD7EFBA1D8DDD1273F57511D146_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* DOSetter_1_t999572008E4C4CFE5EF6FA70393F8B092CA8C438_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Mathf_tE284D016E3B297B72311AAD9EB8F0E643F6A4682_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SpiralPlugin_t0EC3955D810AAED4713FA63AAEACDA66063B604E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* DOTween_To_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisSpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D_m6716A7D71A3E516E4FB46A9B1C8AD83153E46190_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1_GetValueOrDefault_mD88C565C80170105E87BB423C5B2F974840EF90F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1__ctor_m75F3ABB694E26670F021136BD3B9E71A65948BC2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Nullable_1_get_Value_m6A74FA440FE386A9905C61B41B5C261CD9DC4792_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rigidbody_MovePosition_mB2CD29ABC8F59AC338C0A3A5A6B75C38FDA92CA9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TweenSettingsExtensions_SetEase_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_m3605060ADD85D4E33C9F7D03BE6A676211A4A40B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TweenSettingsExtensions_SetTarget_TisSequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C_m87E21291C6C5BECA753976C5CA64050E8AFF93C0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TweenSettingsExtensions_SetTarget_TisTweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271_m7563CA4B8BD3EA41428B31557409B6456D19A876_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77_mA961948C0215BFB1350EBB68B4D0E372AC42B0E9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B_m593D7B49993413FC2357476183683A34122E55B6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass0_0_U3CDOScaleU3Eb__0_mB5E48C8A184950151A5169CCDF186E26E0FCB61C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass0_0_U3CDOScaleU3Eb__1_m61F08DCCA26B7B69804A8ACA70178B684EC9D1C7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass10_0_U3CDOScaleU3Eb__0_mB1EAC091524E7349C6125F11085E8CE2774D8CF3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass10_0_U3CDOScaleU3Eb__1_mDDEBD485BA4CCBAA5BE8B462D23999618B7D830C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass11_0_U3CDOScaleXU3Eb__0_m46228F7BEF3622E9C1D2CEE579CDCEE7F7734024_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass11_0_U3CDOScaleXU3Eb__1_mB122DE18D71E40DD99AACFAAAA6923B90B3A22D5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass12_0_U3CDOScaleYU3Eb__0_mDD0743BC5E84104F770E5D2D2D238EDE940A6802_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass12_0_U3CDOScaleYU3Eb__1_m65EE8F60D687CF96837CE487EBEA5E17671F6A09_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass13_0_U3CDOScaleZU3Eb__0_mC3F5F90007DB9823F07CCDB09A9D828E3724AD93_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass13_0_U3CDOScaleZU3Eb__1_mDA518BE9C06BED6CADB6A8655228B7C5781AFB9C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass14_0_U3CDOColorU3Eb__0_mD614B15FEF4933851FE7EF94B1D57B5C9A252383_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass14_0_U3CDOColorU3Eb__1_m964DE1C15CFED970699C40068B16792D4C5E2218_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass15_0_U3CDOFadeU3Eb__0_mC49B5C60ABEBC79E1366CA2F6F4FC93907917940_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass15_0_U3CDOFadeU3Eb__1_m521FEE450B3B6A93EF87E5BBAE966E54521B379C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass17_0_U3CDOTextU3Eb__0_m8B4B219CD369FEE4E763AC9752288B07F49F63A2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass17_0_U3CDOTextU3Eb__1_mA7CED3164348AAA18200C52532802EC98F3D12D3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass1_0_U3CDOScaleXU3Eb__0_m8D63B2F8E7800BDC72C7759B8C7205CCB3AD23DB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass1_0_U3CDOScaleXU3Eb__1_m2D9D6D522C9D987353FB914C4B03BA2F346C18A4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass1_0_U3CDOSpiralU3Eb__0_m1CCDACC82D59B9DD6DCEEE277BE3D1C410B40CEE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass1_0_U3CDOSpiralU3Eb__1_m2EA9119E181F78B27E9B32146D9861ABD0C3F7E8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass2_0_U3CDOScaleYU3Eb__0_mEE879668EE37DBAC46170ECE00C87B30B84D3971_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass2_0_U3CDOScaleYU3Eb__1_m368AB6696597DF5DD1B191C269D6B6E4BEDB5672_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass2_0_U3CDOSpiralU3Eb__0_mA0C2085BFC6B0541C15180DAD8844FAFBF8E303C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass3_0_U3CDOScaleZU3Eb__0_m112F0998AFD6CA41F674A89B1F7A960289BB080A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass3_0_U3CDOScaleZU3Eb__1_m971AD755BEE5DDC5E148E080B12D16011899378A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass4_0_U3CDOColorU3Eb__0_mAAB45A5C5FB51D6F74120F9A1008350106669C3E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass4_0_U3CDOColorU3Eb__1_m7F6BAAAD699548BA72437E8C8538FDA2AE561D60_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass5_0_U3CDOFadeU3Eb__0_mE1BEBDD002383619C46034FE7CA77504024F5175_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass5_0_U3CDOFadeU3Eb__1_mD6295447001F737B10D4903A4B9869EFBD7EE432_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass7_0_U3CDOScaleDimensionsU3Eb__0_mED7DB8E10CEA7A9170DD2FFC2C35642EACC89257_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass7_0_U3CDOScaleDimensionsU3Eb__1_m3DCE2678951FA5396B20599B9E79F90EE44870E2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass8_0_U3CDOScaleDimensionsXU3Eb__0_m921CB89E85E4A255DE4B4022D238E583E5D84621_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass8_0_U3CDOScaleDimensionsXU3Eb__1_m9DC03CB883E90AF346F8E07E9803BD274A387E7D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass9_0_U3CDOScaleDimensionsYU3Eb__0_m4AFBCD578E52050BD94E098ABB7556D6A320A767_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass9_0_U3CDOScaleDimensionsYU3Eb__1_m6FC87FA1EEEFE3A9F7F75080ED29F69A58F0E9EE_RuntimeMethod_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;

struct GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct ABSTweenPlugin_3_tF7CEB9BBF8FF28D2F3BBF9DB950849DCA238F80C  : public RuntimeObject
{
};
struct U3CPrivateImplementationDetailsU3E_t0F5473E849A5A5185A9F4C5246F0C32816C49FCA  : public RuntimeObject
{
};
struct DOTweenAnimationExtensions_t322F12945BA2ABB5DA1E5C986FC693E30DE1D5C6  : public RuntimeObject
{
};
struct DOTweenProShortcuts_t0DD3C1F28916E0363CF70D8DE3CA7DA4CF7A48A0  : public RuntimeObject
{
};
struct ShortcutExtensionsTk2d_t8E72963A7CAFB7A706ED2E8F8EBE73E52C1C6453  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C  : public RuntimeObject
{
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___target;
};
struct U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B  : public RuntimeObject
{
	Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* ___target;
};
struct U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2  : public RuntimeObject
{
	tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___target;
};
struct U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58  : public RuntimeObject
{
	tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___target;
};
struct U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4  : public RuntimeObject
{
	tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___target;
};
struct U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D  : public RuntimeObject
{
	tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___target;
};
struct U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E  : public RuntimeObject
{
	tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___target;
};
struct U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A  : public RuntimeObject
{
	tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___target;
};
struct U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B  : public RuntimeObject
{
	tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___target;
};
struct U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771  : public RuntimeObject
{
	tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___target;
};
struct U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3  : public RuntimeObject
{
	tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___target;
};
struct U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D  : public RuntimeObject
{
	tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___target;
};
struct U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47  : public RuntimeObject
{
	tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___target;
};
struct U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F  : public RuntimeObject
{
	tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___target;
};
struct U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B  : public RuntimeObject
{
	tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___target;
};
struct U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4  : public RuntimeObject
{
	tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* ___target;
};
struct U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991  : public RuntimeObject
{
	tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* ___target;
};
struct U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45  : public RuntimeObject
{
	tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* ___target;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17 
{
	Il2CppChar ___m_value;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct ColorOptions_t9F2151E3A21F3FE2A41BEEF7D288D670C2685F39 
{
	bool ___alphaOnly;
};
struct ColorOptions_t9F2151E3A21F3FE2A41BEEF7D288D670C2685F39_marshaled_pinvoke
{
	int32_t ___alphaOnly;
};
struct ColorOptions_t9F2151E3A21F3FE2A41BEEF7D288D670C2685F39_marshaled_com
{
	int32_t ___alphaOnly;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Mathf_tE284D016E3B297B72311AAD9EB8F0E643F6A4682 
{
	union
	{
		struct
		{
		};
		uint8_t Mathf_tE284D016E3B297B72311AAD9EB8F0E643F6A4682__padding[1];
	};
};
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct SpiralPlugin_t0EC3955D810AAED4713FA63AAEACDA66063B604E  : public ABSTweenPlugin_3_tF7CEB9BBF8FF28D2F3BBF9DB950849DCA238F80C
{
};
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	uint32_t ___m_value;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	float ___x;
	float ___y;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D10_tC38CF142534A070C2B94C80666D4D49680A1F92A 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D10_tC38CF142534A070C2B94C80666D4D49680A1F92A__padding[10];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D1024_t4A9D9610B3331D96BFF9BD065239C4091A2300C7 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D1024_t4A9D9610B3331D96BFF9BD065239C4091A2300C7__padding[1024];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D116_tC081A6DA1862FE80B878AA942235FC5929D2B4DA 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D116_tC081A6DA1862FE80B878AA942235FC5929D2B4DA__padding[116];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D12_t1BDD2193C3F925556BCD5FF35C0AC52DDB0CAB8F 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D12_t1BDD2193C3F925556BCD5FF35C0AC52DDB0CAB8F__padding[12];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D120_tCE5F35B252DE449EDE2BA38EFA855ABFBF94EF89 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D120_tCE5F35B252DE449EDE2BA38EFA855ABFBF94EF89__padding[120];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D144_t4839144B438E5DCF848D2BCCA695C05728CB3D82 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D144_t4839144B438E5DCF848D2BCCA695C05728CB3D82__padding[144];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D16_tFB2D94E174C3DFBC336BBEE6AD92E07462831A23 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D16_tFB2D94E174C3DFBC336BBEE6AD92E07462831A23__padding[16];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D20_tA394C0A7DC4F4F05D2190B09E23BDE8536718D72 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D20_tA394C0A7DC4F4F05D2190B09E23BDE8536718D72__padding[20];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D2048_t0B1BD728889599A5E758B66FFCDC3437056644E1 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D2048_t0B1BD728889599A5E758B66FFCDC3437056644E1__padding[2048];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D216_tDB72A404E47DEF109BD8C32B757E36068C431C7F 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D216_tDB72A404E47DEF109BD8C32B757E36068C431C7F__padding[216];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D24_t3464DA68B6CCAB9A0A43F94B3DB9AA7E7FDDB19A 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D24_t3464DA68B6CCAB9A0A43F94B3DB9AA7E7FDDB19A__padding[24];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D28_t523FB00435F599517548D4C121316CFE1B43E6C2 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D28_t523FB00435F599517548D4C121316CFE1B43E6C2__padding[28];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D31768_tFAEF8528D703F42DBCBECCF86120227C3DA6743E 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D31768_tFAEF8528D703F42DBCBECCF86120227C3DA6743E__padding[31768];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D32_tC3894D25C1E879699FE1C9BAB1BBF2787B405069 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D32_tC3894D25C1E879699FE1C9BAB1BBF2787B405069__padding[32];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D36_tE3135E025C70F21BBD65107243EE57F8AA699792 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D36_tE3135E025C70F21BBD65107243EE57F8AA699792__padding[36];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D40_t68A41E1D2BAA1C55857C26F7E0C26D1CFDB100B3 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D40_t68A41E1D2BAA1C55857C26F7E0C26D1CFDB100B3__padding[40];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D53556_t488731EBAA87973895D4FC2B864DB8DE8E5FD543 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D53556_t488731EBAA87973895D4FC2B864DB8DE8E5FD543__padding[53556];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D56_tF90903548CF34D92C9F741BD6D3F8DC6C822EF4E 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D56_tF90903548CF34D92C9F741BD6D3F8DC6C822EF4E__padding[56];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D60_t28DCDB387E585965787FF852575673A4F7B6A5E3 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D60_t28DCDB387E585965787FF852575673A4F7B6A5E3__padding[60];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D64_tC5DCC92F71FF4937EDBB6F52C59AD832172ABE88 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D64_tC5DCC92F71FF4937EDBB6F52C59AD832172ABE88__padding[64];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D76_tD26EB59D44A33303FCD119ABE6C31A2829EB22D6 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D76_tD26EB59D44A33303FCD119ABE6C31A2829EB22D6__padding[76];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D80_t144B9F4F4C74AB89365AB7716EE83DECD70D210D 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D80_t144B9F4F4C74AB89365AB7716EE83DECD70D210D__padding[80];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D96_tE962D346DA0011435482BCE5513DB1E0D88A8DEB 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D96_tE962D346DA0011435482BCE5513DB1E0D88A8DEB__padding[96];
	};
};
#pragma pack(pop, tp)
struct Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE 
{
	bool ___hasValue;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___value;
};
struct AxisConstraint_t44CDC917B5BB2911F8930F8886A6581C09901AB6 
{
	int32_t ___value__;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Ease_t1A7F82C47C5B94B9CF9DF46FC58F80488BC6A7EB 
{
	int32_t ___value__;
};
struct Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E  : public RuntimeObject
{
	intptr_t ___m_Ptr;
};
struct Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E_marshaled_pinvoke
{
	intptr_t ___m_Ptr;
};
struct Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E_marshaled_com
{
	intptr_t ___m_Ptr;
};
struct GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___color;
	float ___time;
};
struct LoopType_t3128AD2C907BAF825D244B38F274987C1AA08FE5 
{
	int32_t ___value__;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct ScrambleMode_t21D45529B4B9E069A015791B7C203D93BE14401C 
{
	int32_t ___value__;
};
struct SpecialStartupMode_t872762964D80B3C1F6CE4024236A20D3D889AC44 
{
	int32_t ___value__;
};
struct SpiralMode_tCE02B7D2464AD3118FD56E3DD0DC2A306065335B 
{
	int32_t ___value__;
};
struct TextAnchor_tA46E794186AC1CD0F22888652F589EBF7DFDF830 
{
	int32_t ___value__;
};
struct TweenType_t50BBF64E13F70041085C51B9E0CB40FA61212F3E 
{
	int32_t ___value__;
};
struct UpdateType_tA521F450D94A1E8A88C6967093E093777BBA4C57 
{
	int32_t ___value__;
};
struct AnimationType_tF7CD3CF7798047D14056BB1A36615CF5CF4B020B 
{
	int32_t ___value__;
};
struct TargetType_t44905BCDEEA34D8E3E13EE21AE4AB658D194D877 
{
	int32_t ___value__;
};
struct Anchor_t1F150751826A9E5233692639C3FBBC75DF206D61 
{
	int32_t ___value__;
};
struct UpdateFlags_tA7430BB45216F93D20068B4C90A587E89E325F8D 
{
	int32_t ___value__;
};
struct ABSSequentiable_t05DF85FC63E3650D2D4CF6ABBA0F43263EB8CE89  : public RuntimeObject
{
	int32_t ___tweenType;
	float ___sequencedPosition;
	float ___sequencedEndPosition;
	TweenCallback_t7C8B8A38E7B30905FF1B83C943256EF23617BB24* ___onStart;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D 
{
	float ___depth;
	float ___frequency;
	float ___speed;
	int32_t ___mode;
	bool ___snapping;
	float ___unit;
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___axisQ;
};
struct SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D_marshaled_pinvoke
{
	float ___depth;
	float ___frequency;
	float ___speed;
	int32_t ___mode;
	int32_t ___snapping;
	float ___unit;
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___axisQ;
};
struct SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D_marshaled_com
{
	float ___depth;
	float ___frequency;
	float ___speed;
	int32_t ___mode;
	int32_t ___snapping;
	float ___unit;
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___axisQ;
};
struct StringOptions_tC70D70DB6854CE62E6BBC3AA066517835919E9FA 
{
	bool ___richTextEnabled;
	int32_t ___scrambleMode;
	CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB* ___scrambledChars;
	int32_t ___startValueStrippedLength;
	int32_t ___changeValueStrippedLength;
};
struct StringOptions_tC70D70DB6854CE62E6BBC3AA066517835919E9FA_marshaled_pinvoke
{
	int32_t ___richTextEnabled;
	int32_t ___scrambleMode;
	uint8_t* ___scrambledChars;
	int32_t ___startValueStrippedLength;
	int32_t ___changeValueStrippedLength;
};
struct StringOptions_tC70D70DB6854CE62E6BBC3AA066517835919E9FA_marshaled_com
{
	int32_t ___richTextEnabled;
	int32_t ___scrambleMode;
	uint8_t* ___scrambledChars;
	int32_t ___startValueStrippedLength;
	int32_t ___changeValueStrippedLength;
};
struct VectorOptions_t2814CC842518C92C9DFC5DE6F7A73824758D3EF9 
{
	int32_t ___axisConstraint;
	bool ___snapping;
};
struct VectorOptions_t2814CC842518C92C9DFC5DE6F7A73824758D3EF9_marshaled_pinvoke
{
	int32_t ___axisConstraint;
	int32_t ___snapping;
};
struct VectorOptions_t2814CC842518C92C9DFC5DE6F7A73824758D3EF9_marshaled_com
{
	int32_t ___axisConstraint;
	int32_t ___snapping;
};
struct DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95  : public MulticastDelegate_t
{
};
struct DOGetter_1_t65A2B66606014CD7EFBA1D8DDD1273F57511D146  : public MulticastDelegate_t
{
};
struct DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66  : public MulticastDelegate_t
{
};
struct DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338  : public MulticastDelegate_t
{
};
struct DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89  : public MulticastDelegate_t
{
};
struct DOSetter_1_t999572008E4C4CFE5EF6FA70393F8B092CA8C438  : public MulticastDelegate_t
{
};
struct DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C  : public MulticastDelegate_t
{
};
struct DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358  : public MulticastDelegate_t
{
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Rigidbody_t268697F5A994213ED97393309870968BC1C7393C  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Tween_t8CB06EBC48A5B6F5065C490E4F4909C18CE7983C  : public ABSSequentiable_t05DF85FC63E3650D2D4CF6ABBA0F43263EB8CE89
{
	float ___timeScale;
	bool ___isBackwards;
	RuntimeObject* ___id;
	String_t* ___stringId;
	int32_t ___intId;
	RuntimeObject* ___target;
	int32_t ___updateType;
	bool ___isIndependentUpdate;
	TweenCallback_t7C8B8A38E7B30905FF1B83C943256EF23617BB24* ___onPlay;
	TweenCallback_t7C8B8A38E7B30905FF1B83C943256EF23617BB24* ___onPause;
	TweenCallback_t7C8B8A38E7B30905FF1B83C943256EF23617BB24* ___onRewind;
	TweenCallback_t7C8B8A38E7B30905FF1B83C943256EF23617BB24* ___onUpdate;
	TweenCallback_t7C8B8A38E7B30905FF1B83C943256EF23617BB24* ___onStepComplete;
	TweenCallback_t7C8B8A38E7B30905FF1B83C943256EF23617BB24* ___onComplete;
	TweenCallback_t7C8B8A38E7B30905FF1B83C943256EF23617BB24* ___onKill;
	TweenCallback_1_tF0ADCA0C226C9C243ACB55E67D852E4BB53AEB67* ___onWaypointChange;
	bool ___isFrom;
	bool ___isBlendable;
	bool ___isRecyclable;
	bool ___isSpeedBased;
	bool ___autoKill;
	float ___duration;
	int32_t ___loops;
	int32_t ___loopType;
	float ___delay;
	bool ___U3CisRelativeU3Ek__BackingField;
	int32_t ___easeType;
	EaseFunction_t0F945D9D726B0915C5FBF30862E987EC3AC12A04* ___customEase;
	float ___easeOvershootOrAmplitude;
	float ___easePeriod;
	String_t* ___debugTargetId;
	Type_t* ___typeofT1;
	Type_t* ___typeofT2;
	Type_t* ___typeofTPlugOptions;
	bool ___U3CactiveU3Ek__BackingField;
	bool ___isSequenced;
	Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* ___sequenceParent;
	int32_t ___activeId;
	int32_t ___specialStartupMode;
	bool ___creationLocked;
	bool ___startupDone;
	bool ___U3CplayedOnceU3Ek__BackingField;
	float ___U3CpositionU3Ek__BackingField;
	float ___fullDuration;
	int32_t ___completedLoops;
	bool ___isPlaying;
	bool ___isComplete;
	float ___elapsedDelay;
	bool ___delayComplete;
	int32_t ___miscInt;
};
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ___m_CancellationTokenSource;
};
struct Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C  : public Tween_t8CB06EBC48A5B6F5065C490E4F4909C18CE7983C
{
	List_1_tDA2C18E15C40590123A37DABB6D0D9AEB77A3BBD* ___sequencedTweens;
	List_1_t0C6BF1E3B166E9D2A63FC3291C519D61B950BFDC* ____sequencedObjs;
	float ___lastTweenInsertTime;
};
struct Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140  : public Tween_t8CB06EBC48A5B6F5065C490E4F4909C18CE7983C
{
	bool ___hasManuallySetStartValue;
	bool ___isFromAllowed;
};
struct TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3  : public Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___startValue;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___endValue;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___changeValue;
	ColorOptions_t9F2151E3A21F3FE2A41BEEF7D288D670C2685F39 ___plugOptions;
	DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95* ___getter;
	DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89* ___setter;
	ABSTweenPlugin_3_tC26278B0CA053BAF4547345679D3E8D1F748AC12* ___tweenPlugin;
};
struct TweenerCore_3_t390910F1CE8B4737AA72FEED470399C8DBE1AF8B  : public Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140
{
	String_t* ___startValue;
	String_t* ___endValue;
	String_t* ___changeValue;
	StringOptions_tC70D70DB6854CE62E6BBC3AA066517835919E9FA ___plugOptions;
	DOGetter_1_t65A2B66606014CD7EFBA1D8DDD1273F57511D146* ___getter;
	DOSetter_1_t999572008E4C4CFE5EF6FA70393F8B092CA8C438* ___setter;
	ABSTweenPlugin_3_tE7EF56C4695649256BA2077D8E447E739C869256* ___tweenPlugin;
};
struct TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271  : public Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___startValue;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___endValue;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___changeValue;
	VectorOptions_t2814CC842518C92C9DFC5DE6F7A73824758D3EF9 ___plugOptions;
	DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66* ___getter;
	DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C* ___setter;
	ABSTweenPlugin_3_t4082710A2BB933E2D055E454B3EFAC4C0A319444* ___tweenPlugin;
};
struct TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B  : public Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___startValue;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___endValue;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___changeValue;
	SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D ___plugOptions;
	DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* ___getter;
	DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* ___setter;
	ABSTweenPlugin_3_tF7CEB9BBF8FF28D2F3BBF9DB950849DCA238F80C* ___tweenPlugin;
};
struct TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77  : public Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___startValue;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___endValue;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___changeValue;
	VectorOptions_t2814CC842518C92C9DFC5DE6F7A73824758D3EF9 ___plugOptions;
	DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* ___getter;
	DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* ___setter;
	ABSTweenPlugin_3_tE5A78BE46D046C07A6356B8AB596B2D00F9295E7* ___tweenPlugin;
};
struct tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	tk2dSpriteCollectionData_tB5B540C5BF07C6F84AD5386ED5929BC6FBC722F9* ___collection;
	tk2dSpriteCollectionData_tB5B540C5BF07C6F84AD5386ED5929BC6FBC722F9* ___collectionInst;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ____color;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____scale;
	int32_t ____spriteId;
	BoxCollider2D_tF860C7737FFB062CEC06577E0CD8364EEC1D4EDA* ___boxCollider2D;
	List_1_t1CA49F1FC40D700FB8B833B5E6837D9A76EB3E2F* ___polygonCollider2D;
	List_1_t8F74D963C9C956F3AD8F4A97EF4764A9C2952481* ___edgeCollider2D;
	BoxCollider_tFA5D239388334D6DE0B8FFDAD6825C5B03786E23* ___boxCollider;
	MeshCollider_tB525E4DDE383252364ED0BDD32CF2B53914EE455* ___meshCollider;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___meshColliderPositions;
	Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___meshColliderMesh;
	Action_1_t4A688F1B0820577407F1F7CCBDB6322B1D298E38* ___SpriteChanged;
	Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* ____cachedRenderer;
	int32_t ___renderLayer;
};
struct tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	tk2dFontData_tFF80FF31AC59267FD1EB5A6D039BAEEE62ABC7CD* ____fontInst;
	String_t* ____formattedText;
	tk2dFontData_tFF80FF31AC59267FD1EB5A6D039BAEEE62ABC7CD* ____font;
	String_t* ____text;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ____color;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ____color2;
	bool ____useGradient;
	int32_t ____textureGradient;
	int32_t ____anchor;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ____scale;
	bool ____kerning;
	int32_t ____maxChars;
	bool ____inlineStyling;
	bool ____formatting;
	int32_t ____wordWrapWidth;
	float ___spacing;
	float ___lineSpacing;
	tk2dTextMeshData_t1FE2BE2F5BE79CFF6490FFA6161B563727A134C0* ___data;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___vertices;
	Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___uvs;
	Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___uv2;
	Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259* ___colors;
	Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259* ___untintedColors;
	int32_t ___updateFlags;
	Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___mesh;
	MeshFilter_t6D1CE2473A1E45AC73013400585A1163BF66B2F5* ___meshFilter;
	Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* ____cachedRenderer;
};
struct tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A  : public tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9
{
	Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___mesh;
	Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___meshUvs;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___meshVertices;
	Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259* ___meshColors;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___meshNormals;
	Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* ___meshTangents;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___meshIndices;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ____dimensions;
	int32_t ____anchor;
	bool ____borderOnly;
	bool ___legacyMode;
	float ___borderTop;
	float ___borderBottom;
	float ___borderLeft;
	float ___borderRight;
	bool ____createBoxCollider;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___boundsCenter;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___boundsExtents;
};
struct U3CPrivateImplementationDetailsU3E_t0F5473E849A5A5185A9F4C5246F0C32816C49FCA_StaticFields
{
	__StaticArrayInitTypeSizeU3D1024_t4A9D9610B3331D96BFF9BD065239C4091A2300C7 ___0053F121CDCAF9F510A940BA891FB2BCCD41487953C5A4C0048C4A3CCDEC782E;
	int64_t ___0D53D986AC9AC138A39C2D64E1588FCD87299A81C8ADD9ED9FD9D98955C8CCC5;
	int64_t ___0EEE47048E50C250A2ADDB45A92446A0E929B91307560A759A5CFD44AD29DCE6;
	__StaticArrayInitTypeSizeU3D12_t1BDD2193C3F925556BCD5FF35C0AC52DDB0CAB8F ___11047585FE102FBB5CADB42446612A578D88C6EF5ED076BB7AC360C4F9E4373D;
	__StaticArrayInitTypeSizeU3D1024_t4A9D9610B3331D96BFF9BD065239C4091A2300C7 ___12F3E0576D447EB37B36D82BA0C1C5481B8F0D12FDC70347CE4A076B229D4C86;
	__StaticArrayInitTypeSizeU3D16_tFB2D94E174C3DFBC336BBEE6AD92E07462831A23 ___1B897DDDD4C151E2A2E6E3E91B7EA0F7FC4FD5ED00EF1C9669E8566393A02586;
	int64_t ___1F4870B08C47BFF9029BB6078DAD53700D95E592F9ED91103FD75B89C4537112;
	__StaticArrayInitTypeSizeU3D10_tC38CF142534A070C2B94C80666D4D49680A1F92A ___217FEB1E7490015DD0A2B231B9CEA45804DF3D2A9B37287AC861BB45B8C0DE55;
	__StaticArrayInitTypeSizeU3D24_t3464DA68B6CCAB9A0A43F94B3DB9AA7E7FDDB19A ___2440A77103B18953231782052B2C977450D565D3DB7496A72C1CDA0843239115;
	__StaticArrayInitTypeSizeU3D40_t68A41E1D2BAA1C55857C26F7E0C26D1CFDB100B3 ___24A871635DEFEA3DE9E7D49E71368EA18360DDB67B37757D7744FECFDEB544F0;
	__StaticArrayInitTypeSizeU3D32_tC3894D25C1E879699FE1C9BAB1BBF2787B405069 ___26F3C77893FC3F8964CBA35075BDA269637C985992D3A57CC9E8D591C2A62884;
	__StaticArrayInitTypeSizeU3D20_tA394C0A7DC4F4F05D2190B09E23BDE8536718D72 ___2714FDD0223F1EBB8DDC20968F30141D02FF1C0AF3C8115F4D350C20F7B8AFD1;
	__StaticArrayInitTypeSizeU3D40_t68A41E1D2BAA1C55857C26F7E0C26D1CFDB100B3 ___272BC3456B7CE85DE2CE18D1964316879E840A1201A4664E967EF42BA3F76B96;
	int64_t ___2B33DEA6CD38D4CE686C1D154716D6B7EF94321C797CBEB771D5581E59CC061F;
	int64_t ___3375C0EBF42EB9560D400FCA92E7407C27D6246AD87E3AFAFB4AA10F3EDDDD1A;
	int64_t ___33ABAB3973FDD2036D9F9287F624B6D9ABD5DA6BFA58A02DADB7932188EFFF07;
	int64_t ___3629E8CDF2D4F629072F8463B3C49CD3AB93F695C4D4E33A9CABA6FB18EA8BAE;
	int64_t ___3CDF0387EAFF6E6E4EFF901E9E6CC7B4FA6358CC5AD83FF2BECCDBE64C4AA021;
	__StaticArrayInitTypeSizeU3D80_t144B9F4F4C74AB89365AB7716EE83DECD70D210D ___3D499AAB79D40FAD38F4892F505C8E34DA6FD7E911AC665D6AD93A48D1EAB445;
	int64_t ___429D2BF09BBACDEB57473E7C76B01E6BF292AD1523953244632D8B01D9FE9085;
	int64_t ___46CC7A59A429332A210CB4D933B1D4B5379398F0682CB775BC04F993C3D0B0AA;
	int64_t ___4F0AB0DA7BFC3F488135B803EF12279D108341F6F5872234676E438EA1F5BC44;
	int64_t ___50E92C11B512B57563B4519FC45CC36931BE5EBB5C58B969BAD1C740677C582D;
	__StaticArrayInitTypeSizeU3D216_tDB72A404E47DEF109BD8C32B757E36068C431C7F ___526ED3326CA6F397699F0AE14EA5FAF472CEB1380418E5C775FE4562730C9AA9;
	__StaticArrayInitTypeSizeU3D116_tC081A6DA1862FE80B878AA942235FC5929D2B4DA ___52AA14DBFAD385CC0A67E081C133BE3C353A46E2260E564186F0FEF33B8874E5;
	__StaticArrayInitTypeSizeU3D12_t1BDD2193C3F925556BCD5FF35C0AC52DDB0CAB8F ___53A37728D880062575E9F0AB8C6AB51BF69DD582F88E5F95E3BABB7C73A6729B;
	__StaticArrayInitTypeSizeU3D28_t523FB00435F599517548D4C121316CFE1B43E6C2 ___5B4C995B0EEAD687315B4557D06565438C0616C5F01016FEFD4A6546C5D9EB8C;
	int64_t ___6039A839576CB450A68D98D9B3803789853EEDB9111A2EA5E6BBA91F701FE191;
	__StaticArrayInitTypeSizeU3D12_t1BDD2193C3F925556BCD5FF35C0AC52DDB0CAB8F ___6116ACF9BA29EF61E63AF05766A8CCBC05D3DF52FE07AE0DBCD10FF1065B6938;
	int32_t ___63C1DD951FFEDF6F7FD968AD4EFA39B8ED584F162F46E715114EE184F8DE9201;
	int64_t ___6D62730626CCC8B49CA5CF81AF4F7D2EF875E741DA3D8C6570488AEA013473DA;
	int64_t ___72B74EFC99D3B924ADDEA31E8DF371AC2918DD200D914B018CB7350448F23F0C;
	int64_t ___7F5323E27833E5ACD4967E9A8E009CCC1465419B8A02C7D3C7EC3AA52E71B2D4;
	int64_t ___807911344F9807A1B582873D2F0F38B194E077DD886C3AFC830BC065EBF2CFAE;
	int64_t ___8908FCCC1DF13468816CA7A3A4FBB8A7F96FB3AC00BAA34D70C5397C9B88F9C4;
	int64_t ___89B71AE7D45DE935FC05FF4BF4987BE1426359E2C389AD6149699D8050D0F75B;
	__StaticArrayInitTypeSizeU3D40_t68A41E1D2BAA1C55857C26F7E0C26D1CFDB100B3 ___8A09971C787BEEE474BD54896939731E2A3355B6E32B43DBAD8C01B16989FA81;
	int64_t ___8DCFC69C77BE23ED6C460B27389CCC94F51C0A43E2947AD4FDFCBAA6C13EC607;
	__StaticArrayInitTypeSizeU3D56_tF90903548CF34D92C9F741BD6D3F8DC6C822EF4E ___8DECDB10AEFDB828B1325DD76119F2587EA785882269C22B7818DF39260394A9;
	int64_t ___8EE9327ECC967FC1AB49834169C4F6C562D46189C5CFF4C25C084E82FB924BFA;
	__StaticArrayInitTypeSizeU3D31768_tFAEF8528D703F42DBCBECCF86120227C3DA6743E ___8F44BB6317C0BF393D2D648BA2DFAED141A88C77ECB29E658CB9A4FCF41BCCFD;
	int64_t ___8F8DDC25F7C3822D3BE8C39FF1DE83005C8BAE2572F324A2F1E0E53AD5588E1E;
	int64_t ___9291CB95205633E925E98E9CB0DB4912E4A7BF5B18F7557D52090962CC045ED1;
	__StaticArrayInitTypeSizeU3D32_tC3894D25C1E879699FE1C9BAB1BBF2787B405069 ___95853D8881732E63777CF081EF757E3A1D025544145D488F88F1A2C2A6FCA008;
	__StaticArrayInitTypeSizeU3D16_tFB2D94E174C3DFBC336BBEE6AD92E07462831A23 ___9882EC2ABAAFA22264B143C3B6FF77932CC74EB5C754D4F8C955C52963713866;
	__StaticArrayInitTypeSizeU3D64_tC5DCC92F71FF4937EDBB6F52C59AD832172ABE88 ___98D1685B5E9C9FD24988D4F12B619330F10308FA20A82CC135A7D339A3B3F0CB;
	__StaticArrayInitTypeSizeU3D40_t68A41E1D2BAA1C55857C26F7E0C26D1CFDB100B3 ___9BF8B8F5278471CADD7855C9B61F9E373EE074000698135096C3092424366308;
	int64_t ___9C70DD87C645ADD1E55A15DD75BFC3980636D2EC17C832852F7616C8947C5524;
	__StaticArrayInitTypeSizeU3D144_t4839144B438E5DCF848D2BCCA695C05728CB3D82 ___9E1B156C3A8193314A207FA9E293B42D52F767BDB545347A9834511427AAE1D5;
	__StaticArrayInitTypeSizeU3D80_t144B9F4F4C74AB89365AB7716EE83DECD70D210D ___A0EAAFC684A8BCF5FEEDF47A528FCEACF98A57D5CE1A9FD18201BD07010ACA4A;
	__StaticArrayInitTypeSizeU3D40_t68A41E1D2BAA1C55857C26F7E0C26D1CFDB100B3 ___A7694C04CA4B4D91FB020E8B56FE479930289B8E7A568A1A5D7E0265C796AB8C;
	__StaticArrayInitTypeSizeU3D16_tFB2D94E174C3DFBC336BBEE6AD92E07462831A23 ___A7BECD43C1DE3B2E78F02487AF261536EF05E65091673703AE6BC720345CCF2D;
	int64_t ___A9C5757FBFD31E7C729ED509C347B743BED698564E003AEA467DBC483FA0F4D9;
	__StaticArrayInitTypeSizeU3D40_t68A41E1D2BAA1C55857C26F7E0C26D1CFDB100B3 ___ACC547F8987102813B5AE23A6DC6859C994D7A6E3BF0B577DF0947D6626A26DD;
	__StaticArrayInitTypeSizeU3D40_t68A41E1D2BAA1C55857C26F7E0C26D1CFDB100B3 ___AD018B622D687D58808818167BBDBB232166F3CE7C38A486E3C758467749DCCA;
	int64_t ___AD13E92D9C1D3305D4AFA5B2674500224AD34925FB6A767E825BD67C45332C3B;
	int64_t ___AE465A420BC05BA9297282F4201C8C25D7CBEFDCF3C220435F13110BCD642267;
	int64_t ___AFDFA8643900371FE41615B3C293ABD2511CC874390DF92EC2A79C227329333A;
	__StaticArrayInitTypeSizeU3D120_tCE5F35B252DE449EDE2BA38EFA855ABFBF94EF89 ___B23D510F520CB4BA8AFA847F8A40E757C40CB6A55B237EFA1AC6D3984911B114;
	int64_t ___B3C846C9496A4C09CD134A48383754195F07F2CBB7FF09A5F7D053B31B03141B;
	int64_t ___B586AA924B045960FF6BBC3D68DEA0A98B4B1BFFC64F90B9029AB72A5841856B;
	int64_t ___B99CF1EB09EDB7DC1869D9E6F54A8D60795752019177C8CF5F4E2755758C846F;
	__StaticArrayInitTypeSizeU3D216_tDB72A404E47DEF109BD8C32B757E36068C431C7F ___B99E19B575DAEA6926B690C9B8B622902A7992D460C7E78AE72C12DF8A1B76D9;
	__StaticArrayInitTypeSizeU3D53556_t488731EBAA87973895D4FC2B864DB8DE8E5FD543 ___BF3EBCBF161BA27DF027512F1A36BD81C4CE6747F527C328A79D4B49870C1586;
	__StaticArrayInitTypeSizeU3D76_tD26EB59D44A33303FCD119ABE6C31A2829EB22D6 ___C133E473E5E653C5C4AEDB8BCC1C1A3A44D384FC0B6C0FCF04672B1B325EC01B;
	int64_t ___C2AD3E40941848EBFD50C59D86CA2F0C6F10C5DBFAED314E8B3E49EB52C9B38E;
	int64_t ___C4508EFC736EAC1E91CA30F728EC3AE622E4360F93E6A7F557A5C7590C4E56DD;
	__StaticArrayInitTypeSizeU3D36_tE3135E025C70F21BBD65107243EE57F8AA699792 ___C4EB100C3C763F47842E818CE6C154E0FD34D2EC7108A3A68012BDB513BC7AE3;
	int64_t ___C94BE9E5997BFC7172BFF0E1698DA9ACC0DD14181040A90033F0AD0815979975;
	int64_t ___CAD276F8E0D6D4F23CBE291840AAC59B5D72E7D4A4078ED9719558145C37ADC3;
	__StaticArrayInitTypeSizeU3D40_t68A41E1D2BAA1C55857C26F7E0C26D1CFDB100B3 ___CD2382355D9226A8C1CEEB9A9C63F823E230005D8BCBB8CC159DB065D7FD954C;
	__StaticArrayInitTypeSizeU3D60_t28DCDB387E585965787FF852575673A4F7B6A5E3 ___CD8E463E5E9CF6D0137225E461115DA40B33770CF139A76AF1177A4258A72FB4;
	int64_t ___CED93B5430FE4CAFC5BFFF7C0A499B024657650D5A67770FD16C5CCB8C0D3B3A;
	int64_t ___DAC2C0AEB7C896D6D8D4B54DBCA3B807C3ABAD013DCEDFF4C5D4DB412DD0F5A0;
	int64_t ___DDC0D2A22A9CB07513EDC1C5FE5890BD09CF5FBD58DAE047D70E9D3BBEC7BF33;
	int64_t ___E2CB7C37304F6E20D427BA14E13C306CB5FB7B6CC9E6541A165225961BC36562;
	__StaticArrayInitTypeSizeU3D40_t68A41E1D2BAA1C55857C26F7E0C26D1CFDB100B3 ___E64067540138DE8416DC4C90B90909CE93ECE3A4914BDC46123843DE1698D5C4;
	int64_t ___E79ED44CAC3EC1E173133141A47D3BD3564B09E991F40E9F557DB8A1B064988B;
	int64_t ___EA544CA6FD079BF61EBA836428B5D9EF2662A238CE9DF540D8667148A53321B2;
	__StaticArrayInitTypeSizeU3D24_t3464DA68B6CCAB9A0A43F94B3DB9AA7E7FDDB19A ___ED3E5CB049537A123EB7C0CD83AC408BD4DFE1B1523744C40B7470762D8549A6;
	__StaticArrayInitTypeSizeU3D144_t4839144B438E5DCF848D2BCCA695C05728CB3D82 ___EE8382703A148BCFA8FCC7B32231B0FAF9F45E6183D559599369605124723AB9;
	__StaticArrayInitTypeSizeU3D40_t68A41E1D2BAA1C55857C26F7E0C26D1CFDB100B3 ___F287F1380EB445B32353E2806270F282A7CA37F5A2770C58F4A011F1328E334F;
	__StaticArrayInitTypeSizeU3D96_tE962D346DA0011435482BCE5513DB1E0D88A8DEB ___F358F089F2B1C4134F6EA2EC349D724D1A21F0A73CDBEE63915B1AA162F93EDF;
	__StaticArrayInitTypeSizeU3D12_t1BDD2193C3F925556BCD5FF35C0AC52DDB0CAB8F ___F35C6498936D148797AFC611942CDB3E7A76FDFCD170DBACCF908832DD8B46A8;
	__StaticArrayInitTypeSizeU3D96_tE962D346DA0011435482BCE5513DB1E0D88A8DEB ___F495DE3119355D6829B5961543C161B28E8B59163B01FD28A5FC95DE0757B653;
	__StaticArrayInitTypeSizeU3D116_tC081A6DA1862FE80B878AA942235FC5929D2B4DA ___F8D7861760C88CC514F66095AF0AED47ECBA063ADB65F47125ED07BCC2CF9842;
	__StaticArrayInitTypeSizeU3D2048_t0B1BD728889599A5E758B66FFCDC3437056644E1 ___FA6F7D5596F6084EE582060B76239C49C1BF8567F7EA556E2D83971C75E26951;
	__StaticArrayInitTypeSizeU3D120_tCE5F35B252DE449EDE2BA38EFA855ABFBF94EF89 ___FC216F5C5AE2947D800794ECD5F752EE8381073C2E5D0D095FDA040F541702F3;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17_StaticFields
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___s_categoryForLatin1;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct Mathf_tE284D016E3B297B72311AAD9EB8F0E643F6A4682_StaticFields
{
	float ___Epsilon;
};
struct SpiralPlugin_t0EC3955D810AAED4713FA63AAEACDA66063B604E_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___DefaultDirection;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___zeroVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___oneVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___upVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___downVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___leftVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___rightVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___positiveInfinityVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___negativeInfinityVector;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18  : public RuntimeArray
{
	ALIGN_FIELD (8) GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A m_Items[1];

	inline GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A value)
	{
		m_Items[index] = value;
	}
};


IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_gshared_inline (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Nullable_1_GetValueOrDefault_mD88C565C80170105E87BB423C5B2F974840EF90F_gshared_inline (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Nullable_1__ctor_m75F3ABB694E26670F021136BD3B9E71A65948BC2_gshared (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DOGetter_1__ctor_m59C11B3FE64C69454BE28721314FD6189629F27E_gshared (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DOSetter_1__ctor_m9E67232146D149892F1946395AD1B6B1C53F3B60_gshared (DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Nullable_1_get_Value_m6A74FA440FE386A9905C61B41B5C261CD9DC4792_gshared (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* DOTween_To_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisSpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D_m6716A7D71A3E516E4FB46A9B1C8AD83153E46190_gshared (ABSTweenPlugin_3_tF7CEB9BBF8FF28D2F3BBF9DB950849DCA238F80C* ___0_plugin, DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* ___1_getter, DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* ___2_setter, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___3_endValue, float ___4_duration, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* TweenSettingsExtensions_SetTarget_TisRuntimeObject_m864840DC4375075588F2154A96F118D5FAEE3FD6_gshared (RuntimeObject* ___0_t, RuntimeObject* ___1_target, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DOGetter_1__ctor_m7ABE4FFF2BF8792E4DA3957B81F6BD369634F5F3_gshared (DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DOSetter_1__ctor_m04DC997934D9BCA1419A9AA2F80DFE412B04B5AA_gshared (DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* TweenSettingsExtensions_SetEase_TisRuntimeObject_m28E0D61D2E7C5417FB6048D549C4A02BCABF3F46_gshared (RuntimeObject* ___0_t, int32_t ___1_ease, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DOGetter_1__ctor_m718AE8DCD0734283AD4D7B50ABBA14ED1F4A967C_gshared (DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DOSetter_1__ctor_m5034440D649E86F21337C9E4B543B0C6E97A058A_gshared (DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DOGetter_1__ctor_mFBB4FF82F75E78CFFE065CDF5AA2FCA8A8865B1B_gshared (DOGetter_1_t423CA9B377A96EA7C69ABA0C83C81B8D5B045E85* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DOSetter_1__ctor_m3A565A0966B975450B4DC292BD93FC69C770C935_gshared (DOSetter_1_t6889207117879689DE539960755CE5F277F65C0F* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpiralPlugin__ctor_m9ACFE0F9B14BBBD77A9570420667150892067033 (SpiralPlugin_t0EC3955D810AAED4713FA63AAEACDA66063B604E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass1_0__ctor_m806C59F8972EC150A820E385B593EA0B56B29B1F (U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Mathf_Approximately_m1DADD012A8FC82E11FB282501AE2EBBF9A77150B_inline (float ___0_a, float ___1_b, const RuntimeMethod* method) ;
inline bool Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_inline (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE*, const RuntimeMethod*))Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) ;
inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Nullable_1_GetValueOrDefault_mD88C565C80170105E87BB423C5B2F974840EF90F_inline (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE* __this, const RuntimeMethod* method)
{
	return ((  Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 (*) (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE*, const RuntimeMethod*))Nullable_1_GetValueOrDefault_mD88C565C80170105E87BB423C5B2F974840EF90F_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Vector3_op_Equality_mCDCBB8D2EDC3D3BF20F31A25ACB34705D352B479_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_forward_mAA55A7034304DF8B2152EAD49AE779FC4CA2EB4A_inline (const RuntimeMethod* method) ;
inline void Nullable_1__ctor_m75F3ABB694E26670F021136BD3B9E71A65948BC2 (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method)
{
	((  void (*) (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2, const RuntimeMethod*))Nullable_1__ctor_m75F3ABB694E26670F021136BD3B9E71A65948BC2_gshared)(__this, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ABSTweenPlugin_3_tF7CEB9BBF8FF28D2F3BBF9DB950849DCA238F80C* SpiralPlugin_Get_mEF40CC56A5043E8122C572AFCBC224BA925BC326 (const RuntimeMethod* method) ;
inline void DOGetter_1__ctor_m59C11B3FE64C69454BE28721314FD6189629F27E (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338*, RuntimeObject*, intptr_t, const RuntimeMethod*))DOGetter_1__ctor_m59C11B3FE64C69454BE28721314FD6189629F27E_gshared)(__this, ___0_object, ___1_method, method);
}
inline void DOSetter_1__ctor_m9E67232146D149892F1946395AD1B6B1C53F3B60 (DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358*, RuntimeObject*, intptr_t, const RuntimeMethod*))DOSetter_1__ctor_m9E67232146D149892F1946395AD1B6B1C53F3B60_gshared)(__this, ___0_object, ___1_method, method);
}
inline Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Nullable_1_get_Value_m6A74FA440FE386A9905C61B41B5C261CD9DC4792 (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE* __this, const RuntimeMethod* method)
{
	return ((  Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 (*) (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE*, const RuntimeMethod*))Nullable_1_get_Value_m6A74FA440FE386A9905C61B41B5C261CD9DC4792_gshared)(__this, method);
}
inline TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* DOTween_To_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisSpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D_m6716A7D71A3E516E4FB46A9B1C8AD83153E46190 (ABSTweenPlugin_3_tF7CEB9BBF8FF28D2F3BBF9DB950849DCA238F80C* ___0_plugin, DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* ___1_getter, DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* ___2_setter, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___3_endValue, float ___4_duration, const RuntimeMethod* method)
{
	return ((  TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* (*) (ABSTweenPlugin_3_tF7CEB9BBF8FF28D2F3BBF9DB950849DCA238F80C*, DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338*, DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358*, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2, float, const RuntimeMethod*))DOTween_To_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisSpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D_m6716A7D71A3E516E4FB46A9B1C8AD83153E46190_gshared)(___0_plugin, ___1_getter, ___2_setter, ___3_endValue, ___4_duration, method);
}
inline TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B_m593D7B49993413FC2357476183683A34122E55B6 (TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* ___0_t, RuntimeObject* ___1_target, const RuntimeMethod* method)
{
	return ((  TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* (*) (TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B*, RuntimeObject*, const RuntimeMethod*))TweenSettingsExtensions_SetTarget_TisRuntimeObject_m864840DC4375075588F2154A96F118D5FAEE3FD6_gshared)(___0_t, ___1_target, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass2_0__ctor_m7155F8E8DBB0B55CD44790BE6D26FFC39746D949 (U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_localPosition_mA9C86B990DF0685EA1061A120218993FDCC60A95 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_localPosition_mDE1C997F7D79C0885210B7732B4BA50EE7D73134 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691 (Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass0_0__ctor_m3DCDF7F523CE346B22687E5BBD8BF28993EFEE97 (U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* DOTween_To_m29FF04132DD76AFF686CF5DBCCD84CB6105A2DB8 (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* ___0_getter, DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* ___1_setter, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___2_endValue, float ___3_duration, const RuntimeMethod* method) ;
inline TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77_mA961948C0215BFB1350EBB68B4D0E372AC42B0E9 (TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* ___0_t, RuntimeObject* ___1_target, const RuntimeMethod* method)
{
	return ((  TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* (*) (TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77*, RuntimeObject*, const RuntimeMethod*))TweenSettingsExtensions_SetTarget_TisRuntimeObject_m864840DC4375075588F2154A96F118D5FAEE3FD6_gshared)(___0_t, ___1_target, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass1_0__ctor_m2AD49E6B44FD9230E899D07622140C602A179F54 (U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* TweenSettingsExtensions_SetOptions_m7900FA8CA54979FAA3D9C5EC907723B8E2CAF700 (TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* ___0_t, int32_t ___1_axisConstraint, bool ___2_snapping, const RuntimeMethod* method) ;
inline Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273 (Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* ___0_t, RuntimeObject* ___1_target, const RuntimeMethod* method)
{
	return ((  Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* (*) (Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140*, RuntimeObject*, const RuntimeMethod*))TweenSettingsExtensions_SetTarget_TisRuntimeObject_m864840DC4375075588F2154A96F118D5FAEE3FD6_gshared)(___0_t, ___1_target, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass2_0__ctor_m7871E27C561372F8D4E6F5D7CB7BE59CCD7C2E42 (U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass3_0__ctor_m6386D36D1AEEFE29C12A3DE1A860D634DF82F805 (U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass4_0__ctor_m001EF414C94F580BADFBFBECB888C1E161D9FE62 (U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F* __this, const RuntimeMethod* method) ;
inline void DOGetter_1__ctor_m7ABE4FFF2BF8792E4DA3957B81F6BD369634F5F3 (DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95*, RuntimeObject*, intptr_t, const RuntimeMethod*))DOGetter_1__ctor_m7ABE4FFF2BF8792E4DA3957B81F6BD369634F5F3_gshared)(__this, ___0_object, ___1_method, method);
}
inline void DOSetter_1__ctor_m04DC997934D9BCA1419A9AA2F80DFE412B04B5AA (DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89*, RuntimeObject*, intptr_t, const RuntimeMethod*))DOSetter_1__ctor_m04DC997934D9BCA1419A9AA2F80DFE412B04B5AA_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* DOTween_To_mA1EBEAF49DD682B626CB60B4C53B7069F7933339 (DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95* ___0_getter, DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89* ___1_setter, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___2_endValue, float ___3_duration, const RuntimeMethod* method) ;
inline TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83 (TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* ___0_t, RuntimeObject* ___1_target, const RuntimeMethod* method)
{
	return ((  TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* (*) (TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3*, RuntimeObject*, const RuntimeMethod*))TweenSettingsExtensions_SetTarget_TisRuntimeObject_m864840DC4375075588F2154A96F118D5FAEE3FD6_gshared)(___0_t, ___1_target, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass5_0__ctor_m92C6E5DA7E62C6B93E3C63FE7B904CDA2A18F891 (U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* DOTween_ToAlpha_m93AB0EDCF2956A0DD806D9537799F2727CA5E7C1 (DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95* ___0_getter, DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89* ___1_setter, float ___2_endValue, float ___3_duration, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* DOTween_Sequence_mC3AE53A08EC5DFD7DB9AE18A2FD11226A6611F89 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18* Gradient_get_colorKeys_mA4B24805A35BC4FBF04F182FCDA6E9D0CBB52F0A (Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void tk2dBaseSprite_set_color_m6EF2790F0EA2DA6075F79897A3CB36F2C1517DCC (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float TweenExtensions_Duration_mFF4115983798994675D0431ADD2D333958714222 (Tween_t8CB06EBC48A5B6F5065C490E4F4909C18CE7983C* ___0_t, bool ___1_includeLoops, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* ShortcutExtensionsTk2d_DOColor_m7E288F7F2111D33F381ADC0A055C8B69F2380185 (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___0_target, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___1_endValue, float ___2_duration, const RuntimeMethod* method) ;
inline TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* TweenSettingsExtensions_SetEase_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_m3605060ADD85D4E33C9F7D03BE6A676211A4A40B (TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* ___0_t, int32_t ___1_ease, const RuntimeMethod* method)
{
	return ((  TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* (*) (TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3*, int32_t, const RuntimeMethod*))TweenSettingsExtensions_SetEase_TisRuntimeObject_m28E0D61D2E7C5417FB6048D549C4A02BCABF3F46_gshared)(___0_t, ___1_ease, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* TweenSettingsExtensions_Append_mAFF074C9F7DE2BC8FF383C7729B13E732345F28F (Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* ___0_s, Tween_t8CB06EBC48A5B6F5065C490E4F4909C18CE7983C* ___1_t, const RuntimeMethod* method) ;
inline Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* TweenSettingsExtensions_SetTarget_TisSequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C_m87E21291C6C5BECA753976C5CA64050E8AFF93C0 (Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* ___0_t, RuntimeObject* ___1_target, const RuntimeMethod* method)
{
	return ((  Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* (*) (Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C*, RuntimeObject*, const RuntimeMethod*))TweenSettingsExtensions_SetTarget_TisRuntimeObject_m864840DC4375075588F2154A96F118D5FAEE3FD6_gshared)(___0_t, ___1_target, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass7_0__ctor_mB7C6C44FE24C8C4EF15D864888C74D38395CC71F (U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4* __this, const RuntimeMethod* method) ;
inline void DOGetter_1__ctor_m718AE8DCD0734283AD4D7B50ABBA14ED1F4A967C (DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66*, RuntimeObject*, intptr_t, const RuntimeMethod*))DOGetter_1__ctor_m718AE8DCD0734283AD4D7B50ABBA14ED1F4A967C_gshared)(__this, ___0_object, ___1_method, method);
}
inline void DOSetter_1__ctor_m5034440D649E86F21337C9E4B543B0C6E97A058A (DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C*, RuntimeObject*, intptr_t, const RuntimeMethod*))DOSetter_1__ctor_m5034440D649E86F21337C9E4B543B0C6E97A058A_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* DOTween_To_mAF71D4F17109689870406A4565636216CCFE7680 (DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66* ___0_getter, DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C* ___1_setter, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___2_endValue, float ___3_duration, const RuntimeMethod* method) ;
inline TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* TweenSettingsExtensions_SetTarget_TisTweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271_m7563CA4B8BD3EA41428B31557409B6456D19A876 (TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* ___0_t, RuntimeObject* ___1_target, const RuntimeMethod* method)
{
	return ((  TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* (*) (TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271*, RuntimeObject*, const RuntimeMethod*))TweenSettingsExtensions_SetTarget_TisRuntimeObject_m864840DC4375075588F2154A96F118D5FAEE3FD6_gshared)(___0_t, ___1_target, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass8_0__ctor_m816E3933D7E6928984BBF32E7440F680F5A602B8 (U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* TweenSettingsExtensions_SetOptions_mFD076C0377973484D8607E062753FE55478F0D0C (TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* ___0_t, int32_t ___1_axisConstraint, bool ___2_snapping, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass9_0__ctor_mD8F01F8BF18B27ABFEFFE494D8554397AEB900E7 (U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass10_0__ctor_mC24830B3EF6D421201A2B67B6F345E814CA92DCE (U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass11_0__ctor_mC6064122B712AAE22F50247E11C2E2C7D5FA5F82 (U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass12_0__ctor_mECD80E4CF3DF6DAB9E73F74810E01D8E289C50A3 (U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass13_0__ctor_mE2504E606342AA54BA63419A287046A32F6A0D98 (U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass14_0__ctor_mE3F2DAE2211E5AAE809E3F07C1BD309A8F5912FB (U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass15_0__ctor_mA9D4E74B4FDDE544C21A4027B57F54883B44D228 (U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void tk2dTextMesh_set_color_m799E8CF715E2DC98DCEC7F5228ABEC0CA49FEBB6 (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* ShortcutExtensionsTk2d_DOColor_mDA4EB39611D4BABB2694587398D062C7AE3540E5 (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___0_target, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___1_endValue, float ___2_duration, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass17_0__ctor_m8A37B45737FBFB0EBECE1E42095972C2E16766DC (U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771* __this, const RuntimeMethod* method) ;
inline void DOGetter_1__ctor_mC8C03B65352C1426F2985D1516464D9A6E48D1EC (DOGetter_1_t65A2B66606014CD7EFBA1D8DDD1273F57511D146* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (DOGetter_1_t65A2B66606014CD7EFBA1D8DDD1273F57511D146*, RuntimeObject*, intptr_t, const RuntimeMethod*))DOGetter_1__ctor_mFBB4FF82F75E78CFFE065CDF5AA2FCA8A8865B1B_gshared)(__this, ___0_object, ___1_method, method);
}
inline void DOSetter_1__ctor_mB9C5014DEF58C45C98E2B48F7D6CE677F7213C92 (DOSetter_1_t999572008E4C4CFE5EF6FA70393F8B092CA8C438* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (DOSetter_1_t999572008E4C4CFE5EF6FA70393F8B092CA8C438*, RuntimeObject*, intptr_t, const RuntimeMethod*))DOSetter_1__ctor_m3A565A0966B975450B4DC292BD93FC69C770C935_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_t390910F1CE8B4737AA72FEED470399C8DBE1AF8B* DOTween_To_mA4E61D06204BD01537C08EEB9ED148C18ABC75ED (DOGetter_1_t65A2B66606014CD7EFBA1D8DDD1273F57511D146* ___0_getter, DOSetter_1_t999572008E4C4CFE5EF6FA70393F8B092CA8C438* ___1_setter, String_t* ___2_endValue, float ___3_duration, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* TweenSettingsExtensions_SetOptions_m571339B54CA3A4BD935C07D719EE40E4FAE2C9D2 (TweenerCore_3_t390910F1CE8B4737AA72FEED470399C8DBE1AF8B* ___0_t, bool ___1_richTextEnabled, int32_t ___2_scrambleMode, String_t* ___3_scrambleChars, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 tk2dBaseSprite_get_scale_m8153F8E8C54C8EB55A2D424F6331439DC3FB18EE_inline (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void tk2dBaseSprite_set_scale_m43940DD4B75709F029B05528C36ED1BC8DD41C54 (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 tk2dTextMesh_get_scale_m48359277404F95EA3990B0CE2EC5E4AC47FC5786 (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void tk2dTextMesh_set_scale_m33B83CAA05B8291649859DE73259EBCBCBA2EFDE (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F tk2dTextMesh_get_color_m976F5079A5B69CCD7DC113244F4E2665B2B5B13F (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* tk2dTextMesh_get_text_mFCE4C41DE352006CA98F7D103FEF4DC898488A42 (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void tk2dTextMesh_set_text_mB3FD5D7308FE7C059EC5221451A432518AD66ECC (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F tk2dBaseSprite_get_color_mA97E08381A326A50501CEBBE6EFCA1AA7EA5C644_inline (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 tk2dSlicedSprite_get_dimensions_mB99F1D298B3711CEF6C66A6C6377F229FDC5CE89_inline (tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void tk2dSlicedSprite_set_dimensions_m8DC2C68724409319CCFE80306A0BF9E06B79C35F (tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Il2CppChar String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3 (String_t* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline (String_t* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline (float ___0_a, float ___1_b, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DOTweenProShortcuts__cctor_mD1D6CBBD34A6CC96A78828D0768D55E38AB937A2 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpiralPlugin_t0EC3955D810AAED4713FA63AAEACDA66063B604E_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		SpiralPlugin_t0EC3955D810AAED4713FA63AAEACDA66063B604E* L_0 = (SpiralPlugin_t0EC3955D810AAED4713FA63AAEACDA66063B604E*)il2cpp_codegen_object_new(SpiralPlugin_t0EC3955D810AAED4713FA63AAEACDA66063B604E_il2cpp_TypeInfo_var);
		SpiralPlugin__ctor_m9ACFE0F9B14BBBD77A9570420667150892067033(L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* DOTweenProShortcuts_DOSpiral_m827EE82F8C0956D89D704AE0E0637B15C8EB8644 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_target, float ___1_duration, Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE ___2_axis, int32_t ___3_mode, float ___4_speed, float ___5_frequency, float ___6_depth, bool ___7_snapping, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_To_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisSpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D_m6716A7D71A3E516E4FB46A9B1C8AD83153E46190_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_GetValueOrDefault_mD88C565C80170105E87BB423C5B2F974840EF90F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1__ctor_m75F3ABB694E26670F021136BD3B9E71A65948BC2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_get_Value_m6A74FA440FE386A9905C61B41B5C261CD9DC4792_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpiralPlugin_t0EC3955D810AAED4713FA63AAEACDA66063B604E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B_m593D7B49993413FC2357476183683A34122E55B6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass1_0_U3CDOSpiralU3Eb__0_m1CCDACC82D59B9DD6DCEEE277BE3D1C410B40CEE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass1_0_U3CDOSpiralU3Eb__1_m2EA9119E181F78B27E9B32146D9861ABD0C3F7E8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C* V_0 = NULL;
	Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE V_1;
	memset((&V_1), 0, sizeof(V_1));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_2;
	memset((&V_2), 0, sizeof(V_2));
	int32_t G_B8_0 = 0;
	{
		U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C* L_0 = (U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass1_0__ctor_m806C59F8972EC150A820E385B593EA0B56B29B1F(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C* L_1 = V_0;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		float L_3 = ___4_speed;
		bool L_4;
		L_4 = Mathf_Approximately_m1DADD012A8FC82E11FB282501AE2EBBF9A77150B_inline(L_3, (0.0f), NULL);
		if (!L_4)
		{
			goto IL_0022;
		}
	}
	{
		___4_speed = (1.0f);
	}

IL_0022:
	{
		bool L_5;
		L_5 = Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_inline((&___2_axis), Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_RuntimeMethod_var);
		if (!L_5)
		{
			goto IL_005a;
		}
	}
	{
		Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE L_6 = ___2_axis;
		V_1 = L_6;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline(NULL);
		V_2 = L_7;
		bool L_8;
		L_8 = Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_inline((&V_1), Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_RuntimeMethod_var);
		if (L_8)
		{
			goto IL_003f;
		}
	}
	{
		G_B8_0 = 0;
		goto IL_0058;
	}

IL_003f:
	{
		bool L_9;
		L_9 = Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_inline((&V_1), Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_RuntimeMethod_var);
		if (L_9)
		{
			goto IL_004b;
		}
	}
	{
		G_B8_0 = 1;
		goto IL_0058;
	}

IL_004b:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10;
		L_10 = Nullable_1_GetValueOrDefault_mD88C565C80170105E87BB423C5B2F974840EF90F_inline((&V_1), Nullable_1_GetValueOrDefault_mD88C565C80170105E87BB423C5B2F974840EF90F_RuntimeMethod_var);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11 = V_2;
		bool L_12;
		L_12 = Vector3_op_Equality_mCDCBB8D2EDC3D3BF20F31A25ACB34705D352B479_inline(L_10, L_11, NULL);
		G_B8_0 = ((int32_t)(L_12));
	}

IL_0058:
	{
		if (!G_B8_0)
		{
			goto IL_0066;
		}
	}

IL_005a:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13;
		L_13 = Vector3_get_forward_mAA55A7034304DF8B2152EAD49AE779FC4CA2EB4A_inline(NULL);
		Nullable_1__ctor_m75F3ABB694E26670F021136BD3B9E71A65948BC2((&___2_axis), L_13, Nullable_1__ctor_m75F3ABB694E26670F021136BD3B9E71A65948BC2_RuntimeMethod_var);
	}

IL_0066:
	{
		il2cpp_codegen_runtime_class_init_inline(SpiralPlugin_t0EC3955D810AAED4713FA63AAEACDA66063B604E_il2cpp_TypeInfo_var);
		ABSTweenPlugin_3_tF7CEB9BBF8FF28D2F3BBF9DB950849DCA238F80C* L_14;
		L_14 = SpiralPlugin_Get_mEF40CC56A5043E8122C572AFCBC224BA925BC326(NULL);
		U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C* L_15 = V_0;
		DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* L_16 = (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338*)il2cpp_codegen_object_new(DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m59C11B3FE64C69454BE28721314FD6189629F27E(L_16, L_15, (intptr_t)((void*)U3CU3Ec__DisplayClass1_0_U3CDOSpiralU3Eb__0_m1CCDACC82D59B9DD6DCEEE277BE3D1C410B40CEE_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C* L_17 = V_0;
		DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* L_18 = (DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358*)il2cpp_codegen_object_new(DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m9E67232146D149892F1946395AD1B6B1C53F3B60(L_18, L_17, (intptr_t)((void*)U3CU3Ec__DisplayClass1_0_U3CDOSpiralU3Eb__1_m2EA9119E181F78B27E9B32146D9861ABD0C3F7E8_RuntimeMethod_var), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_19;
		L_19 = Nullable_1_get_Value_m6A74FA440FE386A9905C61B41B5C261CD9DC4792((&___2_axis), Nullable_1_get_Value_m6A74FA440FE386A9905C61B41B5C261CD9DC4792_RuntimeMethod_var);
		float L_20 = ___1_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_21;
		L_21 = DOTween_To_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisSpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D_m6716A7D71A3E516E4FB46A9B1C8AD83153E46190(L_14, L_16, L_18, L_19, L_20, DOTween_To_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisSpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D_m6716A7D71A3E516E4FB46A9B1C8AD83153E46190_RuntimeMethod_var);
		U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C* L_22 = V_0;
		NullCheck(L_22);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_23 = L_22->___target;
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_24;
		L_24 = TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B_m593D7B49993413FC2357476183683A34122E55B6(L_21, L_23, TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B_m593D7B49993413FC2357476183683A34122E55B6_RuntimeMethod_var);
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_25 = L_24;
		NullCheck(L_25);
		SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D* L_26 = (SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D*)(&L_25->___plugOptions);
		int32_t L_27 = ___3_mode;
		L_26->___mode = L_27;
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_28 = L_25;
		NullCheck(L_28);
		SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D* L_29 = (SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D*)(&L_28->___plugOptions);
		float L_30 = ___4_speed;
		L_29->___speed = L_30;
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_31 = L_28;
		NullCheck(L_31);
		SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D* L_32 = (SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D*)(&L_31->___plugOptions);
		float L_33 = ___5_frequency;
		L_32->___frequency = L_33;
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_34 = L_31;
		NullCheck(L_34);
		SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D* L_35 = (SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D*)(&L_34->___plugOptions);
		float L_36 = ___6_depth;
		L_35->___depth = L_36;
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_37 = L_34;
		NullCheck(L_37);
		SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D* L_38 = (SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D*)(&L_37->___plugOptions);
		bool L_39 = ___7_snapping;
		L_38->___snapping = L_39;
		return L_37;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* DOTweenProShortcuts_DOSpiral_m488014602B5973D1E48641A8CCE1A026D8C14A52 (Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* ___0_target, float ___1_duration, Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE ___2_axis, int32_t ___3_mode, float ___4_speed, float ___5_frequency, float ___6_depth, bool ___7_snapping, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_To_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisSpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D_m6716A7D71A3E516E4FB46A9B1C8AD83153E46190_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_GetValueOrDefault_mD88C565C80170105E87BB423C5B2F974840EF90F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1__ctor_m75F3ABB694E26670F021136BD3B9E71A65948BC2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Nullable_1_get_Value_m6A74FA440FE386A9905C61B41B5C261CD9DC4792_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rigidbody_MovePosition_mB2CD29ABC8F59AC338C0A3A5A6B75C38FDA92CA9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SpiralPlugin_t0EC3955D810AAED4713FA63AAEACDA66063B604E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B_m593D7B49993413FC2357476183683A34122E55B6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass2_0_U3CDOSpiralU3Eb__0_mA0C2085BFC6B0541C15180DAD8844FAFBF8E303C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B* V_0 = NULL;
	Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE V_1;
	memset((&V_1), 0, sizeof(V_1));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_2;
	memset((&V_2), 0, sizeof(V_2));
	int32_t G_B8_0 = 0;
	{
		U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B* L_0 = (U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass2_0__ctor_m7155F8E8DBB0B55CD44790BE6D26FFC39746D949(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B* L_1 = V_0;
		Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		float L_3 = ___4_speed;
		bool L_4;
		L_4 = Mathf_Approximately_m1DADD012A8FC82E11FB282501AE2EBBF9A77150B_inline(L_3, (0.0f), NULL);
		if (!L_4)
		{
			goto IL_0022;
		}
	}
	{
		___4_speed = (1.0f);
	}

IL_0022:
	{
		bool L_5;
		L_5 = Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_inline((&___2_axis), Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_RuntimeMethod_var);
		if (!L_5)
		{
			goto IL_005a;
		}
	}
	{
		Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE L_6 = ___2_axis;
		V_1 = L_6;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline(NULL);
		V_2 = L_7;
		bool L_8;
		L_8 = Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_inline((&V_1), Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_RuntimeMethod_var);
		if (L_8)
		{
			goto IL_003f;
		}
	}
	{
		G_B8_0 = 0;
		goto IL_0058;
	}

IL_003f:
	{
		bool L_9;
		L_9 = Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_inline((&V_1), Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_RuntimeMethod_var);
		if (L_9)
		{
			goto IL_004b;
		}
	}
	{
		G_B8_0 = 1;
		goto IL_0058;
	}

IL_004b:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10;
		L_10 = Nullable_1_GetValueOrDefault_mD88C565C80170105E87BB423C5B2F974840EF90F_inline((&V_1), Nullable_1_GetValueOrDefault_mD88C565C80170105E87BB423C5B2F974840EF90F_RuntimeMethod_var);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11 = V_2;
		bool L_12;
		L_12 = Vector3_op_Equality_mCDCBB8D2EDC3D3BF20F31A25ACB34705D352B479_inline(L_10, L_11, NULL);
		G_B8_0 = ((int32_t)(L_12));
	}

IL_0058:
	{
		if (!G_B8_0)
		{
			goto IL_0066;
		}
	}

IL_005a:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13;
		L_13 = Vector3_get_forward_mAA55A7034304DF8B2152EAD49AE779FC4CA2EB4A_inline(NULL);
		Nullable_1__ctor_m75F3ABB694E26670F021136BD3B9E71A65948BC2((&___2_axis), L_13, Nullable_1__ctor_m75F3ABB694E26670F021136BD3B9E71A65948BC2_RuntimeMethod_var);
	}

IL_0066:
	{
		il2cpp_codegen_runtime_class_init_inline(SpiralPlugin_t0EC3955D810AAED4713FA63AAEACDA66063B604E_il2cpp_TypeInfo_var);
		ABSTweenPlugin_3_tF7CEB9BBF8FF28D2F3BBF9DB950849DCA238F80C* L_14;
		L_14 = SpiralPlugin_Get_mEF40CC56A5043E8122C572AFCBC224BA925BC326(NULL);
		U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B* L_15 = V_0;
		DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* L_16 = (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338*)il2cpp_codegen_object_new(DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m59C11B3FE64C69454BE28721314FD6189629F27E(L_16, L_15, (intptr_t)((void*)U3CU3Ec__DisplayClass2_0_U3CDOSpiralU3Eb__0_mA0C2085BFC6B0541C15180DAD8844FAFBF8E303C_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B* L_17 = V_0;
		NullCheck(L_17);
		Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* L_18 = L_17->___target;
		DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* L_19 = (DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358*)il2cpp_codegen_object_new(DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m9E67232146D149892F1946395AD1B6B1C53F3B60(L_19, L_18, (intptr_t)((void*)Rigidbody_MovePosition_mB2CD29ABC8F59AC338C0A3A5A6B75C38FDA92CA9_RuntimeMethod_var), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_20;
		L_20 = Nullable_1_get_Value_m6A74FA440FE386A9905C61B41B5C261CD9DC4792((&___2_axis), Nullable_1_get_Value_m6A74FA440FE386A9905C61B41B5C261CD9DC4792_RuntimeMethod_var);
		float L_21 = ___1_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_22;
		L_22 = DOTween_To_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisSpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D_m6716A7D71A3E516E4FB46A9B1C8AD83153E46190(L_14, L_16, L_19, L_20, L_21, DOTween_To_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisVector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_TisSpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D_m6716A7D71A3E516E4FB46A9B1C8AD83153E46190_RuntimeMethod_var);
		U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B* L_23 = V_0;
		NullCheck(L_23);
		Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* L_24 = L_23->___target;
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_25;
		L_25 = TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B_m593D7B49993413FC2357476183683A34122E55B6(L_22, L_24, TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B_m593D7B49993413FC2357476183683A34122E55B6_RuntimeMethod_var);
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_26 = L_25;
		NullCheck(L_26);
		SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D* L_27 = (SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D*)(&L_26->___plugOptions);
		int32_t L_28 = ___3_mode;
		L_27->___mode = L_28;
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_29 = L_26;
		NullCheck(L_29);
		SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D* L_30 = (SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D*)(&L_29->___plugOptions);
		float L_31 = ___4_speed;
		L_30->___speed = L_31;
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_32 = L_29;
		NullCheck(L_32);
		SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D* L_33 = (SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D*)(&L_32->___plugOptions);
		float L_34 = ___5_frequency;
		L_33->___frequency = L_34;
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_35 = L_32;
		NullCheck(L_35);
		SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D* L_36 = (SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D*)(&L_35->___plugOptions);
		float L_37 = ___6_depth;
		L_36->___depth = L_37;
		TweenerCore_3_tE784F67C453B80BE098F39F499AD7BE5D4F1657B* L_38 = L_35;
		NullCheck(L_38);
		SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D* L_39 = (SpiralOptions_t4D382379BCA86778126603AE4412B2C6D8DB3F1D*)(&L_38->___plugOptions);
		bool L_40 = ___7_snapping;
		L_39->___snapping = L_40;
		return L_38;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass1_0__ctor_m806C59F8972EC150A820E385B593EA0B56B29B1F (U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec__DisplayClass1_0_U3CDOSpiralU3Eb__0_m1CCDACC82D59B9DD6DCEEE277BE3D1C410B40CEE (U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C* __this, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0 = __this->___target;
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Transform_get_localPosition_mA9C86B990DF0685EA1061A120218993FDCC60A95(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass1_0_U3CDOSpiralU3Eb__1_m2EA9119E181F78B27E9B32146D9861ABD0C3F7E8 (U3CU3Ec__DisplayClass1_0_tB5F45687C977F70DAF77F838AE9774426652A19C* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_x, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0 = __this->___target;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___0_x;
		NullCheck(L_0);
		Transform_set_localPosition_mDE1C997F7D79C0885210B7732B4BA50EE7D73134(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass2_0__ctor_m7155F8E8DBB0B55CD44790BE6D26FFC39746D949 (U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec__DisplayClass2_0_U3CDOSpiralU3Eb__0_mA0C2085BFC6B0541C15180DAD8844FAFBF8E303C (U3CU3Ec__DisplayClass2_0_t1295D5B080BAB3F595980E470DA1E9BAF1B4967B* __this, const RuntimeMethod* method) 
{
	{
		Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* L_0 = __this->___target;
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691(L_0, NULL);
		return L_1;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* ShortcutExtensionsTk2d_DOScale_m608664E0CDFCDE332FDF1510A8A8F5069D59356C (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___0_target, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77_mA961948C0215BFB1350EBB68B4D0E372AC42B0E9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass0_0_U3CDOScaleU3Eb__0_mB5E48C8A184950151A5169CCDF186E26E0FCB61C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass0_0_U3CDOScaleU3Eb__1_m61F08DCCA26B7B69804A8ACA70178B684EC9D1C7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2* L_0 = (U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass0_0__ctor_m3DCDF7F523CE346B22687E5BBD8BF28993EFEE97(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2* L_1 = V_0;
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2* L_3 = V_0;
		DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* L_4 = (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338*)il2cpp_codegen_object_new(DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m59C11B3FE64C69454BE28721314FD6189629F27E(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass0_0_U3CDOScaleU3Eb__0_mB5E48C8A184950151A5169CCDF186E26E0FCB61C_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2* L_5 = V_0;
		DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* L_6 = (DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358*)il2cpp_codegen_object_new(DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m9E67232146D149892F1946395AD1B6B1C53F3B60(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass0_0_U3CDOScaleU3Eb__1_m61F08DCCA26B7B69804A8ACA70178B684EC9D1C7_RuntimeMethod_var), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = ___1_endValue;
		float L_8 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_9;
		L_9 = DOTween_To_m29FF04132DD76AFF686CF5DBCCD84CB6105A2DB8(L_4, L_6, L_7, L_8, NULL);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_10 = L_9;
		U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2* L_11 = V_0;
		NullCheck(L_11);
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_12 = L_11->___target;
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_13;
		L_13 = TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77_mA961948C0215BFB1350EBB68B4D0E372AC42B0E9(L_10, L_12, TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77_mA961948C0215BFB1350EBB68B4D0E372AC42B0E9_RuntimeMethod_var);
		return L_10;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* ShortcutExtensionsTk2d_DOScaleX_m8C460D7442039B4E9CBD947890E1490EBC811163 (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___0_target, float ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass1_0_U3CDOScaleXU3Eb__0_m8D63B2F8E7800BDC72C7759B8C7205CCB3AD23DB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass1_0_U3CDOScaleXU3Eb__1_m2D9D6D522C9D987353FB914C4B03BA2F346C18A4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3* L_0 = (U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass1_0__ctor_m2AD49E6B44FD9230E899D07622140C602A179F54(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3* L_1 = V_0;
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3* L_3 = V_0;
		DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* L_4 = (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338*)il2cpp_codegen_object_new(DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m59C11B3FE64C69454BE28721314FD6189629F27E(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass1_0_U3CDOScaleXU3Eb__0_m8D63B2F8E7800BDC72C7759B8C7205CCB3AD23DB_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3* L_5 = V_0;
		DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* L_6 = (DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358*)il2cpp_codegen_object_new(DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m9E67232146D149892F1946395AD1B6B1C53F3B60(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass1_0_U3CDOScaleXU3Eb__1_m2D9D6D522C9D987353FB914C4B03BA2F346C18A4_RuntimeMethod_var), NULL);
		float L_7 = ___1_endValue;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_8), L_7, (0.0f), (0.0f), NULL);
		float L_9 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_10;
		L_10 = DOTween_To_m29FF04132DD76AFF686CF5DBCCD84CB6105A2DB8(L_4, L_6, L_8, L_9, NULL);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_11 = L_10;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_12;
		L_12 = TweenSettingsExtensions_SetOptions_m7900FA8CA54979FAA3D9C5EC907723B8E2CAF700(L_11, 2, (bool)0, NULL);
		U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3* L_13 = V_0;
		NullCheck(L_13);
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_14 = L_13->___target;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_15;
		L_15 = TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273(L_12, L_14, TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* ShortcutExtensionsTk2d_DOScaleY_mD990293B88ED03DAB16004F6CA95286A1A290620 (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___0_target, float ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass2_0_U3CDOScaleYU3Eb__0_mEE879668EE37DBAC46170ECE00C87B30B84D3971_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass2_0_U3CDOScaleYU3Eb__1_m368AB6696597DF5DD1B191C269D6B6E4BEDB5672_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D* L_0 = (U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass2_0__ctor_m7871E27C561372F8D4E6F5D7CB7BE59CCD7C2E42(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D* L_1 = V_0;
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D* L_3 = V_0;
		DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* L_4 = (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338*)il2cpp_codegen_object_new(DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m59C11B3FE64C69454BE28721314FD6189629F27E(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass2_0_U3CDOScaleYU3Eb__0_mEE879668EE37DBAC46170ECE00C87B30B84D3971_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D* L_5 = V_0;
		DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* L_6 = (DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358*)il2cpp_codegen_object_new(DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m9E67232146D149892F1946395AD1B6B1C53F3B60(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass2_0_U3CDOScaleYU3Eb__1_m368AB6696597DF5DD1B191C269D6B6E4BEDB5672_RuntimeMethod_var), NULL);
		float L_7 = ___1_endValue;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_8), (0.0f), L_7, (0.0f), NULL);
		float L_9 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_10;
		L_10 = DOTween_To_m29FF04132DD76AFF686CF5DBCCD84CB6105A2DB8(L_4, L_6, L_8, L_9, NULL);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_11 = L_10;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_12;
		L_12 = TweenSettingsExtensions_SetOptions_m7900FA8CA54979FAA3D9C5EC907723B8E2CAF700(L_11, 4, (bool)0, NULL);
		U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D* L_13 = V_0;
		NullCheck(L_13);
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_14 = L_13->___target;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_15;
		L_15 = TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273(L_12, L_14, TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* ShortcutExtensionsTk2d_DOScaleZ_m4B537E7ACD4520C3851A0F006F52A736A268E47B (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___0_target, float ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass3_0_U3CDOScaleZU3Eb__0_m112F0998AFD6CA41F674A89B1F7A960289BB080A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass3_0_U3CDOScaleZU3Eb__1_m971AD755BEE5DDC5E148E080B12D16011899378A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47* L_0 = (U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass3_0__ctor_m6386D36D1AEEFE29C12A3DE1A860D634DF82F805(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47* L_1 = V_0;
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47* L_3 = V_0;
		DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* L_4 = (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338*)il2cpp_codegen_object_new(DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m59C11B3FE64C69454BE28721314FD6189629F27E(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass3_0_U3CDOScaleZU3Eb__0_m112F0998AFD6CA41F674A89B1F7A960289BB080A_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47* L_5 = V_0;
		DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* L_6 = (DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358*)il2cpp_codegen_object_new(DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m9E67232146D149892F1946395AD1B6B1C53F3B60(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass3_0_U3CDOScaleZU3Eb__1_m971AD755BEE5DDC5E148E080B12D16011899378A_RuntimeMethod_var), NULL);
		float L_7 = ___1_endValue;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_8), (0.0f), (0.0f), L_7, NULL);
		float L_9 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_10;
		L_10 = DOTween_To_m29FF04132DD76AFF686CF5DBCCD84CB6105A2DB8(L_4, L_6, L_8, L_9, NULL);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_11 = L_10;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_12;
		L_12 = TweenSettingsExtensions_SetOptions_m7900FA8CA54979FAA3D9C5EC907723B8E2CAF700(L_11, 8, (bool)0, NULL);
		U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47* L_13 = V_0;
		NullCheck(L_13);
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_14 = L_13->___target;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_15;
		L_15 = TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273(L_12, L_14, TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* ShortcutExtensionsTk2d_DOColor_m7E288F7F2111D33F381ADC0A055C8B69F2380185 (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___0_target, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass4_0_U3CDOColorU3Eb__0_mAAB45A5C5FB51D6F74120F9A1008350106669C3E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass4_0_U3CDOColorU3Eb__1_m7F6BAAAD699548BA72437E8C8538FDA2AE561D60_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F* L_0 = (U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass4_0__ctor_m001EF414C94F580BADFBFBECB888C1E161D9FE62(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F* L_1 = V_0;
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F* L_3 = V_0;
		DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95* L_4 = (DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95*)il2cpp_codegen_object_new(DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m7ABE4FFF2BF8792E4DA3957B81F6BD369634F5F3(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass4_0_U3CDOColorU3Eb__0_mAAB45A5C5FB51D6F74120F9A1008350106669C3E_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F* L_5 = V_0;
		DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89* L_6 = (DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89*)il2cpp_codegen_object_new(DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m04DC997934D9BCA1419A9AA2F80DFE412B04B5AA(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass4_0_U3CDOColorU3Eb__1_m7F6BAAAD699548BA72437E8C8538FDA2AE561D60_RuntimeMethod_var), NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_7 = ___1_endValue;
		float L_8 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_9;
		L_9 = DOTween_To_mA1EBEAF49DD682B626CB60B4C53B7069F7933339(L_4, L_6, L_7, L_8, NULL);
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_10 = L_9;
		U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F* L_11 = V_0;
		NullCheck(L_11);
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_12 = L_11->___target;
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_13;
		L_13 = TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83(L_10, L_12, TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83_RuntimeMethod_var);
		return L_10;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* ShortcutExtensionsTk2d_DOFade_m133206E1E91C32A79C415609C35D776FD70078F9 (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___0_target, float ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass5_0_U3CDOFadeU3Eb__0_mE1BEBDD002383619C46034FE7CA77504024F5175_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass5_0_U3CDOFadeU3Eb__1_mD6295447001F737B10D4903A4B9869EFBD7EE432_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B* L_0 = (U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass5_0__ctor_m92C6E5DA7E62C6B93E3C63FE7B904CDA2A18F891(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B* L_1 = V_0;
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B* L_3 = V_0;
		DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95* L_4 = (DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95*)il2cpp_codegen_object_new(DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m7ABE4FFF2BF8792E4DA3957B81F6BD369634F5F3(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass5_0_U3CDOFadeU3Eb__0_mE1BEBDD002383619C46034FE7CA77504024F5175_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B* L_5 = V_0;
		DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89* L_6 = (DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89*)il2cpp_codegen_object_new(DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m04DC997934D9BCA1419A9AA2F80DFE412B04B5AA(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass5_0_U3CDOFadeU3Eb__1_mD6295447001F737B10D4903A4B9869EFBD7EE432_RuntimeMethod_var), NULL);
		float L_7 = ___1_endValue;
		float L_8 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_9;
		L_9 = DOTween_ToAlpha_m93AB0EDCF2956A0DD806D9537799F2727CA5E7C1(L_4, L_6, L_7, L_8, NULL);
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_10 = L_9;
		U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B* L_11 = V_0;
		NullCheck(L_11);
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_12 = L_11->___target;
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_13;
		L_13 = TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83(L_10, L_12, TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83_RuntimeMethod_var);
		return L_10;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* ShortcutExtensionsTk2d_DOGradientColor_m04C58E793077134767E21E642015B12B91556BA6 (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* ___0_target, Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E* ___1_gradient, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetEase_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_m3605060ADD85D4E33C9F7D03BE6A676211A4A40B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisSequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C_m87E21291C6C5BECA753976C5CA64050E8AFF93C0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* V_0 = NULL;
	GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18* V_1 = NULL;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A V_4;
	memset((&V_4), 0, sizeof(V_4));
	float V_5 = 0.0f;
	float G_B7_0 = 0.0f;
	float G_B6_0 = 0.0f;
	float G_B8_0 = 0.0f;
	float G_B8_1 = 0.0f;
	float G_B10_0 = 0.0f;
	{
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_0;
		L_0 = DOTween_Sequence_mC3AE53A08EC5DFD7DB9AE18A2FD11226A6611F89(NULL);
		V_0 = L_0;
		Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E* L_1 = ___1_gradient;
		NullCheck(L_1);
		GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18* L_2;
		L_2 = Gradient_get_colorKeys_mA4B24805A35BC4FBF04F182FCDA6E9D0CBB52F0A(L_1, NULL);
		V_1 = L_2;
		GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18* L_3 = V_1;
		NullCheck(L_3);
		V_2 = ((int32_t)(((RuntimeArray*)L_3)->max_length));
		V_3 = 0;
		goto IL_0098;
	}

IL_0018:
	{
		GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18* L_4 = V_1;
		int32_t L_5 = V_3;
		NullCheck(L_4);
		int32_t L_6 = L_5;
		GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A L_7 = (L_4)->GetAt(static_cast<il2cpp_array_size_t>(L_6));
		V_4 = L_7;
		int32_t L_8 = V_3;
		if (L_8)
		{
			goto IL_0041;
		}
	}
	{
		GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A L_9 = V_4;
		float L_10 = L_9.___time;
		if ((!(((float)L_10) <= ((float)(0.0f)))))
		{
			goto IL_0041;
		}
	}
	{
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_11 = ___0_target;
		GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A L_12 = V_4;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_13 = L_12.___color;
		NullCheck(L_11);
		tk2dBaseSprite_set_color_m6EF2790F0EA2DA6075F79897A3CB36F2C1517DCC(L_11, L_13, NULL);
		goto IL_0094;
	}

IL_0041:
	{
		int32_t L_14 = V_3;
		int32_t L_15 = V_2;
		if ((((int32_t)L_14) == ((int32_t)((int32_t)il2cpp_codegen_subtract(L_15, 1)))))
		{
			goto IL_006d;
		}
	}
	{
		float L_16 = ___2_duration;
		int32_t L_17 = V_3;
		if (!L_17)
		{
			G_B7_0 = L_16;
			goto IL_0063;
		}
		G_B6_0 = L_16;
	}
	{
		GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A L_18 = V_4;
		float L_19 = L_18.___time;
		GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18* L_20 = V_1;
		int32_t L_21 = V_3;
		NullCheck(L_20);
		float L_22 = ((L_20)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_subtract(L_21, 1)))))->___time;
		G_B8_0 = ((float)il2cpp_codegen_subtract(L_19, L_22));
		G_B8_1 = G_B6_0;
		goto IL_006a;
	}

IL_0063:
	{
		GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A L_23 = V_4;
		float L_24 = L_23.___time;
		G_B8_0 = L_24;
		G_B8_1 = G_B7_0;
	}

IL_006a:
	{
		G_B10_0 = ((float)il2cpp_codegen_multiply(G_B8_1, G_B8_0));
		goto IL_0076;
	}

IL_006d:
	{
		float L_25 = ___2_duration;
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_26 = V_0;
		float L_27;
		L_27 = TweenExtensions_Duration_mFF4115983798994675D0431ADD2D333958714222(L_26, (bool)0, NULL);
		G_B10_0 = ((float)il2cpp_codegen_subtract(L_25, L_27));
	}

IL_0076:
	{
		V_5 = G_B10_0;
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_28 = V_0;
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_29 = ___0_target;
		GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A L_30 = V_4;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_31 = L_30.___color;
		float L_32 = V_5;
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_33;
		L_33 = ShortcutExtensionsTk2d_DOColor_m7E288F7F2111D33F381ADC0A055C8B69F2380185(L_29, L_31, L_32, NULL);
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_34;
		L_34 = TweenSettingsExtensions_SetEase_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_m3605060ADD85D4E33C9F7D03BE6A676211A4A40B(L_33, 1, TweenSettingsExtensions_SetEase_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_m3605060ADD85D4E33C9F7D03BE6A676211A4A40B_RuntimeMethod_var);
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_35;
		L_35 = TweenSettingsExtensions_Append_mAFF074C9F7DE2BC8FF383C7729B13E732345F28F(L_28, L_34, NULL);
	}

IL_0094:
	{
		int32_t L_36 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add(L_36, 1));
	}

IL_0098:
	{
		int32_t L_37 = V_3;
		int32_t L_38 = V_2;
		if ((((int32_t)L_37) < ((int32_t)L_38)))
		{
			goto IL_0018;
		}
	}
	{
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_39 = V_0;
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_40 = ___0_target;
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_41;
		L_41 = TweenSettingsExtensions_SetTarget_TisSequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C_m87E21291C6C5BECA753976C5CA64050E8AFF93C0(L_39, L_40, TweenSettingsExtensions_SetTarget_TisSequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C_m87E21291C6C5BECA753976C5CA64050E8AFF93C0_RuntimeMethod_var);
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_42 = V_0;
		return L_42;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* ShortcutExtensionsTk2d_DOScaleDimensions_m0FB5E323BAB17B9B4C6189EC2462D739398F114C (tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* ___0_target, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271_m7563CA4B8BD3EA41428B31557409B6456D19A876_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass7_0_U3CDOScaleDimensionsU3Eb__0_mED7DB8E10CEA7A9170DD2FFC2C35642EACC89257_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass7_0_U3CDOScaleDimensionsU3Eb__1_m3DCE2678951FA5396B20599B9E79F90EE44870E2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4* L_0 = (U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass7_0__ctor_mB7C6C44FE24C8C4EF15D864888C74D38395CC71F(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4* L_1 = V_0;
		tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4* L_3 = V_0;
		DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66* L_4 = (DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66*)il2cpp_codegen_object_new(DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m718AE8DCD0734283AD4D7B50ABBA14ED1F4A967C(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass7_0_U3CDOScaleDimensionsU3Eb__0_mED7DB8E10CEA7A9170DD2FFC2C35642EACC89257_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4* L_5 = V_0;
		DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C* L_6 = (DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C*)il2cpp_codegen_object_new(DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m5034440D649E86F21337C9E4B543B0C6E97A058A(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass7_0_U3CDOScaleDimensionsU3Eb__1_m3DCE2678951FA5396B20599B9E79F90EE44870E2_RuntimeMethod_var), NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_7 = ___1_endValue;
		float L_8 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* L_9;
		L_9 = DOTween_To_mAF71D4F17109689870406A4565636216CCFE7680(L_4, L_6, L_7, L_8, NULL);
		TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* L_10 = L_9;
		U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4* L_11 = V_0;
		NullCheck(L_11);
		tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* L_12 = L_11->___target;
		TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* L_13;
		L_13 = TweenSettingsExtensions_SetTarget_TisTweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271_m7563CA4B8BD3EA41428B31557409B6456D19A876(L_10, L_12, TweenSettingsExtensions_SetTarget_TisTweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271_m7563CA4B8BD3EA41428B31557409B6456D19A876_RuntimeMethod_var);
		return L_10;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* ShortcutExtensionsTk2d_DOScaleDimensionsX_m3FEDC4C5B11C093869877F4E56526617CD02523B (tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* ___0_target, float ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass8_0_U3CDOScaleDimensionsXU3Eb__0_m921CB89E85E4A255DE4B4022D238E583E5D84621_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass8_0_U3CDOScaleDimensionsXU3Eb__1_m9DC03CB883E90AF346F8E07E9803BD274A387E7D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991* L_0 = (U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass8_0__ctor_m816E3933D7E6928984BBF32E7440F680F5A602B8(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991* L_1 = V_0;
		tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991* L_3 = V_0;
		DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66* L_4 = (DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66*)il2cpp_codegen_object_new(DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m718AE8DCD0734283AD4D7B50ABBA14ED1F4A967C(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass8_0_U3CDOScaleDimensionsXU3Eb__0_m921CB89E85E4A255DE4B4022D238E583E5D84621_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991* L_5 = V_0;
		DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C* L_6 = (DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C*)il2cpp_codegen_object_new(DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m5034440D649E86F21337C9E4B543B0C6E97A058A(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass8_0_U3CDOScaleDimensionsXU3Eb__1_m9DC03CB883E90AF346F8E07E9803BD274A387E7D_RuntimeMethod_var), NULL);
		float L_7 = ___1_endValue;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_8), L_7, (0.0f), NULL);
		float L_9 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* L_10;
		L_10 = DOTween_To_mAF71D4F17109689870406A4565636216CCFE7680(L_4, L_6, L_8, L_9, NULL);
		TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* L_11 = L_10;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_12;
		L_12 = TweenSettingsExtensions_SetOptions_mFD076C0377973484D8607E062753FE55478F0D0C(L_11, 2, (bool)0, NULL);
		U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991* L_13 = V_0;
		NullCheck(L_13);
		tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* L_14 = L_13->___target;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_15;
		L_15 = TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273(L_12, L_14, TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* ShortcutExtensionsTk2d_DOScaleDimensionsY_mE000FB80E2B14AC5E772AA5E0543DA027B89CED9 (tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* ___0_target, float ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass9_0_U3CDOScaleDimensionsYU3Eb__0_m4AFBCD578E52050BD94E098ABB7556D6A320A767_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass9_0_U3CDOScaleDimensionsYU3Eb__1_m6FC87FA1EEEFE3A9F7F75080ED29F69A58F0E9EE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45* L_0 = (U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass9_0__ctor_mD8F01F8BF18B27ABFEFFE494D8554397AEB900E7(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45* L_1 = V_0;
		tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45* L_3 = V_0;
		DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66* L_4 = (DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66*)il2cpp_codegen_object_new(DOGetter_1_t13276681BE97FCE6AB699EF1F894EA98C825FE66_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m718AE8DCD0734283AD4D7B50ABBA14ED1F4A967C(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass9_0_U3CDOScaleDimensionsYU3Eb__0_m4AFBCD578E52050BD94E098ABB7556D6A320A767_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45* L_5 = V_0;
		DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C* L_6 = (DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C*)il2cpp_codegen_object_new(DOSetter_1_t0DAD29F0B667B9ED214C9F6B9C4D7093FE7C0D9C_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m5034440D649E86F21337C9E4B543B0C6E97A058A(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass9_0_U3CDOScaleDimensionsYU3Eb__1_m6FC87FA1EEEFE3A9F7F75080ED29F69A58F0E9EE_RuntimeMethod_var), NULL);
		float L_7 = ___1_endValue;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_8), (0.0f), L_7, NULL);
		float L_9 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* L_10;
		L_10 = DOTween_To_mAF71D4F17109689870406A4565636216CCFE7680(L_4, L_6, L_8, L_9, NULL);
		TweenerCore_3_t2FB3CB0C11B50649FE6CFC6DBE404552B898B271* L_11 = L_10;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_12;
		L_12 = TweenSettingsExtensions_SetOptions_mFD076C0377973484D8607E062753FE55478F0D0C(L_11, 4, (bool)0, NULL);
		U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45* L_13 = V_0;
		NullCheck(L_13);
		tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* L_14 = L_13->___target;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_15;
		L_15 = TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273(L_12, L_14, TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* ShortcutExtensionsTk2d_DOScale_mB0B14C315B321F188BA0A5DA5CD1EA93A62EA4CB (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___0_target, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77_mA961948C0215BFB1350EBB68B4D0E372AC42B0E9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass10_0_U3CDOScaleU3Eb__0_mB1EAC091524E7349C6125F11085E8CE2774D8CF3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass10_0_U3CDOScaleU3Eb__1_mDDEBD485BA4CCBAA5BE8B462D23999618B7D830C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58* L_0 = (U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass10_0__ctor_mC24830B3EF6D421201A2B67B6F345E814CA92DCE(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58* L_1 = V_0;
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58* L_3 = V_0;
		DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* L_4 = (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338*)il2cpp_codegen_object_new(DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m59C11B3FE64C69454BE28721314FD6189629F27E(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass10_0_U3CDOScaleU3Eb__0_mB1EAC091524E7349C6125F11085E8CE2774D8CF3_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58* L_5 = V_0;
		DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* L_6 = (DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358*)il2cpp_codegen_object_new(DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m9E67232146D149892F1946395AD1B6B1C53F3B60(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass10_0_U3CDOScaleU3Eb__1_mDDEBD485BA4CCBAA5BE8B462D23999618B7D830C_RuntimeMethod_var), NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = ___1_endValue;
		float L_8 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_9;
		L_9 = DOTween_To_m29FF04132DD76AFF686CF5DBCCD84CB6105A2DB8(L_4, L_6, L_7, L_8, NULL);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_10 = L_9;
		U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58* L_11 = V_0;
		NullCheck(L_11);
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_12 = L_11->___target;
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_13;
		L_13 = TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77_mA961948C0215BFB1350EBB68B4D0E372AC42B0E9(L_10, L_12, TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77_mA961948C0215BFB1350EBB68B4D0E372AC42B0E9_RuntimeMethod_var);
		return L_10;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* ShortcutExtensionsTk2d_DOScaleX_mC91D2AE05BC4A918460370F9932B329564111C20 (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___0_target, float ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass11_0_U3CDOScaleXU3Eb__0_m46228F7BEF3622E9C1D2CEE579CDCEE7F7734024_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass11_0_U3CDOScaleXU3Eb__1_mB122DE18D71E40DD99AACFAAAA6923B90B3A22D5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4* L_0 = (U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass11_0__ctor_mC6064122B712AAE22F50247E11C2E2C7D5FA5F82(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4* L_1 = V_0;
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4* L_3 = V_0;
		DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* L_4 = (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338*)il2cpp_codegen_object_new(DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m59C11B3FE64C69454BE28721314FD6189629F27E(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass11_0_U3CDOScaleXU3Eb__0_m46228F7BEF3622E9C1D2CEE579CDCEE7F7734024_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4* L_5 = V_0;
		DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* L_6 = (DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358*)il2cpp_codegen_object_new(DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m9E67232146D149892F1946395AD1B6B1C53F3B60(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass11_0_U3CDOScaleXU3Eb__1_mB122DE18D71E40DD99AACFAAAA6923B90B3A22D5_RuntimeMethod_var), NULL);
		float L_7 = ___1_endValue;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_8), L_7, (0.0f), (0.0f), NULL);
		float L_9 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_10;
		L_10 = DOTween_To_m29FF04132DD76AFF686CF5DBCCD84CB6105A2DB8(L_4, L_6, L_8, L_9, NULL);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_11 = L_10;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_12;
		L_12 = TweenSettingsExtensions_SetOptions_m7900FA8CA54979FAA3D9C5EC907723B8E2CAF700(L_11, 2, (bool)0, NULL);
		U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4* L_13 = V_0;
		NullCheck(L_13);
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_14 = L_13->___target;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_15;
		L_15 = TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273(L_12, L_14, TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* ShortcutExtensionsTk2d_DOScaleY_mEBF74C6461E37C596A30A6874264BA7D88011AA7 (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___0_target, float ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass12_0_U3CDOScaleYU3Eb__0_mDD0743BC5E84104F770E5D2D2D238EDE940A6802_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass12_0_U3CDOScaleYU3Eb__1_m65EE8F60D687CF96837CE487EBEA5E17671F6A09_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D* L_0 = (U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass12_0__ctor_mECD80E4CF3DF6DAB9E73F74810E01D8E289C50A3(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D* L_1 = V_0;
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D* L_3 = V_0;
		DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* L_4 = (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338*)il2cpp_codegen_object_new(DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m59C11B3FE64C69454BE28721314FD6189629F27E(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass12_0_U3CDOScaleYU3Eb__0_mDD0743BC5E84104F770E5D2D2D238EDE940A6802_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D* L_5 = V_0;
		DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* L_6 = (DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358*)il2cpp_codegen_object_new(DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m9E67232146D149892F1946395AD1B6B1C53F3B60(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass12_0_U3CDOScaleYU3Eb__1_m65EE8F60D687CF96837CE487EBEA5E17671F6A09_RuntimeMethod_var), NULL);
		float L_7 = ___1_endValue;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_8), (0.0f), L_7, (0.0f), NULL);
		float L_9 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_10;
		L_10 = DOTween_To_m29FF04132DD76AFF686CF5DBCCD84CB6105A2DB8(L_4, L_6, L_8, L_9, NULL);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_11 = L_10;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_12;
		L_12 = TweenSettingsExtensions_SetOptions_m7900FA8CA54979FAA3D9C5EC907723B8E2CAF700(L_11, 4, (bool)0, NULL);
		U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D* L_13 = V_0;
		NullCheck(L_13);
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_14 = L_13->___target;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_15;
		L_15 = TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273(L_12, L_14, TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* ShortcutExtensionsTk2d_DOScaleZ_m1C01D31F0DCE11A6576E1B466AC7B0FD80BCD7E0 (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___0_target, float ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass13_0_U3CDOScaleZU3Eb__0_mC3F5F90007DB9823F07CCDB09A9D828E3724AD93_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass13_0_U3CDOScaleZU3Eb__1_mDA518BE9C06BED6CADB6A8655228B7C5781AFB9C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E* L_0 = (U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass13_0__ctor_mE2504E606342AA54BA63419A287046A32F6A0D98(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E* L_1 = V_0;
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E* L_3 = V_0;
		DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338* L_4 = (DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338*)il2cpp_codegen_object_new(DOGetter_1_t709462C08281F3AA5DFEF36CAF91404B1004C338_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m59C11B3FE64C69454BE28721314FD6189629F27E(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass13_0_U3CDOScaleZU3Eb__0_mC3F5F90007DB9823F07CCDB09A9D828E3724AD93_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E* L_5 = V_0;
		DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358* L_6 = (DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358*)il2cpp_codegen_object_new(DOSetter_1_t02E8F9920F174322F1CF5AC8BCDEAABD14A03358_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m9E67232146D149892F1946395AD1B6B1C53F3B60(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass13_0_U3CDOScaleZU3Eb__1_mDA518BE9C06BED6CADB6A8655228B7C5781AFB9C_RuntimeMethod_var), NULL);
		float L_7 = ___1_endValue;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_8), (0.0f), (0.0f), L_7, NULL);
		float L_9 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_10;
		L_10 = DOTween_To_m29FF04132DD76AFF686CF5DBCCD84CB6105A2DB8(L_4, L_6, L_8, L_9, NULL);
		TweenerCore_3_tCD82DFC45FB71C681FA8659EA63A7D7D16BFFE77* L_11 = L_10;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_12;
		L_12 = TweenSettingsExtensions_SetOptions_m7900FA8CA54979FAA3D9C5EC907723B8E2CAF700(L_11, 8, (bool)0, NULL);
		U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E* L_13 = V_0;
		NullCheck(L_13);
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_14 = L_13->___target;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_15;
		L_15 = TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273(L_12, L_14, TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* ShortcutExtensionsTk2d_DOColor_mDA4EB39611D4BABB2694587398D062C7AE3540E5 (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___0_target, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass14_0_U3CDOColorU3Eb__0_mD614B15FEF4933851FE7EF94B1D57B5C9A252383_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass14_0_U3CDOColorU3Eb__1_m964DE1C15CFED970699C40068B16792D4C5E2218_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A* L_0 = (U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass14_0__ctor_mE3F2DAE2211E5AAE809E3F07C1BD309A8F5912FB(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A* L_1 = V_0;
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A* L_3 = V_0;
		DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95* L_4 = (DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95*)il2cpp_codegen_object_new(DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m7ABE4FFF2BF8792E4DA3957B81F6BD369634F5F3(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass14_0_U3CDOColorU3Eb__0_mD614B15FEF4933851FE7EF94B1D57B5C9A252383_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A* L_5 = V_0;
		DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89* L_6 = (DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89*)il2cpp_codegen_object_new(DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m04DC997934D9BCA1419A9AA2F80DFE412B04B5AA(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass14_0_U3CDOColorU3Eb__1_m964DE1C15CFED970699C40068B16792D4C5E2218_RuntimeMethod_var), NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_7 = ___1_endValue;
		float L_8 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_9;
		L_9 = DOTween_To_mA1EBEAF49DD682B626CB60B4C53B7069F7933339(L_4, L_6, L_7, L_8, NULL);
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_10 = L_9;
		U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A* L_11 = V_0;
		NullCheck(L_11);
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_12 = L_11->___target;
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_13;
		L_13 = TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83(L_10, L_12, TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83_RuntimeMethod_var);
		return L_10;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* ShortcutExtensionsTk2d_DOFade_m39146A6B55ACB27CCAEB4E2CFF2F071DCB4AD620 (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___0_target, float ___1_endValue, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass15_0_U3CDOFadeU3Eb__0_mC49B5C60ABEBC79E1366CA2F6F4FC93907917940_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass15_0_U3CDOFadeU3Eb__1_m521FEE450B3B6A93EF87E5BBAE966E54521B379C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B* L_0 = (U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass15_0__ctor_mA9D4E74B4FDDE544C21A4027B57F54883B44D228(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B* L_1 = V_0;
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B* L_3 = V_0;
		DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95* L_4 = (DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95*)il2cpp_codegen_object_new(DOGetter_1_t4DFFF7454A1BCD259676FED36CCF2114E34B5B95_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_m7ABE4FFF2BF8792E4DA3957B81F6BD369634F5F3(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass15_0_U3CDOFadeU3Eb__0_mC49B5C60ABEBC79E1366CA2F6F4FC93907917940_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B* L_5 = V_0;
		DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89* L_6 = (DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89*)il2cpp_codegen_object_new(DOSetter_1_t5E2B8A5C7040616545D7D029D20C4BF5F3E6DA89_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_m04DC997934D9BCA1419A9AA2F80DFE412B04B5AA(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass15_0_U3CDOFadeU3Eb__1_m521FEE450B3B6A93EF87E5BBAE966E54521B379C_RuntimeMethod_var), NULL);
		float L_7 = ___1_endValue;
		float L_8 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_9;
		L_9 = DOTween_ToAlpha_m93AB0EDCF2956A0DD806D9537799F2727CA5E7C1(L_4, L_6, L_7, L_8, NULL);
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_10 = L_9;
		U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B* L_11 = V_0;
		NullCheck(L_11);
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_12 = L_11->___target;
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_13;
		L_13 = TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83(L_10, L_12, TweenSettingsExtensions_SetTarget_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_mD27623FACC7C328BD7538BCC8936E001F91CBA83_RuntimeMethod_var);
		return L_10;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* ShortcutExtensionsTk2d_DOGradientColor_m0BD80DDFD85E210C098C7D6178D93ED3BEC65FF5 (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___0_target, Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E* ___1_gradient, float ___2_duration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetEase_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_m3605060ADD85D4E33C9F7D03BE6A676211A4A40B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisSequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C_m87E21291C6C5BECA753976C5CA64050E8AFF93C0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* V_0 = NULL;
	GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18* V_1 = NULL;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A V_4;
	memset((&V_4), 0, sizeof(V_4));
	float V_5 = 0.0f;
	float G_B7_0 = 0.0f;
	float G_B6_0 = 0.0f;
	float G_B8_0 = 0.0f;
	float G_B8_1 = 0.0f;
	float G_B10_0 = 0.0f;
	{
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_0;
		L_0 = DOTween_Sequence_mC3AE53A08EC5DFD7DB9AE18A2FD11226A6611F89(NULL);
		V_0 = L_0;
		Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E* L_1 = ___1_gradient;
		NullCheck(L_1);
		GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18* L_2;
		L_2 = Gradient_get_colorKeys_mA4B24805A35BC4FBF04F182FCDA6E9D0CBB52F0A(L_1, NULL);
		V_1 = L_2;
		GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18* L_3 = V_1;
		NullCheck(L_3);
		V_2 = ((int32_t)(((RuntimeArray*)L_3)->max_length));
		V_3 = 0;
		goto IL_0098;
	}

IL_0018:
	{
		GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18* L_4 = V_1;
		int32_t L_5 = V_3;
		NullCheck(L_4);
		int32_t L_6 = L_5;
		GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A L_7 = (L_4)->GetAt(static_cast<il2cpp_array_size_t>(L_6));
		V_4 = L_7;
		int32_t L_8 = V_3;
		if (L_8)
		{
			goto IL_0041;
		}
	}
	{
		GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A L_9 = V_4;
		float L_10 = L_9.___time;
		if ((!(((float)L_10) <= ((float)(0.0f)))))
		{
			goto IL_0041;
		}
	}
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_11 = ___0_target;
		GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A L_12 = V_4;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_13 = L_12.___color;
		NullCheck(L_11);
		tk2dTextMesh_set_color_m799E8CF715E2DC98DCEC7F5228ABEC0CA49FEBB6(L_11, L_13, NULL);
		goto IL_0094;
	}

IL_0041:
	{
		int32_t L_14 = V_3;
		int32_t L_15 = V_2;
		if ((((int32_t)L_14) == ((int32_t)((int32_t)il2cpp_codegen_subtract(L_15, 1)))))
		{
			goto IL_006d;
		}
	}
	{
		float L_16 = ___2_duration;
		int32_t L_17 = V_3;
		if (!L_17)
		{
			G_B7_0 = L_16;
			goto IL_0063;
		}
		G_B6_0 = L_16;
	}
	{
		GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A L_18 = V_4;
		float L_19 = L_18.___time;
		GradientColorKeyU5BU5D_tB8929E6EE130CA1F3C1664B300BE83A95A642C18* L_20 = V_1;
		int32_t L_21 = V_3;
		NullCheck(L_20);
		float L_22 = ((L_20)->GetAddressAt(static_cast<il2cpp_array_size_t>(((int32_t)il2cpp_codegen_subtract(L_21, 1)))))->___time;
		G_B8_0 = ((float)il2cpp_codegen_subtract(L_19, L_22));
		G_B8_1 = G_B6_0;
		goto IL_006a;
	}

IL_0063:
	{
		GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A L_23 = V_4;
		float L_24 = L_23.___time;
		G_B8_0 = L_24;
		G_B8_1 = G_B7_0;
	}

IL_006a:
	{
		G_B10_0 = ((float)il2cpp_codegen_multiply(G_B8_1, G_B8_0));
		goto IL_0076;
	}

IL_006d:
	{
		float L_25 = ___2_duration;
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_26 = V_0;
		float L_27;
		L_27 = TweenExtensions_Duration_mFF4115983798994675D0431ADD2D333958714222(L_26, (bool)0, NULL);
		G_B10_0 = ((float)il2cpp_codegen_subtract(L_25, L_27));
	}

IL_0076:
	{
		V_5 = G_B10_0;
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_28 = V_0;
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_29 = ___0_target;
		GradientColorKey_tBD03A613338639E3774A10265CC5F3619C13421A L_30 = V_4;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_31 = L_30.___color;
		float L_32 = V_5;
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_33;
		L_33 = ShortcutExtensionsTk2d_DOColor_mDA4EB39611D4BABB2694587398D062C7AE3540E5(L_29, L_31, L_32, NULL);
		TweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3* L_34;
		L_34 = TweenSettingsExtensions_SetEase_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_m3605060ADD85D4E33C9F7D03BE6A676211A4A40B(L_33, 1, TweenSettingsExtensions_SetEase_TisTweenerCore_3_tC0593021331EF2AF46590E2184F512C0D38FD4C3_m3605060ADD85D4E33C9F7D03BE6A676211A4A40B_RuntimeMethod_var);
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_35;
		L_35 = TweenSettingsExtensions_Append_mAFF074C9F7DE2BC8FF383C7729B13E732345F28F(L_28, L_34, NULL);
	}

IL_0094:
	{
		int32_t L_36 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add(L_36, 1));
	}

IL_0098:
	{
		int32_t L_37 = V_3;
		int32_t L_38 = V_2;
		if ((((int32_t)L_37) < ((int32_t)L_38)))
		{
			goto IL_0018;
		}
	}
	{
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_39 = V_0;
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_40 = ___0_target;
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_41;
		L_41 = TweenSettingsExtensions_SetTarget_TisSequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C_m87E21291C6C5BECA753976C5CA64050E8AFF93C0(L_39, L_40, TweenSettingsExtensions_SetTarget_TisSequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C_m87E21291C6C5BECA753976C5CA64050E8AFF93C0_RuntimeMethod_var);
		Sequence_tEADBE56D6ED2E9EE8FB2E5459C3E57131EC0545C* L_42 = V_0;
		return L_42;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TweenerCore_3_t390910F1CE8B4737AA72FEED470399C8DBE1AF8B* ShortcutExtensionsTk2d_DOText_m0B5E7DFF88EABF56C4EB1CA2429DC4AE307E2CA5 (tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* ___0_target, String_t* ___1_endValue, float ___2_duration, bool ___3_richTextEnabled, int32_t ___4_scrambleMode, String_t* ___5_scrambleChars, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOGetter_1_t65A2B66606014CD7EFBA1D8DDD1273F57511D146_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOSetter_1_t999572008E4C4CFE5EF6FA70393F8B092CA8C438_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass17_0_U3CDOTextU3Eb__0_m8B4B219CD369FEE4E763AC9752288B07F49F63A2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass17_0_U3CDOTextU3Eb__1_mA7CED3164348AAA18200C52532802EC98F3D12D3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771* V_0 = NULL;
	{
		U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771* L_0 = (U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass17_0__ctor_m8A37B45737FBFB0EBECE1E42095972C2E16766DC(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771* L_1 = V_0;
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_2 = ___0_target;
		NullCheck(L_1);
		L_1->___target = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___target), (void*)L_2);
		U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771* L_3 = V_0;
		DOGetter_1_t65A2B66606014CD7EFBA1D8DDD1273F57511D146* L_4 = (DOGetter_1_t65A2B66606014CD7EFBA1D8DDD1273F57511D146*)il2cpp_codegen_object_new(DOGetter_1_t65A2B66606014CD7EFBA1D8DDD1273F57511D146_il2cpp_TypeInfo_var);
		DOGetter_1__ctor_mC8C03B65352C1426F2985D1516464D9A6E48D1EC(L_4, L_3, (intptr_t)((void*)U3CU3Ec__DisplayClass17_0_U3CDOTextU3Eb__0_m8B4B219CD369FEE4E763AC9752288B07F49F63A2_RuntimeMethod_var), NULL);
		U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771* L_5 = V_0;
		DOSetter_1_t999572008E4C4CFE5EF6FA70393F8B092CA8C438* L_6 = (DOSetter_1_t999572008E4C4CFE5EF6FA70393F8B092CA8C438*)il2cpp_codegen_object_new(DOSetter_1_t999572008E4C4CFE5EF6FA70393F8B092CA8C438_il2cpp_TypeInfo_var);
		DOSetter_1__ctor_mB9C5014DEF58C45C98E2B48F7D6CE677F7213C92(L_6, L_5, (intptr_t)((void*)U3CU3Ec__DisplayClass17_0_U3CDOTextU3Eb__1_mA7CED3164348AAA18200C52532802EC98F3D12D3_RuntimeMethod_var), NULL);
		String_t* L_7 = ___1_endValue;
		float L_8 = ___2_duration;
		il2cpp_codegen_runtime_class_init_inline(DOTween_t96369E1D40ABE93A56308F57DEA6B04219C66D13_il2cpp_TypeInfo_var);
		TweenerCore_3_t390910F1CE8B4737AA72FEED470399C8DBE1AF8B* L_9;
		L_9 = DOTween_To_mA4E61D06204BD01537C08EEB9ED148C18ABC75ED(L_4, L_6, L_7, L_8, NULL);
		TweenerCore_3_t390910F1CE8B4737AA72FEED470399C8DBE1AF8B* L_10 = L_9;
		bool L_11 = ___3_richTextEnabled;
		int32_t L_12 = ___4_scrambleMode;
		String_t* L_13 = ___5_scrambleChars;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_14;
		L_14 = TweenSettingsExtensions_SetOptions_m571339B54CA3A4BD935C07D719EE40E4FAE2C9D2(L_10, L_11, L_12, L_13, NULL);
		U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771* L_15 = V_0;
		NullCheck(L_15);
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_16 = L_15->___target;
		Tweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140* L_17;
		L_17 = TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273(L_14, L_16, TweenSettingsExtensions_SetTarget_TisTweener_tD38633F1A42EDF47A73CE3BF1894D946E830E140_m0BD4EC3CF03134447DFCBD733FC34CB1A965A273_RuntimeMethod_var);
		return L_10;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass0_0__ctor_m3DCDF7F523CE346B22687E5BBD8BF28993EFEE97 (U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec__DisplayClass0_0_U3CDOScaleU3Eb__0_mB5E48C8A184950151A5169CCDF186E26E0FCB61C (U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2* __this, const RuntimeMethod* method) 
{
	{
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_0 = __this->___target;
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = tk2dBaseSprite_get_scale_m8153F8E8C54C8EB55A2D424F6331439DC3FB18EE_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass0_0_U3CDOScaleU3Eb__1_m61F08DCCA26B7B69804A8ACA70178B684EC9D1C7 (U3CU3Ec__DisplayClass0_0_t4E48D72ACC11B29BBCC950F1C2525A9DC543D4A2* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_0 = __this->___target;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___0_x;
		NullCheck(L_0);
		tk2dBaseSprite_set_scale_m43940DD4B75709F029B05528C36ED1BC8DD41C54(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass10_0__ctor_mC24830B3EF6D421201A2B67B6F345E814CA92DCE (U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec__DisplayClass10_0_U3CDOScaleU3Eb__0_mB1EAC091524E7349C6125F11085E8CE2774D8CF3 (U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58* __this, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = tk2dTextMesh_get_scale_m48359277404F95EA3990B0CE2EC5E4AC47FC5786(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass10_0_U3CDOScaleU3Eb__1_mDDEBD485BA4CCBAA5BE8B462D23999618B7D830C (U3CU3Ec__DisplayClass10_0_tDAB22B9CF778AF072069C1442F848A3DB160DF58* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___0_x;
		NullCheck(L_0);
		tk2dTextMesh_set_scale_m33B83CAA05B8291649859DE73259EBCBCBA2EFDE(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass11_0__ctor_mC6064122B712AAE22F50247E11C2E2C7D5FA5F82 (U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec__DisplayClass11_0_U3CDOScaleXU3Eb__0_m46228F7BEF3622E9C1D2CEE579CDCEE7F7734024 (U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4* __this, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = tk2dTextMesh_get_scale_m48359277404F95EA3990B0CE2EC5E4AC47FC5786(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass11_0_U3CDOScaleXU3Eb__1_mB122DE18D71E40DD99AACFAAAA6923B90B3A22D5 (U3CU3Ec__DisplayClass11_0_t2B82D8E878A9CE30A18C1C4ED3031E7B17F5B3F4* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___0_x;
		NullCheck(L_0);
		tk2dTextMesh_set_scale_m33B83CAA05B8291649859DE73259EBCBCBA2EFDE(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass12_0__ctor_mECD80E4CF3DF6DAB9E73F74810E01D8E289C50A3 (U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec__DisplayClass12_0_U3CDOScaleYU3Eb__0_mDD0743BC5E84104F770E5D2D2D238EDE940A6802 (U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D* __this, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = tk2dTextMesh_get_scale_m48359277404F95EA3990B0CE2EC5E4AC47FC5786(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass12_0_U3CDOScaleYU3Eb__1_m65EE8F60D687CF96837CE487EBEA5E17671F6A09 (U3CU3Ec__DisplayClass12_0_t37279DD1F9FE856C5DDB4B3E907959EA2F657F7D* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___0_x;
		NullCheck(L_0);
		tk2dTextMesh_set_scale_m33B83CAA05B8291649859DE73259EBCBCBA2EFDE(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass13_0__ctor_mE2504E606342AA54BA63419A287046A32F6A0D98 (U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec__DisplayClass13_0_U3CDOScaleZU3Eb__0_mC3F5F90007DB9823F07CCDB09A9D828E3724AD93 (U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E* __this, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = tk2dTextMesh_get_scale_m48359277404F95EA3990B0CE2EC5E4AC47FC5786(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass13_0_U3CDOScaleZU3Eb__1_mDA518BE9C06BED6CADB6A8655228B7C5781AFB9C (U3CU3Ec__DisplayClass13_0_t3568EE3FB4963681258F8B9D783C9EC27D78009E* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___0_x;
		NullCheck(L_0);
		tk2dTextMesh_set_scale_m33B83CAA05B8291649859DE73259EBCBCBA2EFDE(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass14_0__ctor_mE3F2DAE2211E5AAE809E3F07C1BD309A8F5912FB (U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F U3CU3Ec__DisplayClass14_0_U3CDOColorU3Eb__0_mD614B15FEF4933851FE7EF94B1D57B5C9A252383 (U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A* __this, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = tk2dTextMesh_get_color_m976F5079A5B69CCD7DC113244F4E2665B2B5B13F(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass14_0_U3CDOColorU3Eb__1_m964DE1C15CFED970699C40068B16792D4C5E2218 (U3CU3Ec__DisplayClass14_0_tC89E3FB61C975579561EBBB5772995C91C1BC66A* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = ___0_x;
		NullCheck(L_0);
		tk2dTextMesh_set_color_m799E8CF715E2DC98DCEC7F5228ABEC0CA49FEBB6(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass15_0__ctor_mA9D4E74B4FDDE544C21A4027B57F54883B44D228 (U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F U3CU3Ec__DisplayClass15_0_U3CDOFadeU3Eb__0_mC49B5C60ABEBC79E1366CA2F6F4FC93907917940 (U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B* __this, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = tk2dTextMesh_get_color_m976F5079A5B69CCD7DC113244F4E2665B2B5B13F(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass15_0_U3CDOFadeU3Eb__1_m521FEE450B3B6A93EF87E5BBAE966E54521B379C (U3CU3Ec__DisplayClass15_0_t6DE9B35AC4EFBF9B98CE7BBF955898A832E24B6B* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = ___0_x;
		NullCheck(L_0);
		tk2dTextMesh_set_color_m799E8CF715E2DC98DCEC7F5228ABEC0CA49FEBB6(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass17_0__ctor_m8A37B45737FBFB0EBECE1E42095972C2E16766DC (U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* U3CU3Ec__DisplayClass17_0_U3CDOTextU3Eb__0_m8B4B219CD369FEE4E763AC9752288B07F49F63A2 (U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771* __this, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		NullCheck(L_0);
		String_t* L_1;
		L_1 = tk2dTextMesh_get_text_mFCE4C41DE352006CA98F7D103FEF4DC898488A42(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass17_0_U3CDOTextU3Eb__1_mA7CED3164348AAA18200C52532802EC98F3D12D3 (U3CU3Ec__DisplayClass17_0_t42A25340538642AF91D0F1EC723D8C610C9C6771* __this, String_t* ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dTextMesh_t01C8AC353D0852CE338E3B1B442F8923AD8AC58B* L_0 = __this->___target;
		String_t* L_1 = ___0_x;
		NullCheck(L_0);
		tk2dTextMesh_set_text_mB3FD5D7308FE7C059EC5221451A432518AD66ECC(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass1_0__ctor_m2AD49E6B44FD9230E899D07622140C602A179F54 (U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec__DisplayClass1_0_U3CDOScaleXU3Eb__0_m8D63B2F8E7800BDC72C7759B8C7205CCB3AD23DB (U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3* __this, const RuntimeMethod* method) 
{
	{
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_0 = __this->___target;
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = tk2dBaseSprite_get_scale_m8153F8E8C54C8EB55A2D424F6331439DC3FB18EE_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass1_0_U3CDOScaleXU3Eb__1_m2D9D6D522C9D987353FB914C4B03BA2F346C18A4 (U3CU3Ec__DisplayClass1_0_t6D974EAEFA502B2A0AE8C5945F725412BB6B62A3* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_0 = __this->___target;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___0_x;
		NullCheck(L_0);
		tk2dBaseSprite_set_scale_m43940DD4B75709F029B05528C36ED1BC8DD41C54(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass2_0__ctor_m7871E27C561372F8D4E6F5D7CB7BE59CCD7C2E42 (U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec__DisplayClass2_0_U3CDOScaleYU3Eb__0_mEE879668EE37DBAC46170ECE00C87B30B84D3971 (U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D* __this, const RuntimeMethod* method) 
{
	{
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_0 = __this->___target;
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = tk2dBaseSprite_get_scale_m8153F8E8C54C8EB55A2D424F6331439DC3FB18EE_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass2_0_U3CDOScaleYU3Eb__1_m368AB6696597DF5DD1B191C269D6B6E4BEDB5672 (U3CU3Ec__DisplayClass2_0_t948B968C8627C32332D1970FABF067C8091A8F9D* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_0 = __this->___target;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___0_x;
		NullCheck(L_0);
		tk2dBaseSprite_set_scale_m43940DD4B75709F029B05528C36ED1BC8DD41C54(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass3_0__ctor_m6386D36D1AEEFE29C12A3DE1A860D634DF82F805 (U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 U3CU3Ec__DisplayClass3_0_U3CDOScaleZU3Eb__0_m112F0998AFD6CA41F674A89B1F7A960289BB080A (U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47* __this, const RuntimeMethod* method) 
{
	{
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_0 = __this->___target;
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = tk2dBaseSprite_get_scale_m8153F8E8C54C8EB55A2D424F6331439DC3FB18EE_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass3_0_U3CDOScaleZU3Eb__1_m971AD755BEE5DDC5E148E080B12D16011899378A (U3CU3Ec__DisplayClass3_0_t05CF4DCD829F42FEF338A313741804ECBBFBFB47* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_0 = __this->___target;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___0_x;
		NullCheck(L_0);
		tk2dBaseSprite_set_scale_m43940DD4B75709F029B05528C36ED1BC8DD41C54(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass4_0__ctor_m001EF414C94F580BADFBFBECB888C1E161D9FE62 (U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F U3CU3Ec__DisplayClass4_0_U3CDOColorU3Eb__0_mAAB45A5C5FB51D6F74120F9A1008350106669C3E (U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F* __this, const RuntimeMethod* method) 
{
	{
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_0 = __this->___target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = tk2dBaseSprite_get_color_mA97E08381A326A50501CEBBE6EFCA1AA7EA5C644_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass4_0_U3CDOColorU3Eb__1_m7F6BAAAD699548BA72437E8C8538FDA2AE561D60 (U3CU3Ec__DisplayClass4_0_t1FB9FD35BCE4CA93A32F8CF355BCE261DAFF784F* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_0 = __this->___target;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = ___0_x;
		NullCheck(L_0);
		tk2dBaseSprite_set_color_m6EF2790F0EA2DA6075F79897A3CB36F2C1517DCC(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass5_0__ctor_m92C6E5DA7E62C6B93E3C63FE7B904CDA2A18F891 (U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F U3CU3Ec__DisplayClass5_0_U3CDOFadeU3Eb__0_mE1BEBDD002383619C46034FE7CA77504024F5175 (U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B* __this, const RuntimeMethod* method) 
{
	{
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_0 = __this->___target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = tk2dBaseSprite_get_color_mA97E08381A326A50501CEBBE6EFCA1AA7EA5C644_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass5_0_U3CDOFadeU3Eb__1_mD6295447001F737B10D4903A4B9869EFBD7EE432 (U3CU3Ec__DisplayClass5_0_t16657942F0F62E4D6B548B3128D3943A2956C61B* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* L_0 = __this->___target;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = ___0_x;
		NullCheck(L_0);
		tk2dBaseSprite_set_color_m6EF2790F0EA2DA6075F79897A3CB36F2C1517DCC(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass7_0__ctor_mB7C6C44FE24C8C4EF15D864888C74D38395CC71F (U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 U3CU3Ec__DisplayClass7_0_U3CDOScaleDimensionsU3Eb__0_mED7DB8E10CEA7A9170DD2FFC2C35642EACC89257 (U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4* __this, const RuntimeMethod* method) 
{
	{
		tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* L_0 = __this->___target;
		NullCheck(L_0);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1;
		L_1 = tk2dSlicedSprite_get_dimensions_mB99F1D298B3711CEF6C66A6C6377F229FDC5CE89_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass7_0_U3CDOScaleDimensionsU3Eb__1_m3DCE2678951FA5396B20599B9E79F90EE44870E2 (U3CU3Ec__DisplayClass7_0_t1CB5D299EAB5DB5842964BBC21054F2DDB8A13A4* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* L_0 = __this->___target;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1 = ___0_x;
		NullCheck(L_0);
		tk2dSlicedSprite_set_dimensions_m8DC2C68724409319CCFE80306A0BF9E06B79C35F(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass8_0__ctor_m816E3933D7E6928984BBF32E7440F680F5A602B8 (U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 U3CU3Ec__DisplayClass8_0_U3CDOScaleDimensionsXU3Eb__0_m921CB89E85E4A255DE4B4022D238E583E5D84621 (U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991* __this, const RuntimeMethod* method) 
{
	{
		tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* L_0 = __this->___target;
		NullCheck(L_0);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1;
		L_1 = tk2dSlicedSprite_get_dimensions_mB99F1D298B3711CEF6C66A6C6377F229FDC5CE89_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass8_0_U3CDOScaleDimensionsXU3Eb__1_m9DC03CB883E90AF346F8E07E9803BD274A387E7D (U3CU3Ec__DisplayClass8_0_tEC953210C0574FD222800A4FDB18B4E5DB1F6991* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* L_0 = __this->___target;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1 = ___0_x;
		NullCheck(L_0);
		tk2dSlicedSprite_set_dimensions_m8DC2C68724409319CCFE80306A0BF9E06B79C35F(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass9_0__ctor_mD8F01F8BF18B27ABFEFFE494D8554397AEB900E7 (U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 U3CU3Ec__DisplayClass9_0_U3CDOScaleDimensionsYU3Eb__0_m4AFBCD578E52050BD94E098ABB7556D6A320A767 (U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45* __this, const RuntimeMethod* method) 
{
	{
		tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* L_0 = __this->___target;
		NullCheck(L_0);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1;
		L_1 = tk2dSlicedSprite_get_dimensions_mB99F1D298B3711CEF6C66A6C6377F229FDC5CE89_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass9_0_U3CDOScaleDimensionsYU3Eb__1_m6FC87FA1EEEFE3A9F7F75080ED29F69A58F0E9EE (U3CU3Ec__DisplayClass9_0_t03298E09D0D98286F79184FABCD9FFEF89D93D45* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_x, const RuntimeMethod* method) 
{
	{
		tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* L_0 = __this->___target;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1 = ___0_x;
		NullCheck(L_0);
		tk2dSlicedSprite_set_dimensions_m8DC2C68724409319CCFE80306A0BF9E06B79C35F(L_0, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t U3CPrivateImplementationDetailsU3E_ComputeStringHash_m6EA1F233618497AEFF8902A5EDFA24C74E2F2876 (String_t* ___0_s, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	int32_t V_1 = 0;
	{
		String_t* L_0 = ___0_s;
		if (!L_0)
		{
			goto IL_002a;
		}
	}
	{
		V_0 = ((int32_t)-2128831035);
		V_1 = 0;
		goto IL_0021;
	}

IL_000d:
	{
		String_t* L_1 = ___0_s;
		int32_t L_2 = V_1;
		NullCheck(L_1);
		Il2CppChar L_3;
		L_3 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_1, L_2, NULL);
		uint32_t L_4 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_multiply(((int32_t)((int32_t)L_3^(int32_t)L_4)), ((int32_t)16777619)));
		int32_t L_5 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_5, 1));
	}

IL_0021:
	{
		int32_t L_6 = V_1;
		String_t* L_7 = ___0_s;
		NullCheck(L_7);
		int32_t L_8;
		L_8 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_7, NULL);
		if ((((int32_t)L_6) < ((int32_t)L_8)))
		{
			goto IL_000d;
		}
	}

IL_002a:
	{
		uint32_t L_9 = V_0;
		return L_9;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Mathf_Approximately_m1DADD012A8FC82E11FB282501AE2EBBF9A77150B_inline (float ___0_a, float ___1_b, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Mathf_tE284D016E3B297B72311AAD9EB8F0E643F6A4682_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	{
		float L_0 = ___1_b;
		float L_1 = ___0_a;
		float L_2;
		L_2 = fabsf(((float)il2cpp_codegen_subtract(L_0, L_1)));
		float L_3 = ___0_a;
		float L_4;
		L_4 = fabsf(L_3);
		float L_5 = ___1_b;
		float L_6;
		L_6 = fabsf(L_5);
		float L_7;
		L_7 = Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline(L_4, L_6, NULL);
		float L_8 = ((Mathf_tE284D016E3B297B72311AAD9EB8F0E643F6A4682_StaticFields*)il2cpp_codegen_static_fields_for(Mathf_tE284D016E3B297B72311AAD9EB8F0E643F6A4682_il2cpp_TypeInfo_var))->___Epsilon;
		float L_9;
		L_9 = Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline(((float)il2cpp_codegen_multiply((9.99999997E-07f), L_7)), ((float)il2cpp_codegen_multiply(L_8, (8.0f))), NULL);
		V_0 = (bool)((((float)L_2) < ((float)L_9))? 1 : 0);
		goto IL_0035;
	}

IL_0035:
	{
		bool L_10 = V_0;
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___zeroVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Vector3_op_Equality_mCDCBB8D2EDC3D3BF20F31A25ACB34705D352B479_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	bool V_4 = false;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		V_0 = ((float)il2cpp_codegen_subtract(L_1, L_3));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_lhs;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		V_1 = ((float)il2cpp_codegen_subtract(L_5, L_7));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_lhs;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_rhs;
		float L_11 = L_10.___z;
		V_2 = ((float)il2cpp_codegen_subtract(L_9, L_11));
		float L_12 = V_0;
		float L_13 = V_0;
		float L_14 = V_1;
		float L_15 = V_1;
		float L_16 = V_2;
		float L_17 = V_2;
		V_3 = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_12, L_13)), ((float)il2cpp_codegen_multiply(L_14, L_15)))), ((float)il2cpp_codegen_multiply(L_16, L_17))));
		float L_18 = V_3;
		V_4 = (bool)((((float)L_18) < ((float)(9.99999944E-11f)))? 1 : 0);
		goto IL_0043;
	}

IL_0043:
	{
		bool L_19 = V_4;
		return L_19;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_forward_mAA55A7034304DF8B2152EAD49AE779FC4CA2EB4A_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___forwardVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		float L_2 = ___2_z;
		__this->___z = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 tk2dBaseSprite_get_scale_m8153F8E8C54C8EB55A2D424F6331439DC3FB18EE_inline (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* __this, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = __this->____scale;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F tk2dBaseSprite_get_color_mA97E08381A326A50501CEBBE6EFCA1AA7EA5C644_inline (tk2dBaseSprite_t98644AD548AC904D2CDF74C9B1D88FEBF0B7E9E9* __this, const RuntimeMethod* method) 
{
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0 = __this->____color;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 tk2dSlicedSprite_get_dimensions_mB99F1D298B3711CEF6C66A6C6377F229FDC5CE89_inline (tk2dSlicedSprite_tD534DAC5ED80D034F05758053A39900CD8E52B3A* __this, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = __this->____dimensions;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline (String_t* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____stringLength;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Nullable_1_get_HasValue_m6B76086B0E863AB1D634FD03E30154F230070435_gshared_inline (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___hasValue;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Nullable_1_GetValueOrDefault_mD88C565C80170105E87BB423C5B2F974840EF90F_gshared_inline (Nullable_1_t9C51B084784B716FFF4ED4575C63CFD8A71A86FE* __this, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = __this->___value;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline (float ___0_a, float ___1_b, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float G_B3_0 = 0.0f;
	{
		float L_0 = ___0_a;
		float L_1 = ___1_b;
		if ((((float)L_0) > ((float)L_1)))
		{
			goto IL_0008;
		}
	}
	{
		float L_2 = ___1_b;
		G_B3_0 = L_2;
		goto IL_0009;
	}

IL_0008:
	{
		float L_3 = ___0_a;
		G_B3_0 = L_3;
	}

IL_0009:
	{
		V_0 = G_B3_0;
		goto IL_000c;
	}

IL_000c:
	{
		float L_4 = V_0;
		return L_4;
	}
}
