using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Text;
using UnityEngine;
using FairyGUI;
using FairyGUI.Utils;
using MiniJSON_New;
using DG.Tweening;
using LitJson;
using Newtonsoft.Json.Linq;
using SA.IOSDeploy;
using PlistCS;
using static GlobalVariable;
using System.Text.RegularExpressions;
using AppleAuth;
using AppleAuth.Enums;
using AppleAuth.Extensions;
using AppleAuth.Interfaces;
using AppleAuth.Native;
using UnityEditor;


public class ParentZone_V2_Control : MonoBehaviour
{
    private string packageName = "UI_ParentZone_Landscape", mainViewName = "ParentZonePage_Main_V2", backSceneName;
    private string packageNameAppParentZone = "UI_ParentZone";

    private AudioClip btnClickSound;

    private GComponent _mainView;
    private JsonData config_data;
    private AudioSource audioSource;
    private int middle_ads_current_idx = 0, middle_ads_count = 0;
    private GLoader[] middle_ads_array;
    private GButton[] middle_ads_btn_num_array;
    private GButton[] leftNavIconButton_array;
    private FairyGUI.Window _win_UserRegister, _winUserLogin, _win_TipUserRegister, _win_ChangePassword, _win_ForgetPassword;
    private List<GComponent> page_GComponent_array;
    private bool isGoingNextScene = false;
    private JsonData qna_data, accountInfo_data;
    private bool isValidEmail = false, isValidPassword = false, isValidUserTerms = false;
    private EC_Apps_BackEnd_Api ec_Apps_BackEnd_Api;
    private string font_color_green = "#6FCA2C", font_color_red = "#990000", font_color_black = "#333333";
    private int forgetPwdStep = 1;
    private IAppleAuthManager _appleAuthManager;
    private bool isAppleLoginSupportedPlatform = true;
    private Apple_CheckOrder _appleCheckOrder;
    private GComponent _panel_AccountCenter, _panel_UserReadyLogin;
    private GButton _btn_Regsiter;
    private string _webUrlUserPrivacy, _webUrlUserTerms;
    private PZ_Webview _pz_webview;

    private GList _nav_button_list;
    private string[] navButtons_array;
    private GObject[] _leftNavButtons;

    private GComponent _currentMainPageContainer;

    void Awake()
    {
        UIConfig.modalLayerColor = new Color(0f, 0f, 0f, 0.4f);

        ec_Apps_BackEnd_Api = FindObjectOfType<EC_Apps_BackEnd_Api>();

        _pz_webview = FindObjectOfType<PZ_Webview>();

        // 创建一个新的 GameObject
        GameObject localizationManagerObject = new GameObject("LocalizationManagerObject");

        // 动态添加 LocalizationManager 脚本
        LocalizationManager localizationManager = localizationManagerObject.AddComponent<LocalizationManager>();

        // 确保 LocalizationManager 是单例
        if (LocalizationManager.Instance == null)
        {
            LocalizationManager.Instance = localizationManager;
        }
        else
        {
            Destroy(localizationManagerObject);
        }

        // 初始化完成后，加载语言文件
        LocalizationManager.Instance.LoadLocalizedText();

    }

    // Use this for initialization
    void Start()
    {
        _appleCheckOrder = FindObjectOfType<Apple_CheckOrder>();
        if (Application.platform == RuntimePlatform.OSXEditor)
        {
            PlayerPrefs.DeleteAll();
        }

        _appleCheckOrder.InitShop();

        //Apple login
        // If the current platform is supported
        if (AppleAuthManager.IsCurrentPlatformSupported)
        {
            // Creates a default JSON deserializer, to transform JSON Native responses to C# instances
            var deserializer = new PayloadDeserializer();
            // Creates an Apple Authentication manager with the deserializer
            this._appleAuthManager = new AppleAuthManager(deserializer);
        }

        InitializeLoginMenuForAppleLogin();


        GlobalVariable.CheckUserOSLanguage();
        //GlobalVariable.GetUserSavedLanguage();

        if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.TraditionalChinese)
        {
            UIPackage.branch = "tc";
        }
        else
        {
            UIPackage.branch = "";
        }

        FGUI_DataInit();

        GRoot.inst.CloseModalWait();

        int user_first = PlayerPrefs.GetInt(GlobalVariable.accountInfo_firstRegister_flag, 0);
        Debug.Log($"user_first:{user_first.ToString()}");

        Init_TipUserRegister();
        _win_TipUserRegister.Hide();



    }

    private void Update()
    {
        // Updates the AppleAuthManager instance to execute
        // pending callbacks inside Unity's execution loop
        if (this._appleAuthManager != null)
        {
            this._appleAuthManager.Update();
        }

    }

    private void InitializeLoginMenuForAppleLogin()
    {
        // Check if the current platform supports Sign In With Apple
        if (this._appleAuthManager == null)
        {
            isAppleLoginSupportedPlatform = false;
            return;
        }

        // If at any point we receive a credentials revoked notification, we delete the stored User ID, and go back to login
        this._appleAuthManager.SetCredentialsRevokedCallback(result =>
        {
            //收到凭证撤销回调
            Debug.Log("Received revoked callback " + result);
            //this.SetupLoginMenuForSignInWithApple();
            //PlayerPrefs.DeleteKey(GlobalVariable.accountInfo_AppleUserIdKey);

        });

        // If we have an Apple User Id available, get the credential status for it
        if (GetIsAppleIdAccount())
        {
            var storedAppleUserId = PlayerPrefs.GetString(GlobalVariable.accountInfo_AppleUserIdKey);
            //this.SetupLoginMenuForCheckingCredentials();
            this.CheckCredentialStatusForUserId(storedAppleUserId);
        }
        // If we do not have an stored Apple User Id, attempt a quick login
        else
        {
            //this.SetupLoginMenuForQuickLoginAttempt();
            //this.AttemptQuickLogin();
        }
    }

    private void CheckCredentialStatusForUserId(string appleUserId)
    {
        // If there is an apple ID available, we should check the credential state
        this._appleAuthManager.GetCredentialState(
            appleUserId,
            state =>
            {
                switch (state)
                {
                    // If it's authorized, login with that user id
                    case CredentialState.Authorized:
                        return;

                    // If it was revoked, or not found, we need a new sign in with apple attempt
                    // Discard previous apple user id
                    case CredentialState.Revoked:
                        Debug.Log("CredentialState.Revoked");
                        ActionForErrorCheckCredentialStatus();

                        return;
                    case CredentialState.NotFound:

                        Debug.Log("CredentialState.NotFound");
                        ActionForErrorCheckCredentialStatus();

                        return;
                }
            },
            error =>
            {
                var authorizationErrorCode = error.GetAuthorizationErrorCode();
                string msg = "Error while trying to get credential state " + authorizationErrorCode.ToString() + " " +
                             error.ToString();
                Debug.LogWarning(msg);
                ActionForErrorCheckCredentialStatus();

                //ShowDialogs(msg);
            });
    }

    void ActionForErrorCheckCredentialStatus()
    {
        var userAccessToken = ec_Apps_BackEnd_Api.LoadAccessToken();
        //没有AppleUserId，显示用户登录界面
        if (userAccessToken == "" || String.IsNullOrEmpty(userAccessToken))
        {
            Debug.Log("user not login in, not action");

        }
        else
        {
            //is apple id
            if (PlayerPrefs.GetInt(GlobalVariable.accountInfo_IsAppleIdAccount, 1) == 1)
            {
                PlayerPrefs.SetString(GlobalVariable.accountInfo_AppleUserIdKey, "");
                PlayerPrefs.SetString(GlobalVariable.accountInfo_save_Email_flag, "");
                PlayerPrefs.SetString(GlobalVariable.accountInfo_save_DisplayName_flag, "");

                if (this != null && gameObject != null)
                {
                    Debug.Log("user logout apple id, not action");
                    StartCoroutine(ec_Apps_BackEnd_Api.UserLogout());
                }
                else
                {
                    Debug.LogWarning("The object has been destroyed, not starting coroutine.");
                }
            }

        }
    }

    void FGUI_DataInit()
    {
        UIObjectFactory.SetPackageItemExtension(UIPackage.GetItemURL("UI_ParentZone_Landscape", "LeftNavIconSimpleButton"), typeof(ParentZone_LeftNavIconButton));

        string parentZone_config_data = Resources.Load(GlobalVariable.ParentZone_dataConfig_flag).ToString();
        config_data = JsonMapper.ToObject(parentZone_config_data);

        string language = GlobalVariable.GetDeviceOSLanguage().ToLower();
        _webUrlUserPrivacy = config_data["Config"]["UserPrivacy_URL"][language].ToString();
        _webUrlUserTerms = config_data["Config"]["UserTerms_URL"][language].ToString();

        packageName = config_data["Config"]["fgui_packageName"].ToString();
        packageNameAppParentZone = config_data["Config"]["fgui_packageNameParentZone"].ToString();

        backSceneName = config_data["Config"]["backSceneName"].ToString();
        mainViewName = config_data["Config"]["fgui_mainView"].ToString();

        string fgui_PaidPartPackage = config_data["FUI_PaidPart"]["fgui_packageName"].ToString();
        string fgui_PaidPartView = config_data["FUI_PaidPart"]["fgui_mainView"].ToString();

        //Debug.Log(category_count);
        string btnClickSound_Path = config_data["Config"]["btnClickSoundPath"].ToString();
        btnClickSound = Resources.Load<AudioClip>(btnClickSound_Path);

        UIPackage.AddPackage(fgui_PaidPartPackage);
        UIPackage.AddPackage(packageNameAppParentZone);
        UIPackage.AddPackage(packageName);

        UIPanel uiPanel = FindObjectOfType<UIPanel>();
        _mainView = uiPanel.ui;

        //_mainView = UIPackage.CreateObject(packageName, mainViewName).asCom;

        //_mainView.fairyBatching = true;
        //_mainView.SetSize(GRoot.inst.width, GRoot.inst.height);
        //_mainView.AddRelation(GRoot.inst, RelationType.Size);

        _mainView.name = mainViewName;

        //GRoot.inst.AddChild(_mainView);

        GButton _backBtn = _mainView.GetChild("BtnBackHome").asButton;
        _backBtn.onClick.Add(() => { GoToHome(_backBtn); });
        //if (IAppsTeamIOSUntil.IsIphoneX()) _backBtn.position = GameUtility.VT_AddX(_backBtn.position, 20);
        //NavLeftButtons
        //InitNavLeftButtons();

        int navButtons_category_count = config_data["NavButtons"]["category"].Count;

        navButtons_array = new string[navButtons_category_count];

        _leftNavButtons = new GObject[navButtons_category_count];

        // 填充row_category_array数组
        for (int i = 0; i < navButtons_array.Length; i++)
        {
            navButtons_array[i] = config_data["NavButtons"]["category"][i].ToString();
        }

        _nav_button_list = _mainView.GetChild("LeftNavList").asList;

        _nav_button_list.itemRenderer = LeftNavButtons_RenderListItem;
        _nav_button_list.numItems = navButtons_category_count;

        //Page_ShoppingCart

        //GButton nav_btn = _mainView.GetChild($"Nav_{GlobalVariable.ParentZoneClickedNavName}").asButton;
        //SwitchToClickNav(nav_btn, GlobalVariable.ParentZoneClickedNavName, true);

        InitMainPageContainer();
    }

    void LeftNavButtons_RenderListItem(int index, GObject obj)
    {
        ParentZone_LeftNavIconButton item = (ParentZone_LeftNavIconButton)obj;
        item.SetPivot(0.5f, 0.5f);

        _leftNavButtons[index] = item;

        Color bg_color = item.m_white_bg.color;
        item.m_white_bg.color = new Color(bg_color.r, bg_color.g, bg_color.b, 0.3f);
        item.m_white_bg.visible = false;

        string key = navButtons_array[index];
        GLoader icon = item.GetChild("icon").asLoader;
        string icon_name = config_data[key]["icon"].ToString();
        GameUtility_FGUI.ChangeSprite(icon, "UI_ParentZone", icon_name);

        if (index == 0)
        {
            item.m_white_bg.visible = true;
        }

        if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
        {
            item.m_text.text = config_data[key]["zh_CN"].ToString();
        }
        else
        {
            item.m_text.text = config_data[key]["zh_TW"].ToString();
        }

        // 添加点击事件
        item.onClick.Set(() =>
        {
            // 播放点击音效
            PlayClickSound();

            InitMainPageContainer(key);

            SetAllNavBg(key);
        });

    }

    void SetAllNavBg(string current_key)
    {
        for (int i = 0; i < _leftNavButtons.Length; i++)
        {
            ParentZone_LeftNavIconButton item = (ParentZone_LeftNavIconButton)_leftNavButtons[i];
            item.m_white_bg.visible = false;
            if (current_key == navButtons_array[i])
            {
                item.m_white_bg.visible = true;
            }
        }
    }

    void InitMainPageContainer(string key = "MyAccount")
    {
        Debug.Log($"InitMainPageContainer: 开始初始化页面容器，key = {key}");
        GGraph m_PagePoint = _mainView.GetChild("PagePoint").asGraph;
        m_PagePoint.visible = false;

        // 如果已有容器，先移除
        if (_currentMainPageContainer != null)
        {
            Debug.Log("InitMainPageContainer: 移除现有的页面容器");
            _mainView.RemoveChild(_currentMainPageContainer);
            _currentMainPageContainer = null;
        }

        if (key == "MyAccount")
        {
            InitMyAccountPage();
        }

        if (key == "FAQ")
        {
            InitFAQPage();
        }

        if (_currentMainPageContainer != null)
        {
            Debug.Log("InitMainPageContainer: 添加页面容器到主视图");
            _mainView.AddChild(_currentMainPageContainer);
            _currentMainPageContainer.position = m_PagePoint.position;
            Debug.Log($"InitMainPageContainer: 设置页面容器位置: {m_PagePoint.position}");
        }
        else
        {
            Debug.LogError("InitMainPageContainer: _currentMainPageContainer 为 null，无法添加到主视图");
        }

    }

    void InitMyAccountPage()
    {

        _currentMainPageContainer = UIPackage.CreateObject(packageName, "Page_MyAccount").asCom;
        _mainView.AddChild(_currentMainPageContainer);

        Init_Page_MyAccount(_currentMainPageContainer);

    }



    void InitNavLefButtonText(GButton btn, string keyName)
    {
        /*
        "MyAccount",
        "ShoppingCart",
        "MoreApps",
        "AppSetting",
        "FAQ",
        "PrivacyPolicy",
        "RateUs",
        "ContactUs"
        */
        GTextField btn_text = btn.GetChild("text").asTextField;

        string strText = "";

        if (keyName == "MyAccount")
        {
            strText = "My Account";
        }
        else if (keyName == "ShoppingCart")
        {
            strText = "Unlock Purchase";
        }
        else if (keyName == "MoreApps")
        {
            strText = "More Apps";
        }
        else if (keyName == "AppSetting")
        {
            strText = "System Settings";
        }
        else if (keyName == "FAQ")
        {
            strText = "FAQ";
        }
        else if (keyName == "PrivacyPolicy")
        {
            strText = "Privacy Policy";
        }
        else if (keyName == "RateUs")
        {
            strText = "Rate Us";
        }
        else if (keyName == "ContactUs")
        {
            strText = "Contact Us";
        }

        btn_text.text = LocalizationManager.Instance.GetLocalizedValue(strText);

    }






    void GoToFunctionScene(GButton btn, string scene)
    {
        btn.onClick.Add(() =>
        {
            if (btn.name == "Btn_KidsPrivacy")
            {

            }

            if (btn.name == "Btn_Privacy")
            {

            }

            PlayClickSound();


            if (this != null && gameObject != null)
            {
                StartCoroutine(GoToScene(scene, false));
            }
            else
            {
                Debug.LogWarning("The object has been destroyed, not starting coroutine.");
            }
        });
    }


    void PlayClickSound()
    {
        SoundManager.PlaySFX(btnClickSound, false, 0, 1);
    }

    IEnumerator GoToScene(string go_scene_name, bool needFade = true)
    {
        yield return new WaitForSeconds(0.1f);

        if (!isGoingNextScene)
        {
            isGoingNextScene = true;

            CancelInvoke("AutoShowAdsApps");

            GRoot.inst.ShowModalWait();

            //GRoot.inst.RemoveChild(_mainView);
            //_mainView.Dispose();

            yield return new WaitForSeconds(0.5f);

            GameUtility.GoToSceneName_NotAddSuffix(go_scene_name, needFade);
        }
    }


    void GoToSetting(string settingScene)
    {
        if (this != null && gameObject != null)
        {
            StartCoroutine(GoToScene(settingScene));
        }
        else
        {
            Debug.LogWarning("The object has been destroyed, not starting coroutine.");
        }
    }

    void GoToHome(GButton button)
    {
        PlayClickSound();

        _appleCheckOrder = null;

        ec_Apps_BackEnd_Api.host_server = GlobalVariable.GetWebSevice_Link();

        Debug.Log($"web back end update: {ec_Apps_BackEnd_Api.host_server}");

        GlobalVariable.ParentZoneClickedNavName = "MyAccount";

        if (this != null && gameObject != null)
        {
            if (UnityEngine.iOS.Device.generation.ToString().Contains("iPad"))
            {
                backSceneName = "ParentZone_Home_Ipad";
            }
            else
            {
                backSceneName = "ParentZone_Home";
            }
            StartCoroutine(GoToScene(backSceneName));
        }
        else
        {
            Debug.LogWarning("The object has been destroyed, not starting coroutine.");
        }
    }

    void RateUs()
    {
        string url = "itms-apps://ax.itunes.apple.com/WebObjects/MZStore.woa/wa/viewContentsUserReviews?type=Purple+Software&id=" + GlobalVariable.AppleAppId;
        if (IAppsTeamIOSUntil.IOSSystemVersion() >= 7.0f)
            url = "itms-apps://itunes.apple.com/app/id" + GlobalVariable.AppleAppId;
        print(url);
        Application.OpenURL(url);
    }



    void GoToPrivacyPage()
    {
        string data_url = config_data["Config"]["PrivacyPolicy"].ToString();
        Application.OpenURL(data_url);
    }




    #region MyAccount
    //Init Page_MyAccount
    void Init_Page_MyAccount(GComponent view_myAccount)
    {


        GComponent panel_AccountCenter = view_myAccount.GetChild("Panel_AccountCenter").asCom;
        _panel_AccountCenter = panel_AccountCenter;
        GComponent panel_UserReadyLogin = view_myAccount.GetChild("Panel_UserReadyLogin").asCom;
        _panel_UserReadyLogin = panel_UserReadyLogin;
        Create_Window_UserRegister();
        panel_AccountCenter.visible = false;
        panel_UserReadyLogin.visible = true;

        panel_UserReadyLogin.GetChild("title").text = LocalizationManager.Instance.GetLocalizedValue("Account Center");

        //家长账号注册
        _btn_Regsiter = panel_UserReadyLogin.GetChild("Btn_Regsiter").asButton;
        _btn_Regsiter.title = LocalizationManager.Instance.GetLocalizedValue("Sign Up");
        _btn_Regsiter.onClick.Add(() =>
        {
            PlayClickSound();

            Init_UserRegsiter(view_myAccount);
        });


        //家长账号登录
        GButton btn_Login = panel_UserReadyLogin.GetChild("Btn_Login").asButton;
        btn_Login.title = LocalizationManager.Instance.GetLocalizedValue("Sign In");
        btn_Login.onClick.Add(() =>
        {
            PlayClickSound();

            Init_UserLogin(view_myAccount);

        });

        panel_AccountCenter.GetChild("title").asTextField.text = LocalizationManager.Instance.GetLocalizedValue("Account Center");

        //修改账号密码
        GButton btn_ResetPwd = panel_AccountCenter.GetChild("Btn_ResetPwd").asButton;
        btn_ResetPwd.title = LocalizationManager.Instance.GetLocalizedValue("Change Member Password");

        btn_ResetPwd.onClick.Add(() =>
        {
            PlayClickSound();

            _win_ChangePassword.Show();

        });

        UserLogout_Action(panel_AccountCenter);

        //恢复购买记录
        GButton btn_SyncOrders = panel_AccountCenter.GetChild("Btn_SyncOrders").asButton;
        btn_SyncOrders.title = LocalizationManager.Instance.GetLocalizedValue("Restore Purchase");

        bool hasRetoreExecuted = false;
        btn_SyncOrders.onClick.Add(() =>
        {
            PlayClickSound();

            if (Application.platform == RuntimePlatform.IPhonePlayer)
            {
                ShowHideMessageBox(panel_AccountCenter, true);
                btn_SyncOrders.visible = false;


                _appleCheckOrder.OnRestoreFail += (string msg) =>
                {
                    if (hasRetoreExecuted) return;
                    hasRetoreExecuted = true;
                    GameUtility.GoToSceneName_NotAddSuffix("Home");
                };

                _appleCheckOrder.OnRetoreFinish += (bool done, string msg) =>
                {
                    Debug.Log("OnRetoreFinish");
                    string appId = config_data["Config"]["app_id"].ToString();


                    if (this != null && gameObject != null)
                    {
                        StartCoroutine(ec_Apps_BackEnd_Api.SynchronizationPurchaseInfo_Apple(msg, appId));
                    }
                    else
                    {
                        Debug.LogWarning("The object has been destroyed, not starting coroutine.");
                    }

                };

                _appleCheckOrder.OnRestoreJobComplete += (bool done, string msg) =>
                {
                    if (hasRetoreExecuted) return;
                    hasRetoreExecuted = true;

                    if (done)
                    {
                        Debug.Log("record purchase ok");
                    }
                    ShowHideMessageBox(panel_AccountCenter, false);

                    //btn_SyncOrders.visible = true;

                    if (msg == "Restore Complete") msg = LocalizationManager.Instance.GetLocalizedValue(msg);

                    ShowDialogs(msg);
                };

                _appleCheckOrder.CheckInternetForRestore();
                Debug.Log("同步购买记录");
            }


        });

        //需要判断AppleId用户是否登录

        //Debug.Log($"LoadAccessToken:{ec_Apps_BackEnd_Api.LoadAccessToken()}");

        var userAccessToken = ec_Apps_BackEnd_Api.LoadAccessToken();
        //没有AppleUserId，显示用户登录界面
        if (userAccessToken == "" || String.IsNullOrEmpty(userAccessToken))
        {
            Debug.Log($"user not login in:{ec_Apps_BackEnd_Api.LoadAccessToken()}");

            ShowLoginedUserPanel(false, view_myAccount);

        }
        else
        {
            ShowLoginedUserPanel(true, view_myAccount, GetIsAppleIdAccount());
            GTextField txt_useremail = panel_AccountCenter.GetChild("txt_useremail").asTextField;
            txt_useremail.text = GameUtility.LimitStringLength(PlayerPrefs.GetString(GlobalVariable.accountInfo_save_Email_flag), 20);

        }

        Init_UserChangePassword(view_myAccount);

        Init_UserForgotPassword();
    }

    bool GetIsAppleIdAccount()
    {
        if (PlayerPrefs.GetInt(GlobalVariable.accountInfo_IsAppleIdAccount, 1) == 1)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    void UserLogout_Action(GComponent panel_accountCenter)
    {
        GTextField text_warn_msg = panel_accountCenter.GetChild("Text_Warn_Msg").asTextField;

        //用户退出登录
        GButton btn_logOut = panel_accountCenter.GetChild("btn_logout").asButton;
        btn_logOut.title = LocalizationManager.Instance.GetLocalizedValue("Log Out");

        ec_Apps_BackEnd_Api.OnUserLogoutFinish += (bool finished) =>
        {
            if (finished)
            {
                Debug.Log("OnUserLogoutFinish");

                PlayerPrefs.SetString(GlobalVariable.accountInfo_AppleUserIdKey, "");
                PlayerPrefs.SetString(GlobalVariable.accountInfo_save_Email_flag, "");
                PlayerPrefs.SetString(GlobalVariable.accountInfo_save_DisplayName_flag, "");

                //ShowLoginedUserPanel(true, panel_accountCenter);
                //Application.LoadLevel(Application.loadedLevelName);
                //ShowLoginedUserPanel(false, panel_accountCenter.parent, false);
                GameUtility.GoToSceneName_NotAddSuffix("Home");
            }
            else
            {
                text_warn_msg.text = LocalizationManager.Instance.GetLocalizedValue("Failed to log out. Please contact customer service for assistance!");
            }
        };

        btn_logOut.onClick.Add(() =>
        {
            PlayClickSound();

            if (this != null && gameObject != null)
            {

                if (this != null && gameObject != null)
                {
                    StartCoroutine(ec_Apps_BackEnd_Api.UserLogout());
                }
                else
                {
                    Debug.LogWarning("The object has been destroyed, not starting coroutine.");
                }
            }
            else
            {
                Debug.LogWarning("The object has been destroyed, not starting coroutine.");
            }

        });

    }

    void Init_TipUserRegister()
    {
        Create_Window_Tip_UserLogin();

        GButton winBtnClose = _win_TipUserRegister.contentPane.GetChild("closeButton").asButton;
        winBtnClose.onClick.Add(() =>
        {
            PlayClickSound();

            _win_TipUserRegister.Hide();

        });

        winBtnClose.visible = false;

        GButton btn_Sumbit = _win_TipUserRegister.contentPane.GetChild("Btn_Sumbit").asButton;
        btn_Sumbit.onClick.Add(() =>
        {
            PlayClickSound();

            _win_TipUserRegister.Hide();

        });

    }

    void Init_UserForgotPassword()
    {
        Create_Window_ForgetPassword();

        GGroup group_Pwd = _win_ForgetPassword.contentPane.GetChild("Group_Pwd").asGroup;
        if (forgetPwdStep == 1)
        {
            group_Pwd.visible = false;
        }
        else
        {
            group_Pwd.visible = true;
        }


        GTextField text_Warn_Email = _win_ForgetPassword.contentPane.GetChild("Text_Warn_Email").asTextField;

        GTextInput input_email = _win_ForgetPassword.contentPane.GetChild("input_email").asTextInput;
        input_email.onFocusOut.Add(() =>
        {
            if (input_email.text.Length > 0)
            {
                string pattern = @"^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$";

                isValidEmail = Regex.IsMatch(input_email.text, pattern);

                if (isValidEmail)
                {
                    text_Warn_Email.text = "";
                }
                else
                {
                    text_Warn_Email.text = LocalizationManager.Instance.GetLocalizedValue("email error");
                }
            }

        });

        input_email.onChanged.Add(() => { text_Warn_Email.text = ""; });

        GTextField text_Warn_Code = _win_ForgetPassword.contentPane.GetChild("Text_Warn_Code").asTextField;

        GTextField input_code = _win_ForgetPassword.contentPane.GetChild("input_code").asTextField;

        GImage icon_checked_password = _win_ForgetPassword.contentPane.GetChild("checked_password").asImage;

        GImage icon_checked_password_Length = _win_ForgetPassword.contentPane.GetChild("checked_Password_Length").asImage;
        GImage icon_checked_password_LowerLetter = _win_ForgetPassword.contentPane.GetChild("checked_Password_LowerLetter").asImage;
        GImage icon_checked_password_UpperLetter = _win_ForgetPassword.contentPane.GetChild("checked_Password_UpperLetter").asImage;
        GImage icon_checked_password_Nubmer = _win_ForgetPassword.contentPane.GetChild("checked_Password_Nubmer").asImage;
        GImage icon_checked_password_Symbols = _win_ForgetPassword.contentPane.GetChild("checked_Password_Symbols").asImage;


        GTextField text_Warn_password_Length = _win_ForgetPassword.contentPane.GetChild("Text_Warn_Password_Length").asTextField;
        text_Warn_password_Length.text = LocalizationManager.Instance.GetLocalizedValue("Password must be between 8 and 20 characters in length");

        GTextField text_Warn_password_LowerLetter = _win_ForgetPassword.contentPane.GetChild("Text_Warn_Password_LowerLetter").asTextField;
        text_Warn_password_LowerLetter.text = LocalizationManager.Instance.GetLocalizedValue("at least one lowercase letter");

        GTextField text_Warn_password_UpperLetter = _win_ForgetPassword.contentPane.GetChild("Text_Warn_Password_UpperLetter").asTextField;
        text_Warn_password_UpperLetter.text = LocalizationManager.Instance.GetLocalizedValue("at least one uppercase letter");

        GTextField text_Warn_password_Nubmer = _win_ForgetPassword.contentPane.GetChild("Text_Warn_Password_Number").asTextField;
        text_Warn_password_Nubmer.text = LocalizationManager.Instance.GetLocalizedValue("at least one digit");

        GTextField text_Warn_password_Symbols = _win_ForgetPassword.contentPane.GetChild("Text_Warn_Password_Symbols").asTextField;
        text_Warn_password_Symbols.text = LocalizationManager.Instance.GetLocalizedValue("not include any symbols");

        GTextInput input_password = _win_ForgetPassword.contentPane.GetChild("input_password").asTextInput;

        input_password.onFocusOut.Add(() =>
        {
            if (input_password.text.Length > 0)
            {
                icon_checked_password.visible = false;

                CheckPasswordValid(icon_checked_password_Length, text_Warn_password_Length, @"^.{8,20}$", input_password.text);
                CheckPasswordValid(icon_checked_password_LowerLetter, text_Warn_password_LowerLetter, @"^(?=.*[a-z]).+$", input_password.text);
                CheckPasswordValid(icon_checked_password_UpperLetter, text_Warn_password_UpperLetter, @"^(?=.*[A-Z]).+$", input_password.text);
                CheckPasswordValid(icon_checked_password_Nubmer, text_Warn_password_Nubmer, @"^(?=.*\d).+$", input_password.text);
                CheckPasswordValid(icon_checked_password_Symbols, text_Warn_password_Symbols, @"^[a-zA-Z0-9]+$", input_password.text);

                string pattern = @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,20}$";

                //.text = "Password123";
                isValidPassword = Regex.IsMatch(input_password.text, pattern);

                if (isValidPassword)
                {
                    icon_checked_password.visible = true;
                    //text_Warn_Email.text = input_password.text;
                }
            }


        });

        GLoader btn_ShowHidePassword = _win_ForgetPassword.contentPane.GetChild("Btn_ShowHidePwd").asLoader;

        input_password.displayAsPassword = true;

        btn_ShowHidePassword.onClick.Add(() =>
        {
            if (input_password.displayAsPassword)
            {

                GameUtility_FGUI.ChangeSprite(btn_ShowHidePassword, packageName, "HideForPassword");

                input_password.displayAsPassword = false;
            }
            else
            {
                GameUtility_FGUI.ChangeSprite(btn_ShowHidePassword, packageName, "ShowForPassword");

                input_password.displayAsPassword = true;
            }

        });


        GButton btn_Sumbit = _win_ForgetPassword.contentPane.GetChild("Btn_Sumbit").asButton;

        Vector2 btn_sumit_step1_pos = new Vector2(btn_Sumbit.position.x, input_code.position.y);
        Vector2 btn_sumit_step2_pos = new Vector2(btn_Sumbit.position.x, _win_ForgetPassword.contentPane.GetChild("bg_pos2").position.y);

        if (forgetPwdStep == 1)
        {
            btn_Sumbit.title = LocalizationManager.Instance.GetLocalizedValue("Get email verification code");
            btn_Sumbit.position = btn_sumit_step1_pos;
        }
        else
        {
            btn_Sumbit.title = LocalizationManager.Instance.GetLocalizedValue("Submit");
            btn_Sumbit.position = btn_sumit_step2_pos;
        }

        btn_Sumbit.onClick.Add(() =>
        {
            PlayClickSound();

            if (forgetPwdStep == 1)
            {
                if (isValidEmail)
                {
                    text_Warn_Email.text = LocalizationManager.Instance.GetLocalizedValue("Email verification code is being sent, please wait…");
                    Debug.Log("email is ok");

                    btn_Sumbit.enabled = false;

                    ec_Apps_BackEnd_Api.OnGetVerifyCodeFinish += (string msg) =>
                    {
                        text_Warn_Email.text = "";

                        if (msg == "Reset code sent to email")
                        {
                            text_Warn_Code.text = LocalizationManager.Instance.GetLocalizedValue("The verification code has been sent to your email. If you haven't received it, please check your spam folder. Thank you.");
                            forgetPwdStep = 2;

                            input_email.enabled = false;
                            group_Pwd.visible = true;
                            btn_Sumbit.title = LocalizationManager.Instance.GetLocalizedValue("Submit");
                            btn_Sumbit.position = btn_sumit_step2_pos;
                        }
                        else
                        {
                            forgetPwdStep = 1;

                            if (msg == "email is error")
                            {
                                msg = LocalizationManager.Instance.GetLocalizedValue("The email account does not exist. Please register a new membership account!");
                            }
                            text_Warn_Email.text = msg;
                        }

                        btn_Sumbit.enabled = true;

                    };

                    if (this != null && gameObject != null)
                    {
                        StartCoroutine(ec_Apps_BackEnd_Api.GetPwdResetCode(input_email.text.ToLower()));
                    }
                    else
                    {
                        Debug.LogWarning("The object has been destroyed, not starting coroutine.");
                    }

                }

            }
            else
            {
                bool isValidCode = false;

                if (input_code.text.Length > 0 && input_code.text.Length <= 20)
                {
                    isValidCode = true;
                }

                if (isValidEmail && isValidCode && isValidPassword)
                {
                    ec_Apps_BackEnd_Api.OnVerifyCodeFinish += (string msg) =>
                    {
                        text_Warn_Email.text = "";
                        text_Warn_Code.text = "";
                        Debug.Log($"msg:{msg}");
                        if (msg == "Password changed successfully")
                        {
                            group_Pwd.visible = false;
                            text_Warn_Email.text = LocalizationManager.Instance.GetLocalizedValue("Password update complete, please log in again. Thank you.");
                            text_Warn_Email.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_green);
                            forgetPwdStep = 2;
                            btn_Sumbit.enabled = false;

                            if (this != null && gameObject != null)
                            {
                                StartCoroutine(DelayCloseWin(_win_ForgetPassword));
                            }
                            else
                            {
                                Debug.LogWarning("The object has been destroyed, not starting coroutine.");
                            }


                        }
                        else if (msg == "Validator data is error")
                        {
                            forgetPwdStep = 2;
                            text_Warn_Code.text = LocalizationManager.Instance.GetLocalizedValue("There is an error in the submitted data");
                        }
                        else if (msg == "Invalid reset code")
                        {
                            forgetPwdStep = 2;
                            text_Warn_Code.text = LocalizationManager.Instance.GetLocalizedValue("Invalid verification code");
                        }
                        else if (msg == "Reset code has expired")
                        {
                            forgetPwdStep = 2;
                            text_Warn_Code.text = LocalizationManager.Instance.GetLocalizedValue("The verification code has expired. Please close the window and obtain a new verification code.");
                        }
                        else
                        {
                            forgetPwdStep = 2;
                            text_Warn_Code.text = LocalizationManager.Instance.GetLocalizedValue("An error has occurred. Please contact customer service for assistance.");
                        }

                        btn_Sumbit.enabled = true;

                    };


                    if (this != null && gameObject != null)
                    {
                        StartCoroutine(ec_Apps_BackEnd_Api.VerifyPwdCode(input_email.text.ToLower(), input_code.text, input_password.text));
                    }
                    else
                    {
                        Debug.LogWarning("The object has been destroyed, not starting coroutine.");
                    }
                }
            }

        });


        GButton winBtnClose = _win_ForgetPassword.contentPane.GetChild("closeButton").asButton;
        winBtnClose.onClick.Add(() =>
        {
            PlayClickSound();

            _win_ForgetPassword.Hide();

            isValidEmail = false;
            isValidPassword = false;

            group_Pwd.visible = false;
            forgetPwdStep = 1;
            btn_Sumbit.position = btn_sumit_step1_pos;

            //重新初始化
            text_Warn_Email.text = "";
            input_email.text = "";
            text_Warn_Code.text = "";
            input_code.text = "";
            input_password.text = "";

            text_Warn_Code.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_red);
            text_Warn_Email.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_red);

            input_email.enabled = true;

            input_password.displayAsPassword = false;

            icon_checked_password.visible = false;

            icon_checked_password_Length.visible = false;
            icon_checked_password_LowerLetter.visible = false;
            icon_checked_password_UpperLetter.visible = false;
            icon_checked_password_Nubmer.visible = false;
            icon_checked_password_Symbols.visible = false;

            text_Warn_password_Length.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);
            text_Warn_password_LowerLetter.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);
            text_Warn_password_UpperLetter.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);
            text_Warn_password_Nubmer.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);
            text_Warn_password_Symbols.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);

            GameUtility_FGUI.ChangeSprite(btn_ShowHidePassword, packageName, "ShowForPassword");

        });

    }

    void ShowLoginedUserPanel(bool showFlag, GComponent view_Page_MyAccount, bool isAppleUserId = false)
    {
        //GComponent panel_AccountCenter = view_Page_MyAccount.GetChild("Panel_AccountCenter").asCom;
        GComponent panel_AccountCenter = _panel_AccountCenter;
        if (panel_AccountCenter == null) Debug.Log("panel_AccountCenter is null");
        //GComponent panel_UserReadyLogin = view_Page_MyAccount.GetChild("Panel_UserReadyLogin").asCom;
        GComponent panel_UserReadyLogin = _panel_UserReadyLogin;

        if (panel_UserReadyLogin == null) Debug.Log("panel_UserReadyLogin is null");

        if (showFlag)
        {
            panel_AccountCenter.GetChild("title").text = LocalizationManager.Instance.GetLocalizedValue("Account Center");

            GLoader icon_logouser = panel_AccountCenter.GetChild("icon_logouser") as GLoader;

            GButton btn_ResetPwd = panel_AccountCenter.GetChild("Btn_ResetPwd") as GButton;
            btn_ResetPwd.text = LocalizationManager.Instance.GetLocalizedValue("Change Member Password");

            if (isAppleUserId)
            {
                btn_ResetPwd.visible = false;
                GameUtility_FGUI.ChangeSprite(icon_logouser, packageName, "Icon_MyAccountApple");
            }
            else
            {
                btn_ResetPwd.visible = true;
                GameUtility_FGUI.ChangeSprite(icon_logouser, packageName, "Icon_MyAccount");
            }

            panel_AccountCenter.visible = true;
            panel_UserReadyLogin.visible = false;

            ShowAccountCenterUserProfile(view_Page_MyAccount, isAppleUserId);

        }
        else
        {
            panel_AccountCenter.visible = false;
            panel_UserReadyLogin.visible = true;
        }
    }


    void Init_UserLogin(GComponent page_myAccount)
    {
        Create_Window_UserLogin();

        _winUserLogin.Show();

        isValidEmail = false;
        isValidPassword = false;
        isValidUserTerms = false;

        GButton winBtnClose = _winUserLogin.contentPane.GetChild("closeButton").asButton;
        winBtnClose.onClick.Add(() =>
        {
            PlayClickSound();

            _winUserLogin.Hide();

            isValidEmail = false;
            isValidPassword = false;
            isValidUserTerms = false;

            ec_Apps_BackEnd_Api.isProcessing = false;

        });

        GTextField text_Warn_Msg = _winUserLogin.contentPane.GetChild("Text_Warn_Msg").asTextField;

        GTextInput input_email = _winUserLogin.contentPane.GetChild("input_email").asTextInput;
        input_email.promptText = LocalizationManager.Instance.GetLocalizedValue("Email");
        input_email.onFocusOut.Add(() =>
        {
            if (input_email.text.Length > 0)
            {
                string pattern = @"^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$";

                isValidEmail = Regex.IsMatch(input_email.text, pattern);

                if (isValidEmail)
                {
                    text_Warn_Msg.text = "";
                }
                else
                {
                    text_Warn_Msg.text = LocalizationManager.Instance.GetLocalizedValue("email error");
                }
            }

        });

        input_email.onChanged.Add(() => { text_Warn_Msg.text = ""; });

        GTextInput input_password1 = _winUserLogin.contentPane.GetChild("input_password").asTextInput;
        input_password1.promptText = LocalizationManager.Instance.GetLocalizedValue("Password");

        GLoader btn_ShowHidePassword = _winUserLogin.contentPane.GetChild("Btn_ShowHidePwd").asLoader;

        input_password1.displayAsPassword = true;

        btn_ShowHidePassword.onClick.Add(() =>
        {
            if (input_password1.displayAsPassword)
            {

                GameUtility_FGUI.ChangeSprite(btn_ShowHidePassword, packageName, "HideForPassword");

                input_password1.displayAsPassword = false;
            }
            else
            {
                GameUtility_FGUI.ChangeSprite(btn_ShowHidePassword, packageName, "ShowForPassword");

                input_password1.displayAsPassword = true;
            }

        });

        string textClose = LocalizationManager.Instance.GetLocalizedValue("Close");

        GButton btn_Privacy = _winUserLogin.contentPane.GetChild("Btn_Privacy").asButton;
        btn_Privacy.text = LocalizationManager.Instance.GetLocalizedValue("Privacy Policy");
        btn_Privacy.onClick.Add(() =>
        {
            PlayClickSound();

            //Application.OpenURL(_webUrlUserPrivacy);
            _pz_webview.ShowWebView(_webUrlUserPrivacy, textClose);
        });


        GButton btn_UserTerms = _winUserLogin.contentPane.GetChild("Btn_UserTerms").asButton;
        btn_UserTerms.text = LocalizationManager.Instance.GetLocalizedValue("terms of use");
        btn_UserTerms.onClick.Add(() =>
        {
            PlayClickSound();

            _pz_webview.ShowWebView(_webUrlUserTerms, textClose);

        });

        GTextField text_AgreePrivacy = _winUserLogin.contentPane.GetChild("Text_AgreePrivacy").asTextField;
        text_AgreePrivacy.text = LocalizationManager.Instance.GetLocalizedValue("By proceeding, you agree to our");

        GButton btn_Checked = _winUserLogin.contentPane.GetChild("Btn_Checked").asButton;
        btn_Checked.GetChildAt(1).visible = true;
        isValidUserTerms = true;
        btn_Checked.onClick.Add(() =>
        {
            if (btn_Checked.GetChildAt(1).visible == true)
            {
                btn_Checked.GetChildAt(1).visible = false;

                isValidUserTerms = false;
            }
            else
            {
                btn_Checked.GetChildAt(1).visible = true;

                isValidUserTerms = true;
            }

        });

        GButton btn_Sumbit = _winUserLogin.contentPane.GetChild("Btn_Sumbit").asButton;
        btn_Sumbit.title = LocalizationManager.Instance.GetLocalizedValue("Sign In");
        //test login

        GButton btn_AppleLogin = _winUserLogin.contentPane.GetChild("Btn_AppleLogin").asButton;

        //用户登录
        btn_Sumbit.onClick.Add(() =>
        {
            PlayClickSound();
            text_Warn_Msg.text = "";
            string pattern = @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,20}$";

            //.text = "Password123";
            isValidPassword = Regex.IsMatch(input_password1.text, pattern);

            if (isValidPassword)
            {
                text_Warn_Msg.text = LocalizationManager.Instance.GetLocalizedValue("email or password has error");
            }
            else
            {
                text_Warn_Msg.text = "";
            }

            //用户开始登录过程
            if (isValidEmail && isValidPassword)
            {
                if (isValidUserTerms)
                {
                    Debug.Log("user login begin");
                    text_Warn_Msg.text = LocalizationManager.Instance.GetLocalizedValue("Logging in…"); ;
                    text_Warn_Msg.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);

                    ec_Apps_BackEnd_Api.OnUserLoginFinish += (bool loginDone, string msg) =>
                    {
                        if (loginDone)
                        {
                            text_Warn_Msg.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_green);
                            text_Warn_Msg.text = LocalizationManager.Instance.GetLocalizedValue("Successfully logged in");

                            PlayerPrefs.SetString(GlobalVariable.accountInfo_AppleUserIdKey, "");
                            PlayerPrefs.SetInt(GlobalVariable.accountInfo_IsAppleIdAccount, 0);
                            PlayerPrefs.SetString(GlobalVariable.accountInfo_save_DisplayName_flag, input_email.text.ToLower());

                            ShowLoginedUserPanel(true, page_myAccount);
                            _winUserLogin.Hide();
                        }
                        else
                        {
                            text_Warn_Msg.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_red);
                            text_Warn_Msg.text = LocalizationManager.Instance.GetLocalizedValue("email or password has error");
                            btn_Sumbit.enabled = true;
                        }

                    };

                    UserLogin_Action(input_email.text.ToLower(), input_password1.text);

                    btn_AppleLogin.visible = false;
                    btn_Sumbit.enabled = false;
                }
                else
                {
                    text_Warn_Msg.text = "";
                    text_AgreePrivacy.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_red);
                    btn_Sumbit.enabled = true;
                }
            }
            else
            {
                text_Warn_Msg.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_red);
                text_Warn_Msg.text = LocalizationManager.Instance.GetLocalizedValue("email or password has error");
            }
        });


        GButton btn_ForgetPwd = _winUserLogin.contentPane.GetChild("Btn_ForgetPwd").asButton;
        btn_ForgetPwd.text = LocalizationManager.Instance.GetLocalizedValue("Forgot your password?");
        btn_ForgetPwd.onClick.Add(() =>
        {
            PlayClickSound();
            _winUserLogin.Hide();

            _win_ForgetPassword.Show();


        });


        btn_AppleLogin.onClick.Add(() =>
        {
            PlayClickSound();

            if (isValidUserTerms)
            {
                ShowHideMessageBox(_winUserLogin.contentPane, true);

                SignInWithApple(btn_AppleLogin, page_myAccount, _winUserLogin, false);
            }
            else
            {
                text_AgreePrivacy.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_red);
                btn_Sumbit.enabled = true;
            }
        });

        btn_AppleLogin.visible = isAppleLoginSupportedPlatform;
    }

    void ShowHideMessageBox(GComponent view, bool showFlag)
    {
        GComponent message_box = view.GetChild("message_box").asCom;
        GTextField message_text = message_box.GetChild("text").asTextField;
        message_text.text = LocalizationManager.Instance.GetLocalizedValue("Processing...");
        message_text.TweenFade(0.5f, 1f).SetRepeat(-1, true);

        message_box.visible = showFlag;

    }

    void UserChangePwd_Action(string old_pwd, string new_pwd)
    {
        Debug.Log("user change pwd begin!");


        if (this != null && gameObject != null)
        {
            StartCoroutine(ec_Apps_BackEnd_Api.UserChangePwd(old_pwd, new_pwd));
        }
        else
        {
            Debug.LogWarning("The object has been destroyed, not starting coroutine.");
        }
    }

    void UserLogin_Action(string email, string password)
    {
        Debug.Log("user login begin!");

        EC_Apps_BackEnd_Api.UserLoginData userData = new EC_Apps_BackEnd_Api.UserLoginData
        {
            email = email,
            password = password
        };

        if (this != null && gameObject != null)
        {
            StartCoroutine(ec_Apps_BackEnd_Api.UserLogin(userData));
        }
        else
        {
            Debug.LogWarning("The object has been destroyed, not starting coroutine.");
        }
    }

    void Init_UserRegsiter(GComponent page_myAccount)
    {
        Create_Window_UserRegister();

        _win_UserRegister.Show();

        isValidEmail = false;
        isValidPassword = false;

        GButton winBtnClose = _win_UserRegister.contentPane.GetChild("closeButton").asButton;
        winBtnClose.onClick.Add(() =>
        {
            PlayClickSound();

            _win_UserRegister.Hide();

            isValidEmail = false;
            isValidPassword = false;
            isValidUserTerms = false;
            ec_Apps_BackEnd_Api.isProcessing = false;
        });

        GButton btn_Checked = _win_UserRegister.contentPane.GetChild("Btn_Checked").asButton;
        btn_Checked.GetChildAt(1).visible = true;
        isValidUserTerms = true;
        btn_Checked.onClick.Add(() =>
        {
            if (btn_Checked.GetChildAt(1).visible == true)
            {
                btn_Checked.GetChildAt(1).visible = false;

                isValidUserTerms = false;
            }
            else
            {
                btn_Checked.GetChildAt(1).visible = true;

                isValidUserTerms = true;
            }

        }); ;


        GButton btn_Skip = _win_UserRegister.contentPane.GetChild("Btn_Skip").asButton;
        btn_Skip.text = LocalizationManager.Instance.GetLocalizedValue("Skip this for now");
        btn_Skip.onClick.Add(() =>
        {
            PlayClickSound();
            if (_win_UserRegister != null)
            {
                _win_UserRegister.Hide();
            }

            if (_win_ChangePassword != null)
            {
                _win_ChangePassword.Hide();
            }

            if (_win_TipUserRegister != null)
            {
                _win_TipUserRegister.Show();
            }
        });

        string textClose = LocalizationManager.Instance.GetLocalizedValue("Close");

        GButton btn_Privacy = _win_UserRegister.contentPane.GetChild("Btn_Privacy").asButton;
        btn_Privacy.text = LocalizationManager.Instance.GetLocalizedValue("Privacy Policy");
        btn_Privacy.onClick.Add(() =>
        {
            PlayClickSound();

            //Application.OpenURL(_webUrlUserPrivacy);
            _pz_webview.ShowWebView(_webUrlUserPrivacy, textClose);

        });


        GButton btn_UserTerms = _win_UserRegister.contentPane.GetChild("Btn_UserTerms").asButton;
        btn_UserTerms.text = LocalizationManager.Instance.GetLocalizedValue("terms of use");
        btn_UserTerms.onClick.Add(() =>
        {
            PlayClickSound();
            //Application.OpenURL(_webUrlUserTerms);
            _pz_webview.ShowWebView(_webUrlUserTerms, textClose);
        });


        GImage icon_checked_email = _win_UserRegister.contentPane.GetChild("checked_email").asImage;

        GTextField text_Warn_Email = _win_UserRegister.contentPane.GetChild("Text_Warn_Email").asTextField;

        GTextInput input_email = _win_UserRegister.contentPane.GetChild("input_email").asTextInput;
        input_email.onFocusOut.Add(() =>
        {
            if (input_email.text.Length > 0)
            {
                string pattern = @"^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$";

                isValidEmail = Regex.IsMatch(input_email.text, pattern);

                if (isValidEmail)
                {
                    text_Warn_Email.text = "";
                    icon_checked_email.visible = true;
                }
                else
                {
                    text_Warn_Email.text = LocalizationManager.Instance.GetLocalizedValue("email error");
                    icon_checked_email.visible = false;
                }
            }
        });

        input_email.onChanged.Add(() => { text_Warn_Email.text = ""; });

        GImage icon_checked_password = _win_UserRegister.contentPane.GetChild("checked_password").asImage;

        GImage icon_checked_password_Length = _win_UserRegister.contentPane.GetChild("checked_Password_Length").asImage;
        GImage icon_checked_password_LowerLetter = _win_UserRegister.contentPane.GetChild("checked_Password_LowerLetter").asImage;
        GImage icon_checked_password_UpperLetter = _win_UserRegister.contentPane.GetChild("checked_Password_UpperLetter").asImage;
        GImage icon_checked_password_Nubmer = _win_UserRegister.contentPane.GetChild("checked_Password_Nubmer").asImage;
        GImage icon_checked_password_Symbols = _win_UserRegister.contentPane.GetChild("checked_Password_Symbols").asImage;


        GTextField text_Warn_password_Length = _win_UserRegister.contentPane.GetChild("Text_Warn_Password_Length").asTextField;
        text_Warn_password_Length.text = LocalizationManager.Instance.GetLocalizedValue("Password must be between 8 and 20 characters in length");

        GTextField text_Warn_password_LowerLetter = _win_UserRegister.contentPane.GetChild("Text_Warn_Password_LowerLetter").asTextField;
        text_Warn_password_LowerLetter.text = LocalizationManager.Instance.GetLocalizedValue("at least one lowercase letter");

        GTextField text_Warn_password_UpperLetter = _win_UserRegister.contentPane.GetChild("Text_Warn_Password_UpperLetter").asTextField;
        text_Warn_password_UpperLetter.text = LocalizationManager.Instance.GetLocalizedValue("at least one uppercase letter");

        GTextField text_Warn_password_Nubmer = _win_UserRegister.contentPane.GetChild("Text_Warn_Password_Number").asTextField;
        text_Warn_password_Nubmer.text = LocalizationManager.Instance.GetLocalizedValue("at least one digit");

        GTextField text_Warn_password_Symbols = _win_UserRegister.contentPane.GetChild("Text_Warn_Password_Symbols").asTextField;
        text_Warn_password_Symbols.text = LocalizationManager.Instance.GetLocalizedValue("not include any symbols");

        GTextInput input_password = _win_UserRegister.contentPane.GetChild("input_password").asTextInput;
        input_password.promptText = LocalizationManager.Instance.GetLocalizedValue("Password");
        input_password.onFocusOut.Add(() =>
        {
            if (input_password.text.Length > 0)
            {
                icon_checked_password.visible = false;

                CheckPasswordValid(icon_checked_password_Length, text_Warn_password_Length, @"^.{8,20}$", input_password.text);
                CheckPasswordValid(icon_checked_password_LowerLetter, text_Warn_password_LowerLetter, @"^(?=.*[a-z]).+$", input_password.text);
                CheckPasswordValid(icon_checked_password_UpperLetter, text_Warn_password_UpperLetter, @"^(?=.*[A-Z]).+$", input_password.text);
                CheckPasswordValid(icon_checked_password_Nubmer, text_Warn_password_Nubmer, @"^(?=.*\d).+$", input_password.text);
                CheckPasswordValid(icon_checked_password_Symbols, text_Warn_password_Symbols, @"^[a-zA-Z0-9]+$", input_password.text);

                string pattern = @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,20}$";

                //.text = "Password123";
                isValidPassword = Regex.IsMatch(input_password.text, pattern);

                if (isValidPassword)
                {
                    icon_checked_password.visible = true;
                }
            }
        });


        GLoader btn_ShowHidePassword = _win_UserRegister.contentPane.GetChild("Btn_ShowHidePwd").asLoader;

        input_password.displayAsPassword = true;

        btn_ShowHidePassword.onClick.Add(() =>
        {
            if (input_password.displayAsPassword)
            {

                GameUtility_FGUI.ChangeSprite(btn_ShowHidePassword, packageName, "HideForPassword");

                input_password.displayAsPassword = false;
            }
            else
            {
                GameUtility_FGUI.ChangeSprite(btn_ShowHidePassword, packageName, "ShowForPassword");

                input_password.displayAsPassword = true;
            }

        });


        GButton btn_Sumbit = _win_UserRegister.contentPane.GetChild("Btn_Sumbit").asButton;
        btn_Sumbit.text = LocalizationManager.Instance.GetLocalizedValue("Sign Up");

        GTextField text_AgreePrivacy = _win_UserRegister.contentPane.GetChild("Text_AgreePrivacy").asTextField;
        text_AgreePrivacy.text = LocalizationManager.Instance.GetLocalizedValue("By proceeding, you agree to our");

        GButton btn_AppleLogin = _win_UserRegister.contentPane.GetChild("Btn_AppleLogin").asButton;

        //用户注册提交
        btn_Sumbit.onClick.Add(() =>
        {

            if (isValidEmail && isValidPassword)
            {
                if (isValidUserTerms)
                {
                    btn_Sumbit.enabled = false;
                    text_AgreePrivacy.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);

                    text_Warn_Email.text = LocalizationManager.Instance.GetLocalizedValue("Processing...");

                    //先检查邮件地址是否注册
                    ec_Apps_BackEnd_Api.OnCheckEmailFinish += (bool isEmailExists) =>
                    {
                        if (isEmailExists)
                        {
                            Debug.Log("Email账号已经存在！直接登录");
                            //text_Warn_Email.text = "Email账号已经存在！";

                            ec_Apps_BackEnd_Api.OnUserLoginFinish += (bool loginDone, string msg) =>
                            {
                                if (loginDone)//Email和密码正确
                                {
                                    //直接登录成功
                                    text_Warn_Email.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_green);
                                    text_Warn_Email.text = LocalizationManager.Instance.GetLocalizedValue("Successfully logged in");

                                    PlayerPrefs.SetString(GlobalVariable.accountInfo_AppleUserIdKey, "");
                                    PlayerPrefs.SetInt(GlobalVariable.accountInfo_IsAppleIdAccount, 0);
                                    PlayerPrefs.SetString(GlobalVariable.accountInfo_save_DisplayName_flag, input_email.text.ToLower());

                                    ShowLoginedUserPanel(true, page_myAccount);
                                    _win_UserRegister.Hide();
                                }
                                else//Email和密码不正确，
                                {
                                    text_Warn_Email.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_red);
                                    text_Warn_Email.text = LocalizationManager.Instance.GetLocalizedValue("The email account has already been registered");
                                    btn_Sumbit.enabled = true;

                                    icon_checked_email.visible = false;
                                    icon_checked_password.visible = false;
                                }

                            };

                            //用户直接登录尝试
                            UserLogin_Action(input_email.text.ToLower(), input_password.text);


                        }
                        else
                        {
                            //邮件地址没有被注册，开始注册用户流程
                            Debug.Log("user register begin");

                            UserRegister_Action(input_email.text.ToLower(), input_password.text, page_myAccount, text_Warn_Email);

                        }

                    };


                    if (this != null && gameObject != null)
                    {
                        StartCoroutine(ec_Apps_BackEnd_Api.CheckEmailExists(input_email.text.ToLower()));
                    }
                    else
                    {
                        Debug.LogWarning("The object has been destroyed, not starting coroutine.");
                    }

                    btn_AppleLogin.visible = false;

                }
                else
                {
                    text_AgreePrivacy.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_red);
                    btn_Sumbit.enabled = true;
                }

            }
        });


        btn_AppleLogin.onClick.Add(() =>
        {
            PlayClickSound();

            if (isValidUserTerms)
            {
                ShowHideMessageBox(_win_UserRegister.contentPane, true);
                SignInWithApple(btn_AppleLogin, page_myAccount, _win_UserRegister, true);
            }
            else
            {
                text_AgreePrivacy.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_red);
                btn_Sumbit.enabled = true;
            }
        });

        btn_AppleLogin.visible = isAppleLoginSupportedPlatform;
    }

    void UserRegister_Action(string email, string password, GComponent page_myAccount, GTextField text_Warn_Email)
    {
        string user_UnionId = PlayerPrefs.GetString(GlobalVariable.accountInfo_save_UnionId_flag);
        string user_author_email = email;

        EC_Apps_BackEnd_Api.UserRegsiterData userRegsiterData = new EC_Apps_BackEnd_Api.UserRegsiterData
        {
            user_name = PlayerPrefs.GetString(GlobalVariable.accountInfo_save_DisplayName_flag, "username"),
            author_email = user_author_email,
            union_id = user_UnionId,
            email = email,
            password = password,
            channel = GlobalVariable.appChannel.ToString().ToLower(),
            user_language = Application.systemLanguage.ToString().ToLower()
        };

        //开始注册提交email和密码，回调检查是否注册成功
        ec_Apps_BackEnd_Api.OnUserRegsiterFinish += (bool finished, string msg) =>
        {
            if (finished)
            {
                Debug.Log("OnUserRegsiterFinish");

                PlayerPrefs.SetString(GlobalVariable.accountInfo_AppleUserIdKey, "");
                PlayerPrefs.SetInt(GlobalVariable.accountInfo_IsAppleIdAccount, 0);
                PlayerPrefs.SetString(GlobalVariable.accountInfo_save_DisplayName_flag, email);


                ShowLoginedUserPanel(true, page_myAccount);
                _win_UserRegister.Hide();

            }
            else
            {
                Debug.Log($"OnUserRegsiterFinish: {msg}");
                text_Warn_Email.text = LocalizationManager.Instance.GetLocalizedValue("Registration failed, please contact customer service for assistance");
            }
        };


        if (this != null && gameObject != null)
        {
            StartCoroutine(ec_Apps_BackEnd_Api.UserRegsiter(userRegsiterData));
        }
        else
        {
            Debug.LogWarning("The object has been destroyed, not starting coroutine.");
        }
    }

    void Init_UserChangePassword(GComponent page_myAccount)
    {
        Create_Window_ChangePassword();

        GTextField text_Warn_OldPwd = _win_ChangePassword.contentPane.GetChild("Text_Warn_OldPwd").asTextField;
        GTextField text_Warn_NewPwd = _win_ChangePassword.contentPane.GetChild("Text_Warn_NewPwd").asTextField;

        GTextInput input_new_pwd = _win_ChangePassword.contentPane.GetChild("input_new_pwd").asTextInput;
        GTextInput input_old_pwd = _win_ChangePassword.contentPane.GetChild("input_old_pwd").asTextInput;

        GLoader btn_ShowHideNewPwd = _win_ChangePassword.contentPane.GetChild("Btn_ShowHideNewPwd").asLoader;
        GLoader btn_ShowHideOldPwd = _win_ChangePassword.contentPane.GetChild("Btn_ShowHideOldPwd").asLoader;

        bool isValidOldPassword = false;
        bool isValidNewPassword = false;

        input_new_pwd.displayAsPassword = true;
        input_old_pwd.displayAsPassword = true;


        GImage icon_checked_password = _win_ChangePassword.contentPane.GetChild("checked_new_pwd").asImage;

        GImage icon_checked_password_Length = _win_ChangePassword.contentPane.GetChild("checked_Password_Length").asImage;
        GImage icon_checked_password_LowerLetter = _win_ChangePassword.contentPane.GetChild("checked_Password_LowerLetter").asImage;
        GImage icon_checked_password_UpperLetter = _win_ChangePassword.contentPane.GetChild("checked_Password_UpperLetter").asImage;
        GImage icon_checked_password_Nubmer = _win_ChangePassword.contentPane.GetChild("checked_Password_Nubmer").asImage;
        GImage icon_checked_password_Symbols = _win_ChangePassword.contentPane.GetChild("checked_Password_Symbols").asImage;

        GTextField text_Warn_password_Length = _win_ChangePassword.contentPane.GetChild("Text_Warn_Password_Length").asTextField;
        text_Warn_password_Length.text = LocalizationManager.Instance.GetLocalizedValue("Password must be between 8 and 20 characters in length");

        GTextField text_Warn_password_LowerLetter = _win_ChangePassword.contentPane.GetChild("Text_Warn_Password_LowerLetter").asTextField;
        text_Warn_password_LowerLetter.text = LocalizationManager.Instance.GetLocalizedValue("at least one lowercase letter");

        GTextField text_Warn_password_UpperLetter = _win_ChangePassword.contentPane.GetChild("Text_Warn_Password_UpperLetter").asTextField;
        text_Warn_password_UpperLetter.text = LocalizationManager.Instance.GetLocalizedValue("at least one uppercase letter");

        GTextField text_Warn_password_Nubmer = _win_ChangePassword.contentPane.GetChild("Text_Warn_Password_Number").asTextField;
        text_Warn_password_Nubmer.text = LocalizationManager.Instance.GetLocalizedValue("at least one digit");

        GTextField text_Warn_password_Symbols = _win_ChangePassword.contentPane.GetChild("Text_Warn_Password_Symbols").asTextField;
        text_Warn_password_Symbols.text = LocalizationManager.Instance.GetLocalizedValue("not include any symbols");


        input_new_pwd.onFocusOut.Add(() =>
        {
            icon_checked_password.visible = false;

            CheckPasswordValid(icon_checked_password_Length, text_Warn_password_Length, @"^.{8,20}$", input_new_pwd.text);
            CheckPasswordValid(icon_checked_password_LowerLetter, text_Warn_password_LowerLetter, @"^(?=.*[a-z]).+$", input_new_pwd.text);
            CheckPasswordValid(icon_checked_password_UpperLetter, text_Warn_password_UpperLetter, @"^(?=.*[A-Z]).+$", input_new_pwd.text);
            CheckPasswordValid(icon_checked_password_Nubmer, text_Warn_password_Nubmer, @"^(?=.*\d).+$", input_new_pwd.text);
            CheckPasswordValid(icon_checked_password_Symbols, text_Warn_password_Symbols, @"^[a-zA-Z0-9]+$", input_new_pwd.text);

            string pattern = @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,20}$";

            //.text = "Password123";
            isValidNewPassword = Regex.IsMatch(input_new_pwd.text, pattern);

            if (isValidNewPassword)
            {
                icon_checked_password.visible = true;
            }

        });

        GButton winBtnClose = _win_ChangePassword.contentPane.GetChild("closeButton").asButton;
        winBtnClose.onClick.Add(() =>
        {
            PlayClickSound();

            _win_ChangePassword.Hide();

            text_Warn_OldPwd.text = "";
            text_Warn_NewPwd.text = "";

            input_new_pwd.text = "";
            input_old_pwd.text = "";

            input_new_pwd.displayAsPassword = false;
            input_old_pwd.displayAsPassword = false;

            icon_checked_password.visible = false;

            icon_checked_password_Length.visible = false;
            icon_checked_password_LowerLetter.visible = false;
            icon_checked_password_UpperLetter.visible = false;
            icon_checked_password_Nubmer.visible = false;
            icon_checked_password_Symbols.visible = false;

            text_Warn_password_Length.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);
            text_Warn_password_LowerLetter.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);
            text_Warn_password_UpperLetter.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);
            text_Warn_password_Nubmer.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);
            text_Warn_password_Symbols.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);

            GameUtility_FGUI.ChangeSprite(btn_ShowHideNewPwd, packageName, "ShowForPassword");
            GameUtility_FGUI.ChangeSprite(btn_ShowHideOldPwd, packageName, "ShowForPassword");

        });



        btn_ShowHideNewPwd.onClick.Add(() =>
        {
            if (input_new_pwd.displayAsPassword)
            {
                GameUtility_FGUI.ChangeSprite(btn_ShowHideNewPwd, packageName, "HideForPassword");

                input_new_pwd.displayAsPassword = false;
            }
            else
            {
                GameUtility_FGUI.ChangeSprite(btn_ShowHideNewPwd, packageName, "ShowForPassword");

                input_new_pwd.displayAsPassword = true;
            }

        });

        btn_ShowHideOldPwd.onClick.Add(() =>
        {
            if (input_old_pwd.displayAsPassword)
            {
                GameUtility_FGUI.ChangeSprite(btn_ShowHideOldPwd, packageName, "HideForPassword");

                input_old_pwd.displayAsPassword = false;
            }
            else
            {
                GameUtility_FGUI.ChangeSprite(btn_ShowHideOldPwd, packageName, "ShowForPassword");

                input_old_pwd.displayAsPassword = true;
            }

        });

        GButton btn_Sumbit = _win_ChangePassword.contentPane.GetChild("Btn_Sumbit").asButton;
        btn_Sumbit.text = LocalizationManager.Instance.GetLocalizedValue("Submit");

        //用户修改密码
        btn_Sumbit.onClick.Add(() =>
        {
            text_Warn_OldPwd.text = "";
            text_Warn_NewPwd.text = "";
            string pattern = @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,20}$";

            //.text = "Password123";
            isValidNewPassword = Regex.IsMatch(input_new_pwd.text, pattern);

            if (!isValidNewPassword && input_new_pwd.text.Length > 0)
            {
                text_Warn_NewPwd.text = LocalizationManager.Instance.GetLocalizedValue("password error");
            }
            else
            {
                text_Warn_NewPwd.text = "";
            }

            isValidOldPassword = Regex.IsMatch(input_old_pwd.text, pattern);

            if (!isValidOldPassword && input_old_pwd.text.Length > 0)
            {
                text_Warn_OldPwd.text = LocalizationManager.Instance.GetLocalizedValue("password error");
            }
            else
            {
                text_Warn_OldPwd.text = "";
            }


            //用户开始登录过程

            if ((input_old_pwd.text == input_new_pwd.text) && (input_old_pwd.text.Length > 0 && input_new_pwd.text.Length > 0))
            {
                text_Warn_NewPwd.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_red);
                text_Warn_NewPwd.text = LocalizationManager.Instance.GetLocalizedValue("The current password is the same as the new password, please re-enter!");
            }
            else if (isValidNewPassword && isValidOldPassword)
            {
                Debug.Log("user modify password begin");
                text_Warn_OldPwd.text = "";
                text_Warn_NewPwd.text = LocalizationManager.Instance.GetLocalizedValue("Updating...");
                text_Warn_OldPwd.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);
                text_Warn_NewPwd.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_black);

                ec_Apps_BackEnd_Api.OnUserChangePwdFinish += (string msg) =>
                {
                    if (msg == "password changed successfully")
                    {
                        text_Warn_NewPwd.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_red);
                        text_Warn_NewPwd.text = LocalizationManager.Instance.GetLocalizedValue("Update successful!"); ;

                        ShowLoginedUserPanel(true, page_myAccount);


                        if (this != null && gameObject != null)
                        {
                            StartCoroutine(DelayCloseWin(_win_ChangePassword, 2));
                        }
                        else
                        {
                            Debug.LogWarning("The object has been destroyed, not starting coroutine.");
                        }
                    }
                    else
                    {
                        if (msg == "both password is same")
                        {
                            msg = LocalizationManager.Instance.GetLocalizedValue("The current password is the same as the new password, please re-enter!");
                        }

                        if (msg == "current password is incorrect")
                        {
                            msg = LocalizationManager.Instance.GetLocalizedValue("The current password is incorrect, please re-enter!");
                        }

                        text_Warn_NewPwd.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_red);
                        text_Warn_NewPwd.text = msg;
                        btn_Sumbit.enabled = true;
                    }

                };

                UserChangePwd_Action(input_old_pwd.text, input_new_pwd.text);

                btn_Sumbit.enabled = false;
            }
        });

    }

    public IEnumerator DelayCloseWin(Window win, float delay = 2)
    {
        yield return new WaitForSeconds(delay);
        win.Hide();

        if (win.Equals(_win_ChangePassword))
        {
            Debug.Log("is _win_ChangePassword");
            win = null;
            Init_UserChangePassword(_win_ChangePassword.parent);
        }
    }

    void CheckPasswordValid(GImage icon_checked, GTextField text_Warn, string reg_pattern, string input_text)
    {
        if (input_text.Length > 0)
        {
            bool isValid = Regex.IsMatch(input_text, reg_pattern);

            if (isValid)
            {
                icon_checked.visible = true;
                text_Warn.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_green);
            }
            else
            {
                icon_checked.visible = false;
                text_Warn.color = GameUtility_FGUI.ConvertHex16_ToColor(font_color_red);
            }
        }


    }

    void ShowAccountCenterUserProfile(GComponent view, bool isAppleUserId = false)
    {
        //GComponent panel_AccountCenter = view.GetChild("Panel_AccountCenter").asCom;
        GComponent panel_AccountCenter = _panel_AccountCenter;
        GTextField txt_useremail = panel_AccountCenter.GetChild("txt_useremail").asTextField;

        GLoader icon_logouser = panel_AccountCenter.GetChild("icon_logouser") as GLoader;
        txt_useremail.text = GameUtility.LimitStringLength(PlayerPrefs.GetString(GlobalVariable.accountInfo_save_Email_flag), 20);

        GButton btn_logOut = panel_AccountCenter.GetChild("btn_logout").asButton;
        btn_logOut.visible = true;
        if (isAppleUserId)
        {
            //btn_logOut.visible = false;
            GameUtility_FGUI.ChangeSprite(icon_logouser, packageName, "Icon_MyAccountApple");
        }
        else
        {
            //btn_logOut.visible = true;
            GameUtility_FGUI.ChangeSprite(icon_logouser, packageName, "Icon_MyAccount");
        }


    }

    private void Create_Window_UserRegister()
    {
        if (_win_UserRegister == null)
            _win_UserRegister = new FairyGUI.Window();

        _win_UserRegister.contentPane = UIPackage.CreateObject(packageName, "Dox_CreateAccount").asCom;
        _win_UserRegister.SetXY((GRoot.inst.width - _win_UserRegister.width) / 2, (GRoot.inst.height - _win_UserRegister.height) / 2);
        _win_UserRegister.modal = true;

        _win_UserRegister.contentPane.GetChild("title").text = LocalizationManager.Instance.GetLocalizedValue("Member Registration");

        ShowHideMessageBox(_win_UserRegister.contentPane, false);
    }

    private void Create_Window_UserLogin()
    {
        if (_winUserLogin == null)
            _winUserLogin = new FairyGUI.Window();

        _winUserLogin.contentPane = UIPackage.CreateObject(packageName, "Dox_UserLogin").asCom;
        _winUserLogin.SetXY((GRoot.inst.width - _winUserLogin.width) / 2, (GRoot.inst.height - _winUserLogin.height) / 2);
        _winUserLogin.modal = true;

        _winUserLogin.contentPane.GetChild("title").text = LocalizationManager.Instance.GetLocalizedValue("Member Login");

        ShowHideMessageBox(_winUserLogin.contentPane, false);
    }

    private void Create_Window_Tip_UserLogin()
    {

        if (_win_TipUserRegister == null)
            _win_TipUserRegister = new FairyGUI.Window();


        _win_TipUserRegister.contentPane = UIPackage.CreateObject(packageName, "Dox_TipForAppleLogin").asCom;
        _win_TipUserRegister.SetXY((GRoot.inst.width - _win_TipUserRegister.width) / 2, (GRoot.inst.height - _win_TipUserRegister.height) / 2);
        _win_TipUserRegister.modal = true;

        _win_TipUserRegister.contentPane.GetChild("title").text = LocalizationManager.Instance.GetLocalizedValue("Important notice");
        _win_TipUserRegister.contentPane.GetChild("detail").text = LocalizationManager.Instance.GetLocalizedValue("Please create a member account to access our other apps.");
        _win_TipUserRegister.contentPane.GetChild("Btn_Sumbit").text = LocalizationManager.Instance.GetLocalizedValue("OK");
    }

    private void Create_Window_ChangePassword()
    {
        if (_win_ChangePassword == null)
            _win_ChangePassword = new FairyGUI.Window();

        _win_ChangePassword.contentPane = UIPackage.CreateObject(packageName, "Dox_ChangePassword").asCom;
        _win_ChangePassword.SetXY((GRoot.inst.width - _win_ChangePassword.width) / 2, (GRoot.inst.height - _win_ChangePassword.height) / 2);
        _win_ChangePassword.modal = true;

        _win_ChangePassword.contentPane.GetChild("title").text = LocalizationManager.Instance.GetLocalizedValue("Change Member Password");

    }

    private void Create_Window_ForgetPassword()
    {
        if (_win_ForgetPassword == null)
            _win_ForgetPassword = new FairyGUI.Window();

        _win_ForgetPassword.contentPane = UIPackage.CreateObject(packageName, "Dox_ForgetPassword").asCom;
        _win_ForgetPassword.SetXY((GRoot.inst.width - _win_ForgetPassword.width) / 2,
            (GRoot.inst.height - _win_ForgetPassword.height) / 2);
        _win_ForgetPassword.modal = true;

        _win_ForgetPassword.contentPane.GetChild("title").text = LocalizationManager.Instance.GetLocalizedValue("Forgot Member Password");

    }

    private void SignInWithApple(GButton btn, GComponent page_myAccount, FairyGUI.Window win, bool isRegsiter = false)
    {

        if (Application.platform == RuntimePlatform.IPhonePlayer)
        {

            var loginArgs = new AppleAuthLoginArgs(LoginOptions.IncludeEmail | LoginOptions.IncludeFullName);

            this._appleAuthManager.LoginWithAppleId(
                loginArgs,
                credential =>
                {
                    // If a sign in with apple succeeds, we should have obtained the credential with the user id, name, and email, save it

                    //this.SetupGameMenu(credential.User, credential);
                    var appleIdCredential = credential as IAppleIDCredential;
                    if (appleIdCredential != null)
                    {
                        // Apple User ID
                        // You should save the user ID somewhere in the device
                        var userId = appleIdCredential.User;
                        PlayerPrefs.SetString(GlobalVariable.accountInfo_AppleUserIdKey, credential.User);
                        Debug.Log($"credential.User: {credential.User}");

                        // Email (Received ONLY in the first login)
                        var email = appleIdCredential.Email;
                        if (email != null)
                        {
                            PlayerPrefs.SetString(GlobalVariable.accountInfo_save_Email_flag, email);
                            Debug.Log($"appleIdCredential.Email: {email}");
                        }


                        // Full name (Received ONLY in the first login)
                        //var fullName = appleIdCredential.FullName;
                        //PlayerPrefs.SetString(GlobalVariable.accountInfo_save_DisplayName_flag, email);

                        //Debug.Log($"fullName: {fullName}");
                        // Identity token
                        var identityToken = Encoding.UTF8.GetString(
                            appleIdCredential.IdentityToken,
                            0,
                            appleIdCredential.IdentityToken.Length);

                        Debug.Log($"identityToken: {identityToken}");

                        // Authorization code
                        var authorizationCode = Encoding.UTF8.GetString(
                            appleIdCredential.AuthorizationCode,
                            0,
                            appleIdCredential.AuthorizationCode.Length);

                        Debug.Log($"AuthorizationCode: {authorizationCode}");

                        if (appleIdCredential.FullName != null)
                        {
                            var fullName = appleIdCredential.FullName;
                            fullName.ToLocalizedString();
                            PlayerPrefs.SetString(GlobalVariable.accountInfo_save_DisplayName_flag, fullName.ToLocalizedString());

                            Debug.Log($"fullName: {fullName.ToLocalizedString()}");
                        }

                        UserAppleIdLogin(btn, page_myAccount, authorizationCode, isRegsiter);

                        // And now you have all the information to create/login a user in your system
                    }


                },
                error =>
                {
                    var authorizationErrorCode = error.GetAuthorizationErrorCode();
                    string msg = $"Sign in with Apple failed {authorizationErrorCode.ToString()} {error.ToString()}";
                    Debug.LogWarning(msg);
                    win.Hide();
                    //ShowDialogs(msg);
                    //this.SetupLoginMenuForSignInWithApple();
                });

            /*
            var quickLoginArgs = new AppleAuthQuickLoginArgs();
            this._appleAuthManager.QuickLogin(
                quickLoginArgs,
                credential =>
                {
                    // If it's an Apple credential, save the user ID, for later logins
                    var appleIdCredential = credential as IAppleIDCredential;
                    if (appleIdCredential != null)
                    {
                        PlayerPrefs.SetString(GlobalVariable.accountInfo_AppleUserIdKey, credential.User);    
                    }

                    SetupAppleData(credential.User, credential);
                },
                error =>
                {
                    // If Quick Login fails, we should show the normal sign in with apple menu, to allow for a normal Sign In with apple
                    var authorizationErrorCode = error.GetAuthorizationErrorCode();
                    Debug.LogWarning("Quick Login Failed " + authorizationErrorCode.ToString() + " " + error.ToString());
                    //this.SetupLoginMenuForSignInWithApple();
                });
                */
        }
        else
        {
            UserAppleIdLogin(btn, page_myAccount, "authorizationCode", isRegsiter);

        }

    }

    public void SetupAppleData(string appleUserId, ICredential receivedCredential)
    {
        Debug.Log("Apple User ID: " + appleUserId);
        if (receivedCredential == null)
        {
            Debug.Log("NO CREDENTIALS RECEIVED\nProbably credential status for " + appleUserId + "was Authorized");
            return;
        }

        var appleIdCredential = receivedCredential as IAppleIDCredential;
        var passwordCredential = receivedCredential as IPasswordCredential;
        if (appleIdCredential != null)
        {
            var stringBuilder = new StringBuilder();
            stringBuilder.AppendLine("RECEIVED APPLE ID CREDENTIAL.\nYOU CAN LOGIN/CREATE A USER WITH THIS");
            stringBuilder.AppendLine("<b>Username:</b> " + appleIdCredential.User);
            stringBuilder.AppendLine("<b>Real user status:</b> " + appleIdCredential.RealUserStatus.ToString());

            if (appleIdCredential.State != null)
                stringBuilder.AppendLine("<b>State:</b> " + appleIdCredential.State);

            if (appleIdCredential.IdentityToken != null)
            {
                var identityToken = Encoding.UTF8.GetString(appleIdCredential.IdentityToken, 0, appleIdCredential.IdentityToken.Length);
                stringBuilder.AppendLine("<b>Identity token (" + appleIdCredential.IdentityToken.Length + " bytes)</b>");
                stringBuilder.AppendLine(identityToken.Substring(0, 45) + "...");
            }

            if (appleIdCredential.AuthorizationCode != null)
            {
                var authorizationCode = Encoding.UTF8.GetString(appleIdCredential.AuthorizationCode, 0, appleIdCredential.AuthorizationCode.Length);
                stringBuilder.AppendLine("<b>Authorization Code (" + appleIdCredential.AuthorizationCode.Length + " bytes)</b>");
                stringBuilder.AppendLine(authorizationCode.Substring(0, 45) + "...");
            }

            if (appleIdCredential.AuthorizedScopes != null)
                stringBuilder.AppendLine("<b>Authorized Scopes:</b> " + string.Join(", ", appleIdCredential.AuthorizedScopes));

            if (appleIdCredential.Email != null)
            {
                stringBuilder.AppendLine();
                stringBuilder.AppendLine("<b>EMAIL RECEIVED: YOU WILL ONLY SEE THIS ONCE PER SIGN UP. SEND THIS INFORMATION TO YOUR BACKEND!</b>");
                stringBuilder.AppendLine("<b>You can test this again by revoking credentials in Settings</b>");
                stringBuilder.AppendLine("<b>Email:</b> " + appleIdCredential.Email);
            }

            if (appleIdCredential.FullName != null)
            {
                var fullName = appleIdCredential.FullName;
                stringBuilder.AppendLine();
                stringBuilder.AppendLine("<b>NAME RECEIVED: YOU WILL ONLY SEE THIS ONCE PER SIGN UP. SEND THIS INFORMATION TO YOUR BACKEND!</b>");
                stringBuilder.AppendLine("<b>You can test this again by revoking credentials in Settings</b>");
                stringBuilder.AppendLine("<b>Name:</b> " + fullName.ToLocalizedString());
                stringBuilder.AppendLine("<b>Name (Short):</b> " + fullName.ToLocalizedString(PersonNameFormatterStyle.Short));
                stringBuilder.AppendLine("<b>Name (Medium):</b> " + fullName.ToLocalizedString(PersonNameFormatterStyle.Medium));
                stringBuilder.AppendLine("<b>Name (Long):</b> " + fullName.ToLocalizedString(PersonNameFormatterStyle.Long));
                stringBuilder.AppendLine("<b>Name (Abbreviated):</b> " + fullName.ToLocalizedString(PersonNameFormatterStyle.Abbreviated));

                if (appleIdCredential.FullName.PhoneticRepresentation != null)
                {
                    var phoneticName = appleIdCredential.FullName.PhoneticRepresentation;
                    stringBuilder.AppendLine("<b>Phonetic name:</b> " + phoneticName.ToLocalizedString());
                    stringBuilder.AppendLine("<b>Phonetic name (Short):</b> " + phoneticName.ToLocalizedString(PersonNameFormatterStyle.Short));
                    stringBuilder.AppendLine("<b>Phonetic name (Medium):</b> " + phoneticName.ToLocalizedString(PersonNameFormatterStyle.Medium));
                    stringBuilder.AppendLine("<b>Phonetic name (Long):</b> " + phoneticName.ToLocalizedString(PersonNameFormatterStyle.Long));
                    stringBuilder.AppendLine("<b>Phonetic name (Abbreviated):</b> " + phoneticName.ToLocalizedString(PersonNameFormatterStyle.Abbreviated));
                }
            }

            Debug.Log(stringBuilder.ToString());
        }
    }

    private void UserAppleIdLogin(GButton btn, GComponent page_myAccount, string author_code, bool isRegsiter = false)
    {
        //btn.enabled = false;

        EC_Apps_BackEnd_Api.AppleUserIdRegsiterData userRegsiterData = new EC_Apps_BackEnd_Api.AppleUserIdRegsiterData
        {
            user_name = PlayerPrefs.GetString(GlobalVariable.accountInfo_save_DisplayName_flag),
            author_email = PlayerPrefs.GetString(GlobalVariable.accountInfo_save_Email_flag),
            union_id = PlayerPrefs.GetString(GlobalVariable.accountInfo_AppleUserIdKey),
            email = PlayerPrefs.GetString(GlobalVariable.accountInfo_save_Email_flag),
            password = "",
            channel = GlobalVariable.appChannel.ToString().ToLower(),
            user_language = Application.systemLanguage.ToString().ToLower(),
            isAppleUserId = true,
            author_code = author_code,
            app_id = config_data["Config"]["app_id"].ToString()
        };

        bool hasRetoreExecuted = false;
        //开始注册提交email和密码，回调检查是否注册成功
        ec_Apps_BackEnd_Api.OnAppleUserLoginFinish += (bool finished, string msg) =>
        {
            if (hasRetoreExecuted) return;
            hasRetoreExecuted = true;
            Debug.Log("OnAppleUserLoginFinish");
            if (finished)
            {


                PlayerPrefs.SetInt(GlobalVariable.accountInfo_IsAppleIdAccount, 1);

                ShowLoginedUserPanel(true, page_myAccount, true);

                if (isRegsiter)
                {
                    _win_UserRegister.Hide();
                }
                else
                {
                    _winUserLogin.Hide();
                }
            }
            else
            {
                if (isRegsiter)
                {
                    _win_UserRegister.Hide();
                }
                else
                {
                    _winUserLogin.Hide();
                }

                if (msg == "not email setting" || msg == "user email account login")
                {
                    string usr_msg = LocalizationManager.Instance.GetLocalizedValue("You cannot use this account to sign in to an Apple account because it is already being used to sign in to a membership account.");
                    ShowDialogs(usr_msg);
                }
                else
                {
                    string usr_msg1 = LocalizationManager.Instance.GetLocalizedValue("An error has occurred. Please contact customer service for assistance.");
                    string usr_msg = $"{usr_msg1}: \n {msg}";
                    ShowDialogs(usr_msg);
                }

            }
        };


        if (this != null && gameObject != null)
        {
            StartCoroutine(ec_Apps_BackEnd_Api.UserLoginByAppleUserId(userRegsiterData, isRegsiter));
        }
        else
        {
            Debug.LogWarning("The object has been destroyed, not starting coroutine.");
        }

    }

    private void ShowDialogs(string msg)
    {
        NativeDialogs.Instance.ShowMessageBox("", msg, new string[] { LocalizationManager.Instance.GetLocalizedValue("OK") }, false, (string b) =>
        {

        });
    }


    #endregion

    void InitFAQPage()
    {
        Debug.Log("InitFAQPage: 开始创建FAQ页面");

        // 创建FAQ页面的UI容器
        _currentMainPageContainer = UIPackage.CreateObject(packageName, "Page_FAQ1").asCom;
        _mainView.AddChild(_currentMainPageContainer);
        Debug.Log("InitFAQPage: UI_Page_FAQ.CreateInstance() 成功");
        _currentMainPageContainer.GetChild("title").text = GetStringForLanguageTitle("FAQ");

        TextAsset qnaConfigAsset = Resources.Load<TextAsset>("Data_QNA_V2_Config");
        if (qnaConfigAsset == null)
        {
            Debug.LogError("PZ_Page_FAQ: 无法加载 Data_QNA_V3_Config 资源文件");
            return;
        }

        string qna_config_data = qnaConfigAsset.text;
        Debug.Log($"PZ_Page_FAQ: 成功加载QNA配置数据，长度: {qna_config_data.Length}");

        qna_data = JsonMapper.ToObject(qna_config_data);
        Debug.Log("PZ_Page_FAQ: 成功解析QNA配置数据");



        GList html_content_list = _currentMainPageContainer.GetChild("html_content").asList;
        if (html_content_list == null)
        {
            Debug.LogError("PZ_Page_FAQ: 无法找到 'html_content' 列表组件");
            return;
        }
        Debug.Log("PZ_Page_FAQ: 成功获取html_content列表组件");

        //html_content_list.SetVirtual();

        int qna_ItemCount = int.Parse(qna_data["Config"]["QNA_ItemCount"].ToString());
        Debug.Log($"PZ_Page_FAQ: QNA项目数量: {qna_ItemCount}");

        html_content_list.itemRenderer = RenderListItemForQNA;
        html_content_list.numItems = qna_ItemCount;
        Debug.Log("PZ_Page_FAQ: 设置列表渲染器和项目数量完成");

    }

    void RenderListItemForQNA(int index, GObject item)
    {
        index++;

        string rowKey = $"q{index.ToString()}";

        GComponent content_item = item.asCom;

        // 获取当前语言代码
        string languageCode = GlobalVariable.GetLanguageCode();

        // 从新的JSON结构中获取标题和内容
        string title = qna_data[rowKey]["title"][languageCode].ToString();
        string content = qna_data[rowKey]["content"][languageCode].ToString();

        // 清理标题的HTML标签并处理换行，转换为纯文本
        title = title.Replace("<p>", "").Replace("</p>", "\n");
        title = title.Replace("<br>", "\n").Replace("<br/>", "\n");
        // 移除多余的换行
        while (title.Contains("\n\n"))
        {
            title = title.Replace("\n\n", "\n");
        }
        title = title.Trim();

        // 清理内容的HTML标签并处理换行，转换为纯文本
        content = content.Replace("<p>", "").Replace("</p>", "\n");
        content = content.Replace("<br>", "\n").Replace("<br/>", "\n");
        // 移除多余的换行
        while (content.Contains("\n\n"))
        {
            content = content.Replace("\n\n", "\n");
        }
        content = content.Trim();

        // 组合标题和内容，使用纯文本格式
        string str_content = "◇ " + title + "\n" + content;

        var textField = new GTextField();

        // 先设置文本格式
        TextFormat textFormat = textField.textFormat;
        textFormat.size = 27;
        textFormat.color = new Color(0.2f, 0f, 0.2f, 1f); // #330033
        textField.textFormat = textFormat;

        // 设置合适的宽度
        int textWidth = 1510;
        textField.SetSize(textWidth, 100); // 先设置一个初始高度

        // 设置最大宽度，强制文本换行
        textField.maxWidth = textWidth;

        // 设置对齐方式
        textField.align = AlignType.Left;

        // 设置文本内容
        textField.text = str_content;

        // 设置autoSize为Height，根据内容自动调整高度
        textField.autoSize = AutoSizeType.Height;

        // 设置位置，添加顶部间距
        int topMargin = 20; // 顶部间距
        textField.position = new Vector2(10, topMargin);

        // 添加到容器
        content_item.AddChild(textField);

        // 计算实际需要的高度：文本高度 + 顶部间距 + 底部间距
        int bottomMargin = 30; // 底部间距，比顶部稍大一些
        int totalHeight = (int)textField.height + topMargin + bottomMargin;

        // 设置容器的实际高度，确保统一间距
        content_item.SetSize(260, totalHeight);

        Debug.Log($"FAQ项目 {index}: 文本高度={textField.height}, 总高度={totalHeight}");
    }
    
    string GetStringForLanguageTitle(string key)
    {
        string title = key;

        if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
        {
            title = config_data[key]["zh_CN"].ToString();
        }
        else
        {
            title = config_data[key]["zh_TW"].ToString();
        }

        return title;
    }

    void OnDestroy()
    {
        // 移除所有按钮的点击监听事件
        RemoveAllButtonListeners();
        
        // 清理Apple登录相关
        if (_appleAuthManager != null)
        {
            _appleAuthManager.SetCredentialsRevokedCallback(null);
            _appleAuthManager = null;
        }
        
        // 清理后端API事件
        if (ec_Apps_BackEnd_Api != null)
        {
            ec_Apps_BackEnd_Api.OnUserLogoutFinish = null;
            ec_Apps_BackEnd_Api.OnUserLoginFinish = null;
            ec_Apps_BackEnd_Api.OnUserRegsiterFinish = null;
            ec_Apps_BackEnd_Api.OnUserChangePwdFinish = null;
            ec_Apps_BackEnd_Api.OnGetVerifyCodeFinish = null;
            ec_Apps_BackEnd_Api.OnVerifyCodeFinish = null;
            ec_Apps_BackEnd_Api.OnCheckEmailExists = null;
            ec_Apps_BackEnd_Api.OnAppleUserLoginFinish = null;
            ec_Apps_BackEnd_Api.OnRetoreFinish = null;
            ec_Apps_BackEnd_Api.OnRestoreFail = null;
            ec_Apps_BackEnd_Api.OnRestoreJobComplete = null;
        }
        
        // 清理Apple订单检查事件
        if (_appleCheckOrder != null)
        {
            _appleCheckOrder.OnRestoreFail = null;
            _appleCheckOrder.OnRetoreFinish = null;
            _appleCheckOrder.OnRestoreJobComplete = null;
        }
        
        // 清理窗口引用
        _win_UserRegister = null;
        _winUserLogin = null;
        _win_TipUserRegister = null;
        _win_ChangePassword = null;
        _win_ForgetPassword = null;
        
        // 清理其他引用
        _mainView = null;
        _currentMainPageContainer = null;
        _panel_AccountCenter = null;
        _panel_UserReadyLogin = null;
        _btn_Regsiter = null;
        _pz_webview = null;
        _nav_button_list = null;
        _leftNavButtons = null;
        
        // 清理数组
        middle_ads_array = null;
        middle_ads_btn_num_array = null;
        leftNavIconButton_array = null;
        page_GComponent_array = null;
        navButtons_array = null;
        
        // 清理数据
        config_data = null;
        qna_data = null;
        accountInfo_data = null;
    }

    void RemoveAllButtonListeners()
    {
        // 移除主视图中的按钮监听器
        if (_mainView != null)
        {
            GButton backBtn = _mainView.GetChild("BtnBackHome")?.asButton;
            if (backBtn != null)
            {
                backBtn.onClick.Clear();
            }
        }
        
        // 移除左侧导航按钮的监听器
        if (_leftNavButtons != null)
        {
            for (int i = 0; i < _leftNavButtons.Length; i++)
            {
                if (_leftNavButtons[i] is ParentZone_LeftNavIconButton navButton)
                {
                    navButton.onClick.Clear();
                }
            }
        }
        
        // 移除账户中心面板中的按钮监听器
        if (_panel_AccountCenter != null)
        {
            GButton resetPwdBtn = _panel_AccountCenter.GetChild("Btn_ResetPwd")?.asButton;
            if (resetPwdBtn != null)
            {
                resetPwdBtn.onClick.Clear();
            }
            
            GButton syncOrdersBtn = _panel_AccountCenter.GetChild("Btn_SyncOrders")?.asButton;
            if (syncOrdersBtn != null)
            {
                syncOrdersBtn.onClick.Clear();
            }
            
            GButton logoutBtn = _panel_AccountCenter.GetChild("btn_logout")?.asButton;
            if (logoutBtn != null)
            {
                logoutBtn.onClick.Clear();
            }
        }
        
        // 移除用户登录准备面板中的按钮监听器
        if (_panel_UserReadyLogin != null)
        {
            if (_btn_Regsiter != null)
            {
                _btn_Regsiter.onClick.Clear();
            }
            
            GButton loginBtn = _panel_UserReadyLogin.GetChild("Btn_Login")?.asButton;
            if (loginBtn != null)
            {
                loginBtn.onClick.Clear();
            }
        }
        
        // 移除用户注册窗口中的按钮监听器
        if (_win_UserRegister != null && _win_UserRegister.contentPane != null)
        {
            GButton closeBtn = _win_UserRegister.contentPane.GetChild("closeButton")?.asButton;
            if (closeBtn != null)
            {
                closeBtn.onClick.Clear();
            }
            
            GButton submitBtn = _win_UserRegister.contentPane.GetChild("Btn_Sumbit")?.asButton;
            if (submitBtn != null)
            {
                submitBtn.onClick.Clear();
            }
            
            GButton skipBtn = _win_UserRegister.contentPane.GetChild("Btn_Skip")?.asButton;
            if (skipBtn != null)
            {
                skipBtn.onClick.Clear();
            }
            
            GButton privacyBtn = _win_UserRegister.contentPane.GetChild("Btn_Privacy")?.asButton;
            if (privacyBtn != null)
            {
                privacyBtn.onClick.Clear();
            }
            
            GButton userTermsBtn = _win_UserRegister.contentPane.GetChild("Btn_UserTerms")?.asButton;
            if (userTermsBtn != null)
            {
                userTermsBtn.onClick.Clear();
            }
            
            GButton checkedBtn = _win_UserRegister.contentPane.GetChild("Btn_Checked")?.asButton;
            if (checkedBtn != null)
            {
                checkedBtn.onClick.Clear();
            }
            
            GButton appleLoginBtn = _win_UserRegister.contentPane.GetChild("Btn_AppleLogin")?.asButton;
            if (appleLoginBtn != null)
            {
                appleLoginBtn.onClick.Clear();
            }
            
            // 移除密码显示/隐藏按钮监听器
            GLoader showHidePwdBtn = _win_UserRegister.contentPane.GetChild("Btn_ShowHidePwd")?.asLoader;
            if (showHidePwdBtn != null)
            {
                showHidePwdBtn.onClick.Clear();
            }
        }
        
        // 移除用户登录窗口中的按钮监听器
        if (_winUserLogin != null && _winUserLogin.contentPane != null)
        {
            GButton closeBtn = _winUserLogin.contentPane.GetChild("closeButton")?.asButton;
            if (closeBtn != null)
            {
                closeBtn.onClick.Clear();
            }
            
            GButton submitBtn = _winUserLogin.contentPane.GetChild("Btn_Sumbit")?.asButton;
            if (submitBtn != null)
            {
                submitBtn.onClick.Clear();
            }
            
            GButton privacyBtn = _winUserLogin.contentPane.GetChild("Btn_Privacy")?.asButton;
            if (privacyBtn != null)
            {
                privacyBtn.onClick.Clear();
            }
            
            GButton userTermsBtn = _winUserLogin.contentPane.GetChild("Btn_UserTerms")?.asButton;
            if (userTermsBtn != null)
            {
                userTermsBtn.onClick.Clear();
            }
            
            GButton checkedBtn = _winUserLogin.contentPane.GetChild("Btn_Checked")?.asButton;
            if (checkedBtn != null)
            {
                checkedBtn.onClick.Clear();
            }
            
            GButton forgetPwdBtn = _winUserLogin.contentPane.GetChild("Btn_ForgetPwd")?.asButton;
            if (forgetPwdBtn != null)
            {
                forgetPwdBtn.onClick.Clear();
            }
            
            GButton appleLoginBtn = _winUserLogin.contentPane.GetChild("Btn_AppleLogin")?.asButton;
            if (appleLoginBtn != null)
            {
                appleLoginBtn.onClick.Clear();
            }
            
            // 移除密码显示/隐藏按钮监听器
            GLoader showHidePwdBtn = _winUserLogin.contentPane.GetChild("Btn_ShowHidePwd")?.asLoader;
            if (showHidePwdBtn != null)
            {
                showHidePwdBtn.onClick.Clear();
            }
        }
        
        // 移除修改密码窗口中的按钮监听器
        if (_win_ChangePassword != null && _win_ChangePassword.contentPane != null)
        {
            GButton closeBtn = _win_ChangePassword.contentPane.GetChild("closeButton")?.asButton;
            if (closeBtn != null)
            {
                closeBtn.onClick.Clear();
            }
            
            GButton submitBtn = _win_ChangePassword.contentPane.GetChild("Btn_Sumbit")?.asButton;
            if (submitBtn != null)
            {
                submitBtn.onClick.Clear();
            }
            
            // 移除密码显示/隐藏按钮监听器
            GLoader showHideNewPwdBtn = _win_ChangePassword.contentPane.GetChild("Btn_ShowHideNewPwd")?.asLoader;
            if (showHideNewPwdBtn != null)
            {
                showHideNewPwdBtn.onClick.Clear();
            }
            
            GLoader showHideOldPwdBtn = _win_ChangePassword.contentPane.GetChild("Btn_ShowHideOldPwd")?.asLoader;
            if (showHideOldPwdBtn != null)
            {
                showHideOldPwdBtn.onClick.Clear();
            }
        }
        
        // 移除忘记密码窗口中的按钮监听器
        if (_win_ForgetPassword != null && _win_ForgetPassword.contentPane != null)
        {
            GButton closeBtn = _win_ForgetPassword.contentPane.GetChild("closeButton")?.asButton;
            if (closeBtn != null)
            {
                closeBtn.onClick.Clear();
            }
            
            GButton submitBtn = _win_ForgetPassword.contentPane.GetChild("Btn_Sumbit")?.asButton;
            if (submitBtn != null)
            {
                submitBtn.onClick.Clear();
            }
            
            // 移除密码显示/隐藏按钮监听器
            GLoader showHidePwdBtn = _win_ForgetPassword.contentPane.GetChild("Btn_ShowHidePwd")?.asLoader;
            if (showHidePwdBtn != null)
            {
                showHidePwdBtn.onClick.Clear();
            }
        }
        
        // 移除提示用户注册窗口中的按钮监听器
        if (_win_TipUserRegister != null && _win_TipUserRegister.contentPane != null)
        {
            GButton closeBtn = _win_TipUserRegister.contentPane.GetChild("closeButton")?.asButton;
            if (closeBtn != null)
            {
                closeBtn.onClick.Clear();
            }
            
            GButton submitBtn = _win_TipUserRegister.contentPane.GetChild("Btn_Sumbit")?.asButton;
            if (submitBtn != null)
            {
                submitBtn.onClick.Clear();
            }
        }
        
        // 移除输入框的事件监听器
        RemoveInputFieldListeners();
    }
    
    void RemoveInputFieldListeners()
    {
        // 移除用户注册窗口中的输入框监听器
        if (_win_UserRegister != null && _win_UserRegister.contentPane != null)
        {
            GTextInput emailInput = _win_UserRegister.contentPane.GetChild("input_email")?.asTextInput;
            if (emailInput != null)
            {
                emailInput.onFocusOut.Clear();
                emailInput.onChanged.Clear();
            }
            
            GTextInput passwordInput = _win_UserRegister.contentPane.GetChild("input_password")?.asTextInput;
            if (passwordInput != null)
            {
                passwordInput.onFocusOut.Clear();
            }
        }
        
        // 移除用户登录窗口中的输入框监听器
        if (_winUserLogin != null && _winUserLogin.contentPane != null)
        {
            GTextInput emailInput = _winUserLogin.contentPane.GetChild("input_email")?.asTextInput;
            if (emailInput != null)
            {
                emailInput.onFocusOut.Clear();
                emailInput.onChanged.Clear();
            }
            
            GTextInput passwordInput = _winUserLogin.contentPane.GetChild("input_password")?.asTextInput;
            if (passwordInput != null)
            {
                passwordInput.onFocusOut.Clear();
            }
        }
        
        // 移除修改密码窗口中的输入框监听器
        if (_win_ChangePassword != null && _win_ChangePassword.contentPane != null)
        {
            GTextInput newPwdInput = _win_ChangePassword.contentPane.GetChild("input_new_pwd")?.asTextInput;
            if (newPwdInput != null)
            {
                newPwdInput.onFocusOut.Clear();
            }
            
            GTextInput oldPwdInput = _win_ChangePassword.contentPane.GetChild("input_old_pwd")?.asTextInput;
            if (oldPwdInput != null)
            {
                oldPwdInput.onFocusOut.Clear();
            }
        }
        
        // 移除忘记密码窗口中的输入框监听器
        if (_win_ForgetPassword != null && _win_ForgetPassword.contentPane != null)
        {
            GTextInput emailInput = _win_ForgetPassword.contentPane.GetChild("input_email")?.asTextInput;
            if (emailInput != null)
            {
                emailInput.onFocusOut.Clear();
                emailInput.onChanged.Clear();
            }
            
            GTextInput passwordInput = _win_ForgetPassword.contentPane.GetChild("input_password")?.asTextInput;
            if (passwordInput != null)
            {
                passwordInput.onFocusOut.Clear();
            }
        }
    }
}


