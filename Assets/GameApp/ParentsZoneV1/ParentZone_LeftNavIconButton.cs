using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using FairyGUI;
using FairyGUI.Utils;
using LitJson;

public class ParentZone_LeftNavIconButton : GButton
{
    public GImage m_white_bg;
    public GTextField m_text;

    public override void ConstructFromXML(FairyGUI.Utils.XML cxml)
    {
        base.ConstructFromXML(cxml);
        
        m_white_bg = (GImage)GetChildAt(0);
        m_text = (GTextField)GetChildAt(2);
    }

   
}