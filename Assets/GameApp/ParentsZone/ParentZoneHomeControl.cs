using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using SA.IOSNative.StoreKit;
using DG.Tweening;
using MiniJSON_New;
using TMPro;
using LitJson;
using UnityEngine.UI;

#if UNITY_IPHONE
public class ParentsZoneHomeControl : MonoBehaviour
{

    public GameObject banner1, banner2, removeAdsBtn, restoreBtn, fixErrorsBtn, btn_back;
    public GameObject btn_userLogin, btn_icp_no, btn_contactUs, btn_rateUs, btn_eyeMode, btn_moreApps, btn_userTerms, btn_privacy;
    public TextMeshProUGUI member_message;

    public AudioClip btnClickSound;

    public UniWebView _webView;


    public bool canShowWebView = true;
    public string backToSceneName = "Home";

    public List<Sprite> sprite_list;
    // Use this for initialization

    private bool canCheckUnLock = false;

    private JsonData config_data;

    private string _errorMessage;
    private int actionId = 1; //1: buy, 2:restore


    void Start()
    {
        //GlobalVariable.CheckUserOSLanguage();

        if (canShowWebView)
        {

            _webView.OnMessageReceived += OnReceivedMessage;
            _webView.OnPageFinished += OnLoadComplete;
            _webView.OnShouldClose += OnWebViewShouldClose;
            _webView.CleanCache();

            //_webView.Frame = new Rect(x, y, w, h); ;

            //http://iappsteam.duapp.com/index.php?c=parentsZone&m=home&ln=sc&os=ipad&appId=123567
            string ln = GlobalVariable.GetOSLanguageSuffix();
            string web_url = GlobalVariable.GetParentsZoneWebLink();
            web_url += "index.php?c=parentsZone&m=home&ln=" + ln.Replace("-", "");
            web_url += "&os=ipad";
            web_url += "&appId=" + GlobalVariable.AppleAppId;

            _webView.Load(web_url);

            _errorMessage = null;
        }


        InitData();


        PaymentManager.OnStoreKitInitComplete += OnStoreKitInitComplete;
        PaymentManager.OnTransactionComplete += OnTransactionComplete;
        PaymentManager.OnRestoreComplete += OnRestoreComplete;
        PaymentManager.OnVerificationComplete += HandleOnVerificationComplete;

        if (IAppsTeamIOSUntil.IOSCheckContentUnLock(GlobalVariable.IAP_ID1))
        {
            UnLockAction();
        }
        else
        {
            LockAction();
        }


        canCheckUnLock = false;
    }

    // Update is called once per frame
    void Update()
    {

        if (canCheckUnLock)
        {
            if (IAppsTeamIOSUntil.IOSCheckContentUnLock(GlobalVariable.IAP_ID1))
            {
                UnLockAction();
                canCheckUnLock = false;
            }
        }

    }

    void OnLoadComplete(UniWebView webView, int statusCode, string url)
    {
        Debug.Log("Web view statusCode: " + statusCode.ToString());
        Debug.Log("Web view loading finished for: " + url);
        if (banner1) banner1.SetActive(false);
        if (banner2) banner2.SetActive(false);
        webView.Show();
    }

    void OnReceivedMessage(UniWebView webView, UniWebViewMessage message)
    {
        //Debug.Log(message.rawMessage);
        //7. You can get the information out from the url path and query in the UniWebViewMessage
        //For example, a url of "uniwebview://move?direction=up&distance=1" in the web page will 
        //be parsed to a UniWebViewMessage object with:
        //				message.scheme => "uniwebview"
        //              message.path => "move"
        //              message.args["direction"] => "up"
        //              message.args["distance"] => "1"
        // "uniwebview" scheme is sending message to Unity by default.
        // If you want to use your customized url schemes and make them sending message to UniWebView,
        // use webView.AddUrlScheme("your_scheme") and webView.RemoveUrlScheme("your_scheme")

        if (string.Equals(message.Path, "goToUrl"))
        {

            string url = message.Args["uri"];
            Debug.Log("url:" + url);
            if (url != "")
                Application.OpenURL("http://" + url);

        }

        if (string.Equals(message.Path, "goToUrls"))
        {

            string url = message.Args["uri"];
            Debug.Log("url:" + url);
            if (url != "")
                Application.OpenURL("https://" + url);

        }


        if (string.Equals(message.Path, "goToAppStore"))
        {

            string appId = message.Args["appId"];
            Debug.Log("appId:" + appId);
            if (appId != "")
                Application.OpenURL("itms-apps://itunes.com/app/id" + appId);
        }

    }


    //10. If the user close the webview by tap back button (Android) or toolbar Done button (iOS), 
    //    we should set your reference to null to release it. 
    //    Then we can return true here to tell the webview to dismiss.
    bool OnWebViewShouldClose(UniWebView webView)
    {
        if (webView == _webView)
        {
            _webView = null;
            return true;
        }
        return false;
    }



    void InitData()
    {
        btn_icp_no.SetActive(GlobalVariable.GetICP_NO());

        if (IAppsTeamIOSUntil.IsIphoneX())
        {
            btn_back.GetComponent<RectTransform>().anchoredPosition = new Vector2(102.5f, btn_back.GetComponent<RectTransform>().anchoredPosition.y);
        }

        string parentZone_config_data = Resources.Load(GlobalVariable.ParentZone_dataConfig_flag).ToString();
        config_data = JsonMapper.ToObject(parentZone_config_data);

        SetTextForUI(member_message, "member_message");
        SetTextForUI(btn_userLogin.transform.GetChild(0).GetComponent<TextMeshProUGUI>(), "btn_userLogin");
        SetTextForUI(btn_icp_no.transform.GetChild(0).GetComponent<TextMeshProUGUI>(), "btn_icp_no");
        SetTextForUI(fixErrorsBtn.transform.GetChild(0).GetComponent<TextMeshProUGUI>(), "btn_FixErrors");
        SetTextForUI(restoreBtn.transform.GetChild(0).GetComponent<TextMeshProUGUI>(), "btn_Restore");
        SetTextForUI(btn_contactUs.transform.GetChild(0).GetComponent<TextMeshProUGUI>(), "btn_ContactUs");
        SetTextForUI(btn_rateUs.transform.GetChild(0).GetComponent<TextMeshProUGUI>(), "btn_RateUs");
        SetTextForUI(btn_eyeMode.transform.GetChild(0).GetComponent<TextMeshProUGUI>(), "btn_EyeMode");
        SetTextForUI(btn_moreApps.transform.GetChild(0).GetComponent<TextMeshProUGUI>(), "btn_MoreApps");
        SetTextForUI(btn_userTerms.transform.GetChild(0).GetComponent<TextMeshProUGUI>(), "btn_Userterms");
        SetTextForUI(btn_privacy.transform.GetChild(0).GetComponent<TextMeshProUGUI>(), "btn_Privacy");

        SetupClickEvents(btn_userLogin);
        SetupClickEvents(fixErrorsBtn);
        SetupClickEvents(restoreBtn);
        SetupClickEvents(btn_contactUs);
        SetupClickEvents(btn_eyeMode);
        SetupClickEvents(btn_rateUs);
        SetupClickEvents(btn_rateUs);
        SetupClickEvents(btn_moreApps);
        SetupClickEvents(btn_userTerms);
        SetupClickEvents(btn_privacy);
        SetupClickEvents(banner1);
        SetupClickEvents(banner2);
        SetupClickEvents(removeAdsBtn);

    }

    void SetTextForUI(TextMeshProUGUI text_obj, string key)
    {
        text_obj.text = config_data[key][$"text{GlobalVariable.GetOSLanguageSuffix()}"].ToString();
    }

    void SetupClickEvents(GameObject btn_obj)
    {
        if (btn_obj != null)
        {
            Button btn = btn_obj.GetComponentInParent<Button>();
            btn.onClick.RemoveAllListeners(); // 清除之前的监听器
            btn.onClick.AddListener(() =>
            {
                Debug.Log("btn_obj.name:" + btn_obj.name);
                SoundManager.PlaySFX(btnClickSound);
                if (btn_obj.name == "Btn_UserLogin")
                {
                    GameUtility.GoToSceneName_NotAddSuffix("ParentZone_V2");
                }

                if (btn_obj.name == "Btn_EyeMode")
                {
                    EyeMode_Click();
                }

                if (btn_obj.name == "Btn_FixErrors")
                {
                    Button_FixErrors();
                }

                if (btn_obj.name == "Btn_Restore")
                {
                    CheckInternetForRestore();
                }

                if (btn_obj.name == "Btn_ContactUs")
                {
                    FeedBack();
                }

                if (btn_obj.name == "Btn_RateUs")
                {
                    RateUs();
                }

                if (btn_obj.name == "Btn_MoreApps")
                {
                    ShowMoreApps();
                }

                if (btn_obj.name == "Btn_Userterms")
                {
                    GoToUserTermsPage();
                }

                if (btn_obj.name == "Btn_Privacy")
                {
                    GoToPrivacyPage();
                }

                if (btn_obj.name == "1b")
                {
                    Banner2Click();
                }

                if (btn_obj.name == "3b")
                {
                    Banner1Click();
                }

                if (btn_obj.name == "Btn_RemoveAds")
                {
                    ShowLockAction();
                }

             
               

            });
        }
    }

    

    void FeedBack()
    {
        //IAppsTeamIOSUntil.ShowFeedBack();

        GameUtility.GoToSceneName_NotAddSuffix("Feedback");

    }

    void EyeMode_Click()
    {
        GameUtility.GoToSceneName_NotAddSuffix("TimeLimitSetting");
    }

    void ReLoadPage()
    {
        if (_webView)
        {
            _webView.Reload();
        }
    }

    void RateUs()
    {
        /*
        string url = "itms-apps://ax.itunes.apple.com/WebObjects/MZStore.woa/wa/viewContentsUserReviews?type=Purple+Software&id=" + GlobalVariable.AppleAppId;
        if (IAppsTeamIOSUntil.IOSSystemVersion() >= 7.0f)
            url = "itms-apps://itunes.apple.com/app/id" + GlobalVariable.AppleAppId;
        print(url);
        Application.OpenURL(url);
        */
        IAppsTeamIOSUntil.RequestAppstoreReview();
    }

    void ShowMoreApps()
    {
        string url_link = GlobalVariable.GetDevNameUrl();
        Application.OpenURL(url_link);
    }

    void GoToHomePage()
    {
        IAppsTeamIOSUntil.MobClickEvent("GoToHomePage");
        Application.OpenURL("http://www.iappsteam.com");
    }

    void GoToPrivacyPage()
    {
        string language = GlobalVariable.GetDeviceOSLanguage().ToLower();
        string webUrlUserPrivacy = config_data["Config"]["UserPrivacy_URL"][language].ToString();
        
        Application.OpenURL(webUrlUserPrivacy);
    }

    void GoToUserTermsPage()
    {
        string language = GlobalVariable.GetDeviceOSLanguage().ToLower();
        string webUrlUserTerms = config_data["Config"]["UserTerms_URL"][language].ToString();
        Application.OpenURL(webUrlUserTerms);
    }

    void Banner1Click()
    {
        string url = "itms-apps://itunes.apple.com/app/id912712970";
        Application.OpenURL(url);
    }

    void Banner2Click()
    {
        string url = "itms-apps://itunes.apple.com/app/id863844431";
        Application.OpenURL(url);
    }

    #region IAP


    void HandleOnVerificationComplete(VerificationResponse response)
    {
        IOSNativePopUpManager.showMessage("Verification", "Transaction verification status: " + response.Status.ToString());
        UnlockProducts(GlobalVariable.IAP_ProductId1);
        Debug.Log("ORIGINAL JSON: " + response.OriginalJSON);
    }

    void OnTransactionComplete(PurchaseResult result)
    {

        Debug.Log("OnTransactionComplete: " + result.ProductIdentifier);
        Debug.Log("OnTransactionComplete: state: " + result.State);

        switch (result.State)
        {
            case PurchaseState.Purchased:

                //IOSInAppPurchaseManager.Instance.VerifyLastPurchase(IOSInAppPurchaseManager.SANDBOX_VERIFICATION_SERVER);
                UnlockProducts(result.ProductIdentifier);
                break;
            case PurchaseState.Restored:
                //Our product been succsesly purchased or restored
                //So we need to provide content to our user depends on productIdentifier
                UnlockProducts(result.ProductIdentifier);
                //IOSInAppPurchaseManager.Instance.VerifyLastPurchase(IOSInAppPurchaseManager.SANDBOX_VERIFICATION_SERVER);

                break;
            case PurchaseState.Deferred:
                //iOS 8 introduces Ask to Buy, which lets parents approve any purchases initiated by children
                //You should update your UI to reflect this deferred state, and expect another Transaction Complete  to be called again with a new transaction state 
                //reflecting the parent’s decision or after the transaction times out. Avoid blocking your UI or gameplay while waiting for the transaction to be updated.
                break;
            case PurchaseState.Failed:
                //Our purchase flow is failed.
                //We can unlock intrefase and repor user that the purchase is failed. 
                Debug.Log("Transaction failed with error, code: " + result.Error.Code);
                Debug.Log("Transaction failed with error, description: " + result.Error.Message);


                break;
        }

        NativeDialogs.Instance.HideProgressDialog();

        if (result.State == PurchaseState.Failed)
        {
            //IOSNativePopUpManager.showMessage("Transaction Failed", "Error code: " + result.Error.Code + "\n" + "Error description:" + result.Error.Description);
            NativeDialogs.Instance.ShowMessageBox("Transaction Failed", "Error code: " + result.Error.Code + "\n" + "Error description:" + result.Error.Message, new string[] { "Ok" }, false, (string b) =>
            {

            });
        }
        else
        {
            //IOSNativePopUpManager.showMessage("Store Kit Response", "product " + result.ProductIdentifier + " state: " + result.State.ToString());
            //NativeDialogs.Instance.ShowMessageBox("Store Kit Response", "product " + result.ProductIdentifier + " state: " + result.State.ToString(), new string[]{"Ok"}, false, (string b) => {

            //});

            Debug.Log("Store Kit Response:product " + result.ProductIdentifier + " state: " + result.State.ToString());
        }

    }

    void UnlockProducts(string productIdentifier)
    {
        Debug.Log("purchased product: " + productIdentifier);
        IAppsTeamIOSUntil.MobClickEvent("success_buy");
        IAppsTeamIOSUntil.AddCode(GlobalVariable.IAP_ID1);
        canCheckUnLock = true;

    }

    void OnRestoreComplete(RestoreResult res)
    {
        NativeDialogs.Instance.HideProgressDialog();
        if (res.IsSucceeded)
        {
            //IOSNativePopUpManager.showMessage("Success", "Restore Compleated");
            Debug.Log("restoreTransactionsFinished");
            IAppsTeamIOSUntil.MobClickEvent("restore_buy");
            UnlockProducts(GlobalVariable.IAP_ProductId1);
            canCheckUnLock = true;
            //NativeDialogs.Instance.HideProgressDialog();
        }
        else
        {
            //IOSNativePopUpManager.showMessage("Error: " + res.Error.Code, res.Error.Description);
            //NativeDialogs.Instance.HideProgressDialog();
            Debug.Log("restoreTransactionsFailed: " + "Error: " + res.Error.Code + " " + res.Error.Message);

            //string message = "Restore transactions failed, please try again";
            NativeDialogs.Instance.ShowMessageBox("Error: " + res.Error.Code, res.Error.Message, new string[] { "Ok" }, false, (string b) =>
            {

            });
        }
    }

    void ShowBuyInfo(string price)
    {
        Debug.Log("ShowBuyInfo");

        string title = GameUtility.LocalizedString("purchaseTitle") + " " + price;
        //title += "\n" + GameUtility.LocalizedString("purchaseDesc");
        string btn1 = GameUtility.LocalizedString("btn1");
        string btn2 = GameUtility.LocalizedString("btn2");
        string btn3 = GameUtility.LocalizedString("btn3");

        NativeDialogs.Instance.ShowMessageBox(title, "", new string[] { btn1, btn2, btn3 }, false, (string b) =>
        {

            Debug.Log(b);
            if (b == btn1)
            {
                //StoreKitBinding.purchaseProduct(GlobalVariable.IAP_ProductId1, 1);
                //NativeDialogs.Instance.ShowProgressDialog("in the process of purchasing", "", false, false);

                PaymentManager.Instance.BuyProduct(GlobalVariable.IAP_ProductId1);
                NativeDialogs.Instance.ShowProgressDialog("in the process of purchasing", "", false, false);
            }
            else if (b == btn2)
            {
                //StoreKitBinding.restoreCompletedTransactions();
                //NativeDialogs.Instance.ShowProgressDialog("restoring...", "", false, false);

                PaymentManager.Instance.RestorePurchases();
                NativeDialogs.Instance.ShowProgressDialog("restoring...", "", false, false);
            }
            else if (b == btn3)
            {
                Debug.Log(b);

            }

        });
    }

    void OnStoreKitInitComplete(SA.Common.Models.Result result)
    {

        NativeDialogs.Instance.HideProgressDialog();
        if (result.IsSucceeded)
        {
            //IOSNativePopUpManager.showMessage("StoreKit Init Succeeded", "Available products count: " + IOSInAppPurchaseManager.instance.Products.Count.ToString());
            //Debug.Log("StoreKit Init Succeeded Available products count: " + IOSInAppPurchaseManager.instance.Products.Count.ToString());

            string strPrice = "", currencySymbol = "", currencyCode = "", priceInfo = "";
            // Do something more useful with the products than printing them to the console
            foreach (Product product in PaymentManager.Instance.Products)
            {
                //Debug.Log( product.ToString() + "\n" );
                priceInfo = product.CurrencySymbol + product.Price;

                Debug.Log("product.currencySymbol:" + product.CurrencySymbol);
                Debug.Log("product.CurrencyCode:" + product.CurrencyCode);
                Debug.Log("product.Price:" + product.Price.ToString());

                strPrice = product.Price.ToString();
                currencySymbol = product.CurrencySymbol;
                currencyCode = product.CurrencyCode;
            }

            string savePriceFlag = GlobalVariable.SavePriceFlag;
            string defaultPrice = GlobalVariable.DefaultPrice;

            if (actionId == 1)
            {
                if (strPrice == "-0.99")
                {
                    priceInfo = PlayerPrefs.GetString(savePriceFlag, defaultPrice);

                }
                else
                {
                    PlayerPrefs.SetString(savePriceFlag, priceInfo);
                }

                priceInfo = PlayerPrefs.GetString(savePriceFlag, defaultPrice);


                ShowBuyInfo(priceInfo);
            }
            else
            {
                RestoreAction();
            }

        }
        else
        {
            //IOSNativePopUpManager.showMessage("StoreKit Init Failed",  "Error code: " + result.Error.Code + "\n" + "Error description:" + result.Error.Description);
            NativeDialogs.Instance.ShowMessageBox("StoreKit Init Failed", "Error code: " + result.Error.Code + "\n" + "Error description:" + result.Error.Message, new string[] { "Ok" }, false, (string b) =>
            {

            });
        }
    }


    void LockAction()
    {
        GameUtility_UGUI.ChangeSpriteImage(removeAdsBtn, sprite_list, "RemoveAds" + GlobalVariable.GetOSLanguageSuffix(),true);
        
        Button_ScaleInOut(removeAdsBtn);
    }

    public void ShowLockAction()
    {
        Debug.Log("Buy Tip");
        if (IAppsTeamIOSUntil.IOSCheckContentUnLock(GlobalVariable.IAP_ID1)) return;

        string title1 = GameUtility.LocalizedString("title1");
        string message = GameUtility.LocalizedString("title2");
        NativeDialogs.Instance.ShowMessageBox(title1, message, new string[] { "No", "Yes" }, false, (string b) =>
        {

            Debug.Log(b);
            if (b == "Yes")
            {
                CheckInternet();
            }

        });
    }

    void CheckInternet()
    {
        if (!IAppsTeamIOSUntil.ConnectedToNetwork())
        {
            print("Please connect to the internet and try again");
            string title = GameUtility.LocalizedString("NetWorkFailed");

            NativeDialogs.Instance.ShowMessageBox(title, "", new string[] { "Ok" }, false, (string b) =>
            {

            });
        }
        else
        {
            print("connected to internet");
            CheckCanPayments();
        }
    }

    void CheckInternetForRestore()
    {
        if (!IAppsTeamIOSUntil.ConnectedToNetwork())
        {
            print("Please connect to the internet and try again");
            string title = GameUtility.LocalizedString("NetWorkFailed");

            NativeDialogs.Instance.ShowMessageBox(title, "", new string[] { "Ok" }, false, (string b) =>
            {

            });
        }
        else
        {
            print("connected to internet");
            CheckCanPaymentsForRestore();
        }
    }


    void CheckCanPayments()
    {
        string message = "";
        //if(StoreKitBinding.canMakePayments())
        if (PaymentManager.Instance.IsInAppPurchasesEnabled)
        {
            IAppsTeamIOSUntil.MobClickEvent("click_buy");

            actionId = 1;
            PaymentManager.Instance.AddProductId(GlobalVariable.IAP_ProductId1);

            PaymentManager.Instance.LoadStore();

            message = GameUtility.LocalizedString("Loading Product Data");

            NativeDialogs.Instance.ShowProgressDialog("", message, false, false);
        }
        else
        {
            message = GameUtility.LocalizedString("alertText1");
            string title = GameUtility.LocalizedString("alert");
            string btn1 = GameUtility.LocalizedString("close");

            NativeDialogs.Instance.ShowMessageBox(title, message, new string[] { btn1 }, false, (string b) =>
            {

            });
        }
    }

    void CheckCanPaymentsForRestore()
    {
        string message = "";
        //if(StoreKitBinding.canMakePayments())
        if (PaymentManager.Instance.IsInAppPurchasesEnabled)
        {
            IAppsTeamIOSUntil.MobClickEvent("restore_buy");

            actionId = 2;

            PaymentManager.Instance.AddProductId(GlobalVariable.IAP_ProductId1);

            PaymentManager.Instance.LoadStore();

        }
        else
        {
            message = GameUtility.LocalizedString("alertText1");
            string title = GameUtility.LocalizedString("alert");
            string btn1 = GameUtility.LocalizedString("close");

            NativeDialogs.Instance.ShowMessageBox(title, message, new string[] { btn1 }, false, (string b) =>
            {

            });
        }
    }

    void RestoreAction()
    {
        PaymentManager.Instance.RestorePurchases();
        NativeDialogs.Instance.ShowProgressDialog("restoring...", "", false, false);
    }

    void UnLockAction()
    {
        GameUtility_UGUI.ChangeSpriteImage(removeAdsBtn, sprite_list, "NoAds" + GlobalVariable.GetOSLanguageSuffix(),true);
        
        Button_ScaleInOut(removeAdsBtn);

        restoreBtn.SetActive(false);

        //HOTween.Pause("PZPaidBTN_Scale");

        DOTween.Pause("PZPaidBTN_Scale");

        removeAdsBtn.transform.localScale = new Vector3(1, 1, 1);
    }

    void OnDisable()
    {
        /*			
            StoreKitManager.productListReceived -= productListReceived;
            StoreKitManager.purchaseSuccessful -= purchaseSuccessful;
            StoreKitManager.restoreTransactionsFinished -= restoreTransactionsFinished;
            StoreKitManager.restoreTransactionsNoRecordsFinished -= restoreTransactionsNoRecordsFinished;
            StoreKitManager.purchaseCancelled -= purchaseCancelled;
            StoreKitManager.purchaseFailed -= purchaseFailed;
            StoreKitManager.productListRequestFailed -= productListRequestFailed;
            StoreKitManager.restoreTransactionsFailed -= restoreTransactionsFailed;
            */
        PaymentManager.OnStoreKitInitComplete -= OnStoreKitInitComplete;
        PaymentManager.OnTransactionComplete -= OnTransactionComplete;
        PaymentManager.OnRestoreComplete -= OnRestoreComplete;
        PaymentManager.OnVerificationComplete -= HandleOnVerificationComplete;
    }

    #endregion

    void Button_ScaleInOut(GameObject button)
    {
        /*
        TweenParms parms1 = new TweenParms();
        parms1.Id("PZPaidBTN_Scale");
        parms1.Prop("localScale", new Vector3(1.15f, 1.15f, 1.15f));
        parms1.AutoKill(true);
        parms1.Ease(EaseType.Linear); //加速度类型
        parms1.Loops(-1, LoopType.Yoyo);//设置循环类型为往复循环
        parms1.Delay(0); //延迟时间
        HOTween.To(button.transform, 1, parms1);
*/
        button.transform.DOScale(new Vector3(1.15f, 1.15f, 1.15f), 1).SetId("PZPaidBTN_Scale")
                 .SetLoops(-1, LoopType.Yoyo).SetEase(Ease.Linear).SetDelay(0);
    }

    void Button_FixErrors()
    {
        //StartCoroutine(Button_FixErrors_Click());

        string title1 = "";
        string message = GameUtility.LocalizedString("FixErrors_Title");
        string no1 = GameUtility.LocalizedString("Cancel");
        string yes1 = GameUtility.LocalizedString("Submit");

        NativeDialogs.Instance.ShowMessageBox(title1, message, new string[] { no1, yes1 }, false, (string b) =>
        {

            Debug.Log(b);
            if (b == yes1)
            {
                StartCoroutine(Button_FixErrors_Click());
            }

        });




    }

    IEnumerator Button_FixErrors_Click()
    {
        fixErrorsBtn.SetActive(false);

        //yield return new WaitForSeconds(0);

        string message = GameUtility.LocalizedString("processing");


        NativeDialogs.Instance.ShowProgressDialog(message, "", false, false);
        Caching.ClearCache();

        //clean unzip folder
        //get zip crc value
        string AB_Zip_Scenes_data = Resources.Load(GlobalVariable_Flashcard.AB_Zip_Scenes_data).ToString();

        var a = Json.Deserialize(AB_Zip_Scenes_data) as Dictionary<string, object>;

        foreach (string key in a.Keys)
        {
            Dictionary<string, object> dict_zip = GameUtility_JSON.ReadDictionaryValue(AB_Zip_Scenes_data, key);

            foreach (string dict_zip_key in dict_zip.Keys)
            {
                Debug.Log(dict_zip_key);

                string folderPath = string.Format("{0}/{1}", GameUtility.GetSaveFilePath() + "AB", dict_zip_key);

#if UNITY_EDITOR

                folderPath = string.Format("{0}/{1}", Application.dataPath + "/Test_DownZip/" + "AB", dict_zip_key);

#endif
                Debug.Log(folderPath);
                if (System.IO.Directory.Exists(folderPath))
                {
                    System.IO.Directory.Delete(folderPath, true);

                    Debug.Log("Delete OK : " + folderPath);
                }

                GameManager.Instance().ResetDownloadZipSuccess(dict_zip_key);
                Debug.Log("ResetDownloadZipSuccess : " + dict_zip_key);

            }
        }

        string FE_Zip_Scenes_data = Resources.Load(GlobalVariable_Flashcard.FE_Zip_Scenes_data).ToString();

        a = Json.Deserialize(FE_Zip_Scenes_data) as Dictionary<string, object>;

        foreach (string key in a.Keys)
        {
            Dictionary<string, object> dict_zip = GameUtility_JSON.ReadDictionaryValue(FE_Zip_Scenes_data, key);

            foreach (string dict_zip_key in dict_zip.Keys)
            {
                Debug.Log(dict_zip_key);

                string folderPath = string.Format("{0}/{1}", GameUtility.GetSaveFilePath() + "AB", dict_zip_key);

#if UNITY_EDITOR

                folderPath = string.Format("{0}/{1}", Application.dataPath + "/Test_DownZip/" + "AB", dict_zip_key);

#endif
                Debug.Log(folderPath);
                if (System.IO.Directory.Exists(folderPath))
                {
                    System.IO.Directory.Delete(folderPath, true);

                    Debug.Log("Delete OK : " + folderPath);
                }

                GameManager.Instance().ResetDownloadZipSuccess(dict_zip_key);
                Debug.Log("ResetDownloadZipSuccess : " + dict_zip_key);

            }
        }


        yield return new WaitForSeconds(5);

        NativeDialogs.Instance.HideProgressDialog();

        string btn1 = GameUtility.LocalizedString("OK");
        message = GameUtility.LocalizedString("Success");

        NativeDialogs.Instance.ShowMessageBox("", message, new string[] { btn1 }, false, (string b) =>
        {

        });


    }

    void OnDestroy()
    {
        // 移除所有按钮的点击监听事件
        RemoveAllButtonListeners();
    }

    void RemoveAllButtonListeners()
    {
        // 移除所有按钮的点击监听器
        RemoveButtonListener(btn_userLogin);
        RemoveButtonListener(fixErrorsBtn);
        RemoveButtonListener(restoreBtn);
        RemoveButtonListener(btn_contactUs);
        RemoveButtonListener(btn_eyeMode);
        RemoveButtonListener(btn_rateUs);
        RemoveButtonListener(btn_moreApps);
        RemoveButtonListener(btn_userTerms);
        RemoveButtonListener(btn_privacy);
        RemoveButtonListener(banner1);
        RemoveButtonListener(banner2);
        RemoveButtonListener(removeAdsBtn);
        RemoveButtonListener(btn_back);

        if (canShowWebView)
        {
            _webView.Hide();
            Destroy(_webView);
            _webView.OnMessageReceived -= OnReceivedMessage;
            _webView.OnPageFinished -= OnLoadComplete;
            _webView.OnShouldClose -= OnWebViewShouldClose;
            _webView = null;
        }
    }
    
    void RemoveButtonListener(GameObject btn_obj)
    {
        if (btn_obj != null)
        {
            Button btn = btn_obj.GetComponentInParent<Button>();
            if (btn != null)
            {
                btn.onClick.RemoveAllListeners();
            }
        }
    }
}
#endif