using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using LitJson;

public class GlobalVariable : MonoBehaviour
{

	public static int right_pos;//当前正确答案的位置，方案判断,从左到右，0为第一个
								//设定设备类型
								//public static bool is_iphone5 = true; //默认：true，部署的时候，请用true
	public static bool mapOfButtonHasClick = false;
	public static int current_cardId = 1, current_learnEnglish_id = 1, current_learnEnglish_countId = -1,
		max_cardId = 29;//字卡分类更新需要修改
	public static bool needCleanNetCache = true;
	public static int canMusic = 1;
	public static int canSound = 1;
	public static int canSpeaker = 1;
	public static string rVersion = "30", saveNewAppVersion_flag = "IOS_NewAppVersion";//config version
	public static int iAppVersion = 30;//app version
	public static bool canCardMove = false;
	public static bool canMogoShowLog = false;
	public static bool swipe = false;
	public static string Share_ID1 = "ShareContent1", UMengKey = "537c821f56240bb5a8031900";
	public static string DefaultPrice = "$3.99", SavePriceFlag = "AllInOnePrice";

	//限时模式
	public static bool isTimeLimitTestMode = false;//测试模式

	public static bool timeLimitMode = false;
	public static string timeLimitSettingValue = "TimeLimitSettingValue", restTimeSettingValue = "RestTimeSettingValue", timeLimitModeKey = "OpenTimeLimitMode";
	public static string timeLimitSettingDefaultValue = "15", restTimeSettingDefaultValue = "5";
	public static string timeLimit_ReadyGoToScene = "Home";


	public static string IAP_ID1 = "ChineseFlashCardsForBabyFullVersion", IAP_ProductId1 = "ChineseFlashCardsForBabyFullVersion";
	public static string AppleAppId = "*********";

	public static string AdsConfig_Ver = "AdsConfig_Ver", AdsConfig_Content = "AdsConfig_Content", AdsConfig_Name = "ads_v2",
					 AdsConfig_Refresh = "AdsConfig_Refresh", AdsConfigFilePath = "";

	public const string app_prex_url = "https://itunes.apple.com/app/apple-store/id",
	app_suffix_url = "?pt=451888&ct=App*********&mt=8";


	public static string ParentZoneClickedNavName = "MyAccount";
	public static string accountInfo_save_AccessToken_flag = "userAccount_AccessToken";
	public static string accountInfo_save_AccessTokenId_flag = "userAccount_AccessTokenId";
	public static string accountInfo_save_DisplayName_flag = "userAccount_DisplayName";
	public static string accountInfo_save_UnionId_flag = "userAccount_UnionId";//华为
	public static string accountInfo_save_Email_flag = "userAccount_Email";
	public static string accountInfo_randomGuid_flag = "userAccount_RandomGuid";

	public const string accountInfo_AppleUserIdKey = "userAccount_AppleUserId";

	public static string accountInfo_firstRegister_flag = "userAccount_FirstRegister";

	public const string accountInfo_IsAppleIdAccount = "userAccount_IsAppleIdAccount";

	public static string ParentZone_dataConfig_flag = "Data_ParentZoneV1_Config";

	public static string prex_SaveLearnMath1_flag = "L_LearnMath1_Process", carMoveLearnMath1_flag = "carMoveLearnMath1_flag",
						 LevelUpFinish_flag = "L_LearnMath1_LevelUpFinish";

	//数学
	public static int current_LearnMath1_Level = 1, max_LearnMath1_Level = 13;
	public static float LearnMath1_Level_Map_Width = 0;

	public enum AppChannel
	{
		AppStore,
		Mall360,
		XiaoMi,
		Baidu,
		Samsung,
		GooglePlay,
		Amazon,
		QQ,
		Hiapk,
		AnZhi,
		Uc,
		MM,
		Wandoujia,
		Gfan,
	}
	public static AppChannel appChannel = AppChannel.MM;

	public enum MoreApps_FromPage
	{
		Home,
		ParentZone
	}
	public static MoreApps_FromPage moreApps_FromPage = MoreApps_FromPage.Home;

	public enum Setting_FromPage
	{
		Flashcard,
		ParentZone
	}
	public static Setting_FromPage setting_FromPage = Setting_FromPage.Flashcard;



	public enum OSLanguage
	{
		SimplifiedChinese,
		TraditionalChinese,
	}
	public static OSLanguage osLanguage = OSLanguage.SimplifiedChinese;

	public enum SoundMode
	{
		Ma_En,
		Ca_En,
	}
	public static SoundMode soundMode = SoundMode.Ma_En;

	public enum FirstSound
	{
		En,
		Ma,
		Ca
	}
	public static FirstSound firstSound = FirstSound.Ma;


	public enum FirstSound_LearnEnglish
	{
		En,
		Ma,
		Ca
	}
	public static FirstSound_LearnEnglish firstSound_learnEnglish = FirstSound_LearnEnglish.En;

	public enum FirstSound_Habits
	{
		En,
		Ma,
		Ca
	}
	public static FirstSound_Habits firstSound_Habits = FirstSound_Habits.Ma;

	public enum FirstSound_Zoo
	{
		En,
		Ma,
		Ca
	}
	public static FirstSound_Zoo firstSound_Zoo = FirstSound_Zoo.Ma;

	public enum FirstSound_LearnMath
	{
		En,
		Ma,
		Ca
	}
	public static FirstSound_LearnMath firstSound_LearnMath = FirstSound_LearnMath.Ma;


	public enum PaidAds_FromPage
	{
		Home,
		Flashcard,
		LearnEnglish,
		Habits,
		ABC,
		Zoo,
		LearnMath1,
		EarlyEdu_Home,
		ParentZone
	}
	public static PaidAds_FromPage paidAds_FromPage = PaidAds_FromPage.Home;

	public enum FlashcardCategoryPageNo
	{
		p1,
		p2,
		p3,
		p4,
		p5
	}
	public static FlashcardCategoryPageNo fd_CategoryPageNo = FlashcardCategoryPageNo.p1;


	public enum Flashcard_QNA_Test_FromPage
	{
		FC_Category,
		FC_Card,
		FC_CardNet
	}
	public static Flashcard_QNA_Test_FromPage fc_QNA_Test_FromPage = Flashcard_QNA_Test_FromPage.FC_Category;

	//check is iphone5?
	public static bool CheckIsIphone5()
	{
		//return true;   
		//return false;    

		if ((Screen.width == 960 && Screen.height == 640)
			|| (Screen.width == 960 * 0.5 && Screen.height == 640 * 0.5))
		{
			return false;
		}
		else
		{
			return true;
		}
	}


	public static string GetOSLanguageSuffix()
	{
		string strResult = "-sc";
		if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
		{
			strResult = "-sc";
		}
		else
		{
			strResult = "-tc";
		}
		//strResult = "-tc";
		return strResult;
	}

	public static void CheckUserOSLanguage()
	{
		string language = PlayerPrefs.GetString("OSLanguage");
		//Debug.Log("PlayerPrefs.language:" + language);

		if (language == "")
		{
			//采用系统的语言
			SystemLanguage systemLanguage = Application.systemLanguage;

			//Debug.Log("Unity systemLanguage:" + systemLanguage);

			// 根据系统语言设置对应的语言
			switch (systemLanguage)
			{
				case SystemLanguage.Chinese:
				case SystemLanguage.ChineseSimplified:
					GlobalVariable.osLanguage = GlobalVariable.OSLanguage.SimplifiedChinese;
					break;
				case SystemLanguage.ChineseTraditional:
					GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
					break;
				default:
					// 其他语言默认使用简体中文
					GlobalVariable.osLanguage = GlobalVariable.OSLanguage.SimplifiedChinese;
					break;
			}
		}
		else if (language == "SimplifiedChinese")
		{
			//采用用户自己设定的语言
			GlobalVariable.osLanguage = GlobalVariable.OSLanguage.SimplifiedChinese;
		}
		else
		{
			GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
		}

		//Debug.Log("language:" + GlobalVariable.osLanguage.ToString());
	}



	public static string GetDeviceOSLanguageSuffix()
	{
		string ln = GlobalVariable.GetDeviceOSLanguage();
		//return "-en";
		if (ln == "sc")
		{
			return "-sc";
		}
		else if (ln == "tc")
		{
			return "-tc";
		}
		else
		{
			return "-en";
		}
	}

	public static string GetDeviceOSLanguage()
	{
		//采用系统的语言
		SystemLanguage systemLanguage = Application.systemLanguage;
		string str = "";

		Debug.Log("Unity systemLanguage:" + systemLanguage);

		// 根据系统语言设置对应的语言代码
		switch (systemLanguage)
		{
			case SystemLanguage.Chinese:
			case SystemLanguage.ChineseSimplified:
				str = "sc";
				break;
			case SystemLanguage.ChineseTraditional:
				str = "tc";
				break;
			default:
				str = "en";
				break;
		}

		//str = "en";
		return str;
	}

	public static void SettingSoundOnOffToSaveDisk()
	{
		GlobalVariable.canSound = GameManager.Instance().GetSoundOnOff();
		if (GlobalVariable.canSound == 1)
		{
			GlobalVariable.canSound = 0;
		}
		else
		{
			GlobalVariable.canSound = 1;
		}

		GameManager.Instance().SaveSoundOnOff();
	}

	public static bool CanSoundAction()
	{
		if (GlobalVariable.canSound == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}

	public static bool CanSpeakerAction()
	{
		if (GlobalVariable.canSpeaker == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}

	public void SaveSoundOnOff()
	{
		PlayerPrefs.SetInt("canSound", GlobalVariable.canSound);
	}

	public int GetSoundOnOff()
	{
		return PlayerPrefs.GetInt("canSound", GlobalVariable.canSound);
	}

	public static void SettingMusicOnOff()
	{
		GlobalVariable.canMusic = GameManager.Instance().GetMusicOnOff();
		if (GlobalVariable.canMusic == 1)
		{
			GlobalVariable.canMusic = 0;
		}
		else
		{
			GlobalVariable.canMusic = 1;
		}

		GameManager.Instance().SaveMusicOnOff();
	}

	public static void SwitchOSLanguage()
	{
		if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
		{
			GlobalVariable.osLanguage = GlobalVariable.OSLanguage.TraditionalChinese;
		}
		else
		{
			GlobalVariable.osLanguage = GlobalVariable.OSLanguage.SimplifiedChinese;
		}

		GameManager.Instance().SaveOsLanguage();
	}

	public static bool CanMusicAction()
	{
		if (GlobalVariable.canMusic == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}

	public static string GetNormalBannerType()
	{

		string strType = "";

		Debug.Log("NormalBanner:" + strType);
		if (strType == "admob")
		{
			strType = "admob";
		}
		else if (strType == "baidumob")
		{
			strType = "baidumob";
		}
		else
		{
			strType = "admob";
		}
		return strType;
	}

	public static string GetInterstitialAdTypeForSC()
	{
		string strType = "";

		if (strType == "chartboost")
		{
			strType = "chartboost";
		}
		else if (strType == "admob")
		{
			strType = "admob";
		}
		else if (strType == "baidumob")
		{
			strType = "baidumob";
		}
		else
		{
			strType = "admob";
		}

		return strType;
	}

	public static string GetInterstitialAdTypeForEN()
	{
		string strType = "";

		if (strType == "chartboost")
		{
			strType = "chartboost";
		}
		else
		{
			strType = "admob";
		}

		return strType;
	}

	public static bool GetInterstitialOpen()
	{

		string strType = "ON";

		if (strType == "OFFOXX")
		{
			return false;
		}
		else
		{
			return true;
		}

	}

	public static bool GetNormalAdOpen()
	{

		string strType = "ON";

		if (strType == "asfsm2kdfdf2")
		{
			return false;
		}
		else
		{
			return true;
		}

	}

	public static string GetParentsZoneWebLink()
	{
		string strType = "";
#if UNITY_IPHONE
		strType = IAppsTeamIOSUntil.GetConfigParams("ParentsZoneWebPage");
#endif

		if (strType == "" || strType == null)
		{
			strType = "http://aps1.appfun8.com/";
		}

		return strType;
	}

	public static bool GetOpenWeChatFlag()
	{
		string strType = "";
#if UNITY_IPHONE
		strType = IAppsTeamIOSUntil.GetConfigParams("OpenWechat");
#endif

		if (strType == "1")
		{
			return true;
		}
		else
		{
			return false;
		}

	}

	public static string GetGoodSoundPath()
	{
		int good_id = Random.Range(1, 10);
		string goodSoundPath = "Sounds/Common/good" + good_id.ToString();
		return goodSoundPath;
	}

	public static string GetGoodClothingSoundPath()
	{
		int good_id = Random.Range(1, 6);
		string goodSoundPath = "Sounds/Common/clothing" + good_id.ToString();
		return goodSoundPath;
	}

	public static bool CheckTimeUpForEyeMode(string prevScene, bool needFadeIn = true)
	{
		if (GlobalVariable.timeLimitMode)
		{
			if (FindObjectOfType<TimeLimitMonitor>().CheckTimeUp())
			{
				GlobalVariable.timeLimit_ReadyGoToScene = prevScene;

				GameUtility.GoToSceneName_NotAddSuffix("TimeLimitTakeABreak", needFadeIn);

				return true;
			}
			else
			{
				return false;
			}
		}
		else
		{
			return false;
		}

	}

	#region web action
	public static string GetWebAddr()
	{
		string strType = "";

		if (strType == "" || strType == null)
		{
			strType = "http://fca1.appfun8.com/";
		}

		return strType;
	}

	#endregion

	#region GameMakerLibrary

	public static AudioClip[] SwitchVoiceSettingForCA(AudioClip[] origin_voiceAudio)
	{
		if (GameManager.Instance().GetReadLanguage() == "ca")
		{
			GlobalVariable.readLanguageMode = GlobalVariable.ReadLanguageMode.ca;

			AudioClip[] voiceAudioClips_CA = new AudioClip[origin_voiceAudio.Length];
			for (int i = 0; i < origin_voiceAudio.Length; i++)
			{
				voiceAudioClips_CA[i] = Resources.Load<AudioClip>(GlobalVariable.soundPathFolder + "/CA/" + origin_voiceAudio[i].name);
			}

			return voiceAudioClips_CA;

		}
		else
		{
			return origin_voiceAudio;
		}

	}

	public static string soundPathFolder = "";

	public enum ReadLanguageMode
	{
		en,
		cn,
		ca
	}
	public static ReadLanguageMode readLanguageMode = ReadLanguageMode.cn;

	#endregion


	public static string LoadAppConfig()
	{
		string path = PlayerPrefs.GetString(GlobalVariable.AdsConfigFilePath, string.Empty);
		if (!string.IsNullOrEmpty(path) && File.Exists(path))
		{
			return File.ReadAllText(path);
		}
		else
		{
			string default_configData = Resources.Load(GlobalVariable.AdsConfig_Name).ToString();
			Debug.LogWarning("Config file not found or path is empty. Use Default Config");
			return default_configData;
		}
	}

	public static string GetDevNameUrl()
	{
		string url_link = "https://apps.apple.com/developer/%E6%99%BA%E5%AA%9B-%E5%86%AF/id420919803?uo=4";
		try
		{

			string config_data = LoadAppConfig();

			JsonData data = JsonMapper.ToObject(config_data);

			string str_value = data["dev"]["n"].ToString();

			if (str_value.Equals("1"))
			{
				url_link = "http://appstore.com/iappsteam";
			}
		}
		catch
		{
			Debug.LogWarning(string.Format("GetIntFromConfig has error from key : {0}_{1}", "dev", "n"));

		}

		return url_link;
	}

	public static bool GetICP_NO()
	{
		bool v = true;

		if (GetDeviceOSLanguage() == "sc")
		{
			try
			{
				string config_data = LoadAppConfig();

				JsonData data = JsonMapper.ToObject(config_data);

				string str_value = data["dev"]["ic"].ToString();

				if (str_value.Equals("0"))
				{
					v = false;
				}
				else
				{
					v = true;
				}

			}
			catch
			{
				Debug.LogWarning(string.Format("GetIntFromConfig has error from key : {0}_{1}", "ic", "n"));

			}
		}
		else
		{
			v = false;
		}

		return v;
	}

	public static string GetWebAddr_Ads()
	{
		string strType = string.Empty;
#if UNITY_IPHONE
		strType = IAppsTeamIOSUntil.GetConfigParams("web_addr_ads");
#endif

		if (strType == "" || strType == null)
		{
			strType = "http://cona.appfun8.com/";
		}

		return strType;
	}

	public static bool GetICP_NO_Link()
	{
		bool v = true;

		try
		{
			string config_data = LoadAppConfig();

			JsonData data = JsonMapper.ToObject(config_data);

			string str_value = data["dev"]["iclk"].ToString();

			if (str_value.Equals("0"))
			{
				v = false;
			}
			else
			{
				v = true;
			}

		}
		catch
		{
			Debug.LogWarning(string.Format("GetIntFromConfig has error from key : {0}_{1}", "iclk", "n"));

		}

		return v;
	}

	public static string GetWebSevice_Link()
	{
		string v = "https://cws1.iappsteam.com/api";

		try
		{
			string config_data = LoadAppConfig();
			Debug.Log($"now config_data:{config_data}");
			JsonData data = JsonMapper.ToObject(config_data);

			string str_value = data["do"]["n1"].ToString();

			if (str_value.Equals("1"))
			{
				v = "https://cws1.iappsteam.com/api";
			}
			else if (str_value.Equals("2"))
			{
				v = "https://cws1.appfun8.com/api";
			}
			else if (str_value.Equals("3"))
			{
				v = "https://cws1.kidsfunworld.cn/api";
			}
			else if (str_value.Equals("4"))
			{
				v = "https://cws2.iappsteam.com/api";
			}
			else if (str_value.Equals("5"))
			{
				v = "https://aws2.qfuntech.com/api";
			}
			else if (str_value.Equals("6"))
			{
				v = "https://cws3.iappsteam.com/api";
			}
			else
			{
				v = "https://cws1.iappsteam.com/api";
			}

		}
		catch
		{
			Debug.LogWarning(string.Format("GetIntFromConfig has error from key : {0}_{1}", "n1", "n"));
			v = "https://cws1.iappsteam.com/api";
		}

		return v;
	}
	
	public static string GetLanguageCode()
	{
		// 语言代码：zh_CN(简体中文)、zh_TW(繁体中文)、en(英文)
		string strResult = "en";
		
	
		if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
		{
			strResult = "zh_CN";
		}
		else
		{
			strResult = "zh_TW";
		}

		return strResult;
	}
}
